"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8128],{1839:(e,r,n)=>{n.d(r,{TM:()=>a.TM,Vh:()=>a.Vh,ah:()=>a.JV,l1:()=>l,Z$:()=>u,vP:()=>t.vP,LP:()=>t.LP,jY:()=>c});var t=n(92135),a=n(38749),d=n(12691);function l(e){switch(e){case d.lk.SearchingDriver:return"Поиск водителя";case d.lk.DriverAssigned:return"Водитель назначен";case d.lk.DriverHeading:return"Водитель в пути";case d.lk.DriverArrived:return"Водитель прибыл";case d.lk.RideStarted:return"Поездка началась";case d.lk.RideFinished:return"Поездка завершена";case d.lk.PaymentPending:return"Ожидание оплаты";case d.lk.PaymentCompleted:return"Оплата завершена";case d.lk.ReviewPending:return"Ожидание отзыва";case d.lk.CancelledByClient:return"Отменено клиентом";case d.lk.CancelledByDriver:return"Отменено водителем";case d.lk.CancelledBySystem:return"Отменено системой";case d.lk.CancelledByOperator:return"Отменено оператором";default:return""}}function u(){return Object.values(d.lk).map(e=>({value:e,label:l(e)}))}function c(e){switch(e){case d.VQ.Requested:return"Запрошена";case d.VQ.Searching:return"Поиск водителя";case d.VQ.Accepted:return"Принята водителем";case d.VQ.Arrived:return"Водитель прибыл";case d.VQ.InProgress:return"В процессе";case d.VQ.Completed:return"Завершена";case d.VQ.Cancelled:return"Отменена";default:return""}}},12691:(e,r,n)=>{n.d(r,{Re:()=>a,lk:()=>d,ZN:()=>t,VQ:()=>l});var t=function(e){return e.Unknown="Unknown",e.Instant="Instant",e.Scheduled="Scheduled",e.Partner="Partner",e.Shuttle="Shuttle",e.Subscription="Subscription",e}({}),a=function(e){return e.Pending="Pending",e.Scheduled="Scheduled",e.InProgress="InProgress",e.Completed="Completed",e.Cancelled="Cancelled",e.Expired="Expired",e}({}),d=function(e){return e.SearchingDriver="SearchingDriver",e.DriverAssigned="DriverAssigned",e.DriverHeading="DriverHeading",e.DriverArrived="DriverArrived",e.RideStarted="RideStarted",e.RideFinished="RideFinished",e.PaymentPending="PaymentPending",e.PaymentCompleted="PaymentCompleted",e.ReviewPending="ReviewPending",e.CancelledByClient="CancelledByClient",e.CancelledByDriver="CancelledByDriver",e.CancelledBySystem="CancelledBySystem",e.CancelledByOperator="CancelledByOperator",e}({}),l=function(e){return e.Requested="Requested",e.Searching="Searching",e.Accepted="Accepted",e.Arrived="Arrived",e.InProgress="InProgress",e.Completed="Completed",e.Cancelled="Cancelled",e}({})},32959:(e,r,n)=>{n.d(r,{N:()=>a,o:()=>d});var t=n(7620);let a=(0,t.createContext)(null),d=()=>{let e=(0,t.useContext)(a);if(!e)throw Error("useDriverQueueContext must be used within a DriverQueueProvider");return e}},34744:(e,r,n)=>{n.d(r,{C:()=>a});var t=n(66478);let a=e=>{let{scheduledRides:r,scheduledRidesPagination:n,isLoadingRides:a,refreshRides:d,error:l}=(0,t.g0)();return{data:r,pagination:n,isLoading:a,error:l,refetch:d}}},38749:(e,r,n)=>{n.d(r,{JV:()=>d,TM:()=>u,Vh:()=>a,dF:()=>c,mJ:()=>l});var t=n(12691);function a(e){switch(e){case t.Re.Pending:return"Ожидание";case t.Re.Scheduled:return"Запланирован";case t.Re.InProgress:return"В процессе";case t.Re.Completed:return"Завершен";case t.Re.Cancelled:return"Отменен";case t.Re.Expired:return"Истек срок";default:return""}}function d(){return Object.values(t.Re).map(e=>({value:e,label:a(e)}))}function l(e){switch(e){case t.Re.Pending:return"bg-yellow-500/50";case t.Re.Scheduled:return"bg-blue-500/50";case t.Re.InProgress:return"bg-green-500/50";case t.Re.Completed:return"bg-emerald-500/50";case t.Re.Cancelled:return"bg-red-500/50";case t.Re.Expired:return"bg-orange-500/50";default:return"bg-gray-400/50"}}function u(e){switch(e){case t.Re.Pending:return{bg:"bg-yellow-50",text:"text-yellow-700",border:"border-yellow-200"};case t.Re.Scheduled:return{bg:"bg-blue-50",text:"text-blue-700",border:"border-blue-200"};case t.Re.InProgress:return{bg:"bg-green-50",text:"text-green-700",border:"border-green-200"};case t.Re.Completed:return{bg:"bg-emerald-50",text:"text-emerald-700",border:"border-emerald-200"};case t.Re.Cancelled:return{bg:"bg-red-50",text:"text-red-700",border:"border-red-200"};case t.Re.Expired:return{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200"};default:return{bg:"bg-gray-50",text:"text-gray-700",border:"border-gray-200"}}}function c(e){switch(e){case t.Re.Pending:return"Заказы, ожидающие назначения водителя";case t.Re.Scheduled:return"Заказы, запланированные на определенное время";case t.Re.InProgress:return"Заказы, которые выполняются в данный момент";case t.Re.Completed:return"Успешно завершенные заказы";case t.Re.Cancelled:return"Отмененные заказы";case t.Re.Expired:return"⚠️ КРИТИЧНО! Заказы с истекшим сроком - требуют немедленного внимания!";default:return""}}},92135:(e,r,n)=>{n.d(r,{LP:()=>d,vP:()=>a});var t=n(12691);function a(e){switch(e){case t.ZN.Unknown:return"Неизвестно";case t.ZN.Instant:return"Мгновенный";case t.ZN.Scheduled:return"Запланированный";case t.ZN.Partner:return"Партнерский";case t.ZN.Shuttle:return"Шаттл";case t.ZN.Subscription:return"Подписка";default:return""}}function d(){return Object.values(t.ZN).map(e=>({value:e,label:a(e)}))}}}]);