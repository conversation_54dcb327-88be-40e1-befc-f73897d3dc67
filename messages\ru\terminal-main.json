{"MainTerminal": {"welcomeMessage": "Направления нашей страны", "heroTitle": "Такси в любую точку Кыргызстана!", "heroTitleBishkek": "Такси по Бишкеку!", "heroTitleChui": "Такси по Чуйской области!", "heroTitleNaryn": "Такси по Нарынской области!", "heroTitleOsh": "Такси по Ошской области!", "heroTitleBatken": "Такси по Баткенской области!", "heroTitleTalas": "Такси по Таласской области!", "heroTitleJalalAbad": "Такси по Жалал-Абадской области!", "heroTitleIssykKul": "Такси по Иссык-Кульской области!", "clickRegionMessage": "Нажмите на подходящую область картинки", "searchPlaceholder": "Куда поедем?", "distanceKm": "км", "taxiDescription": "Здесь вы можете получить мгновенное такси, внесите свой адрес и поехали", "helpButton": "Вопрос ответ"}, "Regions": {"bishkek": "Бишкек", "chui": "<PERSON><PERSON><PERSON>", "naryn": "Нарын", "osh": "Ош", "batken": "<PERSON>а<PERSON><PERSON><PERSON>н", "talas": "<PERSON>а<PERSON><PERSON><PERSON>", "jalal-abad": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "issyk-kul": "Иссык-Куль"}, "FAQ": {"title": "Вопросы и ответы", "close": "Закрыть", "contactTitle": "Не нашли ответ на свой вопрос?", "contactDescription": "Свяжитесь с нашей службой поддержки:", "phone": "Телефон", "email": "Email", "workingHours": "Время работы", "workingHoursValue": "Круглосуточно", "questions": {"howToOrder": {"question": "Как заказать такси через терминал?", "answer": "Выберите регион на главной странице, затем укажите место назначения. Вы можете добавить несколько остановок для сложного маршрута. После выбора локаций перейдите к оплате и выберите удобный способ оплаты."}, "paymentMethods": {"question": "Какие способы оплаты доступны?", "answer": "Доступны следующие способы оплаты: банковская карта через POS-терминал, QR-код для оплаты через мобильное приложение банка, и наличные водителю при посадке."}, "priceCalculation": {"question": "Как рассчитывается стоимость поездки?", "answer": "Стоимость рассчитывается автоматически на основе расстояния маршрута и действующих тарифов. Учитывается базовая стоимость подачи и стоимость за километр. Итоговая сумма отображается перед оплатой."}, "multipleStops": {"question": "Можно ли добавить несколько остановок?", "answer": "Да, вы можете добавить несколько промежуточных остановок. На странице выбора локаций нажмите \"Добавить точку\" после выбора первого места назначения. Все остановки будут учтены при расчете стоимости."}, "noDriver": {"question": "Что делать, если не найден водитель?", "answer": "Если в данный момент нет свободных водителей в вашем районе, система автоматически уведомит об этом. Попробуйте повторить заказ через несколько минут или выберите другое время."}, "cancelOrder": {"question": "Как отменить заказ?", "answer": "Заказ можно отменить до прибытия водителя без дополнительной платы. После принятия заказа водителем отмена может повлечь взимание платы согласно тарифам."}, "waitingTime": {"question": "Сколько времени ждать водителя?", "answer": "Среднее время подачи автомобиля составляет 5-15 минут в зависимости от загруженности дорог и количества свободных водителей в районе. Точное время будет указано при подтверждении заказа."}, "languages": {"question": "Можно ли использовать терминал на разных языках?", "answer": "Да, терминал поддерживает несколько языков: кыргызский, русский и английский. Переключить язык можно в нижней части главной страницы."}, "technicalIssues": {"question": "Что делать при технических проблемах?", "answer": "При возникновении технических проблем с терминалом обратитесь к администратору заведения или свяжитесь с нашей службой поддержки по телефону +*********** 222."}, "safety": {"question": "Безопасны ли поездки через терминал?", "answer": "Все водители проходят проверку и имеют необходимые документы. Автомобили регулярно проходят техосмотр. Каждая поездка отслеживается через GPS, что обеспечивает безопасность пассажиров."}}}, "Footer": {"companyDetails": "Наши реквизиты", "publicOffer": "Публичная оферта и условия пользования услугами", "privacyPolicy": "Политика конфиденциальности"}, "Common": {"back": "Назад", "reset": "Сбросить", "next": "Далее", "cancel": "Отмена", "confirm": "Подтвердить"}}