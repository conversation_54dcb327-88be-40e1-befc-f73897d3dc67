// Регистрация компонентов коллекций
import {
  registerCollectionComponents,
  registerCollectionComponentsForRole,
} from '@entities/collections/config/collections';
import { Role } from '@entities/users/enums/Role.enum';
import { ListComponent, ManageComponent } from '@pages/admin/collections';
import { OrderPage } from '@pages/admin/order';
// import { OrderPartnerPage } from '@pages/admin/order/components/partner/OrderPartnerPage'; // Компонент не найден, проверьте путь или наличие
import { ProfilePage } from '@pages/admin/profile';
import { SettingsPage } from '@pages/admin/settings';

// Регистрируем компоненты для каждой коллекции
export function initializeCollectionComponents() {
  // ========================================
  // КОМПОНЕНТЫ ПО УМОЛЧАНИЮ (для Admin и Operator)
  // ========================================
  // Profile - кастомный компонент (все роли)
  registerCollectionComponents('profile', {
    customComponent: ProfilePage, // Для всех ролей
  });
  // Settings - кастомный компонент (все роли)
  registerCollectionComponents('settings', {
    customComponent: SettingsPage, // Для всех ролей
  });
  // Users - стандартные компоненты (Admin и Operator)
  registerCollectionComponents('users', {
    listComponent: ListComponent, // Для Admin и Operator
    manageComponent: ManageComponent, // Для Admin и Operator
  });
  // Cars - стандартные компоненты (Admin и Operator)
  registerCollectionComponents('cars', {
    listComponent: ListComponent, // Для Admin и Operator
    manageComponent: ManageComponent, // Для Admin и Operator
  });
  // Locations - стандартные компоненты (Admin и Operator)
  registerCollectionComponents('locations', {
    listComponent: ListComponent, // Для Admin и Operator
    manageComponent: ManageComponent, // Для Admin и Operator
  });
  // Services - стандартные компоненты (Admin и Operator)
  registerCollectionComponents('services', {
    listComponent: ListComponent, // Для Admin и Operator
    manageComponent: ManageComponent, // Для Admin и Operator
  });
  // Tariffs - стандартные компоненты (Admin и Operator)
  registerCollectionComponents('tariffs', {
    listComponent: ListComponent, // Для Admin и Operator
    manageComponent: ManageComponent, // Для Admin и Operator
  });
  // Orders - список + кастомный компонент (Admin и Operator)
  registerCollectionComponents('orders', {
    listComponent: ListComponent, // Для Admin и Operator
    customComponent: OrderPage, // Для Admin и Operator
  });
  // Notifications - стандартные компоненты (Admin и Operator)
  registerCollectionComponents('notifications', {
    listComponent: ListComponent, // Для Admin и Operator
    manageComponent: ManageComponent, // Для Admin и Operator
  });
  // ========================================
  // КОМПОНЕНТЫ ДЛЯ КОНКРЕТНЫХ РОЛЕЙ
  // ========================================
  // Orders для партнеров - отдельный интерфейс
  registerCollectionComponentsForRole('orders', Role.Partner, {
    listComponent: ListComponent, // Используем стандартный список, но с другой таблицей
    // customComponent: OrderPartnerPage, // Специальный компонент для партнеров
  });
  // Tariffs - стандартные компоненты (Admin и Operator)
  registerCollectionComponentsForRole('tariffs', Role.Partner, {
    listComponent: ListComponent, // Для Admin и Operator
    manageComponent: ManageComponent, // Для Admin и Operator
  });
  console.log('✅ Компоненты коллекций инициализированы');
}
