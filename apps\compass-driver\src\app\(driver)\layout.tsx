import { DriverDataProvider , DriverLocationProvider , DriverQueueProvider , NotificationProvider , SignalRProvider } from '@app/providers';
import { getRawCookie } from '@shared/lib/parse-cookie';
import { DriverHeader } from '@widgets/header';
import { ModalManagerComponent } from '@widgets/modals';
import { DriverSidebar } from '@widgets/sidebar';

export default async function ClientLayout({ children }: { children: React.ReactNode }) {
  const accessToken = await getRawCookie('.AspNetCore.Identity.Application');
  
  // Получаем настройки фона из куки
  const backgroundType = (await getRawCookie('driver-background-type')) || 'color';
  const backgroundValue = (await getRawCookie('driver-background-value')) || '#f9fafb';
  
  // Получаем настройки масштаба из куки
  const scaleStr = (await getRawCookie('driver-ui-scale')) || '0.8';
  const scale = parseFloat(scaleStr);
  
  // Получаем настройки оверлея планшета из куки
  const overlayOpacityStr = (await getRawCookie('driver-overlay-opacity')) || '0.95';
  const overlayBlurStr = (await getRawCookie('driver-overlay-blur')) || '4';
  const overlayColor = (await getRawCookie('driver-overlay-color')) || '#ffffff'; // По умолчанию белый
  const overlayOpacity = parseFloat(overlayOpacityStr);
  const overlayBlur = parseFloat(overlayBlurStr);

  // Получаем настройки оверлея компонентов из куки
  const componentOpacityStr = (await getRawCookie('driver-component-opacity')) || '0.9';
  const componentBlurStr = (await getRawCookie('driver-component-blur')) || '8';
  const componentOpacity = parseFloat(componentOpacityStr);
  const componentBlur = parseFloat(componentBlurStr);

  // Функция для конвертации HEX в RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);

    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 255, g: 255, b: 255 }; // fallback to white
  };

  const overlayRgb = hexToRgb(overlayColor);

  // Стили для компонентов с оверлеем (БЕЗ масштабирования)
  const componentOverlayStyle = {
    backgroundColor: `rgba(255, 255, 255, ${componentOpacity})`,
    backdropFilter: `blur(${componentBlur}px)`,
    WebkitBackdropFilter: `blur(${componentBlur}px)`,
  };

  return (
    <SignalRProvider accessToken={accessToken || undefined}>
      <DriverDataProvider>
        <DriverLocationProvider>
          <NotificationProvider>
            <DriverQueueProvider refreshInterval={30000}>
              <div 
                className="mx-auto my-auto flex overflow-hidden w-full h-full"
                style={{
                  ...(backgroundType === 'image' 
                    ? {
                        backgroundImage: `url(${backgroundValue})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat',
                        backgroundAttachment: 'fixed'
                      }
                    : {
                        backgroundColor: backgroundValue
                      }
                  ) 
                }}
              >
                <div 
                  className="mx-auto my-auto w-full h-full flex flex-row gap-4 overflow-hidden p-5"
                  style={{
                    // Используем настраиваемый цвет планшета!
                    backgroundColor: `rgba(${overlayRgb.r}, ${overlayRgb.g}, ${overlayRgb.b}, ${overlayOpacity})`,
                    backdropFilter: `blur(${overlayBlur}px)`,
                    WebkitBackdropFilter: `blur(${overlayBlur}px)`,
                    // Применяем масштаб только к содержимому планшета
                    transformOrigin: 'center center',
                  }}
                >
                  {/* Основная область */}
                  <div className="h-full flex-1 flex flex-col gap-4 overflow-hidden">
                    {/* Header с оверлеем */}
                    <div 
                      className="rounded-2xl"
                      style={componentOverlayStyle}
                    >
                      <DriverHeader />
                    </div>
                    {/* Контент с оверлеем */}
                    <main 
                      className="h-ful flex flex-col flex-1 rounded-2xl overflow-hidden"
                      style={{
                        ...componentOverlayStyle,
                        backgroundColor: `rgba(249, 249, 249, ${componentOpacity})`, // Сохраняем цвет F9F9F9 но с настраиваемой прозрачностью
                      }}
                    >
                      {children}
                    </main>
                  </div>
                  {/* Sidebar справа с оверлеем */}
                  <div 
                    className="rounded-2xl overflow-auto"
                    style={componentOverlayStyle}
                  >
                    <DriverSidebar />
                  </div>
                </div>
                {/* Менеджер модальных окон */}
                <ModalManagerComponent />
              </div>
            </DriverQueueProvider>
          </NotificationProvider>
        </DriverLocationProvider>
      </DriverDataProvider>
    </SignalRProvider>
  );
}
