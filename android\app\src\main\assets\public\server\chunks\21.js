"use strict";exports.id=21,exports.ids=[21],exports.modules={80021:(e,s,r)=>{r.r(s),r.d(s,{PotentialDriversModal:()=>o});var t=r(13486),l=r(97008),a=r(2984),i=r(60159),d=r.n(i),n=r(38655),c=r(36249);let x=e=>{let[s,r]=(0,i.useState)([]),[t,l]=(0,i.useState)(!1);return{drivers:s,isLoading:t,load:(0,i.useCallback)(async()=>{if(e){l(!0);try{let s=await c.x.getPotentialDrivers(e);r(s)}finally{l(!1)}}},[e])}},o=()=>{let e=(0,a.useRouter)(),s=(0,l.e3)(n.OT),r=(0,l.e3)(n.Oo),i=s.orderId,{drivers:c,load:o}=x(i);d().useEffect(()=>{i&&o()},[i,o]);let u=()=>{r(void 0),e.push("/orders")},h=c.filter(e=>e.allTrue).length;return(0,t.jsx)("div",{className:"absolute inset-0 z-50 w-full h-full flex justify-center items-center backdrop-filter backdrop-blur-[7px]",children:(0,t.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-xl",children:"\uD83D\uDE97"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Потенциальные водители"}),(0,t.jsx)("p",{className:"text-blue-100 text-sm",children:h>0?`Найдено ${h} подходящих водителей`:"Поиск подходящих водителей..."})]})]}),(0,t.jsx)("button",{onClick:u,className:"w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors",children:(0,t.jsx)("span",{className:"text-white text-xl leading-none",children:"\xd7"})})]})}),(0,t.jsx)("div",{className:"p-4 overflow-y-auto max-h-[60vh]",children:0===c.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl text-gray-400",children:"\uD83D\uDD0D"})}),(0,t.jsx)("h3",{className:"text-lg font-medium  mb-2",children:"Поиск водителей"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Ищем подходящих водителей для вашего заказа..."})]}):(0,t.jsxs)("div",{className:"grid gap-3",children:[c.filter(e=>e.allTrue).map((e,s)=>(0,t.jsx)(m,{driver:e,isRecommended:0===s},e.id)),c.filter(e=>!e.allTrue).map(e=>(0,t.jsx)(m,{driver:e,isRecommended:!1},e.id))]})}),(0,t.jsx)("div",{className:"border-t border-gray-200 p-4 bg-gray-50",children:(0,t.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,t.jsx)("button",{onClick:u,className:"px-4 py-2 text-sm  bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Продолжить"}),(0,t.jsx)("button",{onClick:()=>{r(void 0),i?e.push(`/orders/${i}`):e.push("/orders")},className:"px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Перейти к заказу"})]})})]})})},m=({driver:e,isRecommended:s})=>{let r=e.allTrue;return(0,t.jsx)("div",{className:`
      p-4 rounded-xl border-2 transition-all duration-200
      ${r?"п bg-green-50 hover:bg-green-100":"border-gray-200 bg-white hover:bg-gray-50"}
    `,children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:`
          w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg
          ${r?"bg-green-500":"bg-blue-500"}
        `,children:e.fullName?.charAt(0)||"?"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("h3",{className:"font-semibold",children:e.fullName||"Неизвестный водитель"}),s&&(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full font-medium",children:"Рекомендуемый"})]}),(0,t.jsx)("div",{className:"flex items-center gap-2 mb-2",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-yellow-400",children:"⭐"}),(0,t.jsx)("span",{className:"text-sm  ml-1",children:"number"==typeof e.rank&&e.rank>0?e.rank.toFixed(1):"Нет рейтинга"})]})}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:u.map(s=>(0,t.jsx)(h,{label:g(s),result:!!e[s]},s))})]})]})})},u=["isOnline","activeCarStatus","activeCarPassengerCapacity","activeCarServiceClass","distancePredicate","driverMinRating","driverHasNoActiveRides","driverHasNotBeenRequested","driverQueuePresent"],h=({label:e,result:s})=>(0,t.jsxs)("div",{className:"flex items-center gap-1",title:e,children:[(0,t.jsx)("span",{className:`text-sm ${s?"text-green-600":"text-red-600"}`,children:s?"✓":"✗"}),(0,t.jsx)("span",{className:"text-xs  truncate",children:e})]}),g=e=>({isOnline:"Онлайн",activeCarStatus:"Авто активно",activeCarPassengerCapacity:"Вместимость",activeCarServiceClass:"Класс авто",distancePredicate:"Дистанция",driverMinRating:"Мин. рейтинг",driverHasNoActiveRides:"Нет рейсов",driverHasNotBeenRequested:"Не запрашивался",driverQueuePresent:"В очереди"})[e]||e}};