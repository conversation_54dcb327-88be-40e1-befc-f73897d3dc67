'use client';

import React, { useEffect } from 'react';
import { useDriverAssignment } from '../../hooks/useDriverAssignment';
import { useDriverSearch } from '../../hooks/useDriverSearch';
import { CustomSelect } from './CustomSelect';
import { DriverCard } from './DriverCard';

/**
 * Основной компонент для выбора водителей автомобиля
 * Отвечает только за композицию других компонентов
 */
export const CarDriverSelect: React.FC = () => {
  // Логика поиска
  const {
    driversList,
    isLoading,
    searchQuery,
    setSearchQuery,
    searchField,
    setSearchField,
    activeFilters,
    setActiveFilters,
    showFilters,
    setShowFilters,
  } = useDriverSearch();

  // Логика назначения
  const {
    selectedDrivers,
    assignDriver,
    removeDriver,
    firstShiftDriverId,
    secondShiftDriverId,
  } = useDriverAssignment();

  // Закрытие фильтров при клике вне панели
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      if (showFilters && !target.closest('.filter-panel') && !target.closest('.filter-button')) {
        setShowFilters(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showFilters, setShowFilters]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[700px]">
      {/* Левая колонка - Назначенные водители */}
      <div className="flex flex-col h-full">
        <h3 className="text-lg font-medium mb-4">Назначенные водители</h3>
        
        <div className="flex-1 flex flex-col divide-y rounded-lg border shadow-sm backdrop-blur-md">
          <DriverCard 
            driver={selectedDrivers.firstShift} 
            shift="first" 
            title="Первая смена (08:00 - 20:00)"
            onRemove={removeDriver}
          />
          
          <DriverCard 
            driver={selectedDrivers.secondShift} 
            shift="second" 
            title="Вторая смена (20:00 - 08:00)"
            onRemove={removeDriver}
          />
        </div>
      </div>

      {/* Правая колонка - Поиск и список водителей */}
      <div className="flex flex-col h-full">
        <h3 className="text-lg font-medium mb-4">Доступные водители</h3>

        {/* Поиск */}
        <div className="mb-4 relative">
          <div className="flex gap-2">
            <div className="w-[150px]">
              <CustomSelect
                value={searchField}
                onChange={(value) => setSearchField(value as 'fullName' | 'phoneNumber')}
                options={[
                  { value: 'fullName', label: 'По имени' },
                  { value: 'phoneNumber', label: 'По телефону' },
                ]}
                placeholder="Поиск по..."
                showMarginTop={false}
              />
            </div>

            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={searchField === 'fullName' ? 'Введите имя...' : 'Введите телефон...'}
              className="flex-1 px-3 py-2 text-sm border border-white/10 rounded-md bg-black/20 focus:outline-none focus:border-blue-500/50 hover:bg-white/10 transition-all backdrop-blur-md"
            />
            
            {/* Кнопка фильтров */}
            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className={`filter-button px-3 py-2 text-sm border rounded-md transition-all cursor-pointer backdrop-blur-md ${
                showFilters 
                  ? 'bg-blue-500/20 border-blue-500/50 text-blue-400' 
                  : 'border-white/10 bg-black/20 hover:bg-white/10'
              }`}
              title="Дополнительные фильтры"
            >
              <span className="flex items-center gap-2">
                🔧 Фильтры
                {Object.keys(activeFilters).length > 0 && (
                  <span className="px-1.5 py-0.5 bg-blue-500/30 rounded-full text-xs">
                    {Object.keys(activeFilters).length}
                  </span>
                )}
              </span>
            </button>
          </div>
          
          {/* Панель фильтров */}
          {showFilters && (
            <div className="filter-panel absolute right-0 top-full mt-2 w-[400px] p-4 bg-black/80 rounded-lg border border-white/10 z-50 shadow-2xl">
              <div className="space-y-3">
                <h4 className="text-sm font-medium mb-3">Дополнительные фильтры</h4>
                
                <div className="grid grid-cols-2 gap-3">
                  {/* Класс автомобиля */}
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Класс автомобиля</label>
                    <CustomSelect
                      value={activeFilters.VehicleServiceClass || ''}
                      onChange={(value) => setActiveFilters(prev => ({
                        ...prev,
                        VehicleServiceClass: value
                      }))}
                      options={[
                        { value: '', label: 'Все' },
                        { value: 'Economy', label: 'Эконом' },
                        { value: 'Comfort', label: 'Комфорт' },
                        { value: 'ComfortPlus', label: 'Комфорт+' },
                        { value: 'Business', label: 'Бизнес' },
                        { value: 'Premium', label: 'Премиум' },
                        { value: 'Vip', label: 'VIP' },
                        { value: 'Luxury', label: 'Люкс' },
                      ]}
                      placeholder="Выберите класс"
                    />
                  </div>
                  
                  {/* Опыт вождения */}
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Опыт вождения (лет)</label>
                    <input 
                      type="number"
                      value={activeFilters.DrivingExperience || ''}
                      onChange={(e) => setActiveFilters(prev => ({
                        ...prev,
                        DrivingExperience: e.target.value
                      }))}
                      placeholder="От"
                      className="w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2"
                    />
                  </div>
                  
                  {/* Онлайн статус */}
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Статус</label>
                    <CustomSelect
                      value={activeFilters.Online || ''}
                      onChange={(value) => setActiveFilters(prev => ({
                        ...prev,
                        Online: value
                      }))}
                      options={[
                        { value: '', label: 'Все' },
                        { value: 'true', label: 'Онлайн' },
                        { value: 'false', label: 'Оффлайн' },
                      ]}
                      placeholder="Выберите статус"
                    />
                  </div>
                  
                  {/* Языки */}
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Языки</label>
                    <CustomSelect
                      value={activeFilters.Languages || ''}
                      onChange={(value) => setActiveFilters(prev => ({
                        ...prev,
                        Languages: value
                      }))}
                      options={[
                        { value: '', label: 'Все' },
                        { value: 'ru', label: 'Русский' },
                        { value: 'kg', label: 'Кыргызский' },
                        { value: 'en', label: 'Английский' },
                      ]}
                      placeholder="Выберите язык"
                    />
                  </div>
                </div>
                
                {/* Кнопки управления */}
                <div className="flex justify-end gap-2 pt-2">
                  <button
                    type="button"
                    onClick={() => setActiveFilters({})}
                    className="px-3 py-1 text-xs border border-white/10 rounded hover:bg-white/10 transition-all cursor-pointer"
                  >
                    Очистить
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowFilters(false)}
                    className="px-3 py-1 text-xs bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded hover:bg-blue-500/30 transition-all cursor-pointer"
                  >
                    Применить
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Список водителей */}
        <div className="flex-1 overflow-y-auto space-y-2 border rounded-lg p-2 backdrop-blur-md">
          {isLoading ? (
            <div className="text-center py-8 text-gray-500">
              <div className="inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
              <p className="mt-2 text-sm">Поиск водителей...</p>
            </div>
          ) : driversList.length > 0 ? (
            driversList.map((driver) => (
              <div 
                key={driver.id} 
                className="p-3 rounded-lg border hover:bg-white/20 transition-all cursor-pointer"
              >
                <div className="flex items-start gap-3">
                  {/* Мини-аватар */}
                  <div className="w-10 h-10 rounded-full bg-gray-400/20 border border-gray-400/30 flex items-center justify-center text-gray-300 text-sm font-medium">
                    {driver.fullName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)}
                  </div>

                  {/* Информация */}
                  <div className="flex-1">
                    <p className="font-medium text-sm">{driver.fullName}</p>
                    <p className="text-xs text-gray-400">{driver.phoneNumber}</p>
                    {driver.activeCar && (
                      <p className="text-xs text-gray-500">
                        {driver.activeCar.make} {driver.activeCar.model} • {driver.activeCar.licensePlate}
                      </p>
                    )}
                  </div>

                  {/* Кнопки назначения */}
                  <div className="flex flex-col gap-1">
                    <button
                      type="button"
                      onClick={() => assignDriver(driver, 'first')}
                      disabled={driver.id === firstShiftDriverId}
                      className={`px-3 py-1 text-xs rounded transition-all ${
                        driver.id === firstShiftDriverId
                          ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                          : 'bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 border border-blue-500/30 cursor-pointer'
                      }`}
                    >
                      {driver.id === firstShiftDriverId ? '✓ 1-я смена' : '1-я смена'}
                    </button>
                    <button
                      type="button"
                      onClick={() => assignDriver(driver, 'second')}
                      disabled={driver.id === secondShiftDriverId}
                      className={`px-3 py-1 text-xs rounded transition-all ${
                        driver.id === secondShiftDriverId
                          ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                          : 'bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30 cursor-pointer'
                      }`}
                    >
                      {driver.id === secondShiftDriverId ? '✓ 2-я смена' : '2-я смена'}
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-400">
              <p className="text-sm">
                {searchQuery ? 'Водители не найдены' : 'Начните вводить для поиска'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 