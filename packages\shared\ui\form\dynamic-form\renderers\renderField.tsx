import clsx from 'clsx';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { DriverInfoField } from '@shared/ui/form/dynamic-form/components/DriverInfoField';
import type { RenderFieldProps } from '@shared/ui/form/dynamic-form/types';
import { CheckboxField } from '@shared/ui/form/fields/CheckboxField.context';
import { DateField } from '@shared/ui/form/fields/DateField.context';
import { ImageUploader } from '@shared/ui/form/fields/ImageUploader.context';
import { MapLocationPicker } from '@shared/ui/form/fields/MapLocationPicker';
import { MultiSelectField } from '@shared/ui/form/fields/MultiSelectField.context';
import { PasswordField } from '@shared/ui/form/fields/PasswordField.context';
import { SearchableSelectField } from '@shared/ui/form/fields/SearchableSelectField';
import { SelectField } from '@shared/ui/form/fields/SelectField.context';
import { TextareaField } from '@shared/ui/form/fields/TextareaField.context';
import { TextField } from '@shared/ui/form/fields/TextField.context';
import { FormFieldType } from '@entities/collections/types/formTypes';

/**
 * Компонент для рендеринга поля формы
 * Выбирает компонент в зависимости от типа поля
 *
 * @param props - Пропсы для рендеринга поля
 * @returns React элемент с полем формы
 */
export const renderField = ({
  field,
  errors,
  isLoading,
  lazyOptions,
  loadingOptions,
  fieldOptionsProviders
}: RenderFieldProps): React.ReactNode => {
  // Получаем сообщение об ошибке для поля
  const errorMessage = errors[field.name]?.message as string | undefined;
  
  // Получаем register для скрытых полей
  const { register } = useFormContext();

  // Поддержка вложенных групп полей
  if (field.type === FormFieldType.GROUP && field.fields) {
    // Получаем настройки макета для вложенной группы
    const groupLayout = field.layout || {};
    const groupFlexDirection = groupLayout.flexDirection || 'row';
    const groupItemsAlignment = groupLayout.itemsAlignment || 'stretch';
    const groupGridCols = groupLayout.gridCols || 2;
    const groupClassName = groupLayout.className || field.className || '';
    // Определяем классы для выравнивания элементов
    const getGroupAlignmentClass = (alignment: string) => {
      switch (alignment) {
        case 'start': return 'items-start';
        case 'center': return 'items-center';
        case 'end': return 'items-end';
        case 'stretch': return 'items-stretch';
        default: return 'items-stretch';
      }
    };

    return (
      <div key={field.name} className={groupClassName}>
        {field.label && (
          <div className='flex flex-col'>
            <h4 className="text-md font-medium">{field.label}</h4>
            {field.description && (
              <p className="text-sm">{field.description}</p>
            )}
          </div>
        )}
        <div
          className={clsx(
            'w-full h-full gap-4',
            groupFlexDirection === 'row'
              ? `grid grid-cols-${groupGridCols}`
              : 'flex flex-col',
            getGroupAlignmentClass(groupItemsAlignment)
          )}
          style={{
            gridTemplateColumns: groupFlexDirection === 'row' ? `repeat(${groupGridCols}, 1fr)` : undefined
          }}
        >
          {field.fields.map(subField => {
            // Рекурсивный вызов функции renderField
            return renderField({
              field: subField,
              errors,
              isLoading,
              lazyOptions,
              loadingOptions,
              fieldOptionsProviders
            });
          })}
        </div>
      </div>
    );
  }
  // Общие пропсы для всех типов полей
  const commonProps = {
    name: field.name,
    label: field.label || field.name,
    placeholder: field.placeholder,
    required: field.required,
    disabled: field.disabled || isLoading,
    error: errorMessage,
    helperSmallText: field.helperSmallText,
    helperBigText: field.helperBigText,
    className: field.className,
  };
  // Для полей с динамическими данными используем ленивые options, если есть провайдер для этого поля
  const options =
    fieldOptionsProviders && fieldOptionsProviders[field.name] &&
    (field.type === FormFieldType.SELECT || 
     field.type === FormFieldType.MULTISELECT || 
     field.type === FormFieldType.DRIVER_INFO)
      ? lazyOptions[field.name] || []
      : field.options || [];
  // Проверяем, загружаются ли опции только если есть провайдер для этого поля
  const isOptionsLoading = fieldOptionsProviders &&
    fieldOptionsProviders[field.name] &&
    loadingOptions[field.name];

  // Выбираем компонент в зависимости от типа поля
  switch (field.type) {
    case FormFieldType.TEXT:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <TextField
            {...commonProps}
            type="text"
            readOnly={field.readOnly}
          />
        </div>
      );
    case FormFieldType.TEXTAREA:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <TextareaField
            {...commonProps}
            rows={4}
            readOnly={field.readOnly}
          />
        </div>
      );
    case FormFieldType.NUMBER:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <TextField
            {...commonProps}
            type="number"
            min={field.min}
            max={field.max}
            step={field.step}
            readOnly={field.readOnly}
          />
        </div>
      );
    case FormFieldType.EMAIL:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <TextField
            {...commonProps}
            type="email"
            readOnly={field.readOnly}
          />
        </div>
      );
    case FormFieldType.PASSWORD:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <PasswordField {...commonProps} />
        </div>
      );
    case FormFieldType.PHONE:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <TextField
            {...commonProps}
            type="tel"
            readOnly={field.readOnly}
          />
        </div>
      );
    case FormFieldType.DATE:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <DateField
            {...commonProps}
            disabled={commonProps.disabled || field.readOnly}
          />
        </div>
      );
    case FormFieldType.CHECKBOX:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <CheckboxField
            {...commonProps}
            disabled={commonProps.disabled || field.readOnly}
          />
        </div>
      );
    case FormFieldType.SELECT:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          {isOptionsLoading ? (
            <div className="text-xs text-gray-400">Загрузка...</div>
          ) : (
            // Используем SearchableSelectField если включен поиск, иначе обычный SelectField
            field.searchable ? (
              <SearchableSelectField
                {...commonProps}
                options={options}
                disabled={commonProps.disabled || field.readOnly}
                searchable={field.searchable}
                showDescription={field.showDescription}
              />
            ) : (
              <SelectField
                {...commonProps}
                options={options}
                disabled={commonProps.disabled || field.readOnly}
              />
            )
          )}
        </div>
      );
    case FormFieldType.MULTISELECT:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          {isOptionsLoading ? (
            <div className="text-xs text-gray-400">Загрузка...</div>
          ) : (
            <MultiSelectField
              {...commonProps}
              options={options}
              disabled={commonProps.disabled || field.readOnly}
              showNavigateButton={field.showNavigateButton}
              multiselectDataType={field.multiselectDataType}
              colorFieldName={field.colorFieldName}
              getColorFromValue={field.getColorFromValue}
              getDescriptionFromValue={field.getDescriptionFromValue}
            />
          )}
        </div>
      );

    case FormFieldType.IMAGE:
      return (
        <div key={field.name} className={`relative ${field.className || 'w-full'}`}>
          <ImageUploader
            {...commonProps}
            maxSizeInMB={field.max || 5}
            multiple={false}
            disabled={commonProps.disabled || field.readOnly}
          />
        </div>
      );
    case FormFieldType.DRIVER_INFO:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          {isOptionsLoading ? (
            <div className="text-xs text-gray-400">Загрузка информации о водителе...</div>
          ) : (
            <DriverInfoField
              label={field.label}
              helperSmallText={field.helperSmallText}
              helperBigText={field.helperBigText}
              className={field.className}
              options={options}
              placeholder={field.placeholder}
            />
          )}
        </div>
      );
    case FormFieldType.MAP_LOCATION_PICKER:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <MapLocationPicker
            {...commonProps}
            height={field.height || "400px"}
            zoom={field.zoom || 13}
            disabled={commonProps.disabled || field.readOnly}
            enableAddressSearch={field.enableAddressSearch}
            searchPlaceholder={field.searchPlaceholder}
            maxSearchResults={field.maxSearchResults}
            minSearchLength={field.minSearchLength}
          />
        </div>
      );
    case FormFieldType.HIDDEN:
      return (
        <input
          key={field.name}
            type="hidden"
          {...register(field.name)}
          defaultValue={field.defaultValue as string}
          />
      );
    default:
      return (
        <div key={field.name} className={field.className || 'w-full'}>
          <TextField
            {...commonProps}
            type="text"
            readOnly={field.readOnly}
          />
        </div>
      );
  }
};
