(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26799:(e,r,s)=>{Promise.resolve().then(s.bind(s,95304))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51852:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=s(24332),a=s(48819),o=s(67851),i=s.n(o),l=s(97540),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(r,n);let c={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,84588)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\login\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},52164:(e,r,s)=>{"use strict";s.d(r,{Q:()=>t.LoginForm});var t=s(95304)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58277:(e,r,s)=>{"use strict";s.d(r,{v:()=>c});var t=s(2984),a=s(60159),o=s(50947),i=s(5448),l=s(18673),n=s(75385);let c=()=>{let e=(0,t.useRouter)(),[r,s]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null),[u,m]=(0,a.useState)({});return{login:(0,a.useCallback)(async r=>{s(!0),d(null),m({});let t=await (0,i.n)(n.y.loginWithValidation(r));if(t.success)l.P.success("Вход выполнен успешно!"),e.push(o.vc.MAIN.HOME);else{let e=t.error||"Произошла неизвестная ошибка";d(e),t.fieldErrors&&m(t.fieldErrors),l.P.error(e)}s(!1)},[e]),isLoading:r,error:c,fieldErrors:u,clearFieldError:(0,a.useCallback)(e=>{m(r=>{let s={...r};return delete s[e],s})},[])}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64390:(e,r,s)=>{"use strict";s.r(r),s.d(r,{LoginForm:()=>v});var t=s(13486),a=s(93958),o=s(49989),i=s.n(o),l=s(2984),n=s(60159),c=s(85626),d=s(50947),u=s(65363),m=s(49272),p=s(99087),g=s(66512),h=s(58277);let x=(0,n.lazy)(()=>Promise.resolve().then(s.bind(s,33504))),f=(0,n.lazy)(()=>Promise.resolve().then(s.bind(s,19263))),v=({defaultValues:e,autoLogin:r=!1})=>{let s=(0,l.useRouter)(),{login:o,isLoading:v,fieldErrors:y,clearFieldError:b}=(0,h.v)(),[w,j]=(0,n.useState)(!1),[E,A]=(0,n.useState)(!1),[N,P]=(0,n.useState)(!1),{register:R,handleSubmit:z,formState:{errors:k},getValues:q}=(0,c.mN)({resolver:(0,a.u)(g.X5),defaultValues:e});return(0,n.useEffect)(()=>{r&&e?.email&&e?.password&&!v&&!N&&(P(!0),o(q()))},[r,e?.email,e?.password,v,N,q,o]),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.w,{isVisible:v,message:"Выполняется вход в систему..."}),(0,t.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,t.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Вход в систему"}),(0,t.jsx)("p",{children:"Введите свои учетные данные для входа"})]}),(0,t.jsxs)("form",{onSubmit:z(e=>o(e)),className:"flex flex-col gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,t.jsx)("input",{id:"email",type:"email",...R("email",{onChange:()=>{y.email&&b("email")}}),className:`block w-full px-2 py-1  text-xs border ${k.email||y.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:v}),(0,t.jsx)(u.y,{message:k.email?.message}),(0,t.jsx)("label",{htmlFor:"password",className:"block text-xs font-medium",children:"Пароль"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"password",type:w?"text":"password",...R("password",{onChange:()=>{y.password&&b("password")}}),className:`block w-full px-3 py-2 pr-10  border ${k.password||y.password?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:v}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3  hover:",onClick:()=>j(!w),tabIndex:-1,children:(0,t.jsx)(n.Suspense,{fallback:(0,t.jsx)("div",{className:"w-5 h-5"}),children:w?(0,t.jsx)(f,{size:20}):(0,t.jsx)(x,{size:20})})})]}),(0,t.jsx)(u.y,{message:k.password?.message})]}),(0,t.jsx)("div",{className:"flex justify-end",children:E?(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-[color:var(--color-primary)] transition-opacity duration-300",children:[(0,t.jsx)(m.Z,{size:"sm",className:"text-[color:var(--color-primary)]"}),(0,t.jsx)("span",{children:"Переход на страницу сброса пароля..."})]}):(0,t.jsx)("a",{href:d.vc.AUTH.FORGOT_PASSWORD,onClick:e=>{e.preventDefault(),A(!0),setTimeout(()=>{s.push(d.vc.AUTH.FORGOT_PASSWORD)},1e3)},className:`text-sm text-[color:var(--color-primary)] hover:underline ${E?"pointer-events-none opacity-50":""}`,children:"Забыли пароль?"})}),(0,t.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,t.jsx)("button",{type:"submit",disabled:v,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-brand)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:v?"Вход...":"Войти"}),(0,t.jsx)(i(),{href:d.vc.AUTH.REGISTER,className:"w-full block",children:(0,t.jsx)("button",{type:"button",className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-primary)] rounded-md shadow-sm text-sm font-medium text-[color:var(--color-brand)] bg-transparent hover:text-white hover:bg-[color:var(--color-primary)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)]",children:"Зарегистрироваться"})})]})]})]})]})}},65363:(e,r,s)=>{"use strict";s.d(r,{y:()=>t.y});var t=s(64556)},66512:(e,r,s)=>{"use strict";s.d(r,{E9:()=>i,X5:()=>a,oW:()=>l,rA:()=>o});var t=s(43888);let a=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}),password:t.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"})}),o=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:t.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"}),fullName:t.z.string().min(1,{message:"ФИО обязательно"}).max(255,{message:"ФИО не должно превышать 255 символов"}),companyName:t.z.string().min(1,{message:"Название компании обязательно"}).max(255,{message:"Название компании не должно превышать 255 символов"}),legalAddress:t.z.string().min(1,{message:"Юридический адрес обязателен"}).max(500,{message:"Юридический адрес не должен превышать 500 символов"})}),i=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"})}),l=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"}),resetCode:t.z.string().min(1,{message:"Код сброса пароля обязателен"}),newPassword:t.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"})})},74075:e=>{"use strict";e.exports=require("zlib")},75385:(e,r,s)=>{"use strict";s.d(r,{y:()=>l});var t=s(77243),a=s(75111),o=s(50947);class i extends t.v{async login(e){let r=await this.post("/login",e);return this.handleApiResult(r)}async loginWithValidation(e){try{let r=await this.post("/login",{email:e.email,password:e.password});if(r.error){let s=r.error.message,t={};if(r.error.type===a.r6.Auth)s="Неверный email или пароль. Пожалуйста, проверьте правильность учетных данных.",t.email=!0,t.password=!0;else if(r.error.type===a.r6.Network)s="Проблема с сетевым подключением. Проверьте интернет-соединение.";else if(r.error.type===a.r6.Validation&&r.error.data&&"object"==typeof r.error.data){let a=r.error.data;if(a.errors&&"object"==typeof a.errors){let r=a.errors;if(r.UserNotFound&&r.UserNotFound.length>0)s=`Пользователь с email '${e.email}' не найден.`,t.email=!0;else if(r.InvalidEmail&&r.InvalidEmail.length>0)s="Указан некорректный email.",t.email=!0;else{let e=Object.entries(r).map(([e,r])=>`${"RequiredField"===e?"Обязательное поле":e}: ${r.join(", ")}`).join("\n");e&&(s=e)}}}return{success:!1,error:s,fieldErrors:Object.keys(t).length>0?t:void 0}}return{success:!0,data:this.handleApiResult(r)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла неизвестная ошибка"}}}async register(e){let r=await this.post("/register",e);return this.handleApiResult(r)}async registerPartnerWithValidation(e){try{let r=await this.post("/register/partner",e);if(r.error)return{success:!1,error:r.error.message};return{success:!0,data:this.handleApiResult(r)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при регистрации"}}}async refreshToken(e){let r=await this.post("/refresh",e);return this.handleApiResult(r)}async logout(){let e=await this.post("/logout");this.handleApiResult(e)}async logoutWithCleanup(){try{let e=await this.post("/logout");if(e.error)return{success:!1,error:e.error.message};return this.handleApiResult(e),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при выходе"}}}async resetPassword(e){let r=await this.post("/reset-password",e);this.handleApiResult(r)}async forgotPasswordWithValidation(e){try{let r=await this.post("/forgot-password",e);if(r.error)return{success:!1,error:r.error.message};return this.handleApiResult(r),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при отправке запроса"}}}async resetPasswordWithValidation(e){try{let r=await this.post("/reset-password",e);if(r.error)return{success:!1,error:r.error.message};return this.handleApiResult(r),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при сбросе пароля"}}}async changePassword(e){let r=await this.post("/change-password",e);this.handleApiResult(r)}async verifyToken(){try{let e=await this.get("/verify");return this.handleApiResult(e).valid}catch{return!1}}async getCurrentUser(){let e=await this.get("/me");return this.handleApiResult(e)}constructor(...e){super(...e),this.baseUrl=o.QQ.AUTH.BASE}}let l=new i},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84588:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i,metadata:()=>l});var t=s(38828);s(61365);var a=s(52164);let o=()=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(a.Q,{defaultValues:{email:"<EMAIL>",password:"password"}})});function i(){return(0,t.jsx)(o,{})}let l={title:"Вход в систему | Compass 2.0",description:"Страница входа в административную панель Compass 2.0"}},86631:(e,r,s)=>{Promise.resolve().then(s.bind(s,64390))},94735:e=>{"use strict";e.exports=require("events")},95304:(e,r,s)=>{"use strict";s.r(r),s.d(r,{LoginForm:()=>t});let t=(0,s(33952).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\auth\\login\\ui\\LoginForm.tsx","LoginForm")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[191,5208,9455,6596,3639,2960],()=>s(51852));module.exports=t})();