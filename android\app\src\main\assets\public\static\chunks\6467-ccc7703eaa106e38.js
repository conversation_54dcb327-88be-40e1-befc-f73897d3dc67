"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6467],{21715:(e,r,t)=>{t.d(r,{$P:()=>o,$Y:()=>A,Al:()=>R,Ay:()=>l,Vg:()=>s,mu:()=>T,r6:()=>u});var i=t(44089),a=t(89329),u=function(e){return e.Network="network",e.Auth="auth",e.Forbidden="forbidden",e.NotFound="not_found",e.Validation="validation",e.Server="server",e.Unknown="unknown",e}({});let d=e=>{if((0,i.F0)(e)){var r,t,a,u;let i,d=null==(r=e.response)?void 0:r.status,E="unknown",n=e.message;if(null==(t=e.response)?void 0:t.data){let r=e.response.data;if(r.errors&&"object"==typeof r.errors&&(i=r.errors)&&Object.keys(i).length>0){let e=Object.keys(i)[0];e&&(null==(u=i[e])?void 0:u.length)>0&&(n=i[e][0])}r.title&&"string"==typeof r.title&&(n=r.title)}switch(d){case 401:E="auth",i||(n="Ошибка авторизации. Пожалуйста, войдите в систему.");break;case 403:E="forbidden",i||(n="Доступ запрещен. У вас нет прав для выполнения этого действия.");break;case 404:E="not_found",i||(n="Ресурс не найден.");break;case 400:case 422:E="validation",i||(n="Ошибка валидации данных.");break;case 500:case 502:case 503:case 504:E="server",i||(n="Ошибка сервера. Пожалуйста, попробуйте позже.");break;default:d?d>=400&&d<500?(E="validation",i||(n="Ошибка в запросе.")):d>=500&&(E="server",i||(n="Ошибка сервера. Пожалуйста, попробуйте позже.")):(E="network",i||(n="Ошибка сети. Пожалуйста, проверьте подключение к интернету."))}return{type:E,message:n,statusCode:d,data:null==(a=e.response)?void 0:a.data,errors:i,originalError:e}}return e instanceof Error?{type:"unknown",message:e.message,originalError:e}:{type:"unknown",message:"Произошла неизвестная ошибка",originalError:e}},E=(e=>{let r={},t=a.A.create({baseURL:"https://api.testingkg.su",withCredentials:!0,headers:{"Content-Type":"application/json"},...void 0});return t.interceptors.request.use(e=>e,e=>Promise.reject(e)),t.interceptors.response.use(e=>e,e=>{var t,i,a,u,E,n;let s=d(e);switch(r.onError&&r.onError(s),s.type){case"auth":null==(t=r.onAuthError)||t.call(r,s);break;case"forbidden":null==(i=r.onForbiddenError)||i.call(r,s);break;case"not_found":null==(a=r.onNotFoundError)||a.call(r,s);break;case"validation":null==(u=r.onValidationError)||u.call(r,s);break;case"server":null==(E=r.onServerError)||E.call(r,s);break;case"network":null==(n=r.onNetworkError)||n.call(r,s)}return Promise.reject(s)}),t})(),n=async e=>{try{return{data:await e()}}catch(e){if(e&&"object"==typeof e&&"type"in e)return{error:e};return{error:d(e)}}},s=async(e,r)=>n(async()=>(await E.get(e,r)).data),o=async(e,r,t)=>n(()=>E.post(e,r,t).then(e=>e.data)),T=async(e,r,t)=>n(()=>E.put(e,r,t).then(e=>e.data)),R=async(e,r)=>n(()=>E.delete(e,r).then(e=>e.data)),A=async(e,r,t)=>n(()=>E.patch(e,r,t).then(e=>e.data)),l={client:E,get:s,post:o,put:T,patch:A,delete:R}},22223:(e,r,t)=>{t.d(r,{v:()=>a});var i=t(21715);class a{async get(e,r){let t=this.buildUrl(e);return(0,i.Vg)(t,r)}async post(e,r,t){let a=this.buildUrl(e);return(0,i.$P)(a,r,t)}async put(e,r,t){let a=this.buildUrl(e);return(0,i.mu)(a,r,t)}async patch(e,r,t){let a=this.buildUrl(e);return(0,i.$Y)(a,r,t)}async delete(e,r){let t=this.buildUrl(e);return(0,i.Al)(t,r)}buildUrl(e){let r=e.startsWith("/")?e.slice(1):e,t=this.baseUrl.endsWith("/")?this.baseUrl.slice(0,-1):this.baseUrl;return"".concat(t,"/").concat(r)}handleApiResult(e){if(e.error)throw e.error;if(void 0===e.data)throw Error("API returned undefined data");return e.data}handleApiResultSafe(e){return e.error||void 0===e.data?null:e.data}}},27736:(e,r,t)=>{t.d(r,{QQ:()=>a.QQ,uE:()=>i.Ay,vA:()=>u.v,vc:()=>a.vc});var i=t(21715),a=t(80745),u=t(22223)},64942:(e,r,t)=>{t.d(r,{R:()=>a,V:()=>i});var i=function(e){return e.TEXT="text",e.TEXTAREA="textarea",e.NUMBER="number",e.EMAIL="email",e.PASSWORD="password",e.PHONE="phone",e.DATE="date",e.TIME="time",e.DATETIME="datetime",e.CHECKBOX="checkbox",e.RADIO="radio",e.BUTTON_GROUP="button_group",e.SELECT="select",e.MULTISELECT="multiselect",e.FILE="file",e.IMAGE="image",e.HIDDEN="hidden",e.GROUP="group",e.DRIVER_INFO="driver_info",e.MAP_LOCATION_PICKER="map_location_picker",e}({}),a=function(e){return e.STRING="string",e.NUMBER="number",e.BOOLEAN="boolean",e.DATE="date",e.ARRAY="array",e.OBJECT="object",e.ENUM="enum",e}({})},80745:(e,r,t)=>{t.d(r,{QQ:()=>i,vc:()=>a});let i={AUTH:{BASE:"/Auth",LOGIN:"/Auth/login",FORGOT_PASSWORD:"/Auth/forgot_password",RESET_PASSWORD:"/Auth/reset_password",REGISTER_PARTNER:"/Auth/register/partner",LOGOUT:"/Auth/logout"},USER:{SELF:"/User/self",AVATAR:"/User/avatar",LIST:"/User",SEARCH:"/User",GET_BY_ID:"/User/{uuid}",ADMIN:{GET_BY_ID:"/User/Admin/{uuid}",UPDATE:"/User/Admin/{uuid}",CREATE:"/User/Admin"},CUSTOMER:{GET_BY_ID:"/User/Customer/{uuid}",UPDATE:"/User/Customer/{uuid}",CREATE:"/User/Customer"},DRIVER:{LIST:"/User/Driver",SELF:"/User/Driver/self",GET_BY_ID:"/User/Driver/{uuid}",UPDATE:"/User/Driver/{uuid}",CREATE:"/User/Driver"},OPERATOR:{GET_BY_ID:"/User/Operator/{uuid}",UPDATE:"/User/Operator/{uuid}",CREATE:"/User/Operator"},PARTNER:{GET_BY_ID:"/User/Partner/{uuid}",UPDATE:"/User/Partner/{uuid}",CREATE:"/User/Partner"},TERMINAL:{GET_BY_ID:"/User/Terminal/{uuid}",UPDATE:"/User/Terminal/{uuid}",CREATE:"/User/Terminal"}},CAR:{LIST:"/Car",MY:"/Car/my",CREATE:"/Car",UPDATE:"/Car/{uuid}",GET_BY_ID:"/Car/{uuid}",ASSIGN_DRIVER:"/Car/{uuid}/drivers/{driver_id}",UPDATE_DRIVER:"/Car/{uuid}/drivers/{driver_id}",REMOVE_DRIVER:"/Car/{uuid}/drivers/{driver_id}",MY_SET_ACTIVE:"/Car/my/{uuid}/set-active",MY_SET_INACTIVE:"/Car/my/set-inactive"},LOCATION:{LIST:"/Location",SEARCH:"/Location",CREATE:"/Location",UPDATE:"/Location/{uuid}",GET_BY_ID:"/Location/{uuid}",CURRENT_LOCATION:"/Location/CurrentLocation/self"},SERVICE:{LIST:"/Service",CREATE:"/Service",UPDATE:"/Service/{uuid}",DELETE:"/Service/{uuid}",GET_BY_ID:"/Service/{uuid}"},ORDER:{LIST:"/Order",MY_CREATOR:"/Order/my/creator",MY_PARTICIPANT:"/Order/my/participant/{order_id}",CREATE:"/Order",CREATE_SCHEDULED:"/Order/scheduled",CREATE_SCHEDULED_RIDE:"/Order/scheduled/{uuid}/ride",CREATE_INSTANT_BY_OPERATOR:"/Order/instant/by-operator",CREATE_INSTANT_BY_CUSTOMER:"/Order/instant/by-customer",UPDATE:"/Order/{uuid}",UPDATE_SCHEDULED:"/Order/scheduled/{uuid}",UPDATE_INSTANT:"/Order/instant/{uuid}",UPDATE_PASSENGERS:"/Order/{uuid}/passengers",GET_BY_ID:"/Order/{uuid}",GET_SCHEDULED_BY_ID:"/Order/scheduled/{uuid}",GET_INSTANT_BY_ID:"/Order/instant/{uuid}"},ROUTE:{LIST:"/Route",CREATE:"/Route",UPDATE:"/Route/{uuid}",GET_BY_ID:"/Route/{uuid}",DELETE:"/Route/{uuid}"},TARIFF:{LIST:"/Tariff",CREATE:"/Tariff",UPDATE:"/Tariff/{uuid}",GET_BY_ID:"/Tariff/{uuid}"},NOTIFICATION:{LIST:"/Notification",ME:"/Notification/me",CREATE:"/Notification",UPDATE:"/Notification/{uuid}",GET_BY_ID:"/Notification/{uuid}",READ:"/Notification/read"},GIS:{BASE:"/GIS",ACTIVE_DRIVERS:"/GIS/ActiveDrivers"},RIDE:{LIST:"/Ride",MY:"/Ride/my",MY_ASSIGNED:"/Ride/my/assigned",GET_BY_ID:"/Ride/{uuid}",UPDATE:"/Ride/{uuid}",DELETE:"/Ride/{uuid}",FOR_ORDER:"/Ride/for-order/{order_id}",ACCEPT:"/Ride/{uuid}/accept-by-driver",REJECT:"/Ride/{uuid}/reject-by-driver",NOTIFICATION_BY_DRIVER:"/Ride/{uuid}/notification-by-driver",DRIVER_HEADING:"/Ride/{uuid}/status/driver-heading-to-client",DRIVER_ARRIVED:"/Ride/{uuid}/status/driver-arrived",RIDE_STARTED:"/Ride/{uuid}/status/ride-started",RIDE_FINISHED:"/Ride/{uuid}/status/ride-finished",RIDE_CANCELLED:"/Ride/{uuid}/status/ride-cancelled"}},a={AUTH:{LOGIN:"/login",FORGOT_PASSWORD:"/forgot-password",RESET_PASSWORD:"/reset-password",REGISTER:"/register"},MAIN:{HOME:"/"},PROFILE:{SELF:"/profile"},TERMINAL:{MAIN:"/"},LOCATIONS:{TERMINAL_LOCATION:"/locations"},PAYMENT:{TERMINAL_PAYMENT:"/payment"}}}}]);