services:
  compass-admin:
    image: node:20-alpine3.19
    working_dir: /app
    volumes:
      - ../:/app
    environment:
      - NODE_ENV=production
    env_file:
      - ../apps/compass-admin/.env.production
    # Убираем restart чтобы контейнер не перезапускался
    restart: "no"
    command: >
      sh -c "yarn build:admin"
    # Настройки для больших загрузок
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    # Дополнительная память если нужно
    mem_limit: 2g
    networks:
      - compass-network

networks:
  compass-network:
    name: compass_default
    external: true