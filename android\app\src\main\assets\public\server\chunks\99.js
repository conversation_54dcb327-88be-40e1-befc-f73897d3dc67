"use strict";exports.id=99,exports.ids=[99],exports.modules={5414:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(25121)._(r(12430));function a(e,t){var r;let a={};"function"==typeof e&&(a.loader=e);let l={...a,...t};return(0,n.default)({...l,modules:null==(r=l.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12430:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(13486),a=r(60159),l=r(55201),i=r(21188);function o(e){return{default:e&&"default"in e?e.default:e}}let s={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},u=function(e){let t={...s,...e},r=(0,a.lazy)(()=>t.loader().then(o)),u=t.loading;function d(e){let o=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,s=!t.ssr||!!t.loading,d=s?a.Suspense:a.Fragment,f=t.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.PreloadChunks,{moduleIds:t.modules}),(0,n.jsx)(r,{...e})]}):(0,n.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(d,{...s?{fallback:o}:{},children:f})}return d.displayName="LoadableComponent",d}},18187:(e,t,r)=>{r.d(t,{default:()=>a.a});var n=r(5414),a=r.n(n)},21188:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return o}});let n=r(13486),a=r(22358),l=r(29294),i=r(95718);function o(e){let{moduleIds:t}=e,r=l.workAsyncStorage.getStore();if(void 0===r)return null;let o=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;o.push(...t)}}return 0===o.length?null:(0,n.jsx)(n.Fragment,{children:o.map(e=>{let t=r.assetPrefix+"/_next/"+(0,i.encodeURIPath)(e);return e.endsWith(".css")?(0,n.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,a.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},35703:(e,t,r)=>{var n=r(60159),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=n.useState,i=n.useEffect,o=n.useLayoutEffect,s=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!a(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=l({inst:{value:r,getSnapshot:t}}),a=n[0].inst,d=n[1];return o(function(){a.value=r,a.getSnapshot=t,u(a)&&d({inst:a})},[e,r,t]),i(function(){return u(a)&&d({inst:a}),e(function(){u(a)&&d({inst:a})})},[e]),s(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d},52826:(e,t,r)=>{e.exports=r(35703)},54462:(e,t,r)=>{let n,a;function l(e,t){for(let r in e)t(e[r],r)}function i(e,t){e.forEach(t)}function o(e,t,r){if(!e)throw Error(`${r?r+": ":""}${t}`)}function s({node:e=[],from:t,source:r,parent:n=t||r,to:a,target:l,child:o=a||l,scope:u={},meta:d={},family:f={type:"regular"},regional:c}={}){let p=Y(n),m=Y(f.links),h=Y(f.owners),g=[];i(e,e=>e&&F(g,e));let y={id:K(),seq:g,next:Y(o),meta:d,scope:u,family:{triggers:p.length,type:f.type||"crosslink",links:m,owners:h}};return i(m,e=>F(x(e),y)),i(h,e=>F(w(e),y)),i(p,e=>F(e.next,y)),c&&B&&H($(B),[y]),y}function u(e,t,r){let n,l,o,s,u,d,f,c=eR,p=null,m=eO;if(e.target&&(t=e.params,r=e.defer,f=e.meta,c="page"in e?e.page:c,e.stack&&(p=e.stack),m=O(e)||m,e=e.target),m&&eO&&m!==eO&&(eO=null),Array.isArray(e))for(let r=0;r<e.length;r++)ew("pure",c,k(e[r]),p,t[r],m,f);else ew("pure",c,k(e),p,t,m,f);if(r&&!eP)return;let h={isRoot:eP,currentPage:eR,scope:eO,isWatch:eC,isPure:eM};eP=0;e:for(;s=ex();){let{idx:e,stack:t,type:r}=s;o=t.node,eR=u=t.page,eO=O(t),u?d=u.reg:eO&&(d=eO.reg);let f=!!u,c=!!eO,p={fail:0,scope:o.scope};n=l=0;for(let a=e;a<o.seq.length&&!n;a++){let i=o.seq[a];if(i.order){let{priority:n,barrierID:l}=i.order,o=l?u?`${u.fullID}_${l}`:l:0;if(a!==e||r!==n){l?eE.has(o)||(eE.add(o),ej(a,t,n,l)):ej(a,t,n,0);continue e}l&&eE.delete(o)}switch(i.type){case"mov":{let e,r=i.data;switch(r.from){case"stack":e=$(t);break;case"a":case"b":e=t[r.from];break;case"value":e=r.store;break;case"store":if(d&&!d[r.store.id])if(f){let e=eA(u,r.store.id);t.page=u=e,e?d=e.reg:c?(eF(eO,r.store,0,1,r.softRead),d=eO.reg):d=void 0}else c&&eF(eO,r.store,0,1,r.softRead);e=eg(d&&d[r.store.id]||r.store)}switch(r.to){case"stack":t.value=e;break;case"a":case"b":t[r.to]=e;break;case"store":ez(u,eO,r.target,0).current=e}break}case"compute":let s=i.data;if(s.fn){eC="watch"===o.meta.op,eM=s.pure;let e=s.safe?(0,s.fn)($(t),p.scope,t):eT(p,s.fn,t);s.filter?l=!e:t.value=e,eC=h.isWatch,eM=h.isPure}}n=p.fail||l}if(a&&a(t,p),!n){let e=$(t),r=O(t);if(i(o.next,n=>{ew("child",u,n,t,e,r)}),r){o.meta.needFxCounter&&ew("child",u,r.fxCount,t,e,r),o.meta.storeChange&&ew("child",u,r.storeChange,t,e,r),o.meta.warnSerialize&&ew("child",u,r.warnSerializeNode,t,e,r);let n=r.additionalLinks[o.id];n&&i(n,n=>{ew("child",u,n,t,e,r)})}}}eP=h.isRoot,eR=h.currentPage,eO=O(h)}r.d(t,{EH:()=>g,XM:()=>v,lh:()=>p,y$:()=>h});function d(e,t){if(!t||!t.name&&!t.named&&!t.loc)return e;let r=`[${e}]`,n=t.named||t.name;n&&(r+=` unit '${n}'`);let a=t.loc;return!n&&a&&(r+=` (${a.file}:${a.line}:${a.column})`),r}function f(e,t){let r=t?e:e[0];ee(r);let n=r.or,a=r.and;if(a){let r=t?a:a[0];if(J(r)&&"and"in r){let r=f(a,t);e=r[0],n={...n,...r[1]}}else e=a}return[e,n]}function c(e,...t){let r=X();if(r){let n=r.handlers[e];if(n)return n(r,...t)}}function p(e,t){let r=eV({or:t,and:"string"==typeof e?{name:e}:e}),n=d("event",r),a=(e,...t)=>(o(!P(a,"derived"),"call of derived event is not supported, use createEvent instead",n),o(!eM,"unit call from pure function is not supported, use operators like sample instead",n),eR?((e,t,r,n)=>{let a=eR,l=null;if(t)for(l=eR;l&&l.template!==t;)l=E(l);e_(l);let i=e.create(r,n);return e_(a),i})(a,l,e,t):a.create(e,t)),l=X(),i=Object.assign(a,{graphite:s({meta:eH(r.actualOp||"event",a,r),regional:1}),create:e=>(u({target:a,params:e,scope:eO}),e),watch:e=>eX(a,e),map:e=>eY(a,S,e,[em()]),filter:e=>eY(a,"filter",e.fn?e:e.fn,[em(ei,1)]),filterMap:e=>eY(a,"filterMap",e,[em(),ec(e=>!Z(e),1)]),prepend(e){o(a.targetable,".prepend of derived event is not supported, call source event instead",n);let t=p("* → "+a.shortName,{parent:E(a)});return c("eventPrepend",k(t)),eU(t,a,[em()],"prepend",e),eG(a,t),t}});return null!=r&&r.domain&&r.domain.hooks.event(i),C(i,"id",i.graphite.id),U(i.graphite),i}function m(e,t,r,n,a){return er(r,`${a} ${t}`,"first argument"),o(Q(n),"second argument should be a function",a),T(!P(e,"derived"),`${t} in derived store`,`${t} in store created via createStore`,a),i(Array.isArray(r)?r:[r],t=>{e.off(t),eQ(t,e,"on",el,n)}),e}function h(e,t){let r=eV(t),n=eh(e),a=d("store",r),l=Error();Error.captureStackTrace&&Error.captureStackTrace(l,h);let f=l.stack,g=p({named:"updates",derived:1});c("storeBase",n);let y=n.id,v="skipVoid"in r,b=v&&!r.skipVoid;T(!(v&&r.skipVoid),"{skipVoid: true}","updateFilter",a);let x={updates:g,defaultState:e,stateRef:n,getState(){let e,t=n;if(eR){let t=eR;for(;t&&!t.reg[y];)t=E(t);t&&(e=t)}return!e&&eO&&(eF(eO,n,1),e=eO),e&&(t=e.reg[y]),eg(t)},setState:e=>u({target:x,params:e,defer:1,scope:eO}),reset:(...e)=>(o(x.targetable,".reset of derived store is not supported",a),i(e,e=>m(x,".reset",e,()=>x.defaultState,a)),x),on:(e,t)=>(o(x.targetable,".on of derived store is not supported",a),m(x,".on",e,t,a)),off(e){let t=k(e).id,r=k(x).family.links.find(e=>e.meta.onTrigger===t);return r&&eW(r),x},map(e,t){let r,a;J(e)&&(r=e,e=e.fn);let l=x.getState(),i=Z(l);X()?a=null:(!i||i&&b)&&(a=e(l));let o=h(a,{name:`${x.shortName} \u2192 *`,derived:1,...t,and:r}),s=eQ(x,o,S,ei,e);return ey(j(o),{type:S,fn:e,from:n}),j(o).noInit=1,c("storeMap",n,s),o},watch(e,t){if(T(!t,"watch second argument","sample",a),!t||!R(e)){let t=eX(x,e);return c("storeWatch",n,e)||e(x.getState()),t}return o(Q(t),"second argument should be a function",a),e.watch(e=>t(x.getState(),e))}},w=eH("store",x,r),$=x.defaultConfig.updateFilter;x.graphite=s({scope:{state:n,fn:$},node:[ec((e,t,r)=>(r.scope&&!r.scope.reg[n.id]&&(r.b=1),e)),ep(n),ec((e,t,{a:r,b:n})=>{let l=Z(e);return l&&!v&&V(`${a}: ${eJ}`,f),(l&&b||!l)&&(e!==r||n)},1),$&&em(ea,1),eu({from:"stack",target:n})],child:g,meta:{...w,defaultState:e,storeTrace:f},regional:1}),C(x,"id",x.graphite.id),C(x,"rootStateRefId",y);let O=P(x,"serialize"),M=P(x,"derived"),N=P(x,"sid");N&&(C(x,"storeChange",1),n.sid=N),N||"ignore"===O||M||C(x,"warnSerialize",1);let _=Z(e);return o(M||!_||_&&b,eJ,a),M&&_&&!v&&console.error(`${a}: ${eJ}`),H(x,[g]),null!=r&&r.domain&&r.domain.hooks.store(x),M||(x.reinit=p({named:"reinit"}),x.reset(x.reinit)),n.meta=x.graphite.meta,U(x.graphite),x}function g(e,t={}){let r=eV(Q(e)?{handler:e}:e,t),n=d("effect",r),a=p(Q(e)?{handler:e}:e,{...t,actualOp:"effect"}),l=k(a);C(l,"op",a.kind="effect"),a.use=e=>(o(Q(e),".use argument should be a function",n),v.scope.handler=e,a),a.use.getCurrent=()=>v.scope.handler;let i=a.finally=p({named:"finally",derived:1}),f=a.done=i.filterMap({named:"done",fn({status:e,params:t,result:r}){if("done"===e)return{params:t,result:r}}}),c=a.fail=i.filterMap({named:"fail",fn({status:e,params:t,error:r}){if("fail"===e)return{params:t,error:r}}}),m=a.doneData=f.map({named:"doneData",fn:({result:e})=>e}),y=a.failData=c.map({named:"failData",fn:({error:e})=>e}),v=s({scope:{handler:a.defaultConfig.handler||(()=>o(0,`no handler used in ${a.compositeName.fullName}`))},node:[ec((e,t,r)=>{let n=t.handler,l=O(r);if(l){let e=l.handlers.unitMap.get(a)||l.handlers.sidMap[a.sid];e&&(n=e)}return e.handler=n,e},0,1),ec((e,t,r)=>{if(t.runnerFn&&!t.runnerFn(e,null,r))return;let{params:n,req:a,handler:l,args:o=[n]}=e,s=e0(n,a,1,i,r),u=e0(n,a,0,i,r),[d,f]=e1(l,u,o);d&&(J(f)&&Q(f.then)?f.then(s,u):s(f))},0,1)],meta:{op:"fx",fx:"runner"},regional:1});l.scope.runner=v,F(l.seq,ec((e,{runner:t},r)=>{let n=E(r)?{params:e,req:{rs(e){},rj(e){}}}:e;return r.meta||(r.meta={fxID:W()}),u({target:t,params:n,defer:1,scope:O(r),meta:r.meta}),n.params})),a.create=e=>{let t=function(){let e={};return e.req=new Promise((t,r)=>{e.rs=t,e.rj=r}),e.req.catch(()=>{}),e}();if(eO&&!eC){let e=eO;t.req.finally(()=>{eN(e)}).catch(()=>{})}return u({target:a,params:{params:e,req:t},scope:eO}),t.req};let b=a.inFlight=h(0,{serialize:"ignore",named:(P(a,"name")||a.graphite.id)+".inFlight"}).on(a,e=>e+1).on(i,e=>e-1).map({fn:e=>e,named:"inFlight"});C(i,"needFxCounter","dec"),C(a,"needFxCounter",1);let S=a.pending=b.map({fn:e=>e>0,named:"pending"});return H(a,[i,f,c,m,y,S,b]),null!=r&&r.domain&&r.domain.hooks.effect(a),a}function y(e,t){let r=eV({or:t,and:"string"==typeof e?{name:e}:e}),n=s({family:{type:"domain"},regional:1,parent:(null==r?void 0:r.domain)||(null==r?void 0:r.parent)}),a={history:{},graphite:n,hooks:{}};n.meta=eH("domain",a,{parent:(null==r?void 0:r.domain)||(null==r?void 0:r.parent),or:{...r,derived:1}}),l({Event:p,Effect:g,Store:h,Domain:y},(e,t)=>{let r=t.toLowerCase(),n=p({named:`on${t}`});a.hooks[r]=n;let l=new Set;a.history[`${r}s`]=l,n.create=e=>(u(n,e),e),F(k(n).seq,ec((e,t,r)=>(r.scope=null,e))),n.watch(e=>{H(a,[e]),l.add(e),e.ownerSet||(e.ownerSet=l),E(e)||(e.parent=a)}),H(a,[n]),a[`onCreate${t}`]=e=>(i(l,e),n.watch(e)),a[`create${t}`]=a[r]=(t,r)=>{let l=eV({and:r,or:t});return null!=l&&l.domain?e(t,r):n(e(t,{parent:a,or:l}))}});let o=E(a);return o&&l(a.hooks,(e,t)=>eU(e,o.hooks[t])),null!=r&&r.domain&&r.domain.hooks.domain(a),a}function v(...e){var t,r;let n,a,l,s,u,[[c,p,m],h]=f(e),g=1,y=d("sample",h);return Z(p)&&J(c)&&(t=c,r=y,n=0,i(e5,e=>{e in t&&(o(null!=t[e],e4(r,e)),n=1)}),n)&&(p=c.clock,m=c.fn,"batch"in c?g=c.batch:(T(!("greedy"in c),"greedy in sample","batch",y),g=!c.greedy),u=c.filter,a=c.target,l=c.name,s=c.sid,c=c.source),e8("sample",p,c,u,a,m,l,h,g,1,0,s)}let b="undefined"!=typeof Symbol&&Symbol.observable||"@@observable",S="map",k=e=>e.graphite||e,x=e=>e.family.owners,w=e=>e.family.links,j=e=>e.stateRef,$=e=>e.value,E=e=>e.parent,O=e=>e.scope,P=(e,t)=>k(e).meta[t],C=(e,t,r)=>k(e).meta[t]=r,M=e=>e.compositeName,R=e=>(Q(e)||J(e))&&"kind"in e,N=e=>t=>R(t)&&t.kind===e,_=N("store"),A=(N("event"),N("effect"),N("domain")),z=(N("scope"),(e,t)=>e.includes(t)),I=(e,t)=>{let r=e.indexOf(t);-1!==r&&e.splice(r,1)},F=(e,t)=>e.push(t),T=(e,t,r,n)=>!e&&console.error(`${n?n+": ":""}${t} is deprecated${r?`, use ${r} instead`:""}`),V=(e,t)=>{let r=Error(e);r.stack=t,console.error(r)},q=()=>{let e=0;return()=>""+ ++e},D=q(),L=q(),K=q(),W=q(),B=null,U=e=>{n&&n(e,B)},X=()=>B&&B.template,G=e=>(e&&B&&B.sidRoot&&(e=`${B.sidRoot}|${e}`),e),H=(e,t)=>{let r=k(e);i(t,e=>{let t=k(e);"domain"!==r.family.type&&(t.family.type="crosslink"),F(x(t),r),F(w(r),t)})},Y=(e=[])=>(Array.isArray(e)?e:[e]).flat().map(k),J=e=>"object"==typeof e&&null!==e,Q=e=>"function"==typeof e,Z=e=>void 0===e,ee=e=>o(J(e)||Q(e),"expect first argument be an object"),et=(e,t,r,n)=>o(!(!J(e)&&!Q(e)||!("family"in e)&&!("graphite"in e)),`${t}: expect ${r} to be a unit (store, event or effect)${n}`),er=(e,t,r)=>{Array.isArray(e)?i(e,(e,n)=>et(e,t,`${n} item of ${r}`,"")):et(e,t,r," or array of units")},en=(e,t,r="target")=>i(Y(t),t=>o(!P(t,"derived"),`${e}: derived unit in "${r}" is not supported, use createStore/createEvent instead"`)),ea=(e,{fn:t},{a:r})=>t(e,r),el=(e,{fn:t},{a:r})=>t(r,e),ei=(e,{fn:t})=>t(e),eo=(e,t,r,n)=>{let a={id:L(),type:e,data:t};return r&&(a.order={priority:r},n&&(a.order.barrierID=++es)),a},es=0,eu=({from:e="store",store:t,target:r,to:n=r?"store":"stack",batch:a,priority:l})=>eo("mov",{from:e,store:t,to:n,target:r},l,a),ed=({fn:e,batch:t,priority:r,safe:n=0,filter:a=0,pure:l=0})=>eo("compute",{fn:e,safe:n,filter:a,pure:l},r,t),ef=({fn:e})=>ed({fn:e,priority:"effect"}),ec=(e,t,r)=>ed({fn:e,safe:1,filter:t,priority:r&&"effect"}),ep=(e,t,r)=>eu({store:e,to:t?"stack":"a",priority:r&&"sampler",batch:1}),em=(e=ei,t)=>ed({fn:e,pure:1,filter:t}),eh=e=>({id:L(),current:e,initial:e}),eg=({current:e})=>e,ey=(e,t)=>{e.before||(e.before=[]),F(e.before,t)},ev=null,eb=(e,t)=>{let r;return e?(t&&((e.v.type===t.v.type&&e.v.id>t.v.id||e$(e.v.type)>e$(t.v.type))&&(r=e,e=t,t=r),r=eb(e.r,t),e.r=e.l,e.l=r),e):t},eS=[],ek=0;for(;ek<6;)F(eS,{first:null,last:null,size:0}),ek+=1;let ex=()=>{for(let e=0;e<6;e++){let t=eS[e];if(t.size>0){if(3===e||4===e){t.size-=1;let e=ev.v;return ev=eb(ev.l,ev.r),e}1===t.size&&(t.last=null);let r=t.first;return t.first=r.r,t.size-=1,r.v}}},ew=(e,t,r,n,a,l,i)=>ej(0,{a:null,b:null,node:r,parent:n,value:a,page:t,scope:l,meta:i},e,0),ej=(e,t,r,n)=>{let a=e$(r),l=eS[a],i={v:{idx:e,stack:t,type:r,id:n},l:null,r:null};3===a||4===a?ev=eb(ev,i):(0===l.size?l.first=i:l.last.r=i,l.last=i),l.size+=1},e$=e=>{switch(e){case"child":return 0;case"pure":return 1;case"read":return 2;case"barrier":return 3;case"sampler":return 4;case"effect":return 5;default:return -1}},eE=new Set,eO,eP=1,eC=0,eM=0,eR=null,eN=e=>{eO=e},e_=e=>{eR=e},eA=(e,t)=>{if(e){for(;e&&!e.reg[t];)e=e.parent;if(e)return e}return null},ez=(e,t,r,n)=>{let a=eA(e,r.id);return a?a.reg[r.id]:t?(eF(t,r,n),t.reg[r.id]):r},eI=e=>e,eF=(e,t,r,n,a)=>{let l=e.reg;if(l[t.id])return;let o=t.sid,s={id:t.id,current:t.initial,meta:t.meta};if(s.id in e.values.idMap)s.current=e.values.idMap[s.id];else if(o&&o in e.values.sidMap&&!(o in e.sidIdMap)){var u;let r=null==t||null==(u=t.meta)?void 0:u.serialize;s.current=(e.fromSerialize&&"ignore"!==r&&(null==r?void 0:r.read)||eI)(e.values.sidMap[o])}else if(t.before&&!a){let a=0,o=r||!t.noInit||n;i(t.before,t=>{switch(t.type){case"map":{let a=t.from;if((a||t.fn)&&(a&&eF(e,a,r,n),o)){let e=a&&l[a.id].current;s.current=t.fn?t.fn(e):e}break}case"field":eF(e,t.from,r,n),a||(a=1,s.current=Array.isArray(s.current)?[...s.current]:{...s.current}),o&&(s.current[t.field]=l[l[t.from.id].id].current)}})}o&&(e.sidIdMap[o]=t.id),l[t.id]=s},eT=(e,t,r)=>{try{return t($(r),e.scope,r)}catch(t){console.error(t),e.fail=1,e.failReason=t}},eV=(e,t={})=>(J(e)&&(eV(e.or,t),l(e,(e,r)=>{Z(e)||"or"===r||"and"===r||(t[r]=e)}),eV(e.and,t)),t),eq=(e,t)=>{I(e.next,t),I(x(e),t),I(w(e),t)},eD=["on","reset","sample","split","merge","guard","forward"],eL=(e,t,r,n,a)=>{let l;e.next.length=0,e.seq.length=0,e.scope=null;let i=w(e),o=e.meta.isRegion,s=o?e:n;if(i.length>0){let n=z(eD,e.meta.op),u=!o&&!a,d=u&&r&&!n;for(;l=i.pop();){let i=z(l.next,e);eq(l,e),o&&eL(l,0,0,e,1),i||(l.family.triggers-=1),(t||d||u&&"crosslink"===l.family.type&&!n||a&&z(eD,l.meta.op)&&(i&&0===l.next.length||!i&&l.family.triggers<=0))&&eL(l,t,r&&"on"!==l.meta.op,s,a)}}for(i=x(e);l=i.pop();)eq(l,e),r&&"crosslink"===l.family.type&&eL(l,t,"on"!==l.meta.op,s,a)},eK=e=>e.clear(),eW=(e,{deep:t}={})=>{let r=0;if(e.ownerSet&&e.ownerSet.delete(e),A(e)){r=1;let t=e.history;eK(t.events),eK(t.effects),eK(t.stores),eK(t.domains)}eL(k(e),!!t,r,null,0)},eB=e=>(function(e){let t=()=>e();return t.unsubscribe=()=>e(),t})(()=>eW(e)),eU=(e,t,r,n,a)=>s({node:r,parent:e,child:t,scope:{fn:a},meta:{op:n},family:{owners:[e,t],links:t},regional:1}),eX=(e,t)=>(o(Q(t),".watch argument should be a function"),eB(s({scope:{fn:t},node:[ef({fn:ei})],parent:e,meta:{op:"watch"},family:{owners:e},regional:1}))),eG=(e,t,r="event")=>{E(e)&&E(e).hooks[r](t)},eH=(e,t,r)=>{let n=eV(r),a="domain"===e,l=D(),{sid:i=null,named:o=null,domain:s=null,parent:u=s}=n,d=o||n.name||(a?"":l),f=function(e,t){let r,n;if(t){let a=M(t);0===e.length?(r=a.path,n=a.fullName):(r=a.path.concat([e]),n=0===a.fullName.length?e:a.fullName+"/"+e)}else r=0===e.length?[]:[e],n=e;return{shortName:e,fullName:n,path:r}}(d,u),c={op:t.kind=e,name:t.shortName=d,sid:t.sid=G(i),named:o,unitId:t.id=l,serialize:n.serialize,derived:n.derived,config:n};if(t.targetable=!n.derived,t.parent=u,t.compositeName=f,t.defaultConfig=n,t.getType=()=>(T(0,"getType","compositeName.fullName"),f.fullName),!a){t.subscribe=e=>(ee(e),t.watch(Q(e)?e:t=>e.next&&e.next(t))),t[b]=()=>t;let e=X();e&&(c.nativeTemplate=e)}return c},eY=(e,t,r,n)=>{let a;J(r)&&(a=r,r=r.fn);let l=p({name:`${e.shortName} \u2192 *`,derived:1,and:a});return eU(e,l,n,t,r),l},eJ="undefined is used to skip updates. To allow undefined as a value provide explicit { skipVoid: false } option",eQ=(e,t,r,n,a)=>{let l=j(t),i=eu({store:l,to:"a",priority:"read"});r===S&&(i.data.softRead=1);let o=[i,em(n)];c("storeOnMap",l,o,_(e)&&j(e));let s=eU(e,t,o,r,a);return r!==S&&C(s,"onTrigger",k(e).id),s},eZ=(e,t,r,n,a,i)=>{let s=d("combine",n),u=e?e=>[...e]:e=>({...e}),f=e?[]:{},p=u(f),m=eh(p),g=eh(1);m.type=e?"list":"shape",m.noInit=1,c("combineBase",m,g);let y=h(p,{name:function(e,t="combine"){let r=t+"(",n="",a=0;return l(e,e=>{a<25&&(null!=e&&(r+=n,r+=R(e)?M(e).fullName:e.toString()),a+=1,n=", ")}),r+")"}(r),derived:1,...i,and:n}),v=j(y);v.noInit=1,C(y,"isCombine",1);let b=ep(m);b.order={priority:"barrier"};let k=eu({store:v,to:"b",priority:"read"});k.data.softRead=1;let x=[ec((e,t,r)=>(r.scope&&!r.scope.reg[m.id]&&(r.c=1),e)),b,eu({store:g,to:"b"}),ec((e,{key:r},n)=>{if(n.c||e!==n.a[r])return t&&n.b&&(n.a=u(n.a)),n.a[r]=e,1},1),eu({from:"a",target:m}),eu({from:"value",store:0,target:g}),eu({from:"value",store:1,target:g,priority:"barrier",batch:1}),ep(m,1,1),a&&em(),k];if(l(r,(e,t)=>{if(!_(e))return o(!R(e)&&!Z(e),`combine expects a store in a field ${t}`,s),void(p[t]=f[t]=e);f[t]=e.defaultState,p[t]=e.getState();let r=eU(e,y,x,"combine",a);r.scope.key=t;let n=j(e);ey(m,{type:"field",field:t,from:n}),c("combineField",n,r)}),y.defaultShape=r,ey(v,{type:S,from:m,fn:a}),!X())if(a){let e=a(p);!Z(e)||i&&"skipVoid"in i||console.error(`${s}: ${eJ}`),v.current=e,v.initial=e,y.defaultState=e}else y.defaultState=f;return y},e1=(e,t,r)=>{try{return[1,e(...r)]}catch(e){return t(e),[0,null]}},e0=(e,t,r,n,a)=>l=>{u({target:[n,e2],params:[r?{status:"done",params:e,result:l}:{status:"fail",params:e,error:l},{value:l,fn:r?t.rs:t.rj}],defer:1,page:a.page,scope:a.scope,meta:a.meta})},e2=s({node:[ef({fn:({fn:e,value:t})=>e(t)})],meta:{op:"fx",fx:"sidechain"}}),e5=["source","clock","target"],e4=(e,t)=>e+`: ${t} should be defined`,e8=(e,t,r,n,a,l,i,s,u,m,g,y)=>{let v=d(e,s),b=!!a;o(!Z(r)||!Z(t),e4(v,"either source or clock"));let S=0;Z(r)?S=1:R(r)||(r=function(...e){let t,r,n,a,l,i;[e,n]=f(e);let s=d("combine",n),u=e[e.length-1],c=e.length>1&&!_(u)&&J(u),p=c&&u,m=c?e[e.length-2]:u;if(Q(m)?(r=e.slice(0,c?-2:-1),t=m):r=e,1===r.length){let e=r[0];_(e)||(a=e,l=1)}if(!l&&(a=r,t)){i=1;let e=t;t=t=>e(...t)}return o(J(a),`${s}: shape should be an object`),eZ(Array.isArray(a),!i,a,n,t,p)}(r)),Z(t)?t=r:(er(t,v,"clock"),Array.isArray(t)&&(t=eU(t,[],[],e))),S&&(r=t),s||i||(i=r.shortName);let x="none";(g||n)&&(R(n)?x="unit":(o(Q(n),"`filter` should be function or unit"),x="fn")),a?(er(a,v,"target"),en(v,a)):"none"===x&&m&&_(r)&&_(t)?a=h(l?l(eg(j(r)),eg(j(t))):eg(j(r)),{name:i,sid:y,or:s}):c("sampleTarget",k(a=p({name:i,derived:1,or:s})));let w=eh(),$=[];if("unit"===x){let[r,l,i]=e6(n,a,t,w,e);i||$.push(...e9(l)),$.push(...e9(r))}let E=[];if(S)u&&E.push(ep(w,1,1));else{let[n,l,i]=e6(r,a,t,w,e);i||E.push(...e9(l)),E.push(ep(n,1,u))}let O=eU(t,a,[c("sampleSourceLoader"),eu({from:"stack",target:w}),...E,...$,ep(w),"fn"===x&&em((e,t,{a:r})=>n(e,r),1),l&&em(ea),c("sampleSourceUpward",b)],e,l);return H(r,[O]),Object.assign(O.meta,s,{joint:1}),a},e9=e=>[ep(e),ec((e,t,{a:r})=>r,1)],e6=(e,t,r,n,a)=>{let l=_(e),i=l?j(e):eh(),o=eh(l);return l||s({parent:e,node:[eu({from:"stack",target:i}),eu({from:"value",store:1,target:o})],family:{owners:[...new Set([e,t,r].flat())],links:t},meta:{op:a},regional:1}),c("sampleSource",o,i,n),[i,o,l]}},55201:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let n=r(71629);function a(e){let{reason:t,children:r}=e;throw Object.defineProperty(new n.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},64904:(e,t,r)=>{var n=r(60159),a=r(52826),l="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=a.useSyncExternalStore,o=n.useRef,s=n.useEffect,u=n.useMemo,d=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,a){var f=o(null);if(null===f.current){var c={hasValue:!1,value:null};f.current=c}else c=f.current;var p=i(e,(f=u(function(){function e(e){if(!s){if(s=!0,i=e,e=n(e),void 0!==a&&c.hasValue){var t=c.value;if(a(t,e))return o=t}return o=e}if(t=o,l(i,e))return t;var r=n(e);return void 0!==a&&a(t,r)?(i=e,t):(i=e,o=r)}var i,o,s=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,a]))[0],f[1]);return s(function(){c.hasValue=!0,c.value=p},[p]),d(p),p}},81816:(e,t,r)=>{e.exports=r(64904)},95718:(e,t)=>{function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},97008:(e,t,r)=>{r.d(t,{e3:()=>s});var n=r(60159),a=r(71371),l=r(81816),i=r(52826);function o(e,t){let r=a.is.unit(e),l={};r?l={unit:e}:"@@unitShape"in e?"function"==typeof e["@@unitShape"]?l=e["@@unitShape"]():u("expect @@unitShape to be a function"):l=e;let i=Array.isArray(l),o=n.useRef({stale:1,justSubscribed:0,scope:t}),[s,d,c,m,h]=n.useMemo(()=>{o.current.stale=1;let e=Array.isArray(l)?[]:{},n=[],i=[],s=[],d=[];for(let o in l){if(!({}).hasOwnProperty.call(l,o))continue;let f=l[o];a.is.unit(f)||u(`expect useUnit ${r?"argument":`value in key "${o}"`} to be a unit`),a.is.event(f)||a.is.effect(f)?(e[o]=t?(0,a.GY)(f,{scope:t}):f,s.push(o),d.push(f)):(e[o]=null,n.push(o),i.push(f))}return[e,n,i,s,d]},[o,t,...Object.keys(l),...Object.values(l)]),g=n.useRef({value:s,storeKeys:d,eventKeys:m,eventValues:h}),y=n.useCallback(e=>{let r=o.current;return r.justSubscribed=1,(0,a.AX)({unit:c,fn:()=>{r.stale||(r.stale=1,e())},scope:t,batch:1})},[c,t,g,o]),v=n.useCallback(()=>{let e=g.current,n=o.current,a,l=0,u=e.value,f=e.storeKeys,y=e.eventKeys,v=e.eventValues,b=t!==n.scope;if(n.stale||n.justSubscribed||b){l=!n.justSubscribed||b,a=i?[...s]:{...s},f.length===d.length&&y.length===m.length||(l=1);for(let e=0;e<d.length;e++){let r=p(c[e],t),n=d[e];l||(l=f.includes(n)?u[n]!==r:1),a[n]=r}for(let e=0;e<m.length;e++){let t=h[e],r=m[e];l||(l=y.includes(r)?v[y.indexOf(r)]!==t:1)}}return l&&(e.value=a),e.storeKeys=d,e.eventKeys=m,e.eventValues=h,n.stale=0,n.justSubscribed=!l,n.scope=t,r?e.value.unit:e.value},[y,c,h,t,g,o]);return f(y,v,v)}function s(e,t){return o(e,function(e){let t=n.useContext(h);return e&&!t&&u("No scope found, consider adding <Provider> to app root"),t}(null==t?void 0:t.forceScope))}let u=e=>{throw Error(e)},d="undefined"!=typeof window?n.useLayoutEffect:n.useEffect,{useSyncExternalStore:f}=i,{useSyncExternalStoreWithSelector:c}=l,p=(e,t)=>t?t.getState(e):e.getState(),m=(e,t)=>e!==t,h=n.createContext(null),{Provider:g}=h,y=e=>"object"==typeof e&&null!==e}};