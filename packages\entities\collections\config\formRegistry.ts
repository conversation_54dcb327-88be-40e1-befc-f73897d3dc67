
import { carFormConfig, carFormConfigs } from '@entities/cars/config/formConfig';
import type { FormConfig } from '@entities/collections/types/formTypes';
import { locationFormConfig, locationFormConfigs } from '@entities/locations/config/formConfig';
import { notificationFormConfig, notificationFormConfigs } from '@entities/notifications/config/formConfig';
import { orderFormConfig, orderFormConfigs } from '@entities/order/config/formConfig';
import { serviceFormConfig, serviceFormConfigs } from '@entities/services/config/formConfig';
import { tariffFormConfig, tariffFormConfigs } from '@entities/tariff/config/formConfig';
import { userFormConfig, userFormConfigs } from '@entities/users/config/formConfig';
import { Role } from '@entities/users/enums';

export const formRegistry = {
  users: userFormConfig,
  cars: carFormConfig,
  locations: locationFormConfig,
  notifications: notificationFormConfig,
  orders: orderFormConfig,
  services: serviceFormConfig,
  tariffs: tariffFormConfig,
};// Реестр конфигураций форм для создания записей
export const createFormRegistry = {
  users: userFormConfigs[Role.Admin],
  cars: carFormConfigs.create,
  locations: locationFormConfigs.create,
  notifications: notificationFormConfigs.create,
  orders: orderFormConfigs.create,
  services: serviceFormConfigs.create,
  tariffs: tariffFormConfigs.create,
};// Реестр конфигураций форм для обновления записей
export const updateFormRegistry = {
  users: userFormConfigs[Role.Admin],
  cars: carFormConfigs.update,
  locations: locationFormConfigs.update,
  notifications: notificationFormConfigs.get,
  orders: orderFormConfigs.update,
  services: serviceFormConfigs.update,
  tariffs: tariffFormConfigs.update,
};/**
 * Функция для получения конфигурации формы с учетом роли пользователя
 * @param collectionName Название коллекции
 * @param role Роль пользователя (для коллекции users)
 * @returns Конфигурация формы
 */
export function getFormConfig(collectionName: string, role?: string | null): FormConfig {
  // Для пользователей используем конфигурацию в зависимости от роли
  if (collectionName === 'users' && role) {
    return userFormConfigs[role as keyof typeof userFormConfigs];
  }  // Для остальных коллекций используем конфигурацию из реестра

  return formRegistry[collectionName as keyof typeof formRegistry];
}
