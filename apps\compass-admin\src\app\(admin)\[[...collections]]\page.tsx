import type { Metadata } from 'next';
import { getUserFromCookie } from '@shared/lib/parse-cookie';
import { 
  getCreateComponent, 
  getCustomComponent, 
  getEditComponent, 
  getListComponent 
} from '@entities/collections/config';
import type { CollectionName } from '@entities/collections/types/types';
import type { Role } from '@entities/users/enums/Role.enum';
import { CollectionHeader, type OrderType } from '@widgets/header';
import { DashboardPage } from '@pages/admin/dashboard';

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ collections?: string[] }>;
}): Promise<Metadata> => {
  const { collections } = await params;
  const segments = collections || [];

  if (segments.length === 0) {
    return {
      title: 'Панель управления | Compass 2.0',
      description: 'Главная страница административной панели Compass 2.0',
    };
  }

  const collectionName = segments[0];

  if (segments.length === 1) {
    return {
      title: `${collectionName} | Compass 2.0`,
      description: `Список элементов коллекции ${collectionName} в системе Compass 2.0`,
    };
  }

  if (segments[1] === 'create') {
    return {
      title: `Создание ${collectionName} | Compass 2.0`,
      description: `Создание нового элемента в коллекции ${collectionName}`,
    };
  }

  if (segments.length === 2) {
    // Специальные метаданные для маршрутов профиля
    if (collectionName === 'profile') {
      if (segments[1] === 'edit') {
        return {
          title: 'Редактирование профиля | Compass 2.0',
          description: 'Редактирование профиля пользователя в системе Compass 2.0',
        };
      } else {
        return {
          title: 'Просмотр профиля | Compass 2.0',
          description: 'Просмотр профиля пользователя в системе Compass 2.0',
        };
      }
    }

    // Для остальных коллекций
    return {
      title: `Редактирование ${collectionName} | Compass 2.0`,
      description: `Редактирование элемента в коллекции ${collectionName}`,
    };
  }

  return {
    title: 'Коллекции | Compass 2.0',
    description: 'Управление коллекциями данных в системе Compass 2.0',
  };
};
export default async function CollectionsRouter({
  params,
  searchParams,
}: {
  params: Promise<{ collections?: string[] }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // Дожидаемся разрешения params и searchParams
  const [{ collections }, resolvedSearchParams] = await Promise.all([params, searchParams]);
  const segments = collections || [];
  
  // Получаем роль пользователя из куки
  const userRole = (await getUserFromCookie('role')) as Role | null;
  
  // Получаем тип заказа из URL параметров (для заказов)
  const orderType = resolvedSearchParams.type?.toString().toLowerCase() as OrderType;

  // 🧪 ТЕСТИРОВАНИЕ: Временно переключаем роль на Partner для тестирования OrderPartnerPage
  // Раскомментируйте строку ниже для тестирования партнерского интерфейса:
  // const testUserRole = Role.Partner;
  
  // Если нет сегментов, показываем страницу со списком всех коллекций
  if (segments.length === 0) {
    return <DashboardPage userRole={userRole} />;
  }

  // Первый сегмент - это название коллекции
  const collectionName = segments[0];

  // Если есть только один сегмент (название коллекции), показываем список элементов или профиль
  if (segments.length === 1) {
    // Для профиля показываем компонент профиля
    if (collectionName === 'profile') {
      const ProfileComponent = getCustomComponent(collectionName, userRole);

      if (!ProfileComponent) {
        return (
          <>
            <h1 className='text-2xl font-bold text-red-600 mb-4'>Компонент профиля не найден</h1>
            <p>Компонент профиля не существует.</p>
          </>
        );
      }

      // Отображаем заголовок коллекции и компонент профиля в режиме просмотра
      return (
        <>
          <CollectionHeader collectionName={collectionName} mode='view' />
          <ProfileComponent collectionName={collectionName as CollectionName} mode='view' />
        </>
      );
    }

    if (collectionName === 'settings') {
        const SettingsComponent = getCustomComponent(collectionName);

        if (!SettingsComponent) {
          return (
            <>
              <h1 className='text-2xl font-bold text-red-600 mb-4'>Компонент настроек не найден</h1>
              <p>Компонент настроек не существует.</p>
            </>
          );
        }

        return (
          <>
            <CollectionHeader collectionName={collectionName} mode='view' />
            <SettingsComponent collectionName={collectionName as CollectionName} mode='view' />
          </>
        );
    }

    // Для всех остальных коллекций показываем список
    const ListComponent = getListComponent(collectionName, userRole);

    if (!ListComponent) {
      return (
        <>
          <h1 className='text-2xl font-bold text-red-600 mb-4'>Коллекция не найдена</h1>
          <p>Коллекция &quot;{collectionName}&quot; не существует.</p>
        </>
      );
    }

    // Отображаем заголовок коллекции и компонент списка
    return (
      <>
        <CollectionHeader collectionName={collectionName} mode='list' />
        <div className='flex-1 overflow-auto min-h-0'>
          <ListComponent
            collectionName={collectionName as CollectionName}
            role={userRole || undefined}
          />
        </div>
      </>
    );
  }

  // Если второй сегмент "create", показываем страницу создания
  if (segments[1] === 'create') {
    const CreateComponent = getCreateComponent(collectionName, userRole);
    const CustomComponent = getCustomComponent(collectionName, userRole); // Проверяем сначала manageComponent, потом customComponent

    if (CreateComponent) {
      // Отображаем заголовок коллекции и компонент создания
      return (
        <>
          <CollectionHeader 
            collectionName={collectionName} 
            mode='create' 
            orderType={collectionName === 'orders' ? orderType : undefined}
          />
          <div className='flex-1 overflow-auto'>
            <CreateComponent
              collectionName={collectionName as CollectionName}
              mode='create'
              role={userRole || undefined}
            />
          </div>
        </>
      );
    } else if (CustomComponent) {
      // Используем кастомный компонент для создания
      return (
        <>
          <CollectionHeader 
            collectionName={collectionName} 
            mode='create' 
            orderType={collectionName === 'orders' ? orderType : undefined}
          />
          <div className='flex-1 overflow-auto'>
            <CustomComponent collectionName={collectionName as CollectionName} mode='create' />
          </div>
        </>
      );
    } else {
      return (
        <>
          <h1 className='text-2xl font-bold text-red-600 mb-4'>Коллекция не найдена</h1>
          <p>Коллекция &quot;{collectionName}&quot; не существует или не поддерживает создание.</p>
        </>
      );
    }
  }

  // Если есть два сегмента и второй не "create", обрабатываем в зависимости от коллекции
  if (segments.length === 2) {
    // Для пользователей обрабатываем маршрут users/role?id=uuid
    if (collectionName === 'users' && resolvedSearchParams.id) {
      const role = segments[1];
      const userId = resolvedSearchParams.id.toString();
      const EditComponent = getEditComponent(collectionName, userRole);

      if (!EditComponent) {
        return (
          <>
            <h1 className='text-2xl font-bold text-red-600 mb-4'>Коллекция не найдена</h1>
            <p>
              Коллекция &quot;{collectionName}&quot; не существует или не поддерживает
              редактирование.
            </p>
          </>
        );
      }

      // Отображаем заголовок коллекции и компонент редактирования с ролью пользователя
      return (
        <>
          <CollectionHeader collectionName={collectionName} mode='edit' />
          <div className='flex-1 overflow-auto h-full'>
            <EditComponent
              collectionName={collectionName as CollectionName}
              id={userId}
              role={role}
              mode='edit'
            />
          </div>
        </>
      );
    } // Для профиля обрабатываем специальный маршрут edit

    if (collectionName === 'profile') {
      const ProfileComponent = getCustomComponent(collectionName, userRole);

      if (!ProfileComponent) {
        return (
          <>
            <h1 className='text-2xl font-bold text-red-600 mb-4'>Компонент профиля не найден</h1>
            <p>Компонент профиля не существует.</p>
          </>
        );
      }

      // Отображаем заголовок коллекции и компонент профиля в соответствующем режиме
      return (
        <>
          <CollectionHeader collectionName={collectionName} mode='edit' />
          <div className='flex-1 overflow-auto h-full'>
            <ProfileComponent collectionName={collectionName as CollectionName} mode='edit' />
          </div>
        </>
      );
    }

    // Для остальных коллекций считаем второй сегмент ID элемента для редактирования
    const EditComponent = getEditComponent(collectionName, userRole);
    const CustomComponent = getCustomComponent(collectionName, userRole); // Проверяем сначала manageComponent, потом customComponent

    if (EditComponent) {
      // Отображаем заголовок коллекции и компонент редактирования
      return (
        <>
          <CollectionHeader 
            collectionName={collectionName} 
            mode='edit' 
            orderType={collectionName === 'orders' ? orderType : undefined}
          />
          <div className='flex-1 overflow-auto h-full'>
            <EditComponent
              collectionName={collectionName as CollectionName}
              id={segments[1]}
              mode='edit'
              role={userRole || undefined}
            />
          </div>
        </>
      );
    } else if (CustomComponent) {
      // Используем кастомный компонент для редактирования
      return (
        <>
          <CollectionHeader 
            collectionName={collectionName} 
            mode='edit' 
            orderType={collectionName === 'orders' ? orderType : undefined}
          />
          <div className='flex-1 overflow-auto h-full'>
            <CustomComponent
              collectionName={collectionName as CollectionName}
              id={segments[1]}
              mode='edit'
            />
          </div>
        </>
      );
    } else {
      return (
        <>
          <h1 className='text-2xl font-bold text-red-600 mb-4'>Коллекция не найдена</h1>
          <p>
            Коллекция &quot;{collectionName}&quot; не существует или не поддерживает редактирование.
          </p>
        </>
      );
    }
  } // В остальных случаях показываем страницу "не найдено"

  return (
    <>
      <h1 className='text-2xl font-bold text-red-600 mb-4'>Коллекция не найдена</h1>
      <p>Коллекция &quot;{collectionName}&quot; не существует.</p>
    </>
  );
}
