"use strict";exports.id=5853,exports.ids=[5853],exports.modules={45853:(s,e,l)=>{l.r(e),l.d(e,{CompanyDetailsModal:()=>d});var i=l(13486),n=l(97008);l(60159);var a=l(38655);let d=()=>{let s=(0,n.e3)(a.Oo),e=()=>{s()};return(0,i.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold",children:"Наши реквизиты"}),(0,i.jsx)("button",{onClick:e,className:"text-gray-400 hover: transition-colors",children:(0,i.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,i.jsx)("div",{className:"p-6 space-y-4",children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold  mb-2",children:'ОсОО "Компас Трансфер"'}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"ИНН:"})," [ИНН будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"ОКПО:"})," [ОКПО будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Юридический адрес:"})," [адрес будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Фактический адрес:"})," [адрес будет указан позже]"]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold  mb-2",children:"Банковские реквизиты"}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Банк:"})," ОАО \xabОптима банк\xbb"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Филиал:"})," [филиал будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Адрес банка:"})," [адрес будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"БИК:"})," [БИК будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Расчетный счет:"})," [расчетный счет будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"SWIFT:"})," [SWIFT будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Менеджер банковского счета:"})," [ФИО менеджера будет указан позже]"]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold  mb-2",children:"Контактная информация"}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Телефон:"})," +[телефон будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Email:"})," [email будет указан позже]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Веб-сайт:"})," [website будет указан позже]"]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold  mb-2",children:"Руководство"}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Директор:"})," [Указать ФИО директора]"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Главный бухгалтер:"})," [Указать ФИО главного бухгалтера]"]})]})]})]})}),(0,i.jsx)("div",{className:"flex justify-end p-6 border-t border-gray-200",children:(0,i.jsx)("button",{onClick:e,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Закрыть"})})]})})}}};