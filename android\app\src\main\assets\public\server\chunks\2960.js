"use strict";exports.id=2960,exports.ids=[2960],exports.modules={2065:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",width:e,height:e,className:r,children:(0,a.jsx)("path",{d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})})},2262:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:[(0,a.jsx)("path",{d:"M10.464 8.746c.227-.18.497-.311.786-.394v2.795a2.252 2.252 0 0 1-.786-.393c-.394-.313-.546-.681-.546-1.004 0-.323.152-.691.546-1.004ZM12.75 15.662v-2.824c.347.085.664.228.921.421.427.32.579.686.579.991 0 .305-.152.671-.579.991a2.534 2.534 0 0 1-.921.42Z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v.816a3.836 3.836 0 0 0-1.72.756c-.712.566-1.112 1.35-1.112 2.178 0 .829.4 1.612 1.113 2.178.502.4 1.102.647 1.719.756v2.978a2.536 2.536 0 0 1-.921-.421l-.879-.66a.75.75 0 0 0-.9 1.2l.879.66c.533.4 1.169.645 1.821.75V18a.75.75 0 0 0 1.5 0v-.81a3.833 3.833 0 0 0 1.719-.756c.712-.566 1.112-1.35 1.112-2.178 0-.829-.4-1.612-1.113-2.178a3.833 3.833 0 0 0-1.718-.756V8.334c.29.082.559.213.786.393l.415.33a.75.75 0 0 0 .933-1.175l-.415-.33a3.836 3.836 0 0 0-1.719-.755V6Z",clipRule:"evenodd"})]})},5448:(e,r,t)=>{t.d(r,{n:()=>a});async function a(e,r=1500){let t=Date.now(),s=await e,i=Date.now()-t;if(i<r){let e=r-i;await new Promise(r=>setTimeout(r,e))}return s}},13996:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsxs)("svg",{className:`animate-spin ${r}`,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})},18673:(e,r,t)=>{t.d(r,{P:()=>a.P});var a=t(63497)},19263:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",width:e,height:e,className:r,children:[(0,a.jsx)("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),(0,a.jsx)("line",{x1:"1",y1:"1",x2:"23",y2:"23"})]})},24986:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.502 6h7.128A3.375 3.375 0 0 1 18 9.375v9.375a3 3 0 0 0 3-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 0 0-.673-.05A3 3 0 0 0 15 1.5h-1.5a3 3 0 0 0-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6ZM13.5 3A1.5 1.5 0 0 0 12 4.5h4.5A1.5 1.5 0 0 0 15 3h-1.5Z",clipRule:"evenodd"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V9.375ZM6 12a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V12Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 15a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V15Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 18a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V18Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75Z",clipRule:"evenodd"})]})},27766:(e,r,t)=>{t.r(r),t.d(r,{default:()=>i});var a=t(13486);t(60159);let s=e=>{if(!e)return"#8EBFFF";switch(e){case"Economy":return"#9CA3AF";case"Comfort":return"#60A5FA";case"ComfortPlus":return"#3B82F6";case"Business":return"#8B5CF6";case"Premium":return"#F59E0B";case"Vip":return"#EF4444";case"Luxury":return"#DC2626";default:return"#8EBFFF"}},i=({size:e=32,className:r="",serviceClass:t,isSelected:i=!1})=>{let l=i?"#22C55E":s(t);return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 41 24",fill:"none",width:e,height:e,className:r,style:{filter:i?"drop-shadow(0 0 4px #22C55E)":void 0},children:[(0,a.jsxs)("g",{clipPath:"url(#clip0_42_2510)",children:[(0,a.jsx)("path",{d:"M11.7415 5.22881C11.8153 4.34825 11.3567 3.34659 12.6569 3.01606L12.7498 5.027C14.4654 4.66129 16.0866 4.32077 17.7056 3.96753C20.0512 3.4575 22.4009 2.9697 24.733 2.40747C25.638 2.16049 26.5238 1.84568 27.3832 1.46561C30.2331 0.298448 33.2233 0.235652 36.2282 0.285838C36.9767 0.299309 37.471 0.750296 37.8466 1.38123C38.9376 3.20844 39.5421 5.23256 39.9148 7.29461C40.283 9.32646 40.4181 11.4059 40.5761 13.4698C40.674 14.7074 40.1256 15.5987 38.9536 16.0914C36.0574 17.3071 33.162 18.3387 29.9513 18.5157C27.4256 18.6547 24.926 19.3607 22.4247 19.8603C20.3821 20.2747 18.3515 20.7478 16.2235 21.2158L16.836 23.0363C16.0758 23.2122 16.0758 23.2122 15.0326 21.3316C13.962 21.7123 12.8791 22.2313 11.7456 22.4725C10.1125 22.8264 8.53393 23.5459 6.78885 23.3079C6.35022 23.2481 5.87241 23.4607 5.41619 23.5634C4.48482 23.7664 3.46457 23.3685 3.11709 22.4642C2.30821 20.3614 1.34025 18.2772 0.888129 16.0908C0.431324 13.871 0.472913 11.5449 0.381643 9.26176C0.33822 8.32098 1.06987 7.62934 2.00073 7.38149C3.84511 6.88339 5.66651 6.29513 7.52494 5.86361C8.64497 5.60383 9.81481 5.57383 10.9542 5.4268C11.2204 5.37737 11.4833 5.31126 11.7415 5.22881ZM17.2852 7.05379C15.6778 7.01434 14.2232 6.91981 12.7698 6.95487C10.9704 7.00111 10.5394 7.51561 10.5478 9.36078C10.5377 9.65339 10.556 9.94635 10.6023 10.2354C11.0815 12.6015 11.5422 14.9732 12.0866 17.325C12.2374 17.9775 12.6062 18.596 12.9562 19.1817C13.3386 19.8219 13.9723 20.0664 14.6519 19.7757C16.2725 19.083 17.8666 18.326 19.5297 17.5689C17.8466 14.1892 17.1703 10.7806 17.2852 7.05537L17.2852 7.05379ZM31.7694 14.8319C33.2584 14.5503 34.5568 14.3159 35.8493 14.0562C36.4225 13.9401 36.7368 13.5478 36.8239 12.9548C37.3243 9.82571 36.6332 6.61749 34.8923 3.98739C34.6945 3.67639 34.129 3.38236 33.7933 3.44588C32.4228 3.6804 31.0758 4.05149 29.5778 4.41035C31.221 7.7407 31.9583 11.0777 31.7695 14.8335L31.7694 14.8319ZM30.5847 3.32986L30.517 3.05287C27.326 2.91043 24.2295 3.52235 21.0804 4.28875C21.2131 4.88767 21.3317 5.41842 21.4754 6.05205L30.5847 3.32986ZM24.0576 18.4484C26.7556 18.0662 32.3728 16.5248 33.1672 15.3932L23.6954 16.7156L24.0576 18.4484ZM20.4873 4.38016L13.7881 5.78261C16.123 6.12942 18.4335 6.42295 20.8603 6.14287C20.7244 5.50888 20.6121 4.97785 20.4859 4.38343L20.4873 4.38016ZM16.7459 19.9303L16.8581 20.1525L23.4907 18.633C23.358 17.9989 23.2453 17.4599 23.0884 16.7116L16.7459 19.9303Z",fill:l}),(0,a.jsx)("path",{d:"M17.2847 7.05528C17.1698 10.7805 17.8461 14.1891 19.5293 17.5704C17.8662 18.3275 16.2721 19.0845 14.6515 19.7772C13.9719 20.0679 13.3381 19.8234 12.9558 19.1832C12.6058 18.5975 12.237 17.979 12.0862 17.3265C11.5417 14.9747 11.0842 12.6028 10.6018 10.2369C10.5555 9.94786 10.5373 9.6549 10.5473 9.36228C10.539 7.51711 10.97 7.00262 12.7693 6.95638C14.2227 6.92131 15.6774 7.01582 17.2847 7.05528Z",fill:"white"}),(0,a.jsx)("path",{d:"M31.7696 14.8334C31.9585 11.0776 31.2212 7.7406 29.578 4.41185C31.0761 4.05459 32.423 3.6819 33.7935 3.44738C34.1295 3.39025 34.6949 3.68267 34.8925 3.98888C36.6334 6.61899 37.3245 9.82719 36.8241 12.9562C36.737 13.5493 36.4225 13.9352 35.8496 14.0577C34.557 14.3174 33.2587 14.5518 31.7696 14.8334Z",fill:"white"}),(0,a.jsx)("path",{d:"M30.5845 3.32976L21.472 6.0505C21.3314 5.41672 21.2129 4.88597 21.077 4.2872C24.2261 3.52239 27.3227 2.91047 30.5136 3.05131L30.5845 3.32976Z",fill:"white"}),(0,a.jsx)("path",{d:"M24.0556 18.4516L23.6949 16.7187L33.1667 15.3963C32.371 16.5327 26.7537 18.0725 24.0556 18.4516Z",fill:"white"}),(0,a.jsx)("path",{d:"M20.4857 4.38333C20.6119 4.97775 20.7242 5.50879 20.8587 6.14605C18.4316 6.41814 16.1214 6.1326 13.7865 5.78578L20.4857 4.38333Z",fill:"white"}),(0,a.jsx)("path",{d:"M16.7446 19.9335L23.0887 16.7147C23.244 17.463 23.3563 17.9941 23.491 18.6361L16.8584 20.1556L16.7446 19.9335Z",fill:"white"})]}),(0,a.jsx)("defs",{children:(0,a.jsx)("clipPath",{id:"clip0_42_2510",children:(0,a.jsx)("rect",{width:"40",height:"22",fill:"white",transform:"translate(40.9717 21.9766) rotate(177.357)"})})})]})}},29383:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.25 6.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM15.75 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM2.25 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM6.31 15.117A6.745 6.745 0 0 1 12 12a6.745 6.745 0 0 1 6.709 7.498.75.75 0 0 1-.372.568A12.696 12.696 0 0 1 12 21.75c-2.305 0-4.47-.612-6.337-1.684a.75.75 0 0 1-.372-.568 6.787 6.787 0 0 1 1.019-4.38Z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M5.082 14.254a8.287 8.287 0 0 0-1.308 5.135 9.687 9.687 0 0 1-1.764-.44l-.115-.04a.563.563 0 0 1-.373-.487l-.01-.121a3.75 3.75 0 0 1 3.57-4.047ZM20.226 19.389a8.287 8.287 0 0 0-1.308-5.135 3.75 3.75 0 0 1 3.57 4.047l-.01.121a.563.563 0 0 1-.373.486l-.115.04c-.567.2-1.156.349-1.764.441Z"})]})},33504:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",width:e,height:e,className:r,children:[(0,a.jsx)("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),(0,a.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})},34319:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z",clipRule:"evenodd"})})},35235:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:(0,a.jsx)("path",{d:"M2.25 2.25a.75.75 0 0 0 0 1.5h1.386c.17 0 .318.114.362.278l2.558 9.592a3.752 3.752 0 0 0-2.806 3.63c0 .414.336.75.75.75h15.75a.75.75 0 0 0 0-1.5H5.378A2.25 2.25 0 0 1 7.5 15h11.218a.75.75 0 0 0 .674-.421 60.358 60.358 0 0 0 2.96-*********** 0 0 0-.525-.965A60.864 60.864 0 0 0 5.68 4.509l-.232-.867A1.875 1.875 0 0 0 3.636 2.25H2.25ZM3.75 20.25a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM16.5 20.25a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"})})},39955:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);function s({size:e=24,className:r=""}){return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:r,children:(0,a.jsx)("polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"})})}t(60159)},49272:(e,r,t)=>{t.d(r,{Z:()=>s});var a=t(13486);t(60159);let s=({size:e="md",className:r=""})=>{let t={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"},s=t[e]||t.md;return(0,a.jsx)("div",{className:`inline-block ${s} ${r}`,children:(0,a.jsxs)("svg",{className:"animate-spin",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})}},50947:(e,r,t)=>{t.d(r,{QQ:()=>a,vc:()=>s});let a={AUTH:{BASE:"/Auth",LOGIN:"/Auth/login",FORGOT_PASSWORD:"/Auth/forgot_password",RESET_PASSWORD:"/Auth/reset_password",REGISTER_PARTNER:"/Auth/register/partner",LOGOUT:"/Auth/logout"},USER:{SELF:"/User/self",AVATAR:"/User/avatar",LIST:"/User",SEARCH:"/User",GET_BY_ID:"/User/{uuid}",ADMIN:{GET_BY_ID:"/User/Admin/{uuid}",UPDATE:"/User/Admin/{uuid}",CREATE:"/User/Admin"},CUSTOMER:{GET_BY_ID:"/User/Customer/{uuid}",UPDATE:"/User/Customer/{uuid}",CREATE:"/User/Customer"},DRIVER:{LIST:"/User/Driver",SELF:"/User/Driver/self",GET_BY_ID:"/User/Driver/{uuid}",UPDATE:"/User/Driver/{uuid}",CREATE:"/User/Driver"},OPERATOR:{GET_BY_ID:"/User/Operator/{uuid}",UPDATE:"/User/Operator/{uuid}",CREATE:"/User/Operator"},PARTNER:{GET_BY_ID:"/User/Partner/{uuid}",UPDATE:"/User/Partner/{uuid}",CREATE:"/User/Partner"},TERMINAL:{GET_BY_ID:"/User/Terminal/{uuid}",UPDATE:"/User/Terminal/{uuid}",CREATE:"/User/Terminal"}},CAR:{LIST:"/Car",MY:"/Car/my",CREATE:"/Car",UPDATE:"/Car/{uuid}",GET_BY_ID:"/Car/{uuid}",ASSIGN_DRIVER:"/Car/{uuid}/drivers/{driver_id}",UPDATE_DRIVER:"/Car/{uuid}/drivers/{driver_id}",REMOVE_DRIVER:"/Car/{uuid}/drivers/{driver_id}",MY_SET_ACTIVE:"/Car/my/{uuid}/set-active",MY_SET_INACTIVE:"/Car/my/set-inactive"},LOCATION:{LIST:"/Location",SEARCH:"/Location",CREATE:"/Location",UPDATE:"/Location/{uuid}",GET_BY_ID:"/Location/{uuid}",CURRENT_LOCATION:"/Location/CurrentLocation/self"},SERVICE:{LIST:"/Service",CREATE:"/Service",UPDATE:"/Service/{uuid}",DELETE:"/Service/{uuid}",GET_BY_ID:"/Service/{uuid}"},ORDER:{LIST:"/Order",MY_CREATOR:"/Order/my/creator",MY_PARTICIPANT:"/Order/my/participant/{order_id}",CREATE:"/Order",CREATE_SCHEDULED:"/Order/scheduled",CREATE_SCHEDULED_RIDE:"/Order/scheduled/{uuid}/ride",CREATE_INSTANT_BY_OPERATOR:"/Order/instant/by-operator",CREATE_INSTANT_BY_CUSTOMER:"/Order/instant/by-customer",UPDATE:"/Order/{uuid}",UPDATE_SCHEDULED:"/Order/scheduled/{uuid}",UPDATE_INSTANT:"/Order/instant/{uuid}",UPDATE_PASSENGERS:"/Order/{uuid}/passengers",GET_BY_ID:"/Order/{uuid}",GET_SCHEDULED_BY_ID:"/Order/scheduled/{uuid}",GET_INSTANT_BY_ID:"/Order/instant/{uuid}"},ROUTE:{LIST:"/Route",CREATE:"/Route",UPDATE:"/Route/{uuid}",GET_BY_ID:"/Route/{uuid}",DELETE:"/Route/{uuid}"},TARIFF:{LIST:"/Tariff",CREATE:"/Tariff",UPDATE:"/Tariff/{uuid}",GET_BY_ID:"/Tariff/{uuid}"},NOTIFICATION:{LIST:"/Notification",ME:"/Notification/me",CREATE:"/Notification",UPDATE:"/Notification/{uuid}",GET_BY_ID:"/Notification/{uuid}",READ:"/Notification/read"},GIS:{BASE:"/GIS",ACTIVE_DRIVERS:"/GIS/ActiveDrivers"},RIDE:{LIST:"/Ride",MY:"/Ride/my",MY_ASSIGNED:"/Ride/my/assigned",GET_BY_ID:"/Ride/{uuid}",UPDATE:"/Ride/{uuid}",DELETE:"/Ride/{uuid}",FOR_ORDER:"/Ride/for-order/{order_id}",ACCEPT:"/Ride/{uuid}/accept-by-driver",REJECT:"/Ride/{uuid}/reject-by-driver",NOTIFICATION_BY_DRIVER:"/Ride/{uuid}/notification-by-driver",DRIVER_HEADING:"/Ride/{uuid}/status/driver-heading-to-client",DRIVER_ARRIVED:"/Ride/{uuid}/status/driver-arrived",RIDE_STARTED:"/Ride/{uuid}/status/ride-started",RIDE_FINISHED:"/Ride/{uuid}/status/ride-finished",RIDE_CANCELLED:"/Ride/{uuid}/status/ride-cancelled"}},s={AUTH:{LOGIN:"/login",FORGOT_PASSWORD:"/forgot-password",RESET_PASSWORD:"/reset-password",REGISTER:"/register"},MAIN:{HOME:"/"},PROFILE:{SELF:"/profile"},TERMINAL:{MAIN:"/"},LOCATIONS:{TERMINAL_LOCATION:"/locations"},PAYMENT:{TERMINAL_PAYMENT:"/payment"}}},51249:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.5 3.75a3 3 0 00-3 3v10.5a3 3 0 003 3h15a3 3 0 003-3V6.75a3 3 0 00-3-3h-15zm4.125 3a2.25 2.25 0 100 4.5 2.25 2.25 0 000-4.5zm-3.873 8.703a4.126 4.126 0 017.746 0 .75.75 0 01-.351.92 7.47 7.47 0 01-3.522.877 7.47 7.47 0 01-3.522-.877.75.75 0 01-.351-.92zM15 8.25a.75.75 0 000 1.5h3.75a.75.75 0 000-1.5H15zM15 12a.75.75 0 000 1.5h3.75a.75.75 0 000-1.5H15zm0 3.75a.75.75 0 000 1.5h3.75a.75.75 0 000-1.5H15z",clipRule:"evenodd"})})},56870:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M11.078 2.25c-.917 0-1.699.663-1.85 1.567L9.05 4.889c-.02.12-.115.26-.297.348a7.493 7.493 0 0 0-.986.57c-.166.115-.334.126-.45.083L6.3 5.508a1.875 1.875 0 0 0-2.282.819l-.922 1.597a1.875 1.875 0 0 0 .432 2.385l.84.692c.**************.154.43a7.598 7.598 0 0 0 0 1.139c.015.2-.059.352-.153.43l-.841.692a1.875 1.875 0 0 0-.432 2.385l.922 1.597a1.875 1.875 0 0 0 2.282.818l1.019-.382c.115-.043.283-.031.45.082.312.214.641.405.985.57.182.088.277.228.297.35l.178 1.071c.151.904.933 1.567 1.85 1.567h1.844c.916 0 1.699-.663 1.85-1.567l.178-1.072c.02-.12.114-.26.297-.349.344-.165.673-.356.985-.57.167-.114.335-.125.45-.082l1.02.382a1.875 1.875 0 0 0 2.28-.819l.923-1.597a1.875 1.875 0 0 0-.432-2.385l-.84-.692c-.095-.078-.17-.229-.154-.43a7.614 7.614 0 0 0 0-1.139c-.016-.2.059-.352.153-.43l.84-.692c.708-.582.891-1.59.433-2.385l-.922-1.597a1.875 1.875 0 0 0-2.282-.818l-1.02.382c-.114.043-.282.031-.449-.083a7.49 7.49 0 0 0-.985-.57c-.183-.087-.277-.227-.297-.348l-.179-1.072a1.875 1.875 0 0 0-1.85-1.567h-1.843ZM12 15.75a3.75 3.75 0 1 0 0-7.5 3.75 3.75 0 0 0 0 7.5Z",clipRule:"evenodd"})})},64556:(e,r,t)=>{t.d(r,{y:()=>l});var a=t(13486),s=t(60159),i=t(89327);let l=({message:e,isTooltip:r=!1,position:t="auto"})=>{let[l,o]=(0,s.useState)("right"),d=(0,s.useRef)(null),n=(0,s.useRef)(null),u=(0,s.useCallback)(()=>{if("auto"!==t||!n.current)return void o("auto"===t?"right":t);let e=n.current.getBoundingClientRect(),r=window.innerWidth,a=window.innerHeight,s=e.top,i=a-e.bottom,l=e.left,d=Math.max(s,i,l,r-e.right);d===s?o("top"):d===i?o("bottom"):d===l?o("left"):o("right")},[t,n]);return((0,s.useEffect)(()=>{u();let e=()=>{u()};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[u]),e)?r?(0,a.jsx)("div",{className:"inline-flex items-center",children:(0,a.jsxs)("div",{className:"relative group",ref:n,onMouseEnter:()=>{u()},children:[(0,a.jsx)(i.ms,{size:18,className:"text-[color:var(--color-error)] cursor-help"}),(0,a.jsxs)("div",{ref:d,className:`absolute z-40 ${function(e){switch(e){case"top":return"bottom-full left-1/2 -translate-x-1/2 mb-2";case"right":default:return"left-full top-1/2 -translate-y-1/2 ml-2";case"bottom":return"top-full left-1/2 -translate-x-1/2 mt-2";case"left":return"right-full top-1/2 -translate-y-1/2 mr-2"}}(l)} invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-300 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-lg whitespace-normal w-[250px] max-w-[250px]`,children:[(0,a.jsx)("div",{className:"text-sm font-medium text-[color:var(--color-error)] break-words",children:e}),(0,a.jsx)("div",{className:`absolute ${function(e){switch(e){case"top":return"top-full left-1/2 -translate-x-1/2 -translate-y-1/2";case"right":default:return"right-full top-1/2 -translate-y-1/2 translate-x-1/2";case"bottom":return"bottom-full left-1/2 -translate-x-1/2 translate-y-1/2";case"left":return"left-full top-1/2 -translate-y-1/2 -translate-x-1/2"}}(l)} rotate-45 w-2 h-2 bg-white dark:bg-gray-800`})]})]})}):(0,a.jsxs)("div",{className:"flex items-start gap-2 mt-1 text-[color:var(--color-error)]",children:[(0,a.jsx)(i.ms,{size:16,className:"mt-0.5 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-sm",children:e})]}):null}},71015:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:(0,a.jsx)("path",{d:"M11.25 4.533A9.707 9.707 0 0 0 6 3a9.735 9.735 0 0 0-3.25.555.75.75 0 0 0-.5.707v14.25a.75.75 0 0 0 1 .707A8.237 8.237 0 0 1 6 18.75c1.995 0 3.823.707 5.25 1.886V4.533ZM12.75 20.636A8.214 8.214 0 0 1 18 18.75c.966 0 1.89.166 2.75.47a.75.75 0 0 0 1-.708V4.262a.75.75 0 0 0-.5-.707A9.735 9.735 0 0 0 18 3a9.707 9.707 0 0 0-5.25 1.533v16.103Z"})})},72244:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:e,height:e,className:r,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 19l-7-7 7-7m8 14l-7-7 7-7"})})},75111:(e,r,t)=>{t.d(r,{$P:()=>u,$Y:()=>v,Al:()=>h,Ay:()=>w,Vg:()=>n,mu:()=>c,r6:()=>i});var a=t(33713),s=t(93153),i=function(e){return e.Network="network",e.Auth="auth",e.Forbidden="forbidden",e.NotFound="not_found",e.Validation="validation",e.Server="server",e.Unknown="unknown",e}({});let l=e=>{if((0,a.F0)(e)){let r,t=e.response?.status,a="unknown",s=e.message;if(e.response?.data){let t=e.response.data;if(t.errors&&"object"==typeof t.errors&&(r=t.errors)&&Object.keys(r).length>0){let e=Object.keys(r)[0];e&&r[e]?.length>0&&(s=r[e][0])}t.title&&"string"==typeof t.title&&(s=t.title)}switch(t){case 401:a="auth",r||(s="Ошибка авторизации. Пожалуйста, войдите в систему.");break;case 403:a="forbidden",r||(s="Доступ запрещен. У вас нет прав для выполнения этого действия.");break;case 404:a="not_found",r||(s="Ресурс не найден.");break;case 400:case 422:a="validation",r||(s="Ошибка валидации данных.");break;case 500:case 502:case 503:case 504:a="server",r||(s="Ошибка сервера. Пожалуйста, попробуйте позже.");break;default:t?t>=400&&t<500?(a="validation",r||(s="Ошибка в запросе.")):t>=500&&(a="server",r||(s="Ошибка сервера. Пожалуйста, попробуйте позже.")):(a="network",r||(s="Ошибка сети. Пожалуйста, проверьте подключение к интернету."))}return{type:a,message:s,statusCode:t,data:e.response?.data,errors:r,originalError:e}}return e instanceof Error?{type:"unknown",message:e.message,originalError:e}:{type:"unknown",message:"Произошла неизвестная ошибка",originalError:e}},o=(e=>{let r={},t=s.A.create({baseURL:"https://api.testingkg.su",withCredentials:!0,headers:{"Content-Type":"application/json"},...void 0});return t.interceptors.request.use(e=>e,e=>Promise.reject(e)),t.interceptors.response.use(e=>e,e=>{let t=l(e);switch(r.onError&&r.onError(t),t.type){case"auth":r.onAuthError?.(t);break;case"forbidden":r.onForbiddenError?.(t);break;case"not_found":r.onNotFoundError?.(t);break;case"validation":r.onValidationError?.(t);break;case"server":r.onServerError?.(t);break;case"network":r.onNetworkError?.(t)}return Promise.reject(t)}),t})(),d=async e=>{try{return{data:await e()}}catch(e){if(e&&"object"==typeof e&&"type"in e)return{error:e};return{error:l(e)}}},n=async(e,r)=>d(async()=>(await o.get(e,r)).data),u=async(e,r,t)=>d(()=>o.post(e,r,t).then(e=>e.data)),c=async(e,r,t)=>d(()=>o.put(e,r,t).then(e=>e.data)),h=async(e,r)=>d(()=>o.delete(e,r).then(e=>e.data)),v=async(e,r,t)=>d(()=>o.patch(e,r,t).then(e=>e.data)),w={client:o,get:n,post:u,put:c,patch:v,delete:h}},77243:(e,r,t)=>{t.d(r,{v:()=>s});var a=t(75111);class s{async get(e,r){let t=this.buildUrl(e);return(0,a.Vg)(t,r)}async post(e,r,t){let s=this.buildUrl(e);return(0,a.$P)(s,r,t)}async put(e,r,t){let s=this.buildUrl(e);return(0,a.mu)(s,r,t)}async patch(e,r,t){let s=this.buildUrl(e);return(0,a.$Y)(s,r,t)}async delete(e,r){let t=this.buildUrl(e);return(0,a.Al)(t,r)}buildUrl(e){let r=e.startsWith("/")?e.slice(1):e,t=this.baseUrl.endsWith("/")?this.baseUrl.slice(0,-1):this.baseUrl;return`${t}/${r}`}handleApiResult(e){if(e.error)throw e.error;if(void 0===e.data)throw Error("API returned undefined data");return e.data}handleApiResultSafe(e){return e.error||void 0===e.data?null:e.data}}},84368:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.25 9a6.75 6.75 0 0 1 13.5 0v.75c0 2.123.8 4.057 2.118 5.52a.75.75 0 0 1-.297 1.206c-1.544.57-3.16.99-4.831 1.243a3.75 3.75 0 1 1-7.48 0 24.585 24.585 0 0 1-4.831-*********** 0 0 1-.298-1.205A8.217 8.217 0 0 0 5.25 9.75V9Zm4.502 8.9a2.25 2.25 0 1 0 4.496 0 25.057 25.057 0 0 1-4.496 0Z",clipRule:"evenodd"})})},88534:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsxs)("svg",{className:`animate-spin ${r}`,width:e,height:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})},89327:(e,r,t)=>{t.d(r,{ms:()=>l,bM:()=>s.default,aR:()=>i.default}),t(35235);var a=t(13486);t(60159),t(27766),t(24986),t(56870),t(21093),t(99338),t(84368),t(97376),t(29383),t(2262),t(71015),t(91325),t(72244);var s=t(33504),i=t(19263);t(51249),t(2065),t(34319);let l=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})});t(92089),t(13996),t(88534),t(39955)},91325:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:e,height:e,className:r,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 5l7 7-7 7M5 5l7 7-7 7"})})},92089:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:r,children:[(0,a.jsx)("polyline",{points:"3 6 5 6 21 6"}),(0,a.jsx)("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"})]})},97376:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(13486);t(60159);let s=({size:e=24,className:r=""})=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",width:e,height:e,className:r,children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18.685 19.097A9.723 9.723 0 0021.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 003.065 7.097A9.716 9.716 0 0012 21.75a9.716 9.716 0 006.685-2.653zm-12.54-1.285A7.486 7.486 0 0112 15a7.486 7.486 0 015.855 2.812A8.224 8.224 0 0112 20.25a8.224 8.224 0 01-5.855-2.438zM15.75 9a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z",clipRule:"evenodd"})})},99087:(e,r,t)=>{t.d(r,{w:()=>l});var a=t(13486),s=t(60159);let i=(0,s.lazy)(()=>Promise.resolve().then(t.bind(t,13996))),l=({message:e="Загрузка...",isVisible:r=!0})=>r?(0,a.jsxs)("div",{className:"z-50 absolute [top:inherit] w-full h-full flex flex-col items-center justify-center gap-4 /50 backdrop-filter backdrop-blur-md bg-black/80",children:[(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{className:"w-16 h-16"}),children:(0,a.jsx)(i,{size:64})}),e&&(0,a.jsx)("p",{className:"text-lg font-medium",children:e})]}):null}};