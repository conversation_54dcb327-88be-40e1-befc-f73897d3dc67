"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3822],{7401:(e,a,l)=>{l.d(a,{Ny:()=>t.e}),l(25671);var t=l(27414)},8426:(e,a,l)=>{l.d(a,{aN:()=>r,pl:()=>s,OW:()=>o,g0:()=>i,t0:()=>n,UH:()=>p});var t=l(7620);let r=(0,t.createContext)({driverId:null,driverProfile:null,activeRides:[],scheduledRides:[],scheduledRidesPagination:null,isInitialized:!1,isLoading:!0,isLoadingRides:!1,error:null,refreshDriverData:async()=>{},refreshRides:async()=>{}}),i=()=>{let e=(0,t.useContext)(r);if(!e)throw Error("useDriverData must be used within DriverDataProvider");return e},s=(0,t.createContext)({location:null,isTracking:!1,error:null,startTracking:()=>{},stopTracking:()=>{},getCurrentPosition:async()=>null}),n=()=>{let e=(0,t.useContext)(s);if(!e)throw Error("useDriverLocation must be used within DriverLocationProvider");return e},o=(0,t.createContext)(null),p=()=>{let e=(0,t.useContext)(o);if(!e)throw Error("useTerminalData должен использоваться внутри TerminalDataProvider");return e}},9205:(e,a,l)=>{l.d(a,{Dv:()=>t.Dv}),l(52795);var t=l(47510)},12506:(e,a,l)=>{l.d(a,{l:()=>c});var t=l(54568),r=l(27261),i=l.n(r),s=l(7620),n=l(11447),o=l(66251),p=l(24723),m=l(55115);let d=(0,s.lazy)(()=>l.e(1523).then(l.bind(l,11523))),c={columns:[{id:"fullName",label:"Имя",accessor:"fullName",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","endsWith","equals","notEquals"]}},{id:"email",label:"Email",accessor:"email",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]}},{id:"role",label:"Роль",accessor:"role",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,o.mJ)((0,m.nu)(),[p.Xh.Unknown])},renderCell:e=>(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-[color:var(--color-primary-50)] text-[color:var(--color-primary)]",children:(0,m.r4)(e.role)})},{id:"phoneNumber",label:"Телефон",accessor:"phoneNumber",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","isEmpty","isNotEmpty"]},renderCell:e=>(0,t.jsx)("span",{children:e.phoneNumber||(0,t.jsx)("span",{className:"text-[color:var(--color-neutral-400)]",children:"Не указан"})})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,t.jsx)("div",{className:"flex justify-end gap-2",children:(0,t.jsx)(i(),{href:"/users/".concat(e.id),className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",onClick:()=>{(0,n.$A)(e.role)},children:(0,t.jsx)(d,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"FullName",label:"Имя",operator:"contains",operatorField:"FullNameOp"},{field:"Email",label:"Email",operator:"contains",operatorField:"EmailOp"},{field:"PhoneNumber",label:"Телефон",operator:"contains",operatorField:"PhoneNumberOp"}]},filtersConfig:{availableFilters:[{field:"Email",label:"Email",type:"text"},{field:"FullName",label:"Полное имя",type:"text"},{field:"PhoneNumber",label:"Телефон",type:"text"},{field:"Role",label:"Роль",type:"select",options:(0,o.mJ)((0,m.nu)(),[p.Xh.Unknown]).map(e=>({value:e.value,label:e.label}))},{field:"Online",label:"Онлайн",type:"boolean"}]}}},25671:(e,a,l)=>{l(27736)},27414:(e,a,l)=>{l.d(a,{e:()=>n});var t=l(27736),r=l(21715),i=l(80745);class s extends t.vA{async getLocation(e){let a=await this.get("/".concat(e));return this.handleApiResult(a)}async getLocationSafe(e){let a=await this.get("/".concat(e));return this.handleApiResultSafe(a)}async getById(e){return this.getLocation(e)}async getAll(e){return this.getLocations(e)}async getPopularLocations(e){let a={size:100,popular1:!0,isActive:!0};return(null==e?void 0:e.trim())&&(a.ftsPlain=e.trim(),delete a.popular1),this.getLocations(a)}async getLocations(e){let a=new URLSearchParams;(null==e?void 0:e.first)!==void 0&&a.append("first",e.first.toString()),(null==e?void 0:e.before)&&a.append("before",e.before),(null==e?void 0:e.after)&&a.append("after",e.after),(null==e?void 0:e.last)!==void 0&&a.append("last",e.last.toString()),(null==e?void 0:e.size)!==void 0&&a.append("size",e.size.toString()),(null==e?void 0:e.popular1)!==void 0&&a.append("Popular1",e.popular1.toString()),(null==e?void 0:e.popular2)!==void 0&&a.append("Popular2",e.popular2.toString()),(null==e?void 0:e.isActive)!==void 0&&a.append("IsActive",e.isActive.toString()),(null==e?void 0:e.ftsPlain)&&a.append("FTS.Plain",e.ftsPlain),(null==e?void 0:e.type)&&(Array.isArray(e.type)?e.type.forEach(e=>a.append("Type",e)):a.append("Type",e.type)),(null==e?void 0:e.city)&&(Array.isArray(e.city)?e.city.forEach(e=>a.append("City",e)):a.append("City",e.city),a.append("CityOp",e.cityOp||"Equals")),(null==e?void 0:e.address)&&(Array.isArray(e.address)?e.address.forEach(e=>a.append("Address",e)):a.append("Address",e.address),a.append("AddressOp",e.addressOp||"StartsWith")),(null==e?void 0:e.district)&&(Array.isArray(e.district)?e.district.forEach(e=>a.append("District",e)):a.append("District",e.district),a.append("DistrictOp",e.districtOp||"Equals")),(null==e?void 0:e.region)&&(Array.isArray(e.region)?e.region.forEach(e=>a.append("Region",e)):a.append("Region",e.region),a.append("RegionOp",e.regionOp||"Equals"));let l=a.toString()?"?".concat(a.toString()):"",t=await this.get(l);return this.handleApiResult(t)}async createLocation(e){let a=await this.post("",e);return this.handleApiResult(a)}async updateLocation(e,a){let l=await this.put("/".concat(e),a);return this.handleApiResult(l)}async deleteLocation(e){let a=await this.delete("/".concat(e));this.handleApiResult(a)}async updateCurrentLocation(e){console.log("\uD83D\uDCE1 Отправка координат водителя на сервер:",e);let a=await (0,r.$P)("/Location/CurrentLocation/self",{latitude:e.latitude,longitude:e.longitude});this.handleApiResult(a),console.log("✅ Координаты водителя успешно отправлены на сервер")}async getActiveDrivers(e){let a=await this.get("/GIS/ActiveDrivers",{params:{LatFrom:e.latFrom,LatTo:e.latTo,LongFrom:e.longFrom,LongTo:e.longTo}});return this.handleApiResult(a)}constructor(...e){super(...e),this.baseUrl=i.QQ.LOCATION.LIST}}let n=new s},33822:(e,a,l)=>{l.d(a,{g9:()=>i,hF:()=>r,Ht:()=>s,bU:()=>d,FS:()=>c,nb:()=>g,Z8:()=>h});var t=l(64942);let r={title:{create:"Создание нового пользователя",edit:"Редактирование пользователя"},groups:[{id:"main",title:"Основная информация",order:1,layout:{gridCols:2,gapX:8,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"photoGroup",type:t.V.GROUP,label:"Фото профиля",description:"Загрузите фотографию пользователя",layout:{flexDirection:"column",gapY:4,itemsAlignment:"center",className:"flex-1 flex flex-col justify-between gap-4"},fields:[{name:"avatarUrl",type:t.V.IMAGE,helperSmallText:"JPG, PNG до 5 МБ",helperBigText:"Загрузите фотографию пользователя в формате JPG или PNG. Максимальный размер файла - 5 МБ. Рекомендуемое соотношение сторон 1:1.",className:"w-full h-full"}]},{name:"contactGroup",type:t.V.GROUP,label:"Контактная информация",description:"Основные контактные данные пользователя для связи и идентификации в системе",layout:{flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"email",label:"Email",type:t.V.EMAIL,placeholder:"Введите email пользователя",required:!0,helperSmallText:"Email для входа",helperBigText:"Будет использоваться для входа в систему и восстановления пароля. Должен быть уникальным."},{name:"fullName",label:"Полное имя",type:t.V.TEXT,placeholder:"Введите полное имя пользователя",required:!0,helperSmallText:"ФИО пользователя",helperBigText:"Полное имя пользователя, которое будет отображаться в системе"},{name:"phoneNumber",label:"Телефон",type:t.V.PHONE,placeholder:"Введите номер телефона",required:!1,helperSmallText:"Телефон для связи",helperBigText:"Контактный телефон для связи с пользователем. Используется для уведомлений и подтверждений."}]}]},{id:"security",title:"Безопасность",order:2,description:"Настройки безопасности и доступа администратора",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"center"},fields:[{name:"password",label:"Пароль",type:t.V.PASSWORD,placeholder:"Введите пароль",required:!0,helperSmallText:"Минимум 8 символов",helperBigText:"Требования к паролю:\n- Минимум 8 символов\n- Хотя бы одна заглавная буква\n- Хотя бы одна строчная буква\n- Хотя бы одна цифра\n- Хотя бы один специальный символ\n\nПароль должен содержать не менее 8 символов, включая заглавные и строчные буквы, цифры и специальные символы для обеспечения безопасности."},{name:"confirmPassword",label:"Подтверждение пароля",type:t.V.PASSWORD,placeholder:"Повторите пароль",required:!0,isServiceField:!0,helperSmallText:"Повторите пароль",helperBigText:"Введите пароль повторно для подтверждения. Это поле используется только для проверки правильности ввода и не отправляется на сервер."}]}]},i={...r,title:{create:"Создание нового администратора",edit:"Редактирование администратора"},description:{create:"Заполните информацию для создания нового администратора системы",edit:"Редактирование информации администратора системы"},groups:[{...r.groups[0],description:"Основные данные и контактная информация администратора",fields:[...r.groups[0].fields]},{...r.groups[1],description:"Настройки безопасности и доступа администратора",fields:[...r.groups[1].fields,{name:"profile.accessLevel",label:"Уровень доступа",type:t.V.SELECT,placeholder:"Выберите уровень доступа",required:!0,options:[{value:"full",label:"Полный доступ"},{value:"limited",label:"Ограниченный доступ"},{value:"readonly",label:"Только чтение"}],helperSmallText:"Права доступа",helperBigText:"Определяет уровень прав и доступных функций администратора в системе. От этого зависит, какие действия может выполнять пользователь.\n\nДоступные варианты:\n- Полный доступ: все функции системы без ограничений\n- Ограниченный доступ: базовые функции администрирования\n- Только чтение: просмотр данных без возможности изменения",className:"flex-1"}]},{id:"employment",title:"Информация о работе",order:3,description:"Данные о должности и трудоустройстве",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"profile.department",label:"Отдел",type:t.V.TEXT,placeholder:"Введите отдел",helperSmallText:"Отдел",helperBigText:"Отдел, в котором работает администратор"},{name:"profile.position",label:"Должность",type:t.V.TEXT,placeholder:"Введите должность",helperSmallText:"Должность",helperBigText:"Должность администратора в компании"},{name:"profile.hireDate",label:"Дата найма",type:t.V.DATE,helperSmallText:"Дата найма",helperBigText:"Дата начала работы администратора в компании"},{name:"profile.employeeId",label:"Табельный номер",type:t.V.TEXT,placeholder:"Введите табельный номер",helperSmallText:"Табельный номер",helperBigText:"Уникальный идентификатор сотрудника в системе"}]}]},s={...r,title:{create:"Создание нового клиента",edit:"Редактирование клиента"},description:{create:"Заполните информацию для создания нового клиента в системе",edit:"Редактирование информации клиента в системе"},groups:[{...r.groups[0],description:"Основные данные и контактная информация клиента",fields:[...r.groups[0].fields]},{...r.groups[1],description:"Настройки безопасности и доступа клиента",fields:[...r.groups[1].fields]},{id:"customerProfile",title:"Профиль клиента",order:3,description:"Дополнительная информация и настройки клиента",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"loyaltyPoints",label:"Баллы лояльности",type:t.V.NUMBER,placeholder:"Введите количество баллов",min:0,helperSmallText:"Баллы",helperBigText:"Количество накопленных баллов лояльности клиента"},{name:"phantom",label:"Фантомный аккаунт",type:t.V.CHECKBOX,helperSmallText:"Фантомный",helperBigText:"Отметьте, если это фантомный аккаунт для тестирования"}]}]};var n=l(27736),o=l(37556),p=l(43330),m=l(55115);let d={...r,title:{create:"Создание нового водителя",edit:"Редактирование водителя"},description:{create:"Заполните информацию для создания нового водителя в системе",edit:"Редактирование информации водителя в системе"},groups:[{...r.groups[0],description:"Основные данные и контактная информация водителя",fields:[...r.groups[0].fields]},{...r.groups[1],description:"Настройки безопасности и доступа водителя",fields:[...r.groups[1].fields]},{id:"driverBasic",title:"Основная информация водителя",order:3,description:"Статус и основные параметры водителя",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"verificationStatus",label:"Статус верификации",type:t.V.SELECT,placeholder:"Выберите статус верификации",required:!0,options:[{value:"Pending",label:"Ожидает проверки"},{value:"Verified",label:"Проверен и подтвержден"},{value:"Rejected",label:"Отклонен"},{value:"InReview",label:"На рассмотрении"},{value:"Expired",label:"Истек срок действия"}],helperSmallText:"Статус проверки",helperBigText:"Текущий статус проверки документов и данных водителя"}]},{id:"driverPassport",title:"Паспортные данные",order:4,description:"Данные документа, удостоверяющего личность водителя",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"passportNumberGroup",type:t.V.GROUP,label:"Серия и номер",description:"Тип документа, серия, номер и кем выдан документ, удостоверяющий личность",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.passport.identityType",label:"Тип документа",type:t.V.SELECT,placeholder:"Выберите тип документа",required:!0,options:[{value:"NationalPassport",label:"Внутренний паспорт"},{value:"InternationalPassport",label:"Загранпаспорт"},{value:"IdCard",label:"Идентификационная карта"},{value:"ResidencePermit",label:"Вид на жительство"},{value:"RefugeeId",label:"Удостоверение беженца"},{value:"TemporaryId",label:"Временное удостоверение личности"},{value:"MilitaryId",label:"Военный билет"},{value:"ForeignPassport",label:"Иностранный паспорт"},{value:"DriversLicense",label:"Водительское удостоверение"}],helperSmallText:"Тип документа",helperBigText:"Тип документа, удостоверяющего личность водителя"},{name:"profile.passport.series",label:"Серия паспорта",type:t.V.TEXT,placeholder:"Введите серию паспорта",helperSmallText:"Серия документа",helperBigText:"Серия документа, удостоверяющего личность",pattern:"^\\d{4}$",helperText:"Серия паспорта должна состоять из 4 цифр"},{name:"profile.passport.number",label:"Номер паспорта",type:t.V.TEXT,placeholder:"Введите номер паспорта",required:!0,helperSmallText:"Номер документа",helperBigText:"Номер документа, удостоверяющего личность",pattern:"^\\d{6}$",helperText:"Номер паспорта должен состоять из 6 цифр"},{name:"profile.passport.issuedBy",label:"Кем выдан паспорт",type:t.V.TEXT,placeholder:"Введите кем выдан паспорт",required:!0,helperSmallText:"Кем выдан",helperBigText:"Орган, выдавший документ, удостоверяющий личность"}]},{name:"passportDatesGroup",type:t.V.GROUP,label:"Даты и выдача",description:"Даты выдачи и окончания срока действия документа",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.passport.issueDate",label:"Дата выдачи паспорта",type:t.V.DATE,required:!0,helperSmallText:"Дата выдачи",helperBigText:"Дата выдачи документа, удостоверяющего личность"},{name:"profile.passport.expiryDate",label:"Дата окончания срока действия",type:t.V.DATE,helperSmallText:"Срок действия",helperBigText:"Дата окончания срока действия документа (если применимо)"},{name:"profile.passport.page",label:"Страница паспорта",type:t.V.NUMBER,placeholder:"Введите страницу паспорта",helperSmallText:"Страница",helperBigText:"Страница паспорта (если применимо)",min:1}]}]},{id:"driverLicense",title:"Водительское удостоверение",order:5,description:"Данные водительского удостоверения",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"licenseNumberGroup",type:t.V.GROUP,label:"Номер удостоверения",description:"Номер водительского удостоверения и категории прав",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.licenseNumber",label:"Номер водительского удостоверения",type:t.V.TEXT,placeholder:"Введите номер ВУ",required:!0,helperSmallText:"Номер ВУ",helperBigText:"Номер водительского удостоверения"},{name:"profile.licenseCategories",label:"Категории прав",type:t.V.MULTISELECT,placeholder:"Выберите категории прав",required:!0,options:[{value:"A",label:"A - Мотоциклы"},{value:"B",label:"B - Легковые автомобили"},{value:"C",label:"C - Грузовые автомобили"},{value:"D",label:"D - Автобусы"},{value:"E",label:"E - Прицепы"},{value:"M",label:"M - Мопеды"},{value:"Tb",label:"Tb - Троллейбусы"},{value:"Tm",label:"Tm - Трамваи"}],helperSmallText:"Категории ВУ",helperBigText:"Категории транспортных средств в водительском удостоверении"}]},{name:"licenseDatesGroup",type:t.V.GROUP,label:"Даты выдачи и окончания",description:"Даты выдачи и окончания срока действия водительского удостоверения",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.licenseIssueDate",label:"Дата выдачи прав",type:t.V.DATE,required:!0,helperSmallText:"Дата выдачи",helperBigText:"Дата выдачи водительского удостоверения"},{name:"profile.licenseExpiryDate",label:"Дата окончания прав",type:t.V.DATE,required:!0,helperSmallText:"Срок действия",helperBigText:"Дата окончания срока действия водительского удостоверения"}]}]},{id:"driverPersonalInfo",title:"Личная информация",order:6,description:"Личные данные водителя",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"birthInfoGroup",type:t.V.GROUP,label:"Данные о рождении",description:"Дата и место рождения водителя",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.dateOfBirth",label:"Дата рождения",type:t.V.DATE,required:!0,helperSmallText:"Дата рождения",helperBigText:"Дата рождения водителя"},{name:"profile.birthPlace",label:"Место рождения",type:t.V.TEXT,placeholder:"Введите место рождения",helperSmallText:"Место рождения",helperBigText:"Место рождения водителя (если применимо)"}]},{name:"citizenshipGroup",type:t.V.GROUP,label:"Гражданство",description:"Гражданство и страна гражданства водителя",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.citizenship",label:"Гражданство",type:t.V.SELECT,placeholder:"Выберите гражданство",required:!0,options:(0,m.H)(),helperSmallText:"Гражданство",helperBigText:"Гражданство водителя"},{name:"profile.citizenshipCountry",label:"Страна гражданства",type:t.V.SELECT,placeholder:"Выберите страну гражданства",required:!0,options:(0,m.H)(),helperSmallText:"Страна гражданства",helperBigText:"Страна гражданства водителя"}]}]},{id:"driverAdditionalInfo",title:"Дополнительная информация",order:7,description:"Опыт вождения и налоговые данные водителя",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"profile.drivingExperience",label:"Опыт вождения (лет)",type:t.V.NUMBER,placeholder:"Введите опыт вождения",min:0,helperSmallText:"Опыт вождения",helperBigText:"Опыт вождения в годах"},{name:"profile.taxIdentifier",label:"ИНН",type:t.V.TEXT,placeholder:"Введите ИНН",helperSmallText:"ИНН",helperBigText:"Идентификационный номер налогоплательщика (если применимо)"}]},{id:"driverCar",title:"Назначение автомобиля",order:8,description:"Назначение автомобиля для водителя и управление его активностью",layout:{gridCols:1,gapX:4,gapY:4,className:"h-full",flexDirection:"column",maxItemsPerRow:1,itemsAlignment:"stretch"},fields:[]},{id:"driverPreferences",title:"Предпочтения и языки",order:9,description:"Языки и предпочтения водителя",layout:{gridCols:3,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"languagesGroup",type:t.V.GROUP,label:"Языки общения",description:"Языки, на которых может общаться водитель с пассажирами",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.languages",label:"Языки",type:t.V.MULTISELECT,placeholder:"Выберите языки",required:!0,options:(0,m.DL)(),helperSmallText:"Языки общения",helperBigText:"Языки, на которых может общаться водитель",showNavigateButton:!1,multiselectDataType:"static"}]},{name:"rideTypesGroup",type:t.V.GROUP,label:"Классы обслуживания",description:"Предпочитаемые классы обслуживания для работы водителя",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.preferredRideTypes",label:"Предпочитаемые классы обслуживания",type:t.V.MULTISELECT,placeholder:"Выберите классы обслуживания",required:!0,options:(0,m.qd)(),helperSmallText:"Классы обслуживания",helperBigText:"Предпочитаемые классы обслуживания для водителя",showNavigateButton:!1,multiselectDataType:"static"}]},{name:"workZonesGroup",type:t.V.GROUP,label:"Зоны работы",description:"Предпочитаемые зоны работы водителя в городе",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.preferredWorkZones",label:"Предпочитаемые зоны работы",type:t.V.MULTISELECT,placeholder:"Выберите зоны работы",helperSmallText:"Зоны работы",helperBigText:"Предпочитаемые зоны работы водителя (необязательно)",dynamicReference:{api:"get",endpoint:n.QQ.LOCATION.LIST},showNavigateButton:!0,multiselectDataType:"dynamic",colorFieldName:"type",getColorFromValue:p.b,getDescriptionFromValue:e=>(0,o.al)(e)}]}]}]},c={...r,title:{create:"Создание нового оператора",edit:"Редактирование оператора"},description:{create:"Заполните информацию для создания нового оператора в системе",edit:"Редактирование информации оператора в системе"},groups:[{...r.groups[0],description:"Основные данные и контактная информация оператора",fields:[...r.groups[0].fields]},{...r.groups[1],description:"Настройки безопасности и доступа оператора",fields:[...r.groups[1].fields]},{id:"operatorProfile",title:"Профиль оператора",order:3,description:"Информация о работе и статусе оператора",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"isActive",label:"Активен",type:t.V.CHECKBOX,helperSmallText:"Статус",helperBigText:"Отметьте, если оператор активен и может принимать заказы"},{name:"profile.employeeId",label:"Табельный номер",type:t.V.TEXT,placeholder:"Введите табельный номер",required:!0,helperSmallText:"Табельный номер",helperBigText:"Уникальный идентификатор сотрудника в системе"},{name:"profile.department",label:"Отдел",type:t.V.TEXT,placeholder:"Введите отдел",required:!0,helperSmallText:"Отдел",helperBigText:"Отдел, в котором работает оператор"},{name:"profile.position",label:"Должность",type:t.V.TEXT,placeholder:"Введите должность",required:!0,helperSmallText:"Должность",helperBigText:"Должность оператора в компании"},{name:"profile.hireDate",label:"Дата найма",type:t.V.DATE,required:!0,helperSmallText:"Дата найма",helperBigText:"Дата начала работы оператора в компании"}]}]};var u=l(66478);let g={...r,title:{create:"Создание нового партнера",edit:"Редактирование партнера"},description:{create:"Заполните информацию для создания нового партнера в системе",edit:"Редактирование информации партнера в системе"},groups:[{...r.groups[0],description:"Основные данные и контактная информация партнера",fields:[...r.groups[0].fields||[]]},{...r.groups[1],description:"Настройки безопасности и доступа партнера",fields:[...r.groups[1].fields||[]]},{id:"companyInfo",title:"Информация о компании",order:3,description:"Основная информация о компании-партнере",layout:{gridCols:3,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:3,itemsAlignment:"stretch"},fields:[{name:"profile.companyName",label:"Название компании",type:t.V.TEXT,placeholder:"Введите название компании",required:!0,helperSmallText:"Название",helperBigText:"Полное официальное название компании-партнера",className:"flex-1 col-span-2"},{name:"verificationStatus",label:"Статус верификации",type:t.V.SELECT,placeholder:"Выберите статус верификации",required:!0,options:[{value:"Pending",label:"Ожидает проверки"},{value:"Verified",label:"Проверен и подтвержден"},{value:"Rejected",label:"Отклонен"},{value:"InReview",label:"На рассмотрении"},{value:"Expired",label:"Истек срок действия"}],helperSmallText:"Статус проверки",helperBigText:"Текущий статус проверки документов и данных партнера",className:"flex-1"},{name:"profile.companyType",label:"Тип компании",type:t.V.SELECT,placeholder:"Выберите тип компании",required:!0,options:[{value:"LLC",label:"ООО"},{value:"JSC",label:"АО"},{value:"IE",label:"ИП"},{value:"SE",label:"Самозанятый"},{value:"PJSC",label:"ПАО"},{value:"NGO",label:"НКО"},{value:"GP",label:"ГУП"},{value:"Foreign",label:"Иностранная компания"}],helperSmallText:"Организационно-правовая форма",helperBigText:"Организационно-правовая форма компании-партнера",className:"flex-1"},{name:"profile.registrationNumber",label:"Регистрационный номер",type:t.V.TEXT,placeholder:"Введите регистрационный номер",helperSmallText:"ОГРН/ОГРНИП",helperBigText:"Основной государственный регистрационный номер компании",className:"flex-1"},{name:"profile.taxIdentifier",label:"ИНН",type:t.V.TEXT,placeholder:"Введите ИНН",helperSmallText:"ИНН",helperBigText:"Идентификационный номер налогоплательщика",pattern:"^\\d{10}|\\d{12}$",helperText:"ИНН должен содержать 10 цифр для юр. лиц или 12 цифр для ИП и физ. лиц",className:"flex-1"},{name:"profile.legalAddress",label:"Юридический адрес",type:t.V.TEXT,placeholder:"Введите юридический адрес",required:!0,helperSmallText:"Юр. адрес",helperBigText:"Официальный юридический адрес компании-партнера",className:"flex-1 col-span-3"}]},{id:"contactInfo",title:"Контактная информация",order:4,description:"Контактные данные компании-партнера",layout:{gridCols:3,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:3,itemsAlignment:"stretch"},fields:[{name:"profile.contactEmail",label:"Контактный email",type:t.V.EMAIL,placeholder:"Введите контактный email",helperSmallText:"Email",helperBigText:"Основной email для связи с компанией-партнером",className:"flex-1"},{name:"profile.contactPhone",label:"Контактный телефон",type:t.V.PHONE,placeholder:"Введите контактный телефон",helperSmallText:"Телефон",helperBigText:"Основной телефон для связи с компанией-партнером",className:"flex-1"},{name:"profile.website",label:"Веб-сайт",type:t.V.TEXT,placeholder:"Введите веб-сайт",helperSmallText:"Сайт",helperBigText:"Официальный веб-сайт компании-партнера",className:"flex-1"}]},{id:"partnerRoutes",title:"Маршруты партнера",order:5,description:"Управление локациями, доступными для данного партнера",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"column",maxItemsPerRow:1,itemsAlignment:"stretch"},customComponent:u.rq,fields:[{name:"partnerRoutes",type:t.V.HIDDEN}]}],writeOperations:[{api:"put",endpoint:"/User/Partner/self/routes",method:"update",condition:"always",bodyData:{routes:"partnerRoutes"},description:"Обновление привязанных маршрутов партнера",order:1}]},h={...r,title:{create:"Создание нового терминала",edit:"Редактирование терминала"},description:{create:"Заполните информацию для создания нового терминала в системе",edit:"Редактирование информации терминала в системе"},groups:[{...r.groups[0],description:"Основные данные и контактная информация терминала",fields:[...r.groups[0].fields]},{...r.groups[1],description:"Настройки безопасности и доступа терминала",fields:[...r.groups[1].fields]},{id:"terminalProfile",title:"Профиль терминала",order:3,description:"Техническая информация и настройки терминала",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"status",label:"Статус",type:t.V.SELECT,placeholder:"Выберите статус терминала",required:!0,options:[{value:"Active",label:"Активен"},{value:"Inactive",label:"Неактивен"},{value:"Maintenance",label:"На обслуживании"},{value:"Offline",label:"Офлайн"},{value:"Error",label:"Ошибка"}],helperSmallText:"Статус терминала",helperBigText:"Текущий статус работы терминала в системе"},{name:"locationId",label:"Местоположение",type:t.V.SELECT,placeholder:"Выберите местоположение",required:!0,dynamicReference:{api:"get",endpoint:n.QQ.LOCATION.LIST,filters:[{field:"status",operator:"eq",value:"Active"}],sort:[{field:"name",direction:"asc"}],limit:50},helperSmallText:"Местоположение",helperBigText:"Местоположение, где установлен терминал"},{name:"profile.terminalId",label:"Идентификатор терминала",type:t.V.TEXT,placeholder:"Введите идентификатор терминала",required:!0,helperSmallText:"ID терминала",helperBigText:"Уникальный идентификатор терминала в системе"},{name:"profile.ipAddress",label:"IP адрес",type:t.V.TEXT,placeholder:"Введите IP адрес",helperSmallText:"IP адрес",helperBigText:"IP адрес терминала в сети"},{name:"profile.deviceModel",label:"Модель устройства",type:t.V.TEXT,placeholder:"Введите модель устройства",helperSmallText:"Модель",helperBigText:"Модель устройства терминала"},{name:"profile.osVersion",label:"Версия ОС",type:t.V.TEXT,placeholder:"Введите версию ОС",helperSmallText:"ОС",helperBigText:"Версия операционной системы терминала"},{name:"profile.appVersion",label:"Версия приложения",type:t.V.TEXT,placeholder:"Введите версию приложения",helperSmallText:"Приложение",helperBigText:"Версия установленного приложения на терминале"},{name:"profile.browserInfo",label:"Информация о браузере",type:t.V.TEXT,placeholder:"Введите информацию о браузере",helperSmallText:"Браузер",helperBigText:"Информация о браузере, используемом терминалом"},{name:"profile.screenResolution",label:"Разрешение экрана",type:t.V.TEXT,placeholder:"Введите разрешение экрана",helperSmallText:"Разрешение",helperBigText:"Разрешение экрана терминала (например, 1920x1080)"},{name:"profile.deviceIdentifier",label:"Идентификатор устройства",type:t.V.TEXT,placeholder:"Введите идентификатор устройства",helperSmallText:"Device ID",helperBigText:"Уникальный идентификатор устройства терминала"}]}]}},37556:(e,a,l)=>{l.d(a,{_n:()=>t._n,al:()=>t.al}),l(43330);var t=l(41564)},40529:(e,a,l)=>{l.d(a,{Z:()=>r});var t=l(8426);let r=()=>{let{driverProfile:e,isInitialized:a,isLoading:l,error:r,refreshDriverData:i}=(0,t.g0)();return{profile:e,isLoading:l,isInitialized:a,error:r,refreshProfile:i}}},41564:(e,a,l)=>{l.d(a,{_n:()=>i,al:()=>r});var t=l(55915);function r(e){switch(e){case t.i.Home:return"Дом";case t.i.Work:return"Работа";case t.i.Airport:return"Аэропорт";case t.i.Station:return"Вокзал";case t.i.Hotel:return"Отель";case t.i.Restaurant:return"Ресторан";case t.i.Shop:return"Магазин";case t.i.Entertainment:return"Развлекательное заведение";case t.i.Medical:return"Медицинское учреждение";case t.i.Educational:return"Образовательное учреждение";case t.i.BusinessCenter:return"Бизнес-центр";case t.i.Other:return"Другое";default:return""}}function i(){return Object.values(t.i).map(e=>({value:e,label:r(e)}))}},43330:(e,a,l)=>{l.d(a,{b:()=>r});var t=l(46317);function r(e){return({[t.i.Home]:"blue",[t.i.Work]:"green",[t.i.Airport]:"purple",[t.i.Station]:"orange",[t.i.Hotel]:"red",[t.i.Restaurant]:"indigo",[t.i.Shop]:"blue",[t.i.Entertainment]:"green",[t.i.Medical]:"red",[t.i.Educational]:"orange",[t.i.BusinessCenter]:"purple",[t.i.Other]:"gray"})[e]||"blue"}},46317:(e,a,l)=>{l.d(a,{i:()=>t});var t=function(e){return e.Home="Home",e.Work="Work",e.Airport="Airport",e.Station="Station",e.Hotel="Hotel",e.Restaurant="Restaurant",e.Shop="Shop",e.Entertainment="Entertainment",e.Medical="Medical",e.Educational="Educational",e.BusinessCenter="BusinessCenter",e.Other="Other",e}({})},47510:(e,a,l)=>{l.d(a,{Dv:()=>s});var t=l(27736),r=l(80745);class i extends t.vA{async getUser(e){let a=await this.get("/".concat(e));return this.handleApiResult(a)}async getUserSafe(e){let a=await this.get("/".concat(e));return this.handleApiResultSafe(a)}async getUsers(e){let a=new URLSearchParams;(null==e?void 0:e.first)!==void 0&&a.append("first",e.first.toString()),(null==e?void 0:e.before)&&a.append("before",e.before),(null==e?void 0:e.after)&&a.append("after",e.after),(null==e?void 0:e.last)!==void 0&&a.append("last",e.last.toString()),(null==e?void 0:e.size)!==void 0&&a.append("size",e.size.toString()),(null==e?void 0:e.search)&&a.append("search",e.search),(null==e?void 0:e.role)&&a.append("role",e.role);let l=a.toString()?"?".concat(a.toString()):"",t=await this.get(l);return this.handleApiResult(t)}async createDriver(e){let a=await this.post("/drivers",e);return this.handleApiResult(a)}async createCustomer(e){let a=await this.post("/customers",e);return this.handleApiResult(a)}async createAdmin(e){let a=await this.post("/admins",e);return this.handleApiResult(a)}async createOperator(e){let a=await this.post("/operators",e);return this.handleApiResult(a)}async createPartner(e){let a=await this.post("/partners",e);return this.handleApiResult(a)}async createTerminal(e){let a=await this.post("/terminals",e);return this.handleApiResult(a)}async updateDriver(e,a){let l=await this.put("/drivers/".concat(e),a);return this.handleApiResult(l)}async updateCustomer(e,a){let l=await this.put("/customers/".concat(e),a);return this.handleApiResult(l)}async updateAdmin(e,a){let l=await this.put("/admins/".concat(e),a);return this.handleApiResult(l)}async updateOperator(e,a){let l=await this.put("/operators/".concat(e),a);return this.handleApiResult(l)}async updatePartner(e,a){let l=await this.put("/partners/".concat(e),a);return this.handleApiResult(l)}async updateTerminal(e,a){let l=await this.put("/terminals/".concat(e),a);return this.handleApiResult(l)}async deleteUser(e){let a=await this.delete("/".concat(e));this.handleApiResult(a)}async getCurrentDriver(){let e=await this.get("Driver/self");return this.handleApiResult(e)}async getSelfProfile(){let e=await this.get("self");return this.handleApiResult(e)}async getTerminalSelfProfile(){let e=await this.get("self/profile");return this.handleApiResult(e)}async getDrivers(e){let a=new URLSearchParams;(null==e?void 0:e.first)!==void 0&&a.append("first",e.first.toString()),(null==e?void 0:e.before)&&a.append("before",e.before),(null==e?void 0:e.after)&&a.append("after",e.after),(null==e?void 0:e.last)!==void 0&&a.append("last",e.last.toString()),(null==e?void 0:e.size)!==void 0&&a.append("size",e.size.toString()),(null==e?void 0:e.search)&&a.append("search",e.search),(null==e?void 0:e.online)!==void 0&&a.append("online",e.online.toString());let l=a.toString()?"Driver?".concat(a.toString()):"Driver",t=await this.get(l);return this.handleApiResult(t).data}async getDriversSafe(){let e=await this.get("Driver");return this.handleApiResultSafe(e)}async getDriverById(e){let a=await this.get("Driver/".concat(e));return this.handleApiResult(a)}async getDriverByIdSafe(e){let a=await this.get("Driver/".concat(e));return this.handleApiResultSafe(a)}async getUserByRole(e,a){let l;switch(a.toLowerCase()){case"admin":l="Admin/".concat(e);break;case"customer":l="Customer/".concat(e);break;case"driver":l="Driver/".concat(e);break;case"operator":l="Operator/".concat(e);break;case"partner":l="Partner/".concat(e);break;case"terminal":l="Terminal/".concat(e);break;default:l="".concat(e)}let t=await this.get(l);return this.handleApiResult(t)}constructor(...e){super(...e),this.baseUrl=r.QQ.USER.LIST}}let s=new i},52795:(e,a,l)=>{l(27736)},54912:(e,a,l)=>{l.d(a,{rq:()=>g,PA:()=>n,mp:()=>s});var t=l(24723);let r={[t.Xh.Admin]:"IdentityCardIcon",[t.Xh.Customer]:"ProfileIcon",[t.Xh.Driver]:"VehiclesIcon",[t.Xh.Operator]:"UsersIcon",[t.Xh.Partner]:"TariffsIcon",[t.Xh.Terminal]:"OrdersIcon",[t.Xh.Unknown]:"UsersIcon"},i={[t.Xh.Admin]:"Полный доступ к системе и управлению пользователями",[t.Xh.Customer]:"Клиент, который может заказывать услуги",[t.Xh.Driver]:"Водитель, выполняющий заказы",[t.Xh.Operator]:"Оператор, обрабатывающий заказы и запросы",[t.Xh.Partner]:"Партнер, предоставляющий услуги",[t.Xh.Terminal]:"Терминал для самообслуживания",[t.Xh.Unknown]:"Роль не определена"};function s(e){return r[e]||"UsersIcon"}function n(e){return i[e]||"Роль пользователя в системе"}var o=l(54568),p=l(7620),m=l(61938),d=l(78032),c=l(7401),u=l(41564);let g=()=>{let[e,a]=(0,p.useState)([]),[l,t]=(0,p.useState)(!1),[r,i]=(0,p.useState)(""),[s,n]=(0,p.useState)("address"),[g,h]=(0,p.useState)(!1),[x,b]=(0,p.useState)({}),{watch:f,setValue:v}=(0,m.xW)(),y=f("partnerRoutes")||[],z=(0,p.useMemo)(()=>x,[JSON.stringify(x)]),T=(0,d.d)(r,300),w=(0,p.useCallback)(async()=>{if(!T||!(T.length<2)){t(!0);try{let e={size:100,isActive:!0};T&&T.length>=2&&("address"===s?(e.address=T,e.addressOp="Contains"):"city"===s&&(e.city=T,e.cityOp="Contains")),Object.entries(z).forEach(a=>{let[l,t]=a;t&&(e[l]=t)});let l=await c.Ny.getLocations(e);a(l.data||[])}catch(e){console.error("Ошибка поиска локаций:",e),a([])}finally{t(!1)}}},[T,s,z]);(0,p.useEffect)(()=>{w()},[w]),(0,p.useEffect)(()=>{(async()=>{t(!0);try{let e=await c.Ny.getLocations({size:100,isActive:!0});a(e.data||[])}catch(e){console.error("Ошибка загрузки начального списка локаций:",e)}finally{t(!1)}})()},[]);let N=e=>{v("partnerRoutes",y.includes(e)?y.filter(a=>a!==e):[...y,e])},D=e.length,S=y.length;return(0,p.useEffect)(()=>{let e=e=>{let a=e.target;!g||a.closest(".filter-panel")||a.closest(".filter-button")||h(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[g]),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("h3",{className:"text-lg font-medium",children:"Доступные локации"}),(0,o.jsxs)("div",{className:"text-sm text-gray-400",children:["Выбрано: ",S," из ",D]})]}),(0,o.jsx)("p",{className:"text-sm text-gray-400",children:"Выберите локации, в которых партнер может принимать заказы. Если не выбрано ни одной локации, партнер сможет работать везде."}),(0,o.jsxs)("div",{className:"mb-4 relative",children:[(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsx)("div",{className:"w-[150px]",children:(0,o.jsxs)("select",{value:s,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 text-sm border border-white/10 rounded-md bg-black/20 focus:outline-none focus:border-blue-500/50 hover:bg-white/10 transition-all backdrop-blur-md cursor-pointer",children:[(0,o.jsx)("option",{value:"address",children:"По адресу"}),(0,o.jsx)("option",{value:"city",children:"По городу"})]})}),(0,o.jsx)("input",{type:"text",value:r,onChange:e=>i(e.target.value),placeholder:"address"===s?"Введите адрес...":"Введите город...",className:"flex-1 px-3 py-2 text-sm border border-white/10 rounded-md bg-black/20 focus:outline-none focus:border-blue-500/50 hover:bg-white/10 transition-all backdrop-blur-md"}),(0,o.jsx)("button",{type:"button",onClick:()=>h(!g),className:"filter-button px-3 py-2 text-sm border rounded-md transition-all cursor-pointer backdrop-blur-md ".concat(g?"bg-blue-500/20 border-blue-500/50 text-blue-400":"border-white/10 bg-black/20 hover:bg-white/10"),title:"Дополнительные фильтры",children:(0,o.jsxs)("span",{className:"flex items-center gap-2",children:["\uD83D\uDD27 Фильтры",Object.keys(x).length>0&&(0,o.jsx)("span",{className:"px-1.5 py-0.5 bg-blue-500/30 rounded-full text-xs",children:Object.keys(x).length})]})})]}),g&&(0,o.jsx)("div",{className:"filter-panel absolute right-0 top-full mt-2 w-[400px] p-4 bg-black/80 rounded-lg border border-white/10 z-50 shadow-2xl",children:(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Дополнительные фильтры"}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Тип локации"}),(0,o.jsxs)("select",{value:x.type||"",onChange:e=>b(a=>({...a,type:e.target.value||void 0})),className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2",children:[(0,o.jsx)("option",{value:"",children:"Все"}),(0,o.jsx)("option",{value:"Home",children:"Дом"}),(0,o.jsx)("option",{value:"Work",children:"Работа"}),(0,o.jsx)("option",{value:"Airport",children:"Аэропорт"}),(0,o.jsx)("option",{value:"Station",children:"Вокзал"}),(0,o.jsx)("option",{value:"Hotel",children:"Отель"}),(0,o.jsx)("option",{value:"Restaurant",children:"Ресторан"}),(0,o.jsx)("option",{value:"Shop",children:"Магазин"}),(0,o.jsx)("option",{value:"Entertainment",children:"Развлечения"}),(0,o.jsx)("option",{value:"Medical",children:"Медицина"}),(0,o.jsx)("option",{value:"Educational",children:"Образование"}),(0,o.jsx)("option",{value:"BusinessCenter",children:"Бизнес-центр"}),(0,o.jsx)("option",{value:"Other",children:"Другое"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Город"}),(0,o.jsx)("input",{type:"text",value:x.city||"",onChange:e=>b(a=>({...a,city:e.target.value||void 0})),placeholder:"Введите город",className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Регион"}),(0,o.jsx)("input",{type:"text",value:x.region||"",onChange:e=>b(a=>({...a,region:e.target.value||void 0})),placeholder:"Введите регион",className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Популярность"}),(0,o.jsxs)("select",{value:x.popular1||"",onChange:e=>b(a=>({...a,popular1:e.target.value||void 0})),className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2",children:[(0,o.jsx)("option",{value:"",children:"Все"}),(0,o.jsx)("option",{value:"true",children:"Популярные"}),(0,o.jsx)("option",{value:"false",children:"Обычные"})]})]})]}),(0,o.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,o.jsx)("button",{type:"button",onClick:()=>b({}),className:"px-3 py-1 text-xs border border-white/10 rounded hover:bg-white/10 transition-all cursor-pointer",children:"Очистить"}),(0,o.jsx)("button",{type:"button",onClick:()=>h(!1),className:"px-3 py-1 text-xs bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded hover:bg-blue-500/30 transition-all cursor-pointer",children:"Применить"})]})]})})]}),(0,o.jsx)("div",{className:"max-h-96 overflow-y-auto border border-white/10 rounded-lg bg-black/20 backdrop-blur-md",children:l?(0,o.jsxs)("div",{className:"p-4 text-center text-gray-400",children:[(0,o.jsx)("div",{className:"inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"}),(0,o.jsx)("p",{className:"mt-2 text-sm",children:"Поиск локаций..."})]}):e.length>0?(0,o.jsx)("div",{className:"p-2 space-y-1",children:e.map(e=>{let a=y.includes(e.id);return(0,o.jsx)("div",{onClick:()=>N(e.id),className:"p-3 rounded-lg border transition-all cursor-pointer hover:bg-white/10 ".concat(a?"bg-blue-500/20 border-blue-500/50 text-blue-400":"border-white/10 hover:border-white/20"),children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("h4",{className:"font-medium text-sm",children:e.address}),(0,o.jsx)("span",{className:"text-xs px-2 py-0.5 bg-gray-500/20 rounded-full",children:e.type?(0,u.al)(e.type):"Локация"})]}),(0,o.jsxs)("p",{className:"text-xs text-gray-500",children:[e.city,", ",e.region]})]}),a&&(0,o.jsx)("div",{className:"text-blue-400 text-lg",children:"✓"})]})},e.id)})}):(0,o.jsx)("div",{className:"p-4 text-center text-gray-400",children:(0,o.jsx)("p",{className:"text-sm",children:r?"Локации не найдены":"Начните вводить для поиска"})})})]})}},55915:(e,a,l)=>{l.d(a,{i:()=>t.i});var t=l(46317)},61670:(e,a,l)=>{l.d(a,{Tg:()=>r,AC:()=>i,UG:()=>s,Rv:()=>n,MJ:()=>g,jz:()=>h,Qu:()=>x,JR:()=>b,i5:()=>f,pp:()=>v,yD:()=>y,Pe:()=>z});var t=l(75006);t.z.object({fullName:t.z.string().min(3,{message:"Имя должно содержать не менее 3 символов"}).max(255,{message:"Имя не должно превышать 255 символов"}),phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).regex(/^[+]?[0-9\s()-]{7,}$/,{message:"Введите корректный номер телефона (минимум 7 цифр)"}).optional(),email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"})});let r=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:t.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:t.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),profile:t.z.object({accessLevel:t.z.string().min(1,{message:"Уровень доступа обязателен"}).max(63,{message:"Уровень доступа не должен превышать 63 символа"}),department:t.z.string().max(127,{message:"Отдел не должен превышать 127 символов"}).nullable(),position:t.z.string().max(127,{message:"Должность не должна превышать 127 символов"}).nullable()})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),i=t.z.object({phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),profile:t.z.object({accessLevel:t.z.string().max(63,{message:"Уровень доступа не должен превышать 63 символа"}),department:t.z.string().max(127,{message:"Отдел не должен превышать 127 символов"}).nullable(),position:t.z.string().max(127,{message:"Должность не должна превышать 127 символов"}).nullable()})}),s=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:t.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:t.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().max(511,{message:"URL аватара не должен превышать 511 символов"}).nullable().optional(),loyaltyPoints:t.z.number().int({message:"Баллы лояльности должны быть целым числом"})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),n=t.z.object({phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),loyaltyPoints:t.z.number().int({message:"Баллы лояльности должны быть целым числом"})});var o=l(24723);let p=t.z.object({institution:t.z.string().min(1,{message:"Название учебного заведения обязательно"}).max(255,{message:"Название учебного заведения не должно превышать 255 символов"}),degree:t.z.string().max(255,{message:"Полученная степень/квалификация не должна превышать 255 символов"}).nullable(),fieldOfStudy:t.z.string().max(127,{message:"Специальность не должна превышать 127 символов"}).nullable(),startDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата начала обучения должна быть в формате YYYY-MM-DD"}).nullable(),endDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата окончания обучения должна быть в формате YYYY-MM-DD"}).nullable(),isCompleted:t.z.boolean()}),m=t.z.object({number:t.z.string().min(1,{message:"Номер паспорта обязателен"}).max(63,{message:"Номер паспорта не должен превышать 63 символа"}),series:t.z.string().max(2,{message:"Серия паспорта не должна превышать 2 символа"}).nullable().optional(),issueDate:t.z.string().min(1,{message:"Дата выдачи паспорта обязательна"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата выдачи должна быть в формате YYYY-MM-DD"}),issuedBy:t.z.string().min(1,{message:"Кем выдан паспорт обязательно"}).max(31,{message:"Кем выдан паспорт не должен превышать 31 символ"}),page:t.z.number({message:"Страница паспорта должна быть числом"}).int({message:"Страница паспорта должна быть целым числом"}).min(1,{message:"Страница паспорта должна быть больше 0"}).nullable().optional(),expiryDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата окончания срока действия должна быть в формате YYYY-MM-DD"}).nullable().optional(),identityType:t.z.nativeEnum(o.e5,{errorMap:()=>({message:"Выберите тип документа"})})}),d=t.z.object({testName:t.z.string().min(1,{message:"Название пройденного теста обязательно"}).max(127,{message:"Название пройденного теста не должно превышать 127 символов"}),score:t.z.number({message:"Полученный балл должен быть числом"}),maxPossibleScore:t.z.number({message:"Максимально возможный балл должен быть числом"}),passedDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата прохождения теста должна быть в формате YYYY-MM-DD"}),expiryDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата истечения срока действия теста должна быть в формате YYYY-MM-DD"}).nullable(),comments:t.z.string().max(511,{message:"Комментарии к результату не должны превышать 511 символов"}).nullable()}),c=t.z.object({employerName:t.z.string().min(1,{message:"Название предыдущего работодателя обязательно"}).max(127,{message:"Название предыдущего работодателя не должно превышать 127 символов"}),position:t.z.string().min(1,{message:"Должность на предыдущем месте работы обязательна"}).max(127,{message:"Должность на предыдущем месте работы не должна превышать 127 символов"}),startDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата начала работы должна быть в формате YYYY-MM-DD"}),endDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата окончания работы должна быть в формате YYYY-MM-DD"}).nullable(),isCurrent:t.z.boolean(),responsibilities:t.z.string().max(4095,{message:"Основные обязанности не должны превышать 4095 символов"}).nullable()}),u=t.z.object({licenseNumber:t.z.string({required_error:"Номер водительского удостоверения обязателен",invalid_type_error:"Номер водительского удостоверения должен быть строкой"}).min(1,{message:"Номер водительского удостоверения не может быть пустым"}).max(31,{message:"Номер водительского удостоверения не должен превышать 31 си��вол"}),licenseCategories:t.z.array(t.z.string(),{required_error:"Категории прав обязательны",invalid_type_error:"Категории прав должны быть массивом строк"}).min(1,{message:"Выберите хотя бы одну категорию прав"}),licenseIssueDate:t.z.string({required_error:"Дата выдачи прав обязательна",invalid_type_error:"Дата выдачи прав должна быть строкой"}).min(1,{message:"Дата выдачи прав не может быть пустой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата выдачи прав должна быть в формате YYYY-MM-DD"}),licenseExpiryDate:t.z.string({required_error:"Дата окончания прав обязательна",invalid_type_error:"Дата окончания прав должна быть строкой"}).min(1,{message:"Дата окончания прав не может быть пустой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата окончания прав должна быть в формате YYYY-MM-DD"}),dateOfBirth:t.z.string({required_error:"Дата рождения обязательна",invalid_type_error:"Дата рождения должна быть строкой"}).min(1,{message:"Дата рождения не может быть пустой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата рождения должна быть в формате YYYY-MM-DD"}),birthPlace:t.z.string({invalid_type_error:"Место рождения должно быть строкой"}).max(255,{message:"Место рождения не должно превышать 255 символов"}).nullable().optional(),citizenship:t.z.string({required_error:"Гражданство обязательно",invalid_type_error:"Гражданство должно быть строкой"}).min(1,{message:"Гражданство не может быть пустым"}).max(63,{message:"Гражданство не должно превышать 63 символа"}),citizenshipCountry:t.z.nativeEnum(o.ZA,{errorMap:(e,a)=>e.code===t.z.ZodIssueCode.invalid_type?{message:"Некорректная страна гражданства"}:e.code===t.z.ZodIssueCode.invalid_enum_value?{message:"Недопустимая страна гражданства: ".concat(e.received,". Допустимые значения: ").concat(Object.values(o.ZA).join(", "))}:{message:"Страна гражданства обязательна"}}),drivingExperience:t.z.number({invalid_type_error:"Опыт вождения должен быть числом"}).int({message:"Опыт вождения должен быть целым числом"}).min(0,{message:"Опыт вождения не может быть отрицательным"}).nullable().optional(),languages:t.z.array(t.z.string(),{required_error:"Языки обязательны",invalid_type_error:"Языки должны быть массивом строк"}).min(1,{message:"Выберите хотя бы один язык"}),taxIdentifier:t.z.string({invalid_type_error:"ИНН должен быть строкой"}).max(31,{message:"ИНН не должен превышать 31 си��вол"}).nullable().optional(),totalRides:t.z.number({required_error:"Общее количество поездок обязательно",invalid_type_error:"Общее количество поездок должно быть числом"}).int({message:"Общее количество поездок должно быть целым числом"}).min(0,{message:"Количество поездок не может быть отрицательным"}).default(0),totalDistance:t.z.number({required_error:"Общее пройденное расстояние обязательно",invalid_type_error:"Общее пройденное расстояние должно быть числом"}).min(0,{message:"Пройденное расстояние не может быть отрицательным"}).default(0),lastRideDate:t.z.string({invalid_type_error:"Дата последней поездки должна быть строкой"}).nullable().optional(),medicalExamDate:t.z.string({invalid_type_error:"Дата медосмотра должна быть строкой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата последнего медосмотра должна быть в формате YYYY-MM-DD"}).nullable().optional(),backgroundCheckDate:t.z.string({invalid_type_error:"Дата проверки биографии должна быть строкой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата последней проверки биографии должна быть в формате YYYY-MM-DD"}).nullable().optional(),profilePhoto:t.z.string({invalid_type_error:"URL фото профиля должен быть строкой"}).max(511,{message:"URL фото профиля не должен превышать 511 символов"}).nullable().optional(),preferredRideTypes:t.z.array(t.z.string(),{required_error:"Предпочитаемые классы обслуживания обязательны",invalid_type_error:"Предпочитаемые классы обслуживания должны быть массивом строк"}).min(1,{message:"Выберите хотя бы один предпочитаемый класс обслуживания"}),preferredWorkZones:t.z.array(t.z.string(),{invalid_type_error:"Предпочитаемые зоны работы должны быть массивом строк"}).optional(),trainingCompleted:t.z.boolean({required_error:"Статус обучения обязателен",invalid_type_error:"Статус обучения должен быть булевым значением"}).default(!1),passport:m,workExperience:t.z.preprocess(e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){return[]}return e},t.z.array(c,{invalid_type_error:"Опыт работы должен быть массивом объектов"}).optional()),education:t.z.preprocess(e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){return[]}return e},t.z.array(p,{invalid_type_error:"Образование должно быть массивом объектов"}).optional()),testScore:t.z.preprocess(e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){return[]}return e},t.z.array(d,{invalid_type_error:"Результаты тестов должны быть массивом объектов"}).optional())}),g=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:t.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:t.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),verificationStatus:t.z.nativeEnum(o.iS,{errorMap:()=>({message:"Выберите статус верификации"})}),profile:u,car:t.z.string().uuid({message:"Некорректный ID автомобиля"}).optional()}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]});t.z.object({phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).nullable().optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),verificationStatus:t.z.nativeEnum(o.iS,{errorMap:()=>({message:"Выберите статус верификации"})}),profile:u,car:t.z.string().uuid({message:"Некорректный ID автомобиля"}).nullable().optional()});let h=t.z.object({phoneNumber:t.z.string({invalid_type_error:"Номер телефона должен быть строкой"}).max(63,{message:"Номер телефона не должен превышать 63 символа"}).nullable().optional(),fullName:t.z.string({required_error:"Полное имя обязательно",invalid_type_error:"Полное имя должно быть строкой"}).min(1,{message:"Полное имя не может быть пустым"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string({invalid_type_error:"URL аватара должен быть строкой"}).max(511,{message:"URL аватара не должен превышать 511 символов"}).nullable().optional(),verificationStatus:t.z.nativeEnum(o.iS,{errorMap:(e,a)=>e.code===t.z.ZodIssueCode.invalid_type?{message:"Некорректный статус верификации"}:e.code===t.z.ZodIssueCode.invalid_enum_value?{message:"Недопустимый статус верификации: ".concat(e.received,". Допустимые значения: ").concat(Object.values(o.iS).join(", "))}:{message:"Статус верификации обязателен"}}),profile:u}),x=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:t.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:t.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),isActive:t.z.boolean(),profile:t.z.object({employeeId:t.z.string().min(1,{message:"Табельный номер обязателен"}).max(63,{message:"Табельный номер не должен превышать 63 символа"}),department:t.z.string().min(1,{message:"Отдел обязателен"}).max(127,{message:"Отдел не должен превышать 127 символов"}),position:t.z.string().min(1,{message:"Должность обязательна"}).max(127,{message:"Должность не должна превышать 127 символов"}),hireDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата найма должна быть в формате YYYY-MM-DD"})})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),b=t.z.object({phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),isActive:t.z.boolean(),profile:t.z.object({employeeId:t.z.string().max(63,{message:"Табельный номер не должен превышать 63 символа"}),department:t.z.string().max(127,{message:"Отдел не должен превышать 127 символов"}),position:t.z.string().max(127,{message:"Должность не должна превышать 127 символов"}),hireDate:t.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата найма должна быть в формате YYYY-MM-DD"})})}),f=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:t.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:t.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),verificationStatus:t.z.nativeEnum(o.iS,{errorMap:()=>({message:"Выберите статус верификации"})}),profile:t.z.object({companyName:t.z.string().min(1,{message:"Название компании обязательно"}),companyType:t.z.nativeEnum(o.Vp,{errorMap:()=>({message:"Выберите тип компании"})}),registrationNumber:t.z.string().max(31,{message:"Регистрационный номер не должен превышать 31 символ"}).nullable(),taxIdentifier:t.z.string().max(31,{message:"ИНН не должен превышать 31 символ"}).nullable(),legalAddress:t.z.string().min(1,{message:"Юридический адрес обязателен"}).max(255,{message:"Юридический адрес не должен превышать 255 символов"}),contactEmail:t.z.string().max(255,{message:"Контактный email не должен превышать 255 символов"}).email({message:"Введите корректный email"}).nullable(),contactPhone:t.z.string().max(63,{message:"Контактный телефон не должен превышать 63 символа"}).nullable(),website:t.z.string().max(255,{message:"Веб-сайт не должен превышать 255 символов"}).nullable()})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),v=t.z.object({phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),verificationStatus:t.z.nativeEnum(o.iS,{errorMap:()=>({message:"Выберите статус верификации"})}),profile:t.z.object({companyName:t.z.string().min(1,{message:"Название компании обязательно"}),companyType:t.z.nativeEnum(o.Vp,{errorMap:()=>({message:"Выберите тип компании"})}),registrationNumber:t.z.string().max(31,{message:"Регистрационный номер не должен превышать 31 символ"}).nullable(),taxIdentifier:t.z.string().max(31,{message:"ИНН не должен превышать 31 символ"}).nullable(),legalAddress:t.z.string().min(1,{message:"Юридический адрес обязателен"}).max(255,{message:"Юридический адрес не должен превышать 255 символов"}),contactEmail:t.z.string().max(255,{message:"Контактный email не должен превышать 255 символов"}).email({message:"Введите корректный email"}).nullable(),contactPhone:t.z.string().max(63,{message:"Контактный телефон не должен превышать 63 символа"}).nullable(),website:t.z.string().max(255,{message:"Веб-сайт не должен превышать 255 символов"}).nullable()})}),y=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:t.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:t.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().nullable().optional(),status:t.z.nativeEnum(o.AK,{errorMap:()=>({message:"Выберите статус терминала"})}),locationId:t.z.string().uuid({message:"Некорректный UUID локации"}).optional(),profile:t.z.object({terminalId:t.z.string().min(1,{message:"Идентификатор терминала обязателен"}).max(31,{message:"Идентификатор терминала не должен превышать 31 символ"}),ipAddress:t.z.string().max(45,{message:"IP адрес не должен превышать 45 символов"}).nullable(),deviceModel:t.z.string().max(127,{message:"Модель устройства не должна превышать 127 символов"}).nullable(),osVersion:t.z.string().max(31,{message:"Версия ОС не должна превышать 31 символ"}).nullable(),appVersion:t.z.string().max(31,{message:"Версия приложения не должна превышать 31 символ"}).nullable(),browserInfo:t.z.string().max(127,{message:"Информация о браузере не должна превышать 127 символов"}).nullable(),screenResolution:t.z.string().max(15,{message:"Разрешение экрана не должно превышать 15 символов"}).nullable(),deviceIdentifier:t.z.string().max(31,{message:"Идентификатор устройства не должен превышать 31 символ"}).nullable()})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),z=t.z.object({phoneNumber:t.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:t.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:t.z.string().optional(),status:t.z.nativeEnum(o.AK,{errorMap:()=>({message:"Выберите статус терминала"})}),locationId:t.z.string().uuid({message:"Некорректный UUID локации"}).optional(),profile:t.z.object({terminalId:t.z.string().min(1,{message:"Идентификатор терминала обязателен"}).max(31,{message:"Идентификатор терминала не должен превышать 31 символ"}),ipAddress:t.z.string().max(45,{message:"IP адрес не должен превышать 45 символов"}).nullable(),deviceModel:t.z.string().max(127,{message:"Модель устройства не должна превышать 127 символов"}).nullable(),osVersion:t.z.string().max(31,{message:"Версия ОС не должна превышать 31 символ"}).nullable(),appVersion:t.z.string().max(31,{message:"Версия приложения не должна превышать 31 символ"}).nullable(),browserInfo:t.z.string().max(127,{message:"Информация о браузере не должна превышать 127 символов"}).nullable(),screenResolution:t.z.string().max(15,{message:"Разрешение экрана не должно превышать 15 символов"}).nullable(),deviceIdentifier:t.z.string().max(31,{message:"Идентификатор устройства не должен превышать 31 символ"}).nullable()})});t.z.boolean({required_error:"Статус активности связи обязателен",invalid_type_error:"Статус активности должен быть булевым значением (true/false)"}),t.z.object({carId:t.z.string().uuid("ID автомобиля должен быть валидным UUID").min(1,"ID автомобиля обязателен"),action:t.z.enum(["activate","deactivate"],{errorMap:()=>({message:"Действие должно быть activate или deactivate"})}),reason:t.z.string().max(200,"Причина не должна превышать 200 символов").optional()}),t.z.object({activeCarId:t.z.string().uuid("ID автомобиля должен быть валидным UUID").nullable(),updatedAt:t.z.string().datetime("Дата должна быть в формате ISO 8601").optional()}),t.z.object({availableCarIds:t.z.array(t.z.string().uuid("ID автомобиля должен быть валидным UUID")).min(0,"Список может быть пустым"),activeCarId:t.z.string().uuid("ID автомобиля должен быть валидным UUID").nullable()}),t.z.object({carId:t.z.string().uuid("ID автомобиля должен быть валидным UUID"),driverId:t.z.string().uuid("ID водителя должен быть валидным UUID"),isEligible:t.z.boolean(),reason:t.z.string().optional()}),t.z.object({id:t.z.string().uuid(),driverId:t.z.string().uuid(),carId:t.z.string().uuid(),action:t.z.enum(["activated","deactivated"]),timestamp:t.z.string().datetime(),reason:t.z.string().optional()})},66251:(e,a,l)=>{function t(e,a){return Object.values(e).map(e=>({value:e,label:a(e)}))}function r(e,a){return e.filter(e=>!a.includes(e.value))}l.d(a,{mJ:()=>r,wI:()=>t})},66478:(e,a,l)=>{l.d(a,{rq:()=>r.rq,g0:()=>t.g0}),l(9205),l(40529),l(7620),l(47510),l(33822),l(66517),l(77981);var t=l(8426);l(24723),l(55115),l(61670),l(12506);var r=l(54912)},66517:(e,a,l)=>{l.d(a,{D:()=>i,m:()=>s});var t=l(24723),r=l(33822);let i={[t.Xh.Admin]:r.g9,[t.Xh.Customer]:r.Ht,[t.Xh.Driver]:r.bU,[t.Xh.Operator]:r.FS,[t.Xh.Partner]:r.nb,[t.Xh.Terminal]:r.Z8,[t.Xh.Unknown]:r.hF},s=r.hF},77981:(e,a,l)=>{l.d(a,{L:()=>r});var t=l(12506);t.l;let r=t.l}}]);