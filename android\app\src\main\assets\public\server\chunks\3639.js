exports.id=3639,exports.ids=[3639],exports.modules={2216:(e,t,r)=>{Promise.resolve().then(r.bind(r,3347)),Promise.resolve().then(r.bind(r,93323)),Promise.resolve().then(r.bind(r,76977)),Promise.resolve().then(r.bind(r,63497))},6569:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,49989,23)),Promise.resolve().then(r.t.bind(r,75636,23))},16297:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,42671,23)),Promise.resolve().then(r.t.bind(r,56082,23))},20685:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var o=r(38828),s=r(65208),n=r(32845),i=r(60575),a=r(24985),l=r(66313),c=r(44807),m=r(35311);async function d({children:e}){let t=await (0,i.A)(),r=await (0,a.A)(),d=(await (0,s.UL)()).get("driver-ui-scale"),h=d?parseFloat(d.value):.8;return(0,o.jsxs)("html",{lang:t,children:[(0,o.jsxs)("head",{children:[(0,o.jsx)("meta",{name:"viewport",content:"width=1280, height=720, initial-scale=1.0, user-scalable=no"}),(0,o.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,o.jsxs)("body",{children:[(0,o.jsx)(l.DynamicScale,{scale:h}),(0,o.jsxs)(c.ThemeProvider,{children:[(0,o.jsx)(n.A,{locale:t,messages:r,children:e}),(0,o.jsx)(m.ToastManager,{})]})]})]})}r(41203),r(22832)},21093:(e,t,r)=>{"use strict";r(13486),r(60159)},35311:(e,t,r)=>{"use strict";r.d(t,{ToastManager:()=>s});var o=r(33952);let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call ToastManager() from the server but ToastManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\shared\\toast\\ToastManager.tsx","ToastManager");(0,o.registerClientReference)(function(){throw Error("Attempted to call showToast() from the server but showToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\shared\\toast\\ToastManager.tsx","showToast")},35754:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69355,23)),Promise.resolve().then(r.t.bind(r,54439,23)),Promise.resolve().then(r.t.bind(r,67851,23)),Promise.resolve().then(r.t.bind(r,94730,23)),Promise.resolve().then(r.t.bind(r,19774,23)),Promise.resolve().then(r.t.bind(r,53170,23)),Promise.resolve().then(r.t.bind(r,20968,23)),Promise.resolve().then(r.t.bind(r,78298,23))},38664:(e,t,r)=>{Promise.resolve().then(r.bind(r,9)),Promise.resolve().then(r.bind(r,66313)),Promise.resolve().then(r.bind(r,44807)),Promise.resolve().then(r.bind(r,35311))},39711:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(79748),s=r.n(o),n=r(33873),i=r.n(n),a=r(65208);let l=(0,r(37210).A)(async()=>{let e=process.env.NEXT_PUBLIC_LAN_COOKIE_NAME||"LANG",t=(await (0,a.UL)()).get(e)?.value||"ru",r=i().join(process.cwd(),"..","..","messages");try{let e=i().join(r,t),o=(await s().readdir(e)).filter(e=>e.endsWith(".json")),n={};for(let t of o){let r=i().join(e,t),o=await s().readFile(r,"utf8"),a=JSON.parse(o);n={...n,...a}}return{locale:t,messages:n,onError:e=>(e.code,"")}}catch(e){return console.error("Error loading translations:",e),{locale:"ru",messages:{},onError:()=>""}}})},41203:()=>{},44807:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});var o=r(33952);(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeSwitch() from the server but ThemeSwitch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\shared\\theme\\ThemeProvider.tsx","ThemeSwitch"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\shared\\theme\\ThemeProvider.tsx","useTheme");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\shared\\theme\\ThemeProvider.tsx","ThemeProvider")},57846:()=>{},63497:(e,t,r)=>{"use strict";r.d(t,{P:()=>l,ToastManager:()=>n});var o=r(13486);r(32560),r(57846),r(60159);var s=r(67285);let n=({position:e="top-right",autoClose:t=5e3,hideProgressBar:r=!1,closeOnClick:n=!0,pauseOnHover:i=!0,draggable:a=!1,theme:l="colored"})=>(0,o.jsx)(s.N9,{position:e,autoClose:t,hideProgressBar:r,closeOnClick:n,pauseOnHover:i,draggable:a,theme:l}),i=({message:e})=>(0,o.jsx)("div",{className:"toast-message",children:e}),a=({message:e})=>{if(e.includes("\n")){let t=e.split("\n");return(0,o.jsxs)("div",{className:"toast-message",children:[t[0],(0,o.jsx)("ul",{className:"mt-2 pl-4 list-disc",children:t.slice(1).map((e,t)=>(0,o.jsx)("li",{className:"text-sm",children:e},t))})]})}return(0,o.jsx)("div",{className:"toast-message",children:e})},l={success:(e,t)=>s.oR.success((0,o.jsx)(i,{message:e}),{icon:!1,position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!1,theme:"colored",...t}),error:(e,t)=>s.oR.error((0,o.jsx)(a,{message:e}),{icon:!1,position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!1,theme:"colored",...t}),info:(e,t)=>s.oR.info(e,{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!1,theme:"colored",...t}),warn:(e,t)=>s.oR.warn(e,{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!1,theme:"colored",...t})}},66313:(e,t,r)=>{"use strict";r.d(t,{DynamicScale:()=>o});let o=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call DynamicScale() from the server but DynamicScale is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\shared\\components\\DynamicScale.tsx","DynamicScale")},75506:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30385,23)),Promise.resolve().then(r.t.bind(r,33737,23)),Promise.resolve().then(r.t.bind(r,86081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,35856,23)),Promise.resolve().then(r.t.bind(r,55492,23)),Promise.resolve().then(r.t.bind(r,89082,23)),Promise.resolve().then(r.t.bind(r,45812,23))},76977:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var o=r(13486),s=r(60159),n=r(22358);r(99338),r(21093);let i=(0,s.createContext)(void 0),a=({children:e})=>{let[t,r]=(0,s.useState)("light"),[a,l]=(0,s.useState)(!1),c=(0,s.useRef)(null);(0,s.useEffect)(()=>{let e=localStorage.getItem("theme");e?r(e):r(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),l(!0)},[]),(0,s.useEffect)(()=>{if(a){let e=()=>{document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(t),localStorage.setItem("theme",t)};requestAnimationFrame(()=>{e();let t=setTimeout(()=>{document.documentElement.classList.remove("transitioning")},500);return()=>clearTimeout(t)})}},[t,a]);let m=async()=>{if(!c.current||!("startViewTransition"in document))return void r(e=>"light"===e?"dark":"light");try{let e="light"===t,{top:o,left:s}=c.current.getBoundingClientRect(),i=window.innerWidth-s,a=window.innerHeight-o,l=Math.hypot(Math.max(s,i),Math.max(o,a));await document.startViewTransition(()=>{(0,n.flushSync)(()=>{r(e?"dark":"light")})}).ready,document.documentElement.animate({clipPath:[`circle(0px at ${s}px ${o}px)`,`circle(${l}px at ${s}px ${o}px)`]},{duration:1500,easing:"ease-in-out",pseudoElement:"::view-transition-new(root)"})}catch(e){console.error("Error during view transition:",e),r(e=>"light"===e?"dark":"light")}};return(0,o.jsx)(i.Provider,{value:{theme:t,toggleTheme:m,themeButtonRef:c},children:e})}},87239:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,dynamic:()=>l,metadata:()=>a});var o=r(38828),s=r(14299),n=r(42671),i=r.n(n);let a={title:"Страница не найдена | Мой Сайт",description:"Страница не найдена"},l="force-static",c=()=>(0,o.jsx)("div",{className:"flex flex-col items-center justify-center h-screen",children:(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,o.jsx)(s.default,{src:"/404.webp",alt:"Страница не найдена",width:200,height:200,className:"w-1/2"}),(0,o.jsx)("h1",{className:"text-4xl font-bold text-[color:var(--color-text-primary)] mb-4",children:"404"}),(0,o.jsx)("h2",{className:"text-2xl font-bold text-[color:var(--color-text-primary)] mb-2",children:"Страница не найдена"}),(0,o.jsx)("p",{className:"text-lg text-[color:var(--color-text-primary)] mb-6",children:"Извините, но запрашиваемая вами страница не существует."}),(0,o.jsx)(i(),{href:"/",className:"text-lg font-medium text-[color:var(--color-primary)] hover:underline",children:"Вернуться на главную"})]})})},93323:(e,t,r)=>{"use strict";r.d(t,{DynamicScale:()=>s});var o=r(60159);let s=({scale:e})=>((0,o.useEffect)(()=>{document.documentElement.style.setProperty("--ui-scale",e.toString());let t=16*e;return document.documentElement.style.fontSize=`${t}px`,document.body.style.transform="",document.body.style.transformOrigin="",document.body.style.width="",document.body.style.height="",document.body.style.overflow="",()=>{document.documentElement.style.removeProperty("--ui-scale"),document.documentElement.style.fontSize=""}},[e]),null)},99338:(e,t,r)=>{"use strict";r(13486),r(60159)}};