(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2267],{3884:()=>{},4499:(e,r,t)=>{"use strict";t.d(r,{v:()=>u});var s=t(62942),a=t(7620),i=t(80745),l=t(79702),o=t(8703),n=t(17691);let u=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)({});return{login:(0,a.useCallback)(async r=>{t(!0),c(null),h({});let s=await (0,l.n)(n.y.loginWithValidation(r));if(s.success)o.P.success("Вход выполнен успешно!"),e.push(i.vc.MAIN.HOME);else{let e=s.error||"Произошла неизвестная ошибка";c(e),s.fieldErrors&&h(s.fieldErrors),o.P.error(e)}t(!1)},[e]),isLoading:r,error:u,fieldErrors:d,clearFieldError:(0,a.useCallback)(e=>{h(r=>{let t={...r};return delete t[e],t})},[])}}},17691:(e,r,t)=>{"use strict";t.d(r,{y:()=>o});var s=t(22223),a=t(21715),i=t(80745);class l extends s.v{async login(e){let r=await this.post("/login",e);return this.handleApiResult(r)}async loginWithValidation(e){try{let r=await this.post("/login",{email:e.email,password:e.password});if(r.error){let t=r.error.message,s={};if(r.error.type===a.r6.Auth)t="Неверный email или пароль. Пожалуйста, проверьте правильность учетных данных.",s.email=!0,s.password=!0;else if(r.error.type===a.r6.Network)t="Проблема с сетевым подключением. Проверьте интернет-соединение.";else if(r.error.type===a.r6.Validation&&r.error.data&&"object"==typeof r.error.data){let a=r.error.data;if(a.errors&&"object"==typeof a.errors){let r=a.errors;if(r.UserNotFound&&r.UserNotFound.length>0)t="Пользователь с email '".concat(e.email,"' не найден."),s.email=!0;else if(r.InvalidEmail&&r.InvalidEmail.length>0)t="Указан некорректный email.",s.email=!0;else{let e=Object.entries(r).map(e=>{let[r,t]=e;return"".concat("RequiredField"===r?"Обязательное поле":r,": ").concat(t.join(", "))}).join("\n");e&&(t=e)}}}return{success:!1,error:t,fieldErrors:Object.keys(s).length>0?s:void 0}}return{success:!0,data:this.handleApiResult(r)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла неизвестная ошибка"}}}async register(e){let r=await this.post("/register",e);return this.handleApiResult(r)}async registerPartnerWithValidation(e){try{let r=await this.post("/register/partner",e);if(r.error)return{success:!1,error:r.error.message};return{success:!0,data:this.handleApiResult(r)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при регистрации"}}}async refreshToken(e){let r=await this.post("/refresh",e);return this.handleApiResult(r)}async logout(){let e=await this.post("/logout");this.handleApiResult(e)}async logoutWithCleanup(){try{let e=await this.post("/logout");if(e.error)return{success:!1,error:e.error.message};return this.handleApiResult(e),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при выходе"}}}async resetPassword(e){let r=await this.post("/reset-password",e);this.handleApiResult(r)}async forgotPasswordWithValidation(e){try{let r=await this.post("/forgot-password",e);if(r.error)return{success:!1,error:r.error.message};return this.handleApiResult(r),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при отправке запроса"}}}async resetPasswordWithValidation(e){try{let r=await this.post("/reset-password",e);if(r.error)return{success:!1,error:r.error.message};return this.handleApiResult(r),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при сбросе пароля"}}}async changePassword(e){let r=await this.post("/change-password",e);this.handleApiResult(r)}async verifyToken(){try{let e=await this.get("/verify");return this.handleApiResult(e).valid}catch(e){return!1}}async getCurrentUser(){let e=await this.get("/me");return this.handleApiResult(e)}constructor(...e){super(...e),this.baseUrl=i.QQ.AUTH.BASE}}let o=new l},43356:(e,r,t)=>{"use strict";t.d(r,{G7:()=>d,Wn:()=>u,dO:()=>c,Qc:()=>h}),t(4499);var s=t(62942),a=t(7620),i=t(27736),l=t(79702),o=t(8703),n=t(17691);let u=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)({});return{logout:async r=>{try{if(t(!0),c(null),h(!1),f({}),(null==r?void 0:r.isInQueue)&&(null==r?void 0:r.leaveQueue))try{await r.leaveQueue(),await new Promise(e=>setTimeout(e,500))}catch(e){console.warn("Ошибка при выходе из очереди:",e),o.P.warn("Не удалось корректно выйти из очереди, но выход из системы продолжается")}let s=await (0,l.n)(n.y.logoutWithCleanup());if(s.success)h(!0),o.P.success("Выход выполнен успешно!"),e.push(i.vc.AUTH.LOGIN);else{let e=s.error||"Ошибка при выходе из системы";c(e),o.P.error(e)}}catch(r){let e=r instanceof Error?r.message:"Произошла непредвиденная ошибка при выходе из системы";c(e),o.P.error(e)}finally{t(!1)}},isLoggingOut:r,error:u,success:d,fieldErrors:p,clearFieldError:e=>{f(r=>{let t={...r};return delete t[e],t})}}},c=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)({}),g=async(r,t)=>{let s=await (0,l.n)(n.y.loginWithValidation({email:r,password:t}));s.success?setTimeout(()=>{e.push(i.vc.MAIN.HOME)},2e3):(console.error("Ошибка при автоматическом входе:",s.error),setTimeout(()=>{e.push("/login?registered=true")},2e3))};return{registerPartner:async e=>{t(!0),c(null),h(!1),f({});let r=await (0,l.n)(n.y.registerPartnerWithValidation(e));if(r.success)h(!0),o.P.success("Регистрация успешно завершена! Перенаправление на страницу входа..."),await g(e.email,e.password);else{let e=r.error||"Произошла ошибка при регистрации";e.includes("Email")&&e.includes("уже используется")&&f(e=>({...e,email:!0})),c(e),o.P.error(e)}t(!1)},isLoading:r,error:u,success:d,fieldErrors:p,clearFieldError:e=>{f(r=>{let t={...r};return delete t[e],t})}}},d=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)({});return{forgotPassword:async r=>{t(!0),c(null),h(!1),f({});let s=await (0,l.n)(n.y.forgotPasswordWithValidation(r));if(s.success)h(!0),o.P.success("Инструкции по восстановлению пароля отправлены на указанный email."),o.P.info("Переход на страницу сброса пароля...",{autoClose:2e3}),setTimeout(()=>{e.push("".concat(i.vc.AUTH.RESET_PASSWORD,"?email=").concat(encodeURIComponent(r.email)))},2e3);else{let e=s.error||"Произошла ошибка при отправке запроса";(e.includes("не найден")||e.includes("некорректный email"))&&f(e=>({...e,email:!0})),c(e),o.P.error(e)}t(!1)},isLoading:r,error:u,success:d,fieldErrors:p,clearFieldError:e=>{f(r=>{let t={...r};return delete t[e],t})}}},h=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)({});return{resetPassword:async r=>{t(!0),c(null),h(!1),f({});let s=await (0,l.n)(n.y.resetPasswordWithValidation(r));if(s.success)h(!0),o.P.success("Пароль успешно изменен! Вы будете перенаправлены на страницу входа."),setTimeout(()=>{e.push(i.vc.AUTH.LOGIN)},2e3);else{let e=s.error||"Произошла ошибка при сбросе пароля";e.includes("неверный код")||e.includes("истек")?f(e=>({...e,resetCode:!0})):e.includes("не найден")?f(e=>({...e,email:!0})):e.includes("слишком короткий")&&f(e=>({...e,newPassword:!0})),c(e),o.P.error(e)}t(!1)},isLoading:r,error:u,success:d,fieldErrors:p,clearFieldError:e=>{f(r=>{let t={...r};return delete t[e],t})}}}},53635:(e,r,t)=>{"use strict";t.d(r,{yI:()=>s.y});var s=t(62015);t(80408),t(62885),t(54568),t(7620),t(27736),t(83418),t(42159),t(48630),t(57563),t(31290),t(77579),t(55668),t(86785),t(20089),t(5992),t(28285)},61773:(e,r,t)=>{"use strict";t.d(r,{default:()=>a.a});var s=t(64930),a=t.n(s)},62015:(e,r,t)=>{"use strict";t.d(r,{y:()=>s.y});var s=t(83418)},64930:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return n},getImageProps:function(){return o}});let s=t(16841),a=t(57258),i=t(73970),l=s._(t(50170));function o(e){let{props:r}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let n=i.Image}}]);