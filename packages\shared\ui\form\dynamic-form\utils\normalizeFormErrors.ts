import type { FieldErrors } from 'react-hook-form';
import type { FormError } from '../types';
/**
 * Рекурсивно сплющивает вложенную структуру ошибок в плоскую
 * @param errors Ошибки формы
 * @param prefix Префикс для ключей (для вложенных объектов)
 * @returns Плоская структура ошибок
 */
const flattenErrors = (errors: any, prefix = ''): Record<string, FormError> => {
  const flattened: Record<string, FormError> = {};

  Object.entries(errors).forEach(([key, error]) => {
    const fullKey = prefix ? `${prefix}.${key}` : key;

    if (error && typeof error === 'object') {
      // Если это объект ошибки с message, добавляем его
      if ((error as any).message || (error as any).type) {
        flattened[fullKey] = {
          message: (error as any).message,
          type: (error as any).type,
          ...error
        };
      } else {
        // Если это вложенный объект с ошибками, рекурсивно обрабатываем
        const nestedErrors = flattenErrors(error, fullKey);

        Object.assign(flattened, nestedErrors);
      }
    }
  });

  return flattened;
};

/**
 * Нормализует ошибки формы из react-hook-form в формат, ожидаемый компонентами
 * Поддерживает вложенные ошибки и преобразует их в плоскую структуру
 * @param errors Ошибки формы из react-hook-form
 * @returns Нормализованные ошибки формы
 */
export const normalizeFormErrors = (errors: FieldErrors<any>): Record<string, FormError> => {
  return flattenErrors(errors);
};
