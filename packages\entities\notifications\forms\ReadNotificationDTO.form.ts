import { FormFieldType, FormFieldDataType, type FormConfig } from '@entities/collections/types/formTypes';

/**
 * Конфигурация формы для ReadNotificationDTO
 */
export const ReadNotificationDTOFormConfig: FormConfig = {
  title: {
    create: 'Отметить уведомление как прочитанное',
    edit: 'Отметить уведомление как прочитанное'
  },
  description: {
    create: 'Укажите ID уведомления для отметки как прочитанное',
    edit: 'Укажите ID уведомления для отметки как прочитанное'
  },
  groups: [
  {
    id: "default",
    title: "Информация об уведомлении",
    fields: [
      {
        name: "id",
        label: "ID уведомления",
        type: FormFieldType.TEXT,
        dataType: FormFieldDataType.STRING,
        required: true,
        placeholder: "Введите ID уведомления",
        helperText: "Уникальный идентификатор уведомления"
      }
    ],
    layout: {
      gridCols: 1,
      gapX: 4,
      gapY: 4
    }
  }
]
};
/**
 * Получает конфигурацию формы для ReadNotificationDTO
 * @returns {FormConfig} Конфигурация формы
 */
export function getReadNotificationDTOFormConfig(): FormConfig {
  return ReadNotificationDTOFormConfig;
}
