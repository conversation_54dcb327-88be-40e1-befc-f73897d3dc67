(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2576],{47328:(e,s,a)=>{Promise.resolve().then(a.bind(a,92796))},48484:(e,s,a)=>{"use strict";a.d(s,{Hq:()=>m,c7:()=>g});var r=a(62942),c=a(7620),l=a(66478);let t=null,d=[],i=!1,o=[],n=e=>{t=e,d.forEach(s=>s(e))},u=e=>{i=e,o.forEach(s=>s(e))},h=e=>(o.push(e),()=>{o=o.filter(s=>s!==e)}),v=e=>(d.push(e),()=>{d=d.filter(s=>s!==e)}),g=()=>{let e=(0,r.useRouter)(),[s,a]=(0,c.useState)(t),[d,o]=(0,c.useState)(i),{driverId:g,activeRides:m,isInitialized:p,isLoadingRides:f,refreshRides:x}=(0,l.g0)();(0,c.useEffect)(()=>{let e=v(e=>{a(e)}),s=h(e=>{o(e)});return i&&!d&&o(!0),()=>{e(),s()}},[d]),(0,c.useEffect)(()=>{if(p&&m.length>0){let e=m[0],a={orderId:e.orderId||e.id,rideId:e.id,status:e.status,ride:e};s&&s.rideId===a.rideId||n(a)}else p&&0===m.length&&s&&(console.log("\uD83D\uDDD1️ Нет активных поездок в централизованных данных, очищаем локальное состояние"),n(null))},[m,p,s]);let y=(0,c.useCallback)(async()=>(p&&g?(o(!0),u(!0)):console.log("⏳ Ждем инициализации водителя для проверки активных поездок..."),s),[s,g,p]),I=(0,c.useCallback)((e,s)=>{console.log("\uD83D\uDE80 setActiveRideData вызван - устанавливаем активную поездку глобально");let a={orderId:e,rideId:s,status:"Accepted",ride:null};n(a),console.log("✅ Активная поездка установлена глобально:",a)},[]),N=(0,c.useCallback)(async()=>(await x(),await y()),[y,x]),b=(0,c.useCallback)(()=>{console.log("\uD83D\uDDD1️ clearActiveRide вызван - очищаем активную поездку глобально"),localStorage.removeItem("activeRide"),n(null),console.log("✅ Активная поездка очищена глобально")},[]),j=(0,c.useCallback)(()=>{if(s){let a=s.orderType?"&orderType=".concat(s.orderType):"",r="/active-ride/?orderId=".concat(s.orderId,"&rideId=").concat(s.rideId).concat(a);e.push(r)}},[s,e]);return{activeRide:s,hasActiveRide:!!s,isLoading:f,isInitialized:d,isRideActive:e=>["Accepted","DriverHeading","Arrived","InProgress"].includes(e),checkActiveRides:y,setActiveRideData:I,clearActiveRide:b,navigateToActiveRide:j,forceUpdateActiveRide:N}},m=(e,s)=>{console.log("\uD83C\uDF0D setActiveRideGlobally вызван:",{orderId:e,rideId:s}),n({orderId:e,rideId:s,status:"Accepted",ride:null}),console.log("✅ Активная поездка установлена глобально через прямой вызов")}},92796:(e,s,a)=>{"use strict";a.r(s),a.d(s,{ScheduledOrdersPage:()=>v});var r=a(54568),c=a(7620),l=a(62942),t=a(68327),d=a(32959),i=a(34744),o=a(44090),n=a(12722),u=a(48484);let h=e=>{let{className:s=""}=e,a=(0,l.useRouter)(),{actions:h}=(0,d.o)(),{setActiveRideData:v,forceUpdateActiveRide:g}=(0,u.c7)(),{data:m,pagination:p,isLoading:f,error:x,refetch:y}=(0,i.C)({size:20,orderStatus:["Scheduled"],orderSubStatus:["DriverAssigned"]}),I=(0,c.useCallback)(async(e,s)=>{try{console.log("✅ Подтверждаем запланированную поездку:",{rideId:e,orderId:s}),await (0,n.eX)(e),await (0,n.Z6)(e),v(s||e,e),t.P.success("Поездка подтверждена"),await h.leaveQueue(),setTimeout(()=>g(),1e3),await y();let r=s?"/active-ride?orderId=".concat(s,"&rideId=").concat(e,"&orderType=Scheduled"):"/active-ride?rideId=".concat(e,"&orderType=Scheduled");console.log("\uD83D\uDE97 Переходим к активной поездке:",r),a.push(r)}catch(e){console.error("❌ Ошибка подтверждения поездки:",e),t.P.error("Ошибка подтверждения поездки")}},[a,y,h,v,g]),N=async(e,s)=>{try{await (0,n.eX)(e),await (0,n.Z6)(e),t.P.success("Вы направляетесь к клиенту"),a.push("/active-ride?rideId=".concat(e).concat(s?"&orderId=".concat(s):""))}catch(e){t.P.error('Ошибка: не удалось отправить статус "Еду к клиенту"')}},b=(0,c.useCallback)(async(e,s)=>{try{console.log("\uD83D\uDCE9 Отправляем уведомление о принятии:",{rideId:e,orderId:s}),await (0,n.Or)(e),t.P.success("Уведомление отправлено"),await y()}catch(e){console.error("❌ Ошибка отправки уведомления:",e),t.P.error("Ошибка отправки уведомления")}},[y]),j=async(e,s)=>{let r=null==m?void 0:m.find(s=>s.id===e);if(r&&"InProgress"===r.orderStatus&&"DriverHeading"===r.orderSubStatus)return void a.push("/active-ride?rideId=".concat(e).concat(s?"&orderId=".concat(s):""));if(r&&"Scheduled"===r.orderStatus&&"DriverAssigned"===r.orderSubStatus&&"Requested"===r.status){let s=new Date(r.scheduledTime).getTime(),a=(s-(Date.now()+216e5))/6e4;if(a<=90&&a>0)try{await (0,n.Z6)(e)}catch(e){t.P.error("Ошибка перехода к активной поездке");return}}a.push("/active-ride?rideId=".concat(e).concat(s?"&orderId=".concat(s):""))};return x?(0,r.jsxs)("div",{className:"p-4 bg-red-50 text-red-800 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-medium",children:"Ошибка загрузки данных"}),(0,r.jsx)("p",{className:"mt-1",children:x})]}):f?(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("div",{className:"animate-pulse space-y-4",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"},e))})}):(null==m?void 0:m.length)?(0,r.jsxs)("div",{className:"h-full flex flex-col ".concat(s),children:[(0,r.jsxs)("div",{className:"px-6 pt-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Запланированные заказы"}),p&&(0,r.jsxs)("p",{className:"mt-1",children:["Показано ",m.length," из ",p.totalCount," заказов"]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden px-6",children:(0,r.jsx)("div",{className:"h-full overflow-y-auto",children:(0,r.jsx)("div",{className:"flex flex-col gap-4 pb-6",children:m.map(e=>(0,r.jsx)(o.W,{order:e,onConfirmByDriver:I,onNotifyAcceptance:b,onViewDetails:j,onDriverHeadingToClient:N},e.id))})})})]}):(0,r.jsxs)("div",{className:"p-4 bg-gray-50  rounded-lg",children:[(0,r.jsx)("h3",{className:"font-medium",children:"Нет запланированных поездок"}),(0,r.jsx)("p",{className:"mt-1",children:"На данный момент у вас нет назначенных запланированных поездок."})]})},v=()=>(0,r.jsx)("div",{className:"h-full",children:(0,r.jsx)(h,{})})}},e=>{var s=s=>e(e.s=s);e.O(0,[3257,7261,3766,1938,7914,2906,6467,2634,3822,8128,510,587,8315,7358],()=>s(47328)),_N_E=e.O()}]);