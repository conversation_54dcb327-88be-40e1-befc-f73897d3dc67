import Link from 'next/link';
import { lazy } from 'react';
import type { ITableConfig } from '@shared/ui/table';
import { VehicleStatus } from '@entities/cars/enums';
import {
  getCarColorLabel,
  getVehicleTypeLabel,
  getVehicleStatusLabel,
  getCarColorOptions,
  getVehicleTypeOptions,
  getVehicleStatusOptions,
} from '@entities/cars/helpers';
import type { GetCarDTO } from '@entities/cars/interface';
// Ленивая загрузка иконки редактирования
const EditIcon = lazy(() => import('@shared/icons/EditIcon'));

/**
 * Конфигурация таблицы для списка автомобилей
 */
export const GetCarDTOKeysetPaginationResultTableConfig: ITableConfig<GetCarDTO> = {
  columns: [
    {
      id: 'make',
      label: 'Марка',
      accessor: 'make',
      sortable: true,
      filterable: true,
      filterConfig: {
        fieldType: 'string',
        availableOperators: ['contains', 'startsWith', 'equals', 'notEquals'],
      },
    },
    {
      id: 'model',
      label: 'Модель',
      accessor: 'model',
      sortable: true,
      filterable: true,
      filterConfig: {
        fieldType: 'string',
        availableOperators: ['contains', 'startsWith', 'equals', 'notEquals'],
      },
    },
    {
      id: 'year',
      label: 'Год',
      accessor: 'year',
      sortable: true,
      filterable: true,
      filterConfig: {
        fieldType: 'number',
        availableOperators: ['equals', 'greaterThan', 'lessThan', 'greaterThanOrEqual', 'lessThanOrEqual'],
      },
    },
    {
      id: 'licensePlate',
      label: 'Гос. номер',
      accessor: 'licensePlate',
      sortable: true,
      filterable: true,
      filterConfig: {
        fieldType: 'string',
        availableOperators: ['contains', 'startsWith', 'equals', 'notEquals'],
      },
    },
    {
      id: 'color',
      label: 'Цвет',
      accessor: 'color',
      sortable: true,
      filterable: true,
      filterConfig: {
        fieldType: 'enum',
        enumOptions: getCarColorOptions(),
      },
      renderCell: (car: GetCarDTO) => (
        <span>{getCarColorLabel(car.color)}</span>
      ),
    },
    {
      id: 'type',
      label: 'Тип',
      accessor: 'type',
      sortable: true,
      filterable: true,
      filterConfig: {
        fieldType: 'enum',
        enumOptions: getVehicleTypeOptions(),
      },
      renderCell: (car: GetCarDTO) => (
        <span>{getVehicleTypeLabel(car.type)}</span>
      ),
    },
    {
      id: 'status',
      label: 'Статус',
      accessor: 'status',
      sortable: true,
      filterable: true,
      filterConfig: {
        fieldType: 'enum',
        enumOptions: getVehicleStatusOptions(),
      },
      renderCell: (car: GetCarDTO) => {
        const statusText = getVehicleStatusLabel(car.status);
        let statusClass = '';

        switch (car.status) {
          case VehicleStatus.Available:
            statusClass = 'bg-green-100 text-green-800';
            break;
          case VehicleStatus.Maintenance:
            statusClass = 'bg-yellow-100 text-yellow-800';
            break;
          case VehicleStatus.Repair:
            statusClass = 'bg-red-100 text-red-800';
            break;
          default:
            statusClass = 'bg-gray-100 text-gray-800';
        }

        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusClass}`}>
            {statusText}
          </span>
        );
      },
    },
    {
      id: 'drivers',
      label: 'Водители',
      accessor: 'drivers',
      sortable: false,
      filterable: false,
      renderCell: (car: GetCarDTO) => {
        const driversCount = car.drivers?.length || 0;
        const maxDrivers = 2;
        let statusClass = '';
        let statusText = '';

        if (driversCount === 0) {
          statusClass = 'bg-red-100 text-red-800';
          statusText = 'Нет водителей';
        } else if (driversCount === maxDrivers) {
          statusClass = 'bg-green-100 text-green-800';
          statusText = `${driversCount}/${maxDrivers}`;
        } else {
          statusClass = 'bg-yellow-100 text-yellow-800';
          statusText = `${driversCount}/${maxDrivers}`;
        }

        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusClass}`}>
            {statusText}
          </span>
        );
      },
    },
    {
      id: 'actions',
      label: 'Действия',
      accessor: 'id',
      width: '120px',
      renderCell: (car: GetCarDTO) => (
        <div className="flex justify-end gap-2">
          <Link
            href={`/cars/${car.id}`}
            className="p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105"
            title="Редактировать автомобиль (включая водителей)"
          >
            <EditIcon size={20} className="text-[color:var(--color-primary)]" />
          </Link>
        </div>
      ),
    },
  ],
  // Конфигурация поиска - какие поля доступны для поиска
  searchConfig: {
    searchableFields: [
      {
        field: 'Make',
        label: 'Марка',
        operator: 'contains',
        operatorField: 'MakeOp',
      },
      {
        field: 'Model',
        label: 'Модель',
        operator: 'contains',
        operatorField: 'ModelOp',
      },
      {
        field: 'LicensePlate',
        label: 'Гос. номер',
        operator: 'contains',
        operatorField: 'LicensePlateOp',
      },
    ],
  },
  // Конфигурация фильтров для кнопки "Фильтры"
  filtersConfig: {
    availableFilters: [
      {
        field: 'Status',
        label: 'Статус',
        type: 'select',
        options: getVehicleStatusOptions().map(option => ({
          value: option.value,
          label: option.label,
        })),
      },
      {
        field: 'Type',
        label: 'Тип транспорта',
        type: 'select',
        options: getVehicleTypeOptions().map(option => ({
          value: option.value,
          label: option.label,
        })),
      },
      {
        field: 'Color',
        label: 'Цвет',
        type: 'select',
        options: getCarColorOptions().map(option => ({
          value: option.value,
          label: option.label,
        })),
      },
      {
        field: 'Year',
        label: 'Год выпуска',
        type: 'number',
      },
    ],
  },
};
