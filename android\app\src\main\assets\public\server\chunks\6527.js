"use strict";exports.id=6527,exports.ids=[6527],exports.modules={4004:(e,t,s)=>{s.d(t,{$z:()=>x,Or:()=>i,Z6:()=>c,aZ:()=>a,eX:()=>d,pL:()=>l,uW:()=>n,wp:()=>o});var r=s(96274);s(38013);let a=async e=>(await r.uE.post(`/Order/instant/${e}/accept-by-driver`)).data,d=async e=>{try{console.log("\uD83D\uDCDD Подтверждаем запланированную поездку водителем:",e);let t=await r.uE.post(`/Ride/${e}/accept-by-driver`);return console.log("✅ Поездка подтверждена водителем:",t.data),t.data}catch(e){throw console.error("❌ Ошибка подтверждения поездки водителем:",e),e}},i=async e=>{try{console.log("\uD83D\uDCE2 Уведомляем о принятии запланированной поездки в сведении:",e);let t=await r.uE.post(`/Ride/${e}/notification-by-driver`);console.log("✅ Уведомление отправлено:",t.data)}catch(e){throw console.error("❌ Ошибка отправки уведомления:",e),e}},l=async e=>(await r.uE.post(`/Ride/${e}/reject-by-driver`)).data,c=async e=>(await r.uE.post(`/Ride/${e}/status/driver-heading-to-client`)).data,o=async e=>(await r.uE.post(`/Ride/${e}/status/driver-arrived`)).data,n=async e=>(await r.uE.post(`/Ride/${e}/status/ride-started`)).data,x=async e=>(await r.uE.post(`/Ride/${e}/status/ride-finished`)).data},36706:(e,t,s)=>{s.d(t,{W:()=>d});var r=s(13486);s(60159);var a=s(66971);let d=({order:e,onConfirmByDriver:t,onNotifyAcceptance:s,onViewDetails:d,onDriverHeadingToClient:i})=>{let l=new Date(e.scheduledTime),c=l.toLocaleDateString("ru-RU",{day:"2-digit",month:"2-digit",year:"2-digit"}),o=l.toLocaleTimeString("ru-RU",{hour:"2-digit",minute:"2-digit"}),n=(()=>{let t=(0,a.Vh)(e.orderStatus),s=e.orderSubStatus?(0,a.l1)(e.orderSubStatus):"";switch(e.orderStatus){case"Scheduled":if("DriverAssigned"===e.orderSubStatus)return{text:`${t} (${s})`,color:"orange"};return{text:t,color:"blue"};case"InProgress":return{text:`${t} (${s})`,color:"green"};case"Completed":return{text:t,color:"blue"};case"Cancelled":return{text:t,color:"red"};default:return{text:t,color:"gray"}}})(),x=new Date().getTime(),m=new Date(e.scheduledTime).getTime()-x,u="Scheduled"!==e.orderStatus||"DriverAssigned"!==e.orderSubStatus?[]:"Accepted"===e.status?[{action:"driver-heading",label:"Перейти к активной поездке",color:"green"}]:"Requested"===e.status&&(m<0||m/6e4<=90)?[{action:"accept-by-driver",label:"Еду к клиенту",color:"red"}]:[],h=u.length>0;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg  truncate",children:e.customerName||"-"}),(0,r.jsx)("p",{className:"text-sm mt-1",children:e.customerName?"Контакт в деталях заказа":"-"})]}),(0,r.jsxs)("div",{className:"flex-1 text-center mx-8",children:[(0,r.jsxs)("div",{className:"flex justify-center space-x-8 mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-lg",children:c}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Дата"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-lg",children:o}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Время"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-lg",children:"-"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Рейс"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-center flex-col space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full mr-2 ${"green"===n.color?"bg-green-500":"orange"===n.color?"bg-orange-500":"blue"===n.color?"bg-blue-500":"red"===n.color?"bg-red-500":"bg-gray-500"}`}),(0,r.jsx)("span",{className:"text-sm font-medium",children:n.text})]}),m<0&&"DriverAssigned"===e.orderSubStatus&&(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 animate-pulse",children:"⚠️ Просрочено"})]})]}),(0,r.jsx)("div",{className:"flex-1 min-w-0",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-3 mt-0.5 flex-shrink-0",children:"✈"}),(0,r.jsx)("span",{className:"text-sm  leading-relaxed",children:e.fromAddress||"-"})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-3 mt-0.5 flex-shrink-0",children:"\uD83C\uDFE2"}),(0,r.jsx)("span",{className:"text-sm  leading-relaxed",children:e.toAddress||"-"})]})]})})]}),(0,r.jsx)("div",{className:"mt-6 pt-4 border-t",children:(0,r.jsxs)("div",{className:"flex space-x-3",children:[h&&u.map((s,a)=>(0,r.jsx)("button",{onClick:()=>{switch(s.action){case"accept-by-driver":t?.(e.id,e.orderId||void 0);break;case"driver-heading":i?.(e.id,e.orderId||void 0);break;case"view-active":d?.(e.id,e.orderId||void 0)}},className:`px-6 py-2 rounded-lg font-medium transition-colors ${"red"===s.color?"bg-red-500 hover:bg-red-600 text-white":"orange"===s.color?"bg-orange-500 hover:bg-orange-600 text-white":"blue"===s.color?"bg-blue-500 hover:bg-blue-600 text-white":"bg-green-500 hover:bg-green-600 text-white"}`,children:s.label},a)),(0,r.jsx)("button",{onClick:()=>d?.(e.id,e.orderId||void 0),className:"bg-blue-500 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-600 transition-colors",children:"Перейти к детальному заказу"})]})})]})}}};