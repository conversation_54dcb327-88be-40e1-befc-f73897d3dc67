'use client';

import type { ReactNode } from 'react';
import React from 'react';
import { useTariffs } from '@entities/tariff';
import { TerminalTariffContext } from '@entities/tariff/context';
import type { TerminalTariffContextType } from '@entities/tariff/context';

interface TerminalTariffProviderProps {
  children: ReactNode;
}

/**
 * Провайдер контекста тарифов для терминала
 * Автоматически загружает тарифы и находит эконом седан при монтировании
 */
export const TerminalTariffProvider: React.FC<TerminalTariffProviderProps> = ({ children }) => {
  const { tariffs, isLoading, error, economyTariff } = useTariffs();

  const value: TerminalTariffContextType = {
    economyTariff,
    tariffs,
    isLoading,
    error,
  };

  return (
    <TerminalTariffContext.Provider value={value}>
      {children}
    </TerminalTariffContext.Provider>
  );
}; 