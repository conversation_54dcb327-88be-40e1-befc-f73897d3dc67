'use client';

import { useState, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { useCollectionData } from '@entities/collections/context/ManageCollectionContext';
import { userService } from '@entities/users/api/user-service';
import type { GetDriverDTO } from '@entities/users/interface';
import type { SelectedDrivers, UseDriverAssignmentResult } from '../types';

/**
 * Хук для назначения водителей на смены
 * Отвечает только за логику управления назначениями
 */
export const useDriverAssignment = (): UseDriverAssignmentResult => {
  const { setValue, watch } = useFormContext();
  const { formData } = useCollectionData();

  // Отслеживаем выбранных водителей
  const firstShiftDriverId = watch('firstShiftDriver');
  const secondShiftDriverId = watch('secondShiftDriver');

  // Состояние выбранных водителей
  const [selectedDrivers, setSelectedDrivers] = useState<SelectedDrivers>({});
  const [driversLoaded, setDriversLoaded] = useState(false);

  // Загружаем начальные данные водителей автомобиля
  useEffect(() => {
    const loadCarDrivers = async () => {
      if (
        formData?.drivers &&
        Array.isArray(formData.drivers) &&
        formData.drivers.length > 0 &&
        !driversLoaded
      ) {
        console.log('🚗 Загружаем водителей автомобиля:', formData.drivers);

        setDriversLoaded(true);

        const firstDriver = formData.drivers[0];
        const secondDriver = formData.drivers[1];

        // Загружаем информацию о водителях
        const loadPromises = [];

        if (firstDriver) {
          loadPromises.push(
            userService
              .getDriverById(firstDriver.driverId)
              .then(driver => {
                setSelectedDrivers(prev => ({ ...prev, firstShift: driver }));
                setValue('firstShiftDriver', firstDriver.driverId, { shouldValidate: false });
                setValue('previousFirstShiftDriver', firstDriver.driverId, {
                  shouldValidate: false,
                });
              })
              .catch(err => console.error('Ошибка загрузки водителя 1-й смены:', err)),
          );
        }

        if (secondDriver) {
          loadPromises.push(
            userService
              .getDriverById(secondDriver.driverId)
              .then(driver => {
                setSelectedDrivers(prev => ({ ...prev, secondShift: driver }));
                setValue('secondShiftDriver', secondDriver.driverId, { shouldValidate: false });
                setValue('previousSecondShiftDriver', secondDriver.driverId, {
                  shouldValidate: false,
                });
              })
              .catch(err => console.error('Ошибка загрузки водителя 2-й смены:', err)),
          );
        }

        await Promise.all(loadPromises);
      }
    };

    loadCarDrivers();
  }, [formData?.id, formData?.drivers, driversLoaded, setValue]);

  // Загружаем информацию о выбранных водителях при изменении ID
  useEffect(() => {
    const loadDriverInfo = async (driverId: string, shiftType: 'firstShift' | 'secondShift') => {
      try {
        const driver = await userService.getDriverById(driverId);

        setSelectedDrivers(prev => ({ ...prev, [shiftType]: driver }));
      } catch (error) {
        console.error(`Ошибка загрузки информации о водителе ${driverId}:`, error);
      }
    };

    if (firstShiftDriverId && firstShiftDriverId !== selectedDrivers.firstShift?.id) {
      loadDriverInfo(firstShiftDriverId, 'firstShift');
    }

    if (secondShiftDriverId && secondShiftDriverId !== selectedDrivers.secondShift?.id) {
      loadDriverInfo(secondShiftDriverId, 'secondShift');
    }
  }, [
    firstShiftDriverId,
    secondShiftDriverId,
    selectedDrivers.firstShift?.id,
    selectedDrivers.secondShift?.id,
  ]);

  // Назначить водителя на смену
  const assignDriver = (driver: GetDriverDTO, shift: 'first' | 'second') => {
    if (shift === 'first') {
      // Если водитель уже назначен на вторую смену, убираем его оттуда
      if (driver.id === secondShiftDriverId) {
        setValue('secondShiftDriver', '', { shouldValidate: false });
        setSelectedDrivers(prev => ({ ...prev, secondShift: undefined }));
      }

      setValue('firstShiftDriver', driver.id, { shouldValidate: false });
      setSelectedDrivers(prev => ({ ...prev, firstShift: driver }));
    } else {
      // Если водитель уже назначен на первую смену, убираем его оттуда
      if (driver.id === firstShiftDriverId) {
        setValue('firstShiftDriver', '', { shouldValidate: false });
        setSelectedDrivers(prev => ({ ...prev, firstShift: undefined }));
      }

      setValue('secondShiftDriver', driver.id, { shouldValidate: false });
      setSelectedDrivers(prev => ({ ...prev, secondShift: driver }));
    }
  };

  // Удалить водителя со смены
  const removeDriver = (shift: 'first' | 'second') => {
    if (shift === 'first') {
      setValue('firstShiftDriver', '', { shouldValidate: false });
      setSelectedDrivers(prev => ({ ...prev, firstShift: undefined }));
    } else {
      setValue('secondShiftDriver', '', { shouldValidate: false });
      setSelectedDrivers(prev => ({ ...prev, secondShift: undefined }));
    }
  };

  return {
    selectedDrivers,
    assignDriver,
    removeDriver,
    firstShiftDriverId,
    secondShiftDriverId,
  };
};
