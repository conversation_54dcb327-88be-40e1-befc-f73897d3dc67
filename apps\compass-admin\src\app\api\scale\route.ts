import { NextResponse, type NextRequest } from 'next/server';

export async function GET() {
  return NextResponse.json({ message: 'Scale API endpoint' });
}

export async function POST(request: NextRequest) {
  try {
    const { scale } = await request.json();

    // Валидация значения масштаба
    const scaleValue = parseFloat(scale);

    if (isNaN(scaleValue) || scaleValue < 0.5 || scaleValue > 1.5) {
      return NextResponse.json(
        { error: 'Invalid scale value. Must be between 0.5 and 1.5' },
        { status: 400 },
      );
    }

    // Создаем ответ с установкой куки
    const response = NextResponse.json({
      success: true,
      scale: scaleValue,
      message: 'Scale updated successfully',
    });

    // Устанавливаем куки с масштабом (срок действия 1 год)
    response.cookies.set('ui-scale', scaleValue.toString(), {
      httpOnly: false, // Позволяем читать из клиентского JS
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 365, // 1 год
      path: '/',
    });

    return response;
  } catch (_error) {
    return NextResponse.json({ error: 'Failed to update scale' }, { status: 500 });
  }
}
