import { NextResponse, type NextRequest } from 'next/server';

export async function GET() {
  return NextResponse.json({ message: 'Overlay API endpoint' });
}

export async function POST(request: NextRequest) {
  try {
    const { color, opacity } = await request.json();

    // Валидация цвета
    if (typeof color !== 'string' || color.trim() === '') {
      return NextResponse.json(
        { error: 'Invalid color value. Must be non-empty string' },
        { status: 400 },
      );
    }

    // Валидация прозрачности
    const opacityValue = parseFloat(opacity);

    if (isNaN(opacityValue) || opacityValue < 0 || opacityValue > 1) {
      return NextResponse.json(
        { error: 'Invalid opacity value. Must be between 0 and 1' },
        { status: 400 },
      );
    }

    // Создаем ответ с установкой куки
    const response = NextResponse.json({
      success: true,
      color,
      opacity: opacityValue,
      message: 'Overlay settings updated successfully',
    });

    // Устанавливаем куки с настройками оверлея (срок действия 1 год)
    response.cookies.set('overlay-color', color, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 365, // 1 год
      path: '/',
    });

    response.cookies.set('overlay-opacity', opacityValue.toString(), {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 365, // 1 год
      path: '/',
    });

    return response;
  } catch (_error) {
    return NextResponse.json({ error: 'Failed to update overlay settings' }, { status: 500 });
  }
}
