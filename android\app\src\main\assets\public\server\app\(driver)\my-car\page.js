(()=>{var e={};e.id=2886,e.ids=[2886],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30347:(e,s,t)=>{Promise.resolve().then(t.bind(t,34034))},33732:(e,s,t)=>{"use strict";t.d(s,{MyCarPage:()=>r});let r=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call MyCarPage() from the server but MyCarPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\pages\\driver\\my-car\\MyCarPage.tsx","MyCarPage")},33873:e=>{"use strict";e.exports=require("path")},34034:(e,s,t)=>{"use strict";t.d(s,{MyCarPage:()=>p});var r=t(13486),a=t(60159),i=t(72235),n=t(96274),l=t(50947);class d extends n.vA{async getCar(e){let s=await this.get(`/${e}`);return this.handleApiResult(s)}async getCarSafe(e){let s=await this.get(`/${e}`);return this.handleApiResultSafe(s)}async getCars(e){let s=new URLSearchParams;e?.first!==void 0&&s.append("first",e.first.toString()),e?.before&&s.append("before",e.before),e?.after&&s.append("after",e.after),e?.last!==void 0&&s.append("last",e.last.toString()),e?.size!==void 0&&s.append("size",e.size.toString()),e?.search&&s.append("search",e.search),e?.make&&s.append("make",e.make),e?.model&&s.append("model",e.model),e?.available!==void 0&&s.append("available",e.available.toString()),e?.driverId&&s.append("driverId",e.driverId);let t=s.toString()?`?${s.toString()}`:"",r=await this.get(t);return this.handleApiResult(r)}async getMyCars(){let e=await this.get("/my");return this.handleApiResult(e)}async getMyCarsSafe(){let e=await this.get("/my");return this.handleApiResultSafe(e)}async setActiveCar(e){let s=await this.post(`/my/${e}/set-active`);this.handleApiResult(s)}async setInactiveCar(){let e=await this.post("/my/set-inactive");this.handleApiResult(e)}async createCar(e){let s=await this.post("",e);return this.handleApiResult(s)}async updateCar(e,s){let t=await this.put(`/${e}`,s);return this.handleApiResult(t)}async deleteCar(e){let s=await this.delete(`/${e}`);this.handleApiResult(s)}constructor(...e){super(...e),this.baseUrl=l.QQ.CAR.LIST}}let c=new d,o=()=>{let[e,s]=(0,a.useState)([]),[t,r]=(0,a.useState)(!0),[i,n]=(0,a.useState)(null),l=async()=>{try{r(!0),n(null);let e=await c.getMyCars();s(e.data)}catch(e){n(e.message||"Ошибка загрузки автомобилей")}finally{r(!1)}},d=async e=>{try{await c.setActiveCar(e),await l()}catch(e){throw Error(e.message||"Ошибка активации автомобиля")}},o=async()=>{try{await c.setInactiveCar(),await l()}catch(e){throw Error(e.message||"Ошибка деактивации автомобиля")}};return(0,a.useEffect)(()=>{l()},[]),{cars:e,loading:t,error:i,refetch:l,setActiveCar:d,setInactiveCar:o}},p=()=>{let{cars:e,loading:s,error:t,refetch:n,setActiveCar:l,setInactiveCar:d}=o(),{driverProfile:c,refreshDriverData:p}=(0,i.g0)(),[u,m]=(0,a.useState)(!1),[x,h]=(0,a.useState)(null);(0,a.useLayoutEffect)(()=>{s||m(!0)},[s]);let g=c?.activeCar?.id||c?.activeCarId,y=async(e,s)=>{h(e.id);try{s?await l(e.id):await d(),await p()}catch(e){alert(e.message)}finally{h(null)}};return s||!u?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{children:"Загрузка автомобилей..."})]})}):t?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-2xl",children:"⚠️"})}),(0,r.jsx)("h3",{className:"text-lg font-medium  mb-2",children:"Ошибка загрузки"}),(0,r.jsx)("p",{className:"mb-4",children:t}),(0,r.jsx)("button",{onClick:n,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Попробовать снова"})]})}):(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 p-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Мои автомобили"}),(0,r.jsxs)("p",{children:["Всего автомобилей: ",e.length]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-auto p-4",children:0===e.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h3",{className:"text-lg font-medium  mb-2",children:"У вас нет автомобилей"}),(0,r.jsx)("p",{children:"Обратитесь к администратору для назначения автомобилей"})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:e.map(e=>{let s=g===e.id,t=x===e.id;return(0,r.jsxs)("div",{className:`bg-white rounded-lg shadow border p-4 ${s?"border-blue-500 ring-2 ring-blue-200":"border-gray-200"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold",children:[e.make," ",e.model]}),(0,r.jsxs)("p",{className:"text-sm",children:[e.year," • ",e.licensePlate]})]}),s&&(0,r.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:"Активный"})]}),(0,r.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Цвет:"}),(0,r.jsx)("span",{className:"text-sm",children:e.color})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Тип:"}),(0,r.jsx)("span",{className:"text-sm",children:e.type})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Класс:"}),(0,r.jsx)("span",{className:"text-sm",children:e.serviceClass})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Статус:"}),(0,r.jsx)("span",{className:"text-sm",children:e.status})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Мест:"}),(0,r.jsx)("span",{className:"text-sm",children:e.passengerCapacity})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-3 border-t border-gray-200",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Активировать автомобиль"}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:s,disabled:t,onChange:s=>y(e,s.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 disabled:opacity-50"}),t&&(0,r.jsx)("div",{className:"ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"})]})]})]},e.id)})})})]})}},35923:(e,s,t)=>{Promise.resolve().then(t.bind(t,33732))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63846:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>n});var r=t(38828),a=t(33732);async function i(){return(0,r.jsx)(a.MyCarPage,{})}let n={title:"",description:""}},69412:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>o,routeModule:()=>u,tree:()=>c});var r=t(24332),a=t(48819),i=t(67851),n=t.n(i),l=t(97540),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["(driver)",{children:["my-car",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,63846)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\my-car\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94058)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\my-car\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(driver)/my-car/page",pathname:"/my-car",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[191,5208,9455,6596,4680,99,3639,2960,9100,1738],()=>t(69412));module.exports=r})();