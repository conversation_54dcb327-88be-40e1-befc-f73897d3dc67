(()=>{var e={};e.id=2576,e.ids=[2576],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8869:(e,r,s)=>{"use strict";s.r(r),s.d(r,{ScheduledOrdersPage:()=>h});var t=s(13486),i=s(60159),d=s(2984),a=s(63497),o=s(26134),n=s(28412),c=s(36706),l=s(4004),u=s(95816);let p=({className:e=""})=>{let r=(0,d.useRouter)(),{actions:s}=(0,o.o)(),{setActiveRideData:p,forceUpdateActiveRide:h}=(0,u.c7)(),{data:m,pagination:x,isLoading:v,error:g,refetch:f}=(0,n.C)({size:20,orderStatus:["Scheduled"],orderSubStatus:["DriverAssigned"]}),b=(0,i.useCallback)(async(e,t)=>{try{console.log("✅ Подтверждаем запланированную поездку:",{rideId:e,orderId:t}),await (0,l.eX)(e),await (0,l.Z6)(e),p(t||e,e),a.P.success("Поездка подтверждена"),await s.leaveQueue(),setTimeout(()=>h(),1e3),await f();let i=t?`/active-ride?orderId=${t}&rideId=${e}&orderType=Scheduled`:`/active-ride?rideId=${e}&orderType=Scheduled`;console.log("\uD83D\uDE97 Переходим к активной поездке:",i),r.push(i)}catch(e){console.error("❌ Ошибка подтверждения поездки:",e),a.P.error("Ошибка подтверждения поездки")}},[r,f,s,p,h]),P=async(e,s)=>{try{await (0,l.eX)(e),await (0,l.Z6)(e),a.P.success("Вы направляетесь к клиенту"),r.push(`/active-ride?rideId=${e}${s?`&orderId=${s}`:""}`)}catch(e){a.P.error('Ошибка: не удалось отправить статус "Еду к клиенту"')}},y=(0,i.useCallback)(async(e,r)=>{try{console.log("\uD83D\uDCE9 Отправляем уведомление о принятии:",{rideId:e,orderId:r}),await (0,l.Or)(e),a.P.success("Уведомление отправлено"),await f()}catch(e){console.error("❌ Ошибка отправки уведомления:",e),a.P.error("Ошибка отправки уведомления")}},[f]),j=async(e,s)=>{let t=m?.find(r=>r.id===e);if(t&&"InProgress"===t.orderStatus&&"DriverHeading"===t.orderSubStatus)return void r.push(`/active-ride?rideId=${e}${s?`&orderId=${s}`:""}`);if(t&&"Scheduled"===t.orderStatus&&"DriverAssigned"===t.orderSubStatus&&"Requested"===t.status){let r=new Date(t.scheduledTime).getTime(),s=(r-(Date.now()+216e5))/6e4;if(s<=90&&s>0)try{await (0,l.Z6)(e)}catch(e){a.P.error("Ошибка перехода к активной поездке");return}}r.push(`/active-ride?rideId=${e}${s?`&orderId=${s}`:""}`)};return g?(0,t.jsxs)("div",{className:"p-4 bg-red-50 text-red-800 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Ошибка загрузки данных"}),(0,t.jsx)("p",{className:"mt-1",children:g})]}):v?(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("div",{className:"animate-pulse space-y-4",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"},e))})}):m?.length?(0,t.jsxs)("div",{className:`h-full flex flex-col ${e}`,children:[(0,t.jsxs)("div",{className:"px-6 pt-6 mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold",children:"Запланированные заказы"}),x&&(0,t.jsxs)("p",{className:"mt-1",children:["Показано ",m.length," из ",x.totalCount," заказов"]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-hidden px-6",children:(0,t.jsx)("div",{className:"h-full overflow-y-auto",children:(0,t.jsx)("div",{className:"flex flex-col gap-4 pb-6",children:m.map(e=>(0,t.jsx)(c.W,{order:e,onConfirmByDriver:b,onNotifyAcceptance:y,onViewDetails:j,onDriverHeadingToClient:P},e.id))})})})]}):(0,t.jsxs)("div",{className:"p-4 bg-gray-50  rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Нет запланированных поездок"}),(0,t.jsx)("p",{className:"mt-1",children:"На данный момент у вас нет назначенных запланированных поездок."})]})},h=()=>(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(p,{})})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68160:(e,r,s)=>{"use strict";s.r(r),s.d(r,{ScheduledOrdersPage:()=>t});let t=(0,s(33952).registerClientReference)(function(){throw Error("Attempted to call ScheduledOrdersPage() from the server but ScheduledOrdersPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\pages\\driver\\scheduled-orders\\ScheduledOrdersPage.tsx","ScheduledOrdersPage")},73928:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var t=s(24332),i=s(48819),d=s(67851),a=s.n(d),o=s(97540),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(r,n);let c={children:["",{children:["(driver)",{children:["scheduled-orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,77949)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\scheduled-orders\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94058)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\scheduled-orders\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(driver)/scheduled-orders/page",pathname:"/scheduled-orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{"use strict";e.exports=require("zlib")},75640:(e,r,s)=>{Promise.resolve().then(s.bind(s,68160))},77949:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d,metadata:()=>a});var t=s(38828),i=s(68160);async function d(){return(0,t.jsx)(i.ScheduledOrdersPage,{})}let a={title:"",description:""}},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85368:(e,r,s)=>{Promise.resolve().then(s.bind(s,8869))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[191,5208,9455,6596,4680,99,3639,2960,9100,1738,6527],()=>s(73928));module.exports=t})();