'use client';

import type { ReactNode } from 'react';
import React, { useState, useEffect, useCallback } from 'react';
import { showToast } from '@shared/toast/ToastManager';
import { driverQueueService } from '@entities/ride/api/driver-queue-service';
import type { DriverQueuePosition, DriverQueueItem } from '@entities/ride/api/ride-service';
import { DriverQueueContext } from '@entities/ride/context';
import type { DriverQueueContextType } from '@entities/ride/context';

/**
 * Пропсы провайдера очереди водителей
 */
export interface DriverQueueProviderProps {
  children: ReactNode;
  /** Интервал обновления позиции в миллисекундах */
  refreshInterval?: number;
}

/**
 * Провайдер контекста очереди водителей
 * Использует rideService для работы с очередью
 */
export const DriverQueueProvider: React.FC<DriverQueueProviderProps> = ({
  children,
  refreshInterval = 30000 // 30 секунд
}) => {
  // Состояние
  const [isInQueue, setIsInQueue] = useState<boolean>(false);
  const [position, setPosition] = useState<number | null>(null);
  const [joinedAt, setJoinedAt] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isActionLoading, setIsActionLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Проверка статуса очереди
  const checkQueueStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🔍 Проверяем статус очереди через rideService...');
      
      // Используем ТОЛЬКО rideService - правильная архитектура!
      const queuePosition: DriverQueuePosition | null = await driverQueueService.getMyPosition();

      if (queuePosition) {
        setIsInQueue(true);
        setPosition(queuePosition.position);
        setJoinedAt(queuePosition.joinedAt);
        console.log('✅ Водитель в очереди, позиция:', queuePosition.position);
      } else {
        setIsInQueue(false);
        setPosition(null);
        setJoinedAt(null);
        console.log('ℹ️ Водитель не в очереди');
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Ошибка проверки статуса очереди';

      console.error('❌ Ошибка проверки статуса очереди через rideService:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Встать в очередь
  const joinQueue = useCallback(async () => {
    try {
      setIsActionLoading(true);
      setError(null);
      
      console.log('🚶‍♂️ Встаем в очередь через rideService...');
      
      // Используем ТОЛЬКО rideService - правильная архитектура!
      const result: DriverQueueItem = await driverQueueService.join();

      setIsInQueue(true);
      setJoinedAt(result.joinedAt);
      
      // Обновляем позицию
      await checkQueueStatus();
      showToast.success('Вы встали в очередь');
      console.log('✅ Успешно встали в очередь');
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Ошибка входа в очередь';

      console.error('❌ Ошибка входа в очередь через rideService:', err);
      setError(errorMessage);
      showToast.error(errorMessage);
    } finally {
      setIsActionLoading(false);
    }
  }, [checkQueueStatus]);

  // Выйти из очереди
  const leaveQueue = useCallback(async () => {
    try {
      setIsActionLoading(true);
      setError(null);
      
      console.log('🚪 Выходим из очереди через rideService...');
      
      // Используем ТОЛЬКО rideService - правильная архитектура!
      await driverQueueService.leave();
      
      setIsInQueue(false);
      setPosition(null);
      setJoinedAt(null);
      showToast.success('Вы вышли из очереди');
      console.log('✅ Успешно вышли из очереди');
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Ошибка выхода из очереди';

      console.error('❌ Ошибка выхода из очереди через rideService:', err);
      setError(errorMessage);
      showToast.error(errorMessage);
    } finally {
      setIsActionLoading(false);
    }
  }, []);

  // Обновление статуса
  const refresh = useCallback(async () => {
    await checkQueueStatus();
  }, [checkQueueStatus]);

  // Автоматическая проверка при монтировании
  useEffect(() => {
    checkQueueStatus();
  }, [checkQueueStatus]);

  // Автообновление по интервалу
  useEffect(() => {
    if (refreshInterval > 0 && isInQueue) {
      const interval = setInterval(checkQueueStatus, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, isInQueue, checkQueueStatus]);

  const value: DriverQueueContextType = {
    isInQueue,
    position,
    joinedAt,
    isLoading,
    isActionLoading,
    error,
    actions: {
      joinQueue,
      leaveQueue,
      refresh
    }
  };

  return (
    <DriverQueueContext.Provider value={value}>
      {children}
    </DriverQueueContext.Provider>
  );
}; 