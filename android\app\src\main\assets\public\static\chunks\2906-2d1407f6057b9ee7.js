"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2906],{52906:(e,t,r)=>{let a,n;function i(e,t){for(let r in e)t(e[r],r)}function o(e,t){e.forEach(t)}function l(e,t,r){if(!e)throw Error(`${r?r+": ":""}${t}`)}function s({node:e=[],from:t,source:r,parent:a=t||r,to:n,target:i,child:l=n||i,scope:d={},meta:f={},family:u={type:"regular"},regional:c}={}){let p=er(a),m=er(u.links),h=er(u.owners),g=[];o(e,e=>e&&B(g,e));let y={id:Y(),seq:g,next:er(l),meta:f,scope:d,family:{triggers:p.length,type:u.type||"crosslink",links:m,owners:h}};return o(m,e=>B(N(e),y)),o(h,e=>B(A(e),y)),o(p,e=>B(e.next,y)),c&&K&&et(C(K),[y]),y}function d(e,t,r){let a,i,l,s,d,f,u,c=e_,p=null,m=eF;if(e.target&&(t=e.params,r=e.defer,u=e.meta,c="page"in e?e.page:c,e.stack&&(p=e.stack),m=M(e)||m,e=e.target),m&&eF&&m!==eF&&(eF=null),Array.isArray(e))for(let r=0;r<e.length;r++)eM("pure",c,x(e[r]),p,t[r],m,u);else eM("pure",c,x(e),p,t,m,u);if(r&&!eT)return;let h={isRoot:eT,currentPage:e_,scope:eF,isWatch:ej,isPure:eD};eT=0;e:for(;s=eE();){let{idx:e,stack:t,type:r}=s;l=t.node,e_=d=t.page,eF=M(t),d?f=d.reg:eF&&(f=eF.reg);let u=!!d,c=!!eF,p={fail:0,scope:l.scope};a=i=0;for(let n=e;n<l.seq.length&&!a;n++){let o=l.seq[n];if(o.order){let{priority:a,barrierID:i}=o.order,l=i?d?`${d.fullID}_${i}`:i:0;if(n!==e||r!==a){i?eI.has(l)||(eI.add(l),eR(n,t,a,i)):eR(n,t,a,0);continue e}i&&eI.delete(l)}switch(o.type){case"mov":{let e,r=o.data;switch(r.from){case"stack":e=C(t);break;case"a":case"b":e=t[r.from];break;case"value":e=r.store;break;case"store":if(f&&!f[r.store.id])if(u){let e=eV(d,r.store.id);t.page=d=e,e?f=e.reg:c?(eW(eF,r.store,0,1,r.softRead),f=eF.reg):f=void 0}else c&&eW(eF,r.store,0,1,r.softRead);e=eS(f&&f[r.store.id]||r.store)}switch(r.to){case"stack":t.value=e;break;case"a":case"b":t[r.to]=e;break;case"store":eL(d,eF,r.target,0).current=e}break}case"compute":let s=o.data;if(s.fn){ej="watch"===l.meta.op,eD=s.pure;let e=s.safe?(0,s.fn)(C(t),p.scope,t):eX(p,s.fn,t);s.filter?i=!e:t.value=e,ej=h.isWatch,eD=h.isPure}}a=p.fail||i}if(n&&n(t,p),!a){let e=C(t),r=M(t);if(o(l.next,a=>{eM("child",d,a,t,e,r)}),r){l.meta.needFxCounter&&eM("child",d,r.fxCount,t,e,r),l.meta.storeChange&&eM("child",d,r.storeChange,t,e,r),l.meta.warnSerialize&&eM("child",d,r.warnSerializeNode,t,e,r);let a=r.additionalLinks[l.id];a&&o(a,a=>{eM("child",d,a,t,e,r)})}}}eT=h.isRoot,e_=h.currentPage,eF=M(h)}r.d(t,{AX:()=>$,EH:()=>y,GY:()=>k,XM:()=>b,is:()=>P,lh:()=>m,y$:()=>g});function f(e,t){if(!t||!t.name&&!t.named&&!t.loc)return e;let r=`[${e}]`,a=t.named||t.name;a&&(r+=` unit '${a}'`);let n=t.loc;return!a&&n&&(r+=` (${n.file}:${n.line}:${n.column})`),r}function u(e,t){let r=t?e:e[0];eo(r);let a=r.or,n=r.and;if(n){let r=t?n:n[0];if(ea(r)&&"and"in r){let r=u(n,t);e=r[0],a={...a,...r[1]}}else e=n}return[e,a]}function c(e){let t=()=>e();return t.unsubscribe=()=>e(),t}function p(e,...t){let r=Z();if(r){let a=r.handlers[e];if(a)return a(r,...t)}}function m(e,t){let r=eG({or:t,and:"string"==typeof e?{name:e}:e}),a=f("event",r),n=(e,...t)=>(l(!R(n,"derived"),"call of derived event is not supported, use createEvent instead",a),l(!eD,"unit call from pure function is not supported, use operators like sample instead",a),e_?((e,t,r,a)=>{let n=e_,i=null;if(t)for(i=e_;i&&i.template!==t;)i=E(i);eP(i);let o=e.create(r,a);return eP(n),o})(n,i,e,t):n.create(e,t)),i=Z(),o=Object.assign(n,{graphite:s({meta:e2(r.actualOp||"event",n,r),regional:1}),create:e=>(d({target:n,params:e,scope:eF}),e),watch:e=>e1(n,e),map:e=>e6(n,S,e,[ek()]),filter:e=>e6(n,"filter",e.fn?e:e.fn,[ek(ec,1)]),filterMap:e=>e6(n,"filterMap",e,[ek(),ev(e=>!ei(e),1)]),prepend(e){l(n.targetable,".prepend of derived event is not supported, call source event instead",a);let t=m("* → "+n.shortName,{parent:E(n)});return p("eventPrepend",x(t)),eZ(t,n,[ek()],"prepend",e),e0(n,t),t}});return null!=r&&r.domain&&r.domain.hooks.event(o),q(o,"id",o.graphite.id),Q(o.graphite),o}function h(e,t,r,a,n){return es(r,`${n} ${t}`,"first argument"),l(en(a),"second argument should be a function",n),W(!R(e,"derived"),`${t} in derived store`,`${t} in store created via createStore`,n),o(Array.isArray(r)?r:[r],t=>{e.off(t),e3(t,e,"on",eu,a)}),e}function g(e,t){let r=eG(t),a=ew(e),n=f("store",r),i=Error();Error.captureStackTrace&&Error.captureStackTrace(i,g);let u=i.stack,c=m({named:"updates",derived:1});p("storeBase",a);let y=a.id,v="skipVoid"in r,b=v&&!r.skipVoid;W(!(v&&r.skipVoid),"{skipVoid: true}","updateFilter",n);let k={updates:c,defaultState:e,stateRef:a,getState(){let e,t=a;if(e_){let t=e_;for(;t&&!t.reg[y];)t=E(t);t&&(e=t)}return!e&&eF&&(eW(eF,a,1),e=eF),e&&(t=e.reg[y]),eS(t)},setState:e=>d({target:k,params:e,defer:1,scope:eF}),reset:(...e)=>(l(k.targetable,".reset of derived store is not supported",n),o(e,e=>h(k,".reset",e,()=>k.defaultState,n)),k),on:(e,t)=>(l(k.targetable,".on of derived store is not supported",n),h(k,".on",e,t,n)),off(e){let t=x(e).id,r=x(k).family.links.find(e=>e.meta.onTrigger===t);return r&&eK(r),k},map(e,t){let r,n;ea(e)&&(r=e,e=e.fn);let i=k.getState(),o=ei(i);Z()?n=null:(!o||o&&b)&&(n=e(i));let l=g(n,{name:`${k.shortName} \u2192 *`,derived:1,...t,and:r}),s=e3(k,l,S,ec,e);return ex(z(l),{type:S,fn:e,from:a}),z(l).noInit=1,p("storeMap",a,s),l},watch(e,t){if(W(!t,"watch second argument","sample",n),!t||!F(e)){let t=e1(k,e);return p("storeWatch",a,e)||e(k.getState()),t}return l(en(t),"second argument should be a function",n),e.watch(e=>t(k.getState(),e))}},$=e2("store",k,r),w=k.defaultConfig.updateFilter;k.graphite=s({scope:{state:a,fn:w},node:[ev((e,t,r)=>(r.scope&&!r.scope.reg[a.id]&&(r.b=1),e)),eb(a),ev((e,t,{a:r,b:a})=>{let i=ei(e);return i&&!v&&X(`${n}: ${e9}`,u),(i&&b||!i)&&(e!==r||a)},1),w&&ek(ef,1),eh({from:"stack",target:a})],child:c,meta:{...$,defaultState:e,storeTrace:u},regional:1}),q(k,"id",k.graphite.id),q(k,"rootStateRefId",y);let N=R(k,"serialize"),A=R(k,"derived"),C=R(k,"sid");C&&(q(k,"storeChange",1),a.sid=C),C||"ignore"===N||A||q(k,"warnSerialize",1);let M=ei(e);return l(A||!M||M&&b,e9,n),A&&M&&!v&&console.error(`${n}: ${e9}`),et(k,[c]),null!=r&&r.domain&&r.domain.hooks.store(k),A||(k.reinit=m({named:"reinit"}),k.reset(k.reinit)),a.meta=k.graphite.meta,Q(k.graphite),k}function y(e,t={}){let r=eG(en(e)?{handler:e}:e,t),a=f("effect",r),n=m(en(e)?{handler:e}:e,{...t,actualOp:"effect"}),i=x(n);q(i,"op",n.kind="effect"),n.use=e=>(l(en(e),".use argument should be a function",a),v.scope.handler=e,n),n.use.getCurrent=()=>v.scope.handler;let o=n.finally=m({named:"finally",derived:1}),u=n.done=o.filterMap({named:"done",fn({status:e,params:t,result:r}){if("done"===e)return{params:t,result:r}}}),c=n.fail=o.filterMap({named:"fail",fn({status:e,params:t,error:r}){if("fail"===e)return{params:t,error:r}}}),p=n.doneData=u.map({named:"doneData",fn:({result:e})=>e}),h=n.failData=c.map({named:"failData",fn:({error:e})=>e}),v=s({scope:{handler:n.defaultConfig.handler||(()=>l(0,`no handler used in ${n.compositeName.fullName}`))},node:[ev((e,t,r)=>{let a=t.handler,i=M(r);if(i){let e=i.handlers.unitMap.get(n)||i.handlers.sidMap[n.sid];e&&(a=e)}return e.handler=a,e},0,1),ev((e,t,r)=>{if(t.runnerFn&&!t.runnerFn(e,null,r))return;let{params:a,req:n,handler:i,args:l=[a]}=e,s=e7(a,n,1,o,r),d=e7(a,n,0,o,r),[f,u]=e5(i,d,l);f&&(ea(u)&&en(u.then)?u.then(s,d):s(u))},0,1)],meta:{op:"fx",fx:"runner"},regional:1});i.scope.runner=v,B(i.seq,ev((e,{runner:t},r)=>{let a=E(r)?{params:e,req:{rs(e){},rj(e){}}}:e;return r.meta||(r.meta={fxID:J()}),d({target:t,params:a,defer:1,scope:M(r),meta:r.meta}),a.params})),n.create=e=>{let t=function(){let e={};return e.req=new Promise((t,r)=>{e.rs=t,e.rj=r}),e.req.catch(()=>{}),e}();if(eF&&!ej){let e=eF;t.req.finally(()=>{eO(e)}).catch(()=>{})}return d({target:n,params:{params:e,req:t},scope:eF}),t.req};let b=n.inFlight=g(0,{serialize:"ignore",named:(R(n,"name")||n.graphite.id)+".inFlight"}).on(n,e=>e+1).on(o,e=>e-1).map({fn:e=>e,named:"inFlight"});q(o,"needFxCounter","dec"),q(n,"needFxCounter",1);let k=n.pending=b.map({fn:e=>e>0,named:"pending"});return et(n,[o,u,c,p,h,k,b]),null!=r&&r.domain&&r.domain.hooks.effect(n),n}function v(e,t){let r=eG({or:t,and:"string"==typeof e?{name:e}:e}),a=s({family:{type:"domain"},regional:1,parent:(null==r?void 0:r.domain)||(null==r?void 0:r.parent)}),n={history:{},graphite:a,hooks:{}};a.meta=e2("domain",n,{parent:(null==r?void 0:r.domain)||(null==r?void 0:r.parent),or:{...r,derived:1}}),i({Event:m,Effect:y,Store:g,Domain:v},(e,t)=>{let r=t.toLowerCase(),a=m({named:`on${t}`});n.hooks[r]=a;let i=new Set;n.history[`${r}s`]=i,a.create=e=>(d(a,e),e),B(x(a).seq,ev((e,t,r)=>(r.scope=null,e))),a.watch(e=>{et(n,[e]),i.add(e),e.ownerSet||(e.ownerSet=i),E(e)||(e.parent=n)}),et(n,[a]),n[`onCreate${t}`]=e=>(o(i,e),a.watch(e)),n[`create${t}`]=n[r]=(t,r)=>{let i=eG({and:r,or:t});return null!=i&&i.domain?e(t,r):a(e(t,{parent:n,or:i}))}});let l=E(n);return l&&i(n.hooks,(e,t)=>eZ(e,l.hooks[t])),null!=r&&r.domain&&r.domain.hooks.domain(n),n}function b(...e){var t,r;let a,n,i,s,d,[[c,p,m],h]=u(e),g=1,y=f("sample",h);return ei(p)&&ea(c)&&(t=c,r=y,a=0,o(te,e=>{e in t&&(l(null!=t[e],tt(r,e)),a=1)}),a)&&(p=c.clock,m=c.fn,"batch"in c?g=c.batch:(W(!("greedy"in c),"greedy in sample","batch",y),g=!c.greedy),d=c.filter,n=c.target,i=c.name,s=c.sid,c=c.source),tr("sample",p,c,d,n,m,i,h,g,1,0,s)}function k(e,{scope:t,safe:r}={}){l(t||eF||r,"scopeBind: scope not found");let a=t||eF;return(...t)=>{function r(){eO(o)}let n,i=0,o=eF;eO(a);try{n=e(...t)}catch(e){n=e,i=1}if(r(),i)throw n;return n instanceof Promise&&n.then(r,r),n}}function $({unit:e,fn:t,scope:r,batch:a}){let n=[e$.run({fn:e=>t(e)})];a&&n.unshift(e$.compute({priority:"sampler",batch:1})),j(e)&&n.unshift(e$.mov({store:e.stateRef,to:"stack"}));let i=Array.isArray(e)?e:[e];if(r){let e=[],t=r.additionalLinks;return i.forEach(r=>{var a,i;let o=t[r.graphite.id]||[];t[r.graphite.id]=o;let l=s({node:(a=n,j(i=r)?[e$.mov({store:i.stateRef,to:"stack"}),...a]:a),meta:{watchOp:r.kind}});o.push(l),e.push(()=>{let e=o.indexOf(l);-1!==e&&o.splice(e,1),eK(l)})}),c(()=>{e.forEach(e=>e())})}{let e=s({node:n,parent:i,family:{owners:i}});return c(()=>{eK(e)})}}let w="undefined"!=typeof Symbol&&Symbol.observable||"@@observable",S="map",x=e=>e.graphite||e,N=e=>e.family.owners,A=e=>e.family.links,z=e=>e.stateRef,C=e=>e.value,E=e=>e.parent,M=e=>e.scope,R=(e,t)=>x(e).meta[t],q=(e,t,r)=>x(e).meta[t]=r,I=e=>e.compositeName,F=e=>(en(e)||ea(e))&&"kind"in e,T=e=>t=>F(t)&&t.kind===e,j=T("store"),D=T("event"),_=T("effect"),O=T("domain");var P={__proto__:null,unit:F,store:j,event:D,effect:_,targetable:e=>F(e)&&!!e.targetable,domain:O,scope:T("scope"),attached:e=>_(e)&&1==R(e,"attached")};let V=(e,t)=>e.includes(t),L=(e,t)=>{let r=e.indexOf(t);-1!==r&&e.splice(r,1)},B=(e,t)=>e.push(t),W=(e,t,r,a)=>!e&&console.error(`${a?a+": ":""}${t} is deprecated${r?`, use ${r} instead`:""}`),X=(e,t)=>{let r=Error(e);r.stack=t,console.error(r)},G=()=>{let e=0;return()=>""+ ++e},H=G(),U=G(),Y=G(),J=G(),K=null,Q=e=>{a&&a(e,K)},Z=()=>K&&K.template,ee=e=>(e&&K&&K.sidRoot&&(e=`${K.sidRoot}|${e}`),e),et=(e,t)=>{let r=x(e);o(t,e=>{let t=x(e);"domain"!==r.family.type&&(t.family.type="crosslink"),B(N(t),r),B(A(r),t)})},er=(e=[])=>(Array.isArray(e)?e:[e]).flat().map(x),ea=e=>"object"==typeof e&&null!==e,en=e=>"function"==typeof e,ei=e=>void 0===e,eo=e=>l(ea(e)||en(e),"expect first argument be an object"),el=(e,t,r,a)=>l(!(!ea(e)&&!en(e)||!("family"in e)&&!("graphite"in e)),`${t}: expect ${r} to be a unit (store, event or effect)${a}`),es=(e,t,r)=>{Array.isArray(e)?o(e,(e,a)=>el(e,t,`${a} item of ${r}`,"")):el(e,t,r," or array of units")},ed=(e,t,r="target")=>o(er(t),t=>l(!R(t,"derived"),`${e}: derived unit in "${r}" is not supported, use createStore/createEvent instead"`)),ef=(e,{fn:t},{a:r})=>t(e,r),eu=(e,{fn:t},{a:r})=>t(r,e),ec=(e,{fn:t})=>t(e),ep=(e,t,r,a)=>{let n={id:U(),type:e,data:t};return r&&(n.order={priority:r},a&&(n.order.barrierID=++em)),n},em=0,eh=({from:e="store",store:t,target:r,to:a=r?"store":"stack",batch:n,priority:i})=>ep("mov",{from:e,store:t,to:a,target:r},i,n),eg=({fn:e,batch:t,priority:r,safe:a=0,filter:n=0,pure:i=0})=>ep("compute",{fn:e,safe:a,filter:n,pure:i},r,t),ey=({fn:e})=>eg({fn:e,priority:"effect"}),ev=(e,t,r)=>eg({fn:e,safe:1,filter:t,priority:r&&"effect"}),eb=(e,t,r)=>eh({store:e,to:t?"stack":"a",priority:r&&"sampler",batch:1}),ek=(e=ec,t)=>eg({fn:e,pure:1,filter:t}),e$={mov:eh,compute:eg,run:ey},ew=e=>({id:U(),current:e,initial:e}),eS=({current:e})=>e,ex=(e,t)=>{e.before||(e.before=[]),B(e.before,t)},eN=null,eA=(e,t)=>{let r;return e?(t&&((e.v.type===t.v.type&&e.v.id>t.v.id||eq(e.v.type)>eq(t.v.type))&&(r=e,e=t,t=r),r=eA(e.r,t),e.r=e.l,e.l=r),e):t},ez=[],eC=0;for(;eC<6;)B(ez,{first:null,last:null,size:0}),eC+=1;let eE=()=>{for(let e=0;e<6;e++){let t=ez[e];if(t.size>0){if(3===e||4===e){t.size-=1;let e=eN.v;return eN=eA(eN.l,eN.r),e}1===t.size&&(t.last=null);let r=t.first;return t.first=r.r,t.size-=1,r.v}}},eM=(e,t,r,a,n,i,o)=>eR(0,{a:null,b:null,node:r,parent:a,value:n,page:t,scope:i,meta:o},e,0),eR=(e,t,r,a)=>{let n=eq(r),i=ez[n],o={v:{idx:e,stack:t,type:r,id:a},l:null,r:null};3===n||4===n?eN=eA(eN,o):(0===i.size?i.first=o:i.last.r=o,i.last=o),i.size+=1},eq=e=>{switch(e){case"child":return 0;case"pure":return 1;case"read":return 2;case"barrier":return 3;case"sampler":return 4;case"effect":return 5;default:return -1}},eI=new Set,eF,eT=1,ej=0,eD=0,e_=null,eO=e=>{eF=e},eP=e=>{e_=e},eV=(e,t)=>{if(e){for(;e&&!e.reg[t];)e=e.parent;if(e)return e}return null},eL=(e,t,r,a)=>{let n=eV(e,r.id);return n?n.reg[r.id]:t?(eW(t,r,a),t.reg[r.id]):r},eB=e=>e,eW=(e,t,r,a,n)=>{let i=e.reg;if(i[t.id])return;let l=t.sid,s={id:t.id,current:t.initial,meta:t.meta};if(s.id in e.values.idMap)s.current=e.values.idMap[s.id];else if(l&&l in e.values.sidMap&&!(l in e.sidIdMap)){var d;let r=null==t||null==(d=t.meta)?void 0:d.serialize;s.current=(e.fromSerialize&&"ignore"!==r&&(null==r?void 0:r.read)||eB)(e.values.sidMap[l])}else if(t.before&&!n){let n=0,l=r||!t.noInit||a;o(t.before,t=>{switch(t.type){case"map":{let n=t.from;if((n||t.fn)&&(n&&eW(e,n,r,a),l)){let e=n&&i[n.id].current;s.current=t.fn?t.fn(e):e}break}case"field":eW(e,t.from,r,a),n||(n=1,s.current=Array.isArray(s.current)?[...s.current]:{...s.current}),l&&(s.current[t.field]=i[i[t.from.id].id].current)}})}l&&(e.sidIdMap[l]=t.id),i[t.id]=s},eX=(e,t,r)=>{try{return t(C(r),e.scope,r)}catch(t){console.error(t),e.fail=1,e.failReason=t}},eG=(e,t={})=>(ea(e)&&(eG(e.or,t),i(e,(e,r)=>{ei(e)||"or"===r||"and"===r||(t[r]=e)}),eG(e.and,t)),t),eH=(e,t)=>{L(e.next,t),L(N(e),t),L(A(e),t)},eU=["on","reset","sample","split","merge","guard","forward"],eY=(e,t,r,a,n)=>{let i;e.next.length=0,e.seq.length=0,e.scope=null;let o=A(e),l=e.meta.isRegion,s=l?e:a;if(o.length>0){let a=V(eU,e.meta.op),d=!l&&!n,f=d&&r&&!a;for(;i=o.pop();){let o=V(i.next,e);eH(i,e),l&&eY(i,0,0,e,1),o||(i.family.triggers-=1),(t||f||d&&"crosslink"===i.family.type&&!a||n&&V(eU,i.meta.op)&&(o&&0===i.next.length||!o&&i.family.triggers<=0))&&eY(i,t,r&&"on"!==i.meta.op,s,n)}}for(o=N(e);i=o.pop();)eH(i,e),r&&"crosslink"===i.family.type&&eY(i,t,"on"!==i.meta.op,s,n)},eJ=e=>e.clear(),eK=(e,{deep:t}={})=>{let r=0;if(e.ownerSet&&e.ownerSet.delete(e),O(e)){r=1;let t=e.history;eJ(t.events),eJ(t.effects),eJ(t.stores),eJ(t.domains)}eY(x(e),!!t,r,null,0)},eQ=e=>c(()=>eK(e)),eZ=(e,t,r,a,n)=>s({node:r,parent:e,child:t,scope:{fn:n},meta:{op:a},family:{owners:[e,t],links:t},regional:1}),e1=(e,t)=>(l(en(t),".watch argument should be a function"),eQ(s({scope:{fn:t},node:[ey({fn:ec})],parent:e,meta:{op:"watch"},family:{owners:e},regional:1}))),e0=(e,t,r="event")=>{E(e)&&E(e).hooks[r](t)},e2=(e,t,r)=>{let a=eG(r),n="domain"===e,i=H(),{sid:o=null,named:l=null,domain:s=null,parent:d=s}=a,f=l||a.name||(n?"":i),u=function(e,t){let r,a;if(t){let n=I(t);0===e.length?(r=n.path,a=n.fullName):(r=n.path.concat([e]),a=0===n.fullName.length?e:n.fullName+"/"+e)}else r=0===e.length?[]:[e],a=e;return{shortName:e,fullName:a,path:r}}(f,d),c={op:t.kind=e,name:t.shortName=f,sid:t.sid=ee(o),named:l,unitId:t.id=i,serialize:a.serialize,derived:a.derived,config:a};if(t.targetable=!a.derived,t.parent=d,t.compositeName=u,t.defaultConfig=a,t.getType=()=>(W(0,"getType","compositeName.fullName"),u.fullName),!n){t.subscribe=e=>(eo(e),t.watch(en(e)?e:t=>e.next&&e.next(t))),t[w]=()=>t;let e=Z();e&&(c.nativeTemplate=e)}return c},e6=(e,t,r,a)=>{let n;ea(r)&&(n=r,r=r.fn);let i=m({name:`${e.shortName} \u2192 *`,derived:1,and:n});return eZ(e,i,a,t,r),i},e9="undefined is used to skip updates. To allow undefined as a value provide explicit { skipVoid: false } option",e3=(e,t,r,a,n)=>{let i=z(t),o=eh({store:i,to:"a",priority:"read"});r===S&&(o.data.softRead=1);let l=[o,ek(a)];p("storeOnMap",i,l,j(e)&&z(e));let s=eZ(e,t,l,r,n);return r!==S&&q(s,"onTrigger",x(e).id),s},e4=(e,t,r,a,n,o)=>{let s=f("combine",a),d=e?e=>[...e]:e=>({...e}),u=e?[]:{},c=d(u),m=ew(c),h=ew(1);m.type=e?"list":"shape",m.noInit=1,p("combineBase",m,h);let y=g(c,{name:function(e,t="combine"){let r=t+"(",a="",n=0;return i(e,e=>{n<25&&(null!=e&&(r+=a,r+=F(e)?I(e).fullName:e.toString()),n+=1,a=", ")}),r+")"}(r),derived:1,...o,and:a}),v=z(y);v.noInit=1,q(y,"isCombine",1);let b=eb(m);b.order={priority:"barrier"};let k=eh({store:v,to:"b",priority:"read"});k.data.softRead=1;let $=[ev((e,t,r)=>(r.scope&&!r.scope.reg[m.id]&&(r.c=1),e)),b,eh({store:h,to:"b"}),ev((e,{key:r},a)=>{if(a.c||e!==a.a[r])return t&&a.b&&(a.a=d(a.a)),a.a[r]=e,1},1),eh({from:"a",target:m}),eh({from:"value",store:0,target:h}),eh({from:"value",store:1,target:h,priority:"barrier",batch:1}),eb(m,1,1),n&&ek(),k];if(i(r,(e,t)=>{if(!j(e))return l(!F(e)&&!ei(e),`combine expects a store in a field ${t}`,s),void(c[t]=u[t]=e);u[t]=e.defaultState,c[t]=e.getState();let r=eZ(e,y,$,"combine",n);r.scope.key=t;let a=z(e);ex(m,{type:"field",field:t,from:a}),p("combineField",a,r)}),y.defaultShape=r,ex(v,{type:S,from:m,fn:n}),!Z())if(n){let e=n(c);!ei(e)||o&&"skipVoid"in o||console.error(`${s}: ${e9}`),v.current=e,v.initial=e,y.defaultState=e}else y.defaultState=u;return y},e5=(e,t,r)=>{try{return[1,e(...r)]}catch(e){return t(e),[0,null]}},e7=(e,t,r,a,n)=>i=>{d({target:[a,e8],params:[r?{status:"done",params:e,result:i}:{status:"fail",params:e,error:i},{value:i,fn:r?t.rs:t.rj}],defer:1,page:n.page,scope:n.scope,meta:n.meta})},e8=s({node:[ey({fn:({fn:e,value:t})=>e(t)})],meta:{op:"fx",fx:"sidechain"}}),te=["source","clock","target"],tt=(e,t)=>e+`: ${t} should be defined`,tr=(e,t,r,a,n,i,o,s,d,c,h,y)=>{let v=f(e,s),b=!!n;l(!ei(r)||!ei(t),tt(v,"either source or clock"));let k=0;ei(r)?k=1:F(r)||(r=function(...e){let t,r,a,n,i,o;[e,a]=u(e);let s=f("combine",a),d=e[e.length-1],c=e.length>1&&!j(d)&&ea(d),p=c&&d,m=c?e[e.length-2]:d;if(en(m)?(r=e.slice(0,c?-2:-1),t=m):r=e,1===r.length){let e=r[0];j(e)||(n=e,i=1)}if(!i&&(n=r,t)){o=1;let e=t;t=t=>e(...t)}return l(ea(n),`${s}: shape should be an object`),e4(Array.isArray(n),!o,n,a,t,p)}(r)),ei(t)?t=r:(es(t,v,"clock"),Array.isArray(t)&&(t=eZ(t,[],[],e))),k&&(r=t),s||o||(o=r.shortName);let $="none";(h||a)&&(F(a)?$="unit":(l(en(a),"`filter` should be function or unit"),$="fn")),n?(es(n,v,"target"),ed(v,n)):"none"===$&&c&&j(r)&&j(t)?n=g(i?i(eS(z(r)),eS(z(t))):eS(z(r)),{name:o,sid:y,or:s}):p("sampleTarget",x(n=m({name:o,derived:1,or:s})));let w=ew(),S=[];if("unit"===$){let[r,i,o]=tn(a,n,t,w,e);o||S.push(...ta(i)),S.push(...ta(r))}let N=[];if(k)d&&N.push(eb(w,1,1));else{let[a,i,o]=tn(r,n,t,w,e);o||N.push(...ta(i)),N.push(eb(a,1,d))}let A=eZ(t,n,[p("sampleSourceLoader"),eh({from:"stack",target:w}),...N,...S,eb(w),"fn"===$&&ek((e,t,{a:r})=>a(e,r),1),i&&ek(ef),p("sampleSourceUpward",b)],e,i);return et(r,[A]),Object.assign(A.meta,s,{joint:1}),n},ta=e=>[eb(e),ev((e,t,{a:r})=>r,1)],tn=(e,t,r,a,n)=>{let i=j(e),o=i?z(e):ew(),l=ew(i);return i||s({parent:e,node:[eh({from:"stack",target:o}),eh({from:"value",store:1,target:l})],family:{owners:[...new Set([e,t,r].flat())],links:t},meta:{op:n},regional:1}),p("sampleSource",l,o,a),[o,l,i]}}}]);