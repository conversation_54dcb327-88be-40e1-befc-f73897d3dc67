'use client';

import { useState, useCallback, useEffect, useRef, type ReactNode } from 'react';
import { SignalRContext, type SignalRContextType } from '@entities/ws/context/SignalRContext';
import type { SignalREventHandler, SignalRCallback, SignalREventData } from '@entities/ws/context/types/signalr.types';
import { notificationManager } from '@features/notifications/services/NotificationManager';

export interface SignalRProviderProps {
  children: ReactNode;
  accessToken?: string;
}

export const SignalRProvider: React.FC<SignalRProviderProps> = ({ children, accessToken }) => {
  const [connection, setConnection] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const eventHandlers = useRef<Map<string, SignalRCallback[]>>(new Map());

  const setupNotificationHandlers = useCallback(() => {
    const notificationTypes = [
      'RideRequestNotification',
      'RideAcceptedNotification',
      'RideAssignedNotification',
      'RideCancelledNotification',
      'RideCompletedNotification',
      'DriverActivityUpdated',
      'DriverArrivedNotification',
      'DriverHeadingNotification',
      'DriverCancelledNotification',
      'OrderConfirmedNotification',
      'OrderCancelledNotification',
      'OrderCompletedNotification',
      'RideRejectedNotification',
      'RideStartedNotification',
      'PaymentNotification',
      'PaymentReceivedNotification',
      'PaymentFailedNotification'
    ];

    notificationTypes.forEach(type => {
      const handleNotification = (data: SignalREventData) => {
        console.log(`WebSocket уведомление [${type}]:`, data);
        notificationManager.handleNotification(type, data);
      };
      const handlers = eventHandlers.current.get(type) || [];

      handlers.push(handleNotification);
      eventHandlers.current.set(type, handlers);
    });
    console.log('Подписка на уведомления активирована');
  }, []);

  const connect = useCallback(async (): Promise<void> => {
    try {
      setIsConnecting(true);
      setError(null);
      if (!accessToken) {
        throw new Error('JWT токен не найден');
      }
      setupNotificationHandlers();
      const wsBaseUrl = process.env.NEXT_PUBLIC_WS_BASE_URL!;
      const wsUrl = `${wsBaseUrl}?access_token=${accessToken}`;
      const newConnection = new WebSocket(wsUrl);

      newConnection.onopen = () => {
        console.log('WebSocket подключен');
        newConnection.send('{"protocol":"json","version":1}\x1e');
        setConnection(newConnection);
        setIsConnected(true);
        setIsConnecting(false);
      };
      newConnection.onclose = (event) => {
        console.log('WebSocket соединение закрыто:', event);
        setIsConnected(false);
        setConnection(null);
      };
      newConnection.onerror = (error) => {
        console.error('Ошибка WebSocket:', error);
        setError('Ошибка подключения к WebSocket');
        setIsConnecting(false);
      };
      newConnection.onmessage = (event) => {
        try {
          console.log('Получено сообщение WebSocket:', event.data);
          // Удаляем разделитель SignalR (\x1e) в конце сообщения
          const cleanData = event.data.replace(/\x1e$/, '');

          // Пропускаем пустые сообщения и handshake
          if (!cleanData || cleanData === '{}') {
            console.log('Handshake успешно завершен');

            return;
          }
          if (cleanData.includes('"error"')) {
            const errorMessage = JSON.parse(cleanData);

            console.error('Ошибка от сервера:', errorMessage);
            setError(errorMessage.error || 'Ошибка сервера');
            setIsConnected(false);
            setConnection(null);

            return;
          }
          const message = JSON.parse(cleanData);

          if (message.type === 1 && message.target && message.arguments) {
            const eventType = message.target;
            const eventData = message.arguments[0];

            console.log(`Обработка события [${eventType}]:`, eventData);
            if (eventHandlers.current.has(eventType)) {
              const handlers = eventHandlers.current.get(eventType) || [];

              console.log(`ВЫЗЫВАЮ ${handlers.length} обработчиков для ${eventType}`);
              handlers.forEach((handler) => {
                handler(eventData);
              });
            } else {
              console.log(`НЕТ ОБРАБОТЧИКОВ для события ${eventType}`);
            }
          }
        } catch (err) {
          console.error('Ошибка парсинга сообщения WebSocket:', err, 'Данные:', event.data);
        }
      };

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Ошибка подключения');
      setIsConnecting(false);
    }
  }, [accessToken, setupNotificationHandlers]);

  const disconnect = useCallback(async (): Promise<void> => {
    if (connection) {
      connection.close();
      setConnection(null);
      setIsConnected(false);
      eventHandlers.current.clear();
    }
  }, [connection]);

  const on = useCallback<SignalREventHandler>((event: string, callback: SignalRCallback): void => {
    const handlers = eventHandlers.current.get(event) || [];

    handlers.push(callback);
    eventHandlers.current.set(event, handlers);
  }, []);

  const off = useCallback<SignalREventHandler>((event: string, callback: SignalRCallback): void => {
    const handlers = eventHandlers.current.get(event) || [];
    const filteredHandlers = handlers.filter(handler => handler !== callback);

    if (filteredHandlers.length > 0) {
      eventHandlers.current.set(event, filteredHandlers);
    } else {
      eventHandlers.current.delete(event);
    }
  }, []);

  // Автоматическое подключение при монтировании
  useEffect(() => {
    if (accessToken && !isConnected && !isConnecting) {
      console.log('Автоматическое подключение к WebSocket...');
      connect().catch((error) => {
        console.error('Ошибка автоподключения к WebSocket:', error);
      });
    }
  }, [accessToken, isConnected, isConnecting, connect]);

  // Логирование состояния подключения
  useEffect(() => {
    if (isConnected) {
      console.log('WebSocket подключен и готов к работе');
    } else if (error) {
      console.error('Ошибка WebSocket:', error);
    }
  }, [isConnected, error]);

  const value: SignalRContextType = {
    connection,
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    on,
    off,
  };

  // Показываем загрузку пока не подключились
  if (!isConnected && !error) {
    return (
      <SignalRContext.Provider value={value}>
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4" />
            <div >
              {isConnecting ? 'Подключение к серверу...' : 'Инициализация...'}
            </div>
          </div>
        </div>
      </SignalRContext.Provider>
    );
  }

  // Показываем ошибку если не удалось подключиться
  if (error) {
    return (
      <SignalRContext.Provider value={value}>
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <div className="text-red-500 text-lg mb-2">Ошибка подключения</div>
            <div >{error}</div>
          </div>
        </div>
      </SignalRContext.Provider>
    );
  }

  // Рендерим children только после успешного подключения
  return (
    <SignalRContext.Provider value={value}>
      {children}
    </SignalRContext.Provider>
  );
}; 