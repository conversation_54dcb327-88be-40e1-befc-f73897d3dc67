(()=>{var e={};e.id=8345,e.ids=[8345],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74445:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>i,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>p});var o=r(48106),a=r(48819),u=r(12050),n=r(4235);async function p(){try{let e=n.NextResponse.json({success:!0,message:"Успешно вышел из системы"},{status:200});return e.cookies.delete({name:process.env.AUTH_COOKIE_NAME,domain:".karaoketest.ru"}),e}catch(e){return console.error("Ошибка выхода из системы:",e),n.NextResponse.json({success:!1,message:"⚠️ Не удалось выйти из системы"},{status:500})}}let i=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/Auth/logout/route",pathname:"/api/Auth/logout",filename:"route",bundlePath:"app/api/Auth/logout/route"},resolvedPagePath:"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\api\\Auth\\logout\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=i;function x(){return(0,u.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},80408:()=>{},87032:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[191,3744],()=>r(74445));module.exports=s})();