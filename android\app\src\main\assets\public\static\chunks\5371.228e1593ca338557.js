"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5371],{15371:(e,s,n)=>{n.r(s),n.d(s,{default:()=>t});var r=n(54568);function t(e){let{size:s=24,className:n=""}=e;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:n,children:[(0,r.jsx)("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),(0,r.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12"})]})}n(7620)}}]);