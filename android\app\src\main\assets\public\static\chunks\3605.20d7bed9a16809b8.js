(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3605],{12722:(e,t,r)=>{"use strict";r.d(t,{$z:()=>l,Or:()=>o,Z6:()=>d,aZ:()=>s,eX:()=>i,pL:()=>a,uW:()=>u,wp:()=>c});var n=r(27736);r(62571);let s=async e=>(await n.uE.post("/Order/instant/".concat(e,"/accept-by-driver"))).data,i=async e=>{try{console.log("\uD83D\uDCDD Подтверждаем запланированную поездку водителем:",e);let t=await n.uE.post("/Ride/".concat(e,"/accept-by-driver"));return console.log("✅ Поездка подтверждена водителем:",t.data),t.data}catch(e){throw console.error("❌ Ошибка подтверждения поездки водителем:",e),e}},o=async e=>{try{console.log("\uD83D\uDCE2 Уведомляем о принятии запланированной поездки в сведении:",e);let t=await n.uE.post("/Ride/".concat(e,"/notification-by-driver"));console.log("✅ Уведомление отправлено:",t.data)}catch(e){throw console.error("❌ Ошибка отправки уведомления:",e),e}},a=async e=>(await n.uE.post("/Ride/".concat(e,"/reject-by-driver"))).data,d=async e=>(await n.uE.post("/Ride/".concat(e,"/status/driver-heading-to-client"))).data,c=async e=>(await n.uE.post("/Ride/".concat(e,"/status/driver-arrived"))).data,u=async e=>(await n.uE.post("/Ride/".concat(e,"/status/ride-started"))).data,l=async e=>(await n.uE.post("/Ride/".concat(e,"/status/ride-finished"))).data},23605:(e,t,r)=>{"use strict";r.r(t),r.d(t,{RideRequestModal:()=>f});var n=r(54568),s=r(94275),i=r.n(s),o=r(62942),a=r(7620),d=r(68327),c=r(32959),u=r(8426),l=r(12722),h=r(48484);let f=e=>{let{requestData:t,isOpen:r,onClose:s,onAccept:f,onReject:p}=e,m=(0,o.useRouter)(),[b,y]=(0,a.useState)(30),[x,v]=(0,a.useState)(!1),{actions:_}=(0,c.o)(),{refreshRides:g}=(0,u.g0)(),w=(0,a.useRef)(null);(0,a.useEffect)(()=>(r&&t?(w.current||(w.current=new Audio("/sounds/notification.wav"),w.current.loop=!0,w.current.volume=.7,w.current.onerror=()=>{console.warn("Не удалось загрузить аудиофайл уведомления"),S()}),(async()=>{try{w.current&&(w.current.currentTime=0,await w.current.play())}catch(e){console.warn("Не удалось воспроизвести аудио:",e),S()}})()):w.current&&(w.current.pause(),w.current.currentTime=0),()=>{w.current&&(w.current.pause(),w.current.currentTime=0)}),[r,t]);let S=(0,a.useCallback)(()=>{try{let e=new(window.AudioContext||window.webkitAudioContext),t=(t,r,n)=>{setTimeout(()=>{let n=e.createOscillator(),s=e.createGain();n.connect(s),s.connect(e.destination),n.frequency.setValueAtTime(t,e.currentTime),n.type="sine",s.gain.setValueAtTime(.3,e.currentTime),s.gain.exponentialRampToValueAtTime(.01,e.currentTime+r/1e3),n.start(e.currentTime),n.stop(e.currentTime+r/1e3)},n)};t(800,200,0),t(1e3,200,300),t(1200,300,600)}catch(e){console.warn("Не удалось создать fallback звук:",e)}},[]),j=(0,a.useCallback)(async()=>{if(!x){w.current&&(w.current.pause(),w.current.currentTime=0);try{await _.leaveQueue(),d.P.info("Заказ отклонен. Вы исключены из очереди")}catch(e){console.error("Ошибка при исключении из очереди:",e)}p(),s()}},[x,_,p,s]);(0,a.useEffect)(()=>{if(!r||!t)return void y(30);let e=setInterval(()=>{y(e=>e<=1?(j(),0):e-1)},1e3);return()=>clearInterval(e)},[r,t,j]);let R=(0,a.useCallback)(async()=>{if(t&&!x){w.current&&(w.current.pause(),w.current.currentTime=0),v(!0);try{if("Instant"===t.orderType){let e=await (0,l.aZ)(t.orderId),r=t.rideId||e.id||t.orderId;(0,h.Hq)(t.orderId,r);try{await (0,l.Z6)(r)}catch(e){console.error('❌ Ошибка установки статуса "Водитель направляется к клиенту":',e)}d.P.success("Заказ принят, направляюсь к клиенту"),await _.leaveQueue(),await g();let n="&orderType=".concat(t.orderType),i=t.rideId?"/active-ride?orderId=".concat(t.orderId,"&rideId=").concat(t.rideId).concat(n):"/active-ride?orderId=".concat(t.orderId).concat(n);console.log("\uD83D\uDE97 Переходим на активную поездку:",i),f(),s(),m.push(i)}else d.P.success("Вы уведомлены о назначенной поездке"),f(),s()}catch(e){console.error("Ошибка принятия заказа:",e),d.P.error("Ошибка принятия заказа")}finally{v(!1)}}},[t,x,_,g,f,s,m]);return r&&t?(0,n.jsxs)("div",{className:"jsx-908492bef7274fd9 absolute inset-0 z-50 w-full h-full flex justify-between items-center backdrop-filter backdrop-blur-md bg-black/80",children:[(0,n.jsxs)("div",{className:"jsx-908492bef7274fd9 rounded-xl shadow-lg p-6 w-full max-w-lg mx-auto backdrop-blur-md text-black bg-white/95 animate-pulse-border",children:[(0,n.jsxs)("div",{className:"jsx-908492bef7274fd9 flex justify-between items-center mb-4",children:[(0,n.jsx)("h2",{className:"jsx-908492bef7274fd9 text-xl font-semibold flex items-center",children:"\uD83D\uDD14 Новый заказ"}),(0,n.jsx)("button",{onClick:j,className:"jsx-908492bef7274fd9 text-gray-500 hover:text-gray-700 text-2xl leading-none",children:"\xd7"})]}),(0,n.jsxs)("div",{className:"jsx-908492bef7274fd9 space-y-6",children:[(0,n.jsxs)("div",{className:"jsx-908492bef7274fd9 text-center",children:[(0,n.jsx)("div",{className:"jsx-908492bef7274fd9 "+"text-6xl font-bold mb-2 transition-colors duration-300 ".concat(b<=10?"text-red-500 animate-pulse":"text-blue-500"),children:b}),(0,n.jsx)("p",{className:"jsx-908492bef7274fd9 text-gray-600",children:"секунд до автоматического отклонения"})]}),(0,n.jsx)("div",{className:"jsx-908492bef7274fd9 bg-gray-50 rounded-lg p-4 space-y-3",children:(0,n.jsxs)("div",{className:"jsx-908492bef7274fd9 flex items-center justify-between",children:[(0,n.jsx)("span",{className:"jsx-908492bef7274fd9 font-medium",children:"Тип заказа:"}),(0,n.jsx)("span",{className:"jsx-908492bef7274fd9 "+"px-2 py-1 rounded text-sm font-medium ".concat("Instant"===t.orderType?"bg-orange-100 text-orange-800":"bg-blue-100 text-blue-800"),children:"Instant"===t.orderType?"Мгновенный":"Запланированный"})]})}),(0,n.jsx)("div",{className:"jsx-908492bef7274fd9 flex space-x-4",children:"Scheduled"===t.orderType?(0,n.jsx)("button",{onClick:R,disabled:x,className:"jsx-908492bef7274fd9 w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?"Подтверждаю...":"Уведомлен"}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{onClick:j,disabled:x,className:"jsx-908492bef7274fd9 flex-1 bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Отклонить"}),(0,n.jsx)("button",{onClick:R,disabled:x,className:"jsx-908492bef7274fd9 flex-1 bg-green-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?"Принимаю...":"Принять заказ"})]})})]})]}),(0,n.jsx)(i(),{id:"908492bef7274fd9",children:"@-webkit-keyframes pulse-border{0%,100%{-webkit-box-shadow:0 0 20px rgba(59,130,246,.5);box-shadow:0 0 20px rgba(59,130,246,.5)}50%{-webkit-box-shadow:0 0 40px rgba(59,130,246,.8);box-shadow:0 0 40px rgba(59,130,246,.8)}}@-moz-keyframes pulse-border{0%,100%{-moz-box-shadow:0 0 20px rgba(59,130,246,.5);box-shadow:0 0 20px rgba(59,130,246,.5)}50%{-moz-box-shadow:0 0 40px rgba(59,130,246,.8);box-shadow:0 0 40px rgba(59,130,246,.8)}}@-o-keyframes pulse-border{0%,100%{box-shadow:0 0 20px rgba(59,130,246,.5)}50%{box-shadow:0 0 40px rgba(59,130,246,.8)}}@keyframes pulse-border{0%,100%{-webkit-box-shadow:0 0 20px rgba(59,130,246,.5);-moz-box-shadow:0 0 20px rgba(59,130,246,.5);box-shadow:0 0 20px rgba(59,130,246,.5)}50%{-webkit-box-shadow:0 0 40px rgba(59,130,246,.8);-moz-box-shadow:0 0 40px rgba(59,130,246,.8);box-shadow:0 0 40px rgba(59,130,246,.8)}}.animate-pulse-border.jsx-908492bef7274fd9{-webkit-animation:pulse-border 2s ease-in-out infinite;-moz-animation:pulse-border 2s ease-in-out infinite;-o-animation:pulse-border 2s ease-in-out infinite;animation:pulse-border 2s ease-in-out infinite}"})]}):null}},43522:()=>{},94275:(e,t,r)=>{"use strict";e.exports=r(98650).style},98650:(e,t,r)=>{"use strict";var n=r(24338);r(43522);var s=r(7620),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),o=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},d=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,s=t.optimizeForSpeed,i=void 0===s?o:s;c(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",c("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var d="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=d?d.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(c(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];c(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&c(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return r?s.insertBefore(n,r):s.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var u=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},l={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return l[n]||(l[n]="jsx-"+u(e+"-"+r)),l[n]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return l[r]||(l[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),l[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,s=t.optimizeForSpeed,i=void 0!==s&&s;this._sheet=n||new d({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),n&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,s=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var i=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=i,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var s=h(n,r);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return f(s,e)}):[f(s,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=s.createContext(null);m.displayName="StyleSheetContext";var b=i.default.useInsertionEffect||i.default.useLayoutEffect,y="undefined"!=typeof window?new p:void 0;function x(e){var t=y||s.useContext(m);return t&&("undefined"==typeof window?t.add(e):b(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}x.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=x}}]);