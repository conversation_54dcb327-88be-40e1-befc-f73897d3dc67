import clsx from 'clsx';
import React from 'react';
import type { FieldGroupProps } from '@shared/ui/form/dynamic-form/types';

/**
 * Компактный компонент для отображения группы полей формы
 * Минималистичный дизайн без рамок и с меньшими отступами
 *
 * @param group - Группа полей
 * @param renderField - Функция рендеринга поля
 */
export const CompactFieldGroup: React.FC<FieldGroupProps> = ({ group, renderField }) => {
  // Получаем параметры макета с дефолтными значениями
  const flexDirection = group.layout?.flexDirection || 'row';
  const itemsAlignment = group.layout?.itemsAlignment || 'stretch';
  const gridCols = group.layout?.gridCols || 2;
  const additionalClassName = group.layout?.className || '';

  // Определяем классы для контейнера группы
  const groupContainerClass = clsx(
    'flex flex-col gap-2',
    additionalClassName
  );

  // Определяем классы для выравнивания элементов
  const getAlignmentClass = (alignment: string) => {
    switch (alignment) {
      case 'start': return 'items-start';
      case 'center': return 'items-center';
      case 'end': return 'items-end';
      case 'stretch': return 'items-stretch';
      default: return 'items-stretch';
    }
  };

  return (
    <div key={group.id} className={`${groupContainerClass}`}>
      {/* Компактный заголовок */}
      {group.title && (
        <div className="mb-2">
          <h4 className="text-sm font-medium">{group.title}</h4>
          {group.description && (
            <p className="text-xs text-gray-500">{group.description}</p>
          )}
        </div>
      )}

      {/* Контейнер для полей группы */}
      <div
        className={clsx(
          'w-full',
          flexDirection === 'row'
            ? `grid gap-x-3 gap-y-2 grid-cols-${gridCols}`
            : 'flex flex-col gap-y-2',
          getAlignmentClass(itemsAlignment)
        )}
        style={{
          gridTemplateColumns: flexDirection === 'row' ? `repeat(${gridCols}, 1fr)` : undefined
        }}
      >
        {/* Рендерим каждое поле группы */}
        {group.fields.map(field => renderField(field))}
      </div>
    </div>
  );
};
