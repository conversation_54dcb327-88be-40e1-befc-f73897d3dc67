import { NotificationProvider , SignalRProvider } from '@app/providers';
import { getUserFromCookie, getRawCookie } from '@shared/lib/parse-cookie';
import type { Role } from '@entities/users/enums/Role.enum';
import { Footer } from '@widgets/footer';
import { Header } from '@widgets/header';
import { ModalManagerComponent } from '@widgets/modals';
import { Sidebar } from '@widgets/sidebar';
import { initializeCollectionComponents } from '@compass-admin/config/register-components';

export default async function ClientLayout({ children }: { children: React.ReactNode }) {
  // Получаем данные пользователя из куки
  const accessToken = await getRawCookie('.AspNetCore.Identity.Application');
  const userRole = (await getUserFromCookie('role')) as Role | null;
  const userEmail = (await getUserFromCookie('email')) as string | null;
  const userFullName = (await getUserFromCookie('fullName')) as string | null;
  
  // Получаем состояние сайдбара из куки
  const sidebarCollapsedCookie = await getRawCookie('sidebar-collapsed');
  const sidebarCollapsed = sidebarCollapsedCookie === 'true';
  
  // Получаем настройки фона из куки
  const backgroundType = (await getRawCookie('background-type')) || 'color';
  const backgroundValue = (await getRawCookie('background-value')) || '#374151';
  
  // Получаем настройки оверлея из куки
  const overlayColor = (await getRawCookie('overlay-color')) || '#07080A';
  const overlayOpacityStr = (await getRawCookie('overlay-opacity')) || '0.85';
  const overlayOpacity = parseFloat(overlayOpacityStr);

  return (
    <SignalRProvider accessToken={accessToken || undefined}>
      <NotificationProvider>
        <div className='flex h-full overflow-hidden relative'
                    style={{
                      ...(backgroundType === 'image' 
                        ? {
                            backgroundImage: `url(${backgroundValue})`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center',
                            backgroundRepeat: 'no-repeat',
                            backgroundAttachment: 'fixed'
                          }
                        : {
                            backgroundColor: backgroundValue
                          }
                      )
                    }}
        
        >
          {/* Контент поверх фона */}
          <div className='relative z-10 flex h-full w-full text-white'>
            {/* Боковая панель */}
            <Sidebar 
              userRole={userRole} 
              initialCollapsed={sidebarCollapsed}
              overlayColor={overlayColor}
              overlayOpacity={overlayOpacity}
            />
            <div className='flex flex-col flex-1 overflow-hidden'>
              {/* Шапка */}
              <Header userEmail={userEmail} userFullName={userFullName} userRole={userRole} />
              {/* Основной контент */}
              <div className='py-4 pr-4 overflow-hidden h-full'>
                <main
                  className='flex flex-col h-full flex-1 overflow-hidden
                    transition-colors duration-200 rounded-xl
                    outline outline-offset-[-1px]'
                  style={{
                    backgroundColor: `${overlayColor}${Math.round(overlayOpacity * 255).toString(16).padStart(2, '0')}`,
                    outlineColor: `${overlayColor}${Math.round(Math.min(overlayOpacity + 0.05, 1) * 255).toString(16).padStart(2, '0')}`
                  }}
                >
                  {children}
                  {/* Менеджер модальных окон */}
                  <ModalManagerComponent />
                </main>
              </div>
              <Footer />
            </div>
          </div>
        </div>
      </NotificationProvider>
    </SignalRProvider>
  );
}
// Инициализируем компоненты коллекций при загрузке модуля
initializeCollectionComponents();
