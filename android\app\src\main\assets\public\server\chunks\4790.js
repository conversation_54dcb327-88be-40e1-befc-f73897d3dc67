"use strict";exports.id=4790,exports.ids=[4790],exports.modules={74790:(e,r,s)=>{s.r(r),s.d(r,{SelectOrderTypeModal:()=>c});var l=s(13486),a=s(2984);s(60159);var o=s(34319),t=s(97743),i=s(29457),n=s(42429);let c=({onClose:e})=>{let r=(0,a.useRouter)();return(0,l.jsx)("div",{className:"absolute inset-0 z-50 w-full h-full flex justify-between items-center backdrop-filter backdrop-blur-[7px]",children:(0,l.jsxs)("div",{className:"rounded-lg shadow-lg p-6 w-full max-w-6xl mx-auto",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Выбор типа заказа"}),(0,l.jsx)("button",{onClick:()=>{e()},className:"w-10 h-10 bg-gray-50/30 rounded-full flex items-center justify-center text-red-500 hover:bg-red-100/30 transition-all duration-200 cursor-pointer",children:(0,l.jsx)(o.default,{size:20})})]}),(0,l.jsx)(u,{onTypeSelect:s=>{e(),r.push(`/orders/create?type=${s}`)}})]})})},d={[i.ZN.Scheduled]:{type:i.ZN.Scheduled,icon:"calendar",description:"Заказ на определенное время с возможностью предварительного планирования",isAvailable:!0},[i.ZN.Instant]:{type:i.ZN.Instant,icon:"zap",description:"Заказ прямо сейчас с быстрым выполнением",isAvailable:!0},[i.ZN.Shuttle]:{type:i.ZN.Shuttle,icon:"bus",description:"Регулярные маршруты по расписанию (скоро)",isAvailable:!1},[i.ZN.Partner]:{type:i.ZN.Partner,icon:"handshake",description:"Заказы от партнеров и корпоративных клиентов (скоро)",isAvailable:!1}};function u({onTypeSelect:e}){return(0,l.jsx)("div",{className:"w-full h-full flex flex-col gap-6",children:(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6",children:Object.keys(d).filter(e=>e!==i.ZN.Unknown).map(r=>{let s=d[r],a=!s.isAvailable;return(0,l.jsxs)("div",{onClick:()=>s.isAvailable&&e(r),className:`
                   rounded-lg shadow-[0_0_10px_rgba(0,0,0,0.1)] backdrop-blur-md bg-black/80
                  transition-shadow duration-300 p-6 h-full border
                  ${a?"opacity-50 cursor-not-allowed border-[color:var(--color-neutral-200)]":"cursor-pointer hover:shadow-[0_0_15px_rgba(0,0,0,0.2)] border-[color:var(--color-neutral-200)] hover:border-[color:var(--color-primary)]"}
                `,children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:`
                      min-w-12 min-h-12 rounded-full flex items-center justify-center border-2
                      ${a?"bg-[color:var(--color-neutral-100)] text-[color:var(--color-neutral-400)] border-[color:var(--color-neutral-100)]":"bg-[color:var(--color-neutral-100)] text-[color:var(--color-primary)] border-[color:var(--color-neutral-100)]"}
                    `,children:(0,l.jsx)(t.J,{iconName:s.icon})}),(0,l.jsx)("h2",{className:`
                      text-xl font-semibold ml-4
                      ${a?"text-[color:var(--color-neutral-400)]":""}
                    `,children:(0,n.vP)(s.type)})]}),(0,l.jsx)("p",{className:`
                    ${a?"text-[color:var(--color-neutral-400)]":""}
                  `,children:s.description}),(0,l.jsxs)("div",{className:"mt-4 flex items-center",children:[(0,l.jsx)("div",{className:`
                      w-2 h-2 rounded-full mr-2
                      ${s.isAvailable?"bg-green-500":"bg-gray-400"}
                    `}),(0,l.jsx)("span",{className:`
                      text-xs font-medium
                      ${s.isAvailable?"text-green-600":a?"text-[color:var(--color-neutral-400)]":"text-gray-500"}
                    `,children:s.isAvailable?"Доступно":"Скоро"})]})]},r)})})})}},97743:(e,r,s)=>{s.d(r,{J:()=>m});var l=s(13486),a=s(60159);let o=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,35235))),t=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,97376))),i=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,51249))),n=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,29383))),c=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,27766))),d=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,2262))),u=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,24986))),x=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,71015))),m=({iconName:e,className:r="w-6 h-6",size:s=24})=>{let m=(e=>{switch(e){case"DashboardIcon":return(0,l.jsx)(o,{className:r,size:s});case"ProfileIcon":return(0,l.jsx)(t,{className:r,size:s});case"IdentityCardIcon":return(0,l.jsx)(i,{className:r,size:s});case"UsersIcon":return(0,l.jsx)(n,{className:r,size:s});case"VehiclesIcon":return(0,l.jsx)(c,{className:r,size:s});case"TariffsIcon":return(0,l.jsx)(d,{className:r,size:s});case"OrdersIcon":return(0,l.jsx)(u,{className:r,size:s});case"ReferencesIcon":return(0,l.jsx)(x,{className:r,size:s});default:return null}})(e);return m?(0,l.jsx)(a.Suspense,{fallback:(0,l.jsx)("div",{className:r}),children:m}):(0,l.jsx)("div",{className:r})}}};