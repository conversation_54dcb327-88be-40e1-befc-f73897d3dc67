(()=>{var e={};e.id=450,e.ids=[450],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28871:(e,s,r)=>{"use strict";r.r(s),r.d(s,{OrderPage:()=>t});let t=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call OrderPage() from the server but OrderPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\pages\\driver\\order\\OrderPage.tsx","OrderPage")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50668:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=r(24332),i=r(48819),n=r(67851),a=r.n(n),d=r(97540),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(s,l);let o={children:["",{children:["(driver)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96718)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94058)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(driver)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62353:(e,s,r)=>{"use strict";r.r(s),r.d(s,{OrderPage:()=>x});var t=r(13486),i=r(2984),n=r(60159),a=r(66971),d=r(95816),l=r(26134),o=r(32270);let c=({showPosition:e=!0})=>{let s=(0,i.useRouter)(),[r,a]=(0,n.useState)(!1),{isInQueue:d,position:c,isLoading:x,isActionLoading:u,error:p,actions:m}=(0,l.o)(),{hasUpcomingRideWithin1_5Hours:h,hoursUntilNextRide:g,hasOverdueRequestedRides:v,overdueRidesCount:j,isLoading:b}=(0,o.U)(),f=!d&&(h||v);return((0,n.useLayoutEffect)(()=>{x||b||a(!0)},[x,b]),r)?x?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,t.jsx)("span",{className:"ml-2 text-sm",children:"Проверка статуса..."})]}):p?(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-red-600 text-sm mb-2",children:["Ошибка: ",p]}),(0,t.jsx)("button",{onClick:m.refresh,className:"px-3 py-1 text-xs bg-gray-200  rounded hover:bg-gray-300 transition-colors",children:"Повторить"})]}):(0,t.jsxs)("div",{className:"space-y-2",children:[d&&e&&null!==c&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-sm",children:"Позиция в очереди"}),(0,t.jsxs)("div",{className:"text-lg font-bold text-blue-600",children:["№",c]})]}),(0,t.jsx)("button",{onClick:()=>{if(d)m.leaveQueue();else{if(f)return void s.push("/scheduled-orders");m.joinQueue()}},disabled:u||f,className:`
          w-full px-4 py-3 rounded-lg font-medium transition-all duration-200
          ${f?v?"bg-red-500 text-white cursor-not-allowed":"bg-orange-500 text-white cursor-not-allowed":d?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}
          ${u||f?"opacity-75":"hover:shadow-md"}
        `,children:u?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),d?"Выходим...":"Входим..."]}):f?v?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"text-lg mr-2",children:"⚠️"}),"У вас просроченные заказы (",j,")"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"text-lg mr-2",children:"\uD83D\uDEAB"}),"У вас запланированный заказ"]}):(0,t.jsx)(t.Fragment,{children:d?(0,t.jsx)(t.Fragment,{children:"Выйти из очереди"}):(0,t.jsx)(t.Fragment,{children:"Встать в очередь"})})}),f?v?(0,t.jsxs)("div",{className:"text-center text-xs text-red-600 bg-red-50 p-2 rounded",children:["Вход в очередь временно отключен.",(0,t.jsx)("br",{}),"Сначала обработайте просроченные заказы."]}):(0,t.jsxs)("div",{className:"text-center text-xs text-orange-600 bg-orange-50 p-2 rounded",children:["Вход в очередь временно отключен.",(0,t.jsx)("br",{}),"Запланированная поездка через ",g?Math.round(60*g):"?"," мин."]}):d?(0,t.jsx)("div",{className:"text-center text-xs text-gray-500",children:"Вы получите уведомление, когда подойдет ваша очередь"}):null]}):(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsx)("div",{className:"w-full px-4 py-3 rounded-lg bg-gray-200 animate-pulse",children:(0,t.jsx)("div",{className:"h-6 bg-gray-300 rounded"})})})},x=()=>{let e=(0,i.useRouter)(),{activeRide:s,isLoading:r,isInitialized:n}=(0,d.c7)();return!n||r?(0,t.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"}),(0,t.jsx)("p",{children:"Загрузка..."})]})}):(0,t.jsxs)("div",{className:"h-full flex flex-col p-4",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Доступные заказы"}),(0,t.jsx)("p",{children:"Выберите заказ для выполнения"})]}),s&&(0,t.jsx)("div",{className:"g-full flex justyfy-center items-center",children:(0,t.jsxs)("div",{className:"p-4 w-full flex flex-col items-center justify-between gap-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)("h3",{className:"font-medium text-blue-900",children:"У вас есть активная поездка"})}),(0,t.jsxs)("button",{onClick:()=>e.push(`/active-ride?orderId=${s.orderId}&rideId=${s.rideId}`),className:"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"Перейти к поездке"}),(0,t.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-auto",children:[!s&&(0,t.jsx)("div",{className:"h-full flex flex-col justify-center items-center",children:(0,t.jsx)(c,{})}),!s&&(0,t.jsx)("div",{className:"space-y-4",children:[].map(e=>(0,t.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsxs)("span",{className:"text-lg font-semibold",children:["Заказ #",e.id.slice(-8).toUpperCase()]}),(0,t.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full",children:(0,a.jY)(e.status)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Расстояние:"})," ",e.distance?`${e.distance} км`:"Не указано"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Длительность:"})," ",e.duration||"Не указано"]})]}),e.waypoints&&(0,t.jsxs)("div",{className:"mt-2 text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Точек маршрута:"})," ",Array.isArray(e.waypoints)?e.waypoints.length:1]})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsx)("button",{className:"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium",children:"Принять заказ"}),(0,t.jsx)("button",{className:"bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm font-medium",children:"Подробнее"})]})]})},e.id))})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87429:(e,s,r)=>{Promise.resolve().then(r.bind(r,62353))},93509:(e,s,r)=>{Promise.resolve().then(r.bind(r,28871))},94735:e=>{"use strict";e.exports=require("events")},96718:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n,metadata:()=>a});var t=r(38828),i=r(28871);async function n(){return(0,t.jsx)(i.OrderPage,{})}let a={title:"",description:""}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[191,5208,9455,6596,4680,99,3639,2960,9100,1738],()=>r(50668));module.exports=t})();