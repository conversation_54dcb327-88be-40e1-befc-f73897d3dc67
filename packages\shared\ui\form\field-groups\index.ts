/**
 * Экспорт всех доступных компонентов FieldGroup
 * 
 * Каждый компонент предоставляет свой уникальный стиль отображения групп полей:
 * - FieldGroup: стандартный дизайн с рамками и фоном
 * - CompactFieldGroup: минималистичный дизайн без рамок
 * - CardFieldGroup: дизайн в виде карточек с тенью
 */

// Стандартный FieldGroup из DynamicForm
export { FieldGroup } from '@shared/ui/form/dynamic-form/components/FieldGroup';

// Компактный FieldGroup
export { CompactFieldGroup } from './CompactFieldGroup';

// FieldGroup в виде карточки
export { CardFieldGroup } from './CardFieldGroup';

// Типы для TypeScript
export type { FieldGroupProps } from '@shared/ui/form/dynamic-form/types';
