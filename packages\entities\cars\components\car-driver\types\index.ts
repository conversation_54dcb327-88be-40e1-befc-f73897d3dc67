import type { GetDriverDTO } from '@entities/users/interface';

export interface SelectedDrivers {
  firstShift?: GetDriverDTO;
  secondShift?: GetDriverDTO;
}

export interface CustomSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  placeholder?: string;
  showMarginTop?: boolean;
}

export interface DriverCardProps {
  driver?: GetDriverDTO;
  shift: 'first' | 'second';
  title: string;
  onRemove: (shift: 'first' | 'second') => void;
}

export interface DriverSearchFilters {
  VehicleServiceClass?: string;
  DrivingExperience?: string;
  Online?: string;
  Languages?: string;
}

export interface UseDriverSearchResult {
  driversList: GetDriverDTO[];
  isLoading: boolean;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  searchField: 'fullName' | 'phoneNumber';
  setSearchField: (field: 'fullName' | 'phoneNumber') => void;
  activeFilters: DriverSearchFilters;
  setActiveFilters: React.Dispatch<React.SetStateAction<DriverSearchFilters>>;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
}

export interface UseDriverAssignmentResult {
  selectedDrivers: SelectedDrivers;
  assignDriver: (driver: GetDriverDTO, shift: 'first' | 'second') => void;
  removeDriver: (shift: 'first' | 'second') => void;
  firstShiftDriverId: string;
  secondShiftDriverId: string;
}
