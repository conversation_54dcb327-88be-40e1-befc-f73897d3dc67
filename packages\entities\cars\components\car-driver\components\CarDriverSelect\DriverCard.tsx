'use client';

import React from 'react';
import type { DriverCardProps } from '../../types';

/**
 * Компонент карточки водителя
 * Отвечает только за отображение информации о водителе
 */
export const DriverCard: React.FC<DriverCardProps> = ({ 
  driver, 
  shift, 
  title, 
  onRemove 
}) => (
  <div className="h-full p-4 flex flex-col overflow-hidden">
    <div className="flex items-center justify-between mb-3">
      <h4 className="text-sm font-medium">{title}</h4>
      {driver && (
        <button
          type="button"
          onClick={() => onRemove(shift)}
          className="text-red-500 hover:text-red-700 text-sm cursor-pointer"
          title="Удалить водителя"
        >
          ✕
        </button>
      )}
    </div>

    {driver ? (
      <div className="flex-1 flex flex-col">
        <div className="flex items-start gap-3">
          {/* Аватар */}
          <div className="w-12 h-12 rounded-full bg-blue-500/20 border border-blue-500/30 flex items-center justify-center text-blue-400 font-medium flex-shrink-0">
            {driver.fullName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)}
          </div>

          {/* Основная информация */}
          <div className="flex-1">
            <p className="font-medium">{driver.fullName}</p>
            <p className="text-sm text-gray-400">{driver.phoneNumber || 'Нет телефона'}</p>
          </div>
        </div>

        {/* Детальная информация */}
        <div className="mt-3 space-y-2 text-xs">
          {/* Опыт */}
          <div className="flex justify-between">
            <span className="text-gray-500">Опыт вождения:</span>
            <span className="text-gray-300">
              {driver.profile?.drivingExperience 
                ? `${driver.profile.drivingExperience} лет` 
                : 'Нет данных'}
            </span>
          </div>

          {/* Рабочие зоны */}
          <div className="flex justify-between">
            <span className="text-gray-500">Зоны работы:</span>
            <span className="text-gray-300">
              {driver.profile?.preferredWorkZones && driver.profile.preferredWorkZones.length > 0
                ? driver.profile.preferredWorkZones.join(', ')
                : 'Нет данных'}
            </span>
          </div>

          {/* Рейтинг */}
          <div className="flex justify-between">
            <span className="text-gray-500">Рейтинг:</span>
            <span className="text-gray-300">
              {driver.rating ? `⭐ ${driver.rating.toFixed(1)}` : 'Нет данных'}
            </span>
          </div>

          {/* Статус */}
          <div className="flex justify-between">
            <span className="text-gray-500">Статус:</span>
            <span className="flex items-center gap-1">
              <span className={`w-2 h-2 rounded-full ${driver.online ? 'bg-green-500' : 'bg-gray-400'}`} />
              <span className="text-gray-300">{driver.online ? 'Онлайн' : 'Оффлайн'}</span>
            </span>
          </div>
        </div>
      </div>
    ) : (
      <div className="flex-1 flex flex-col items-center justify-center text-gray-400">
        <div className="w-16 h-16 mb-3 rounded-full bg-white/5" />
        <p className="text-sm">Водитель не назначен</p>
        <p className="text-xs text-gray-500 mt-1">Выберите из списка справа</p>
      </div>
    )}
  </div>
); 