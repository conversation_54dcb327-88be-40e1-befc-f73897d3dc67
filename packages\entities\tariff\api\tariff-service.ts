import { BaseApiService } from '@shared/api';
import { API_ROUTES } from '@shared/api/constants';
import type { GetTariffDTO, CreateTariffDTO, UpdateTariffDTO } from '../interface';

interface GetTariffDTOKeysetPaginationResult {
  data: GetTariffDTO[];
  hasMore: boolean;
  nextCursor?: string;
  total?: number;
}

/**
 * Сервис для работы с тарифами
 * Использует BaseApiService для унификации API паттернов
 */
export class TariffService extends BaseApiService {
  protected baseUrl = API_ROUTES.TARIFF.LIST;

  /**
   * Получить тариф по ID
   */
  async getTariff(id: string): Promise<GetTariffDTO> {
    const result = await this.get<GetTariffDTO>(`/${id}`);

    return this.handleApiResult(result);
  }

  /**
   * Получить тариф по ID (безопасно)
   */
  async getTariffSafe(id: string): Promise<GetTariffDTO | null> {
    const result = await this.get<GetTariffDTO>(`/${id}`);

    return this.handleApiResultSafe(result);
  }

  /**
   * Получить список тарифов
   */
  async getTariffs(params?: {
    first?: boolean;
    before?: string;
    after?: string;
    last?: boolean;
    size?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<GetTariffDTOKeysetPaginationResult> {
    const queryParams = new URLSearchParams();

    if (params?.first !== undefined) queryParams.append('first', params.first.toString());
    if (params?.before) queryParams.append('before', params.before);
    if (params?.after) queryParams.append('after', params.after);
    if (params?.last !== undefined) queryParams.append('last', params.last.toString());
    if (params?.size !== undefined) queryParams.append('size', params.size.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.isActive !== undefined) queryParams.append('isActive', params.isActive.toString());
    const endpoint = queryParams.toString() ? `?${queryParams.toString()}` : '';
    const result = await this.get<GetTariffDTOKeysetPaginationResult>(endpoint);

    return this.handleApiResult(result);
  }

  /**
   * Создать тариф
   */
  async createTariff(data: CreateTariffDTO): Promise<GetTariffDTO> {
    const result = await this.post<GetTariffDTO, CreateTariffDTO>('', data);

    return this.handleApiResult(result);
  }

  /**
   * Обновить тариф
   */
  async updateTariff(id: string, data: UpdateTariffDTO): Promise<GetTariffDTO> {
    const result = await this.put<GetTariffDTO, UpdateTariffDTO>(`/${id}`, data);

    return this.handleApiResult(result);
  }

  /**
   * Удалить тариф
   */
  async deleteTariff(id: string): Promise<void> {
    const result = await this.delete<void>(`/${id}`);

    this.handleApiResult(result);
  }

  /**
   * Активировать/деактивировать тариф
   */
  async toggleTariffStatus(id: string, isActive: boolean): Promise<GetTariffDTO> {
    const result = await this.patch<GetTariffDTO, { isActive: boolean }>(`/${id}/status`, {
      isActive,
    });

    return this.handleApiResult(result);
  }

  /**
   * Рассчитать стоимость поездки
   */
  async calculatePrice(
    tariffId: string,
    distance: number,
    duration: number,
  ): Promise<{ price: number }> {
    const result = await this.post<{ price: number }, { distance: number; duration: number }>(
      `/${tariffId}/calculate`,
      {
        distance,
        duration,
      },
    );

    return this.handleApiResult(result);
  }
}

// Создаем экземпляр сервиса для использования
export const tariffService = new TariffService();
