import type { OrderType, OrderStatus, OrderSubStatus } from '@entities/order/enums';
import type { GetRideDTO } from '@entities/ride/interface/GetRideDTO';
import type { GetOrderServiceDTO } from './GetOrderServiceDTO';
import type { GetPassengerDTO } from './GetPassengerDTO';

/**
 * Интерфейс для получения полной информации о заказе
 * @interface GetOrderDTO
 */
export interface GetOrderDTO {
  /**
   * Идентификатор заказа
   */
  id: string;

  /**
   * Номер заказа
   */
  orderNumber: string;

  /**
   * Тип заказа
   */
  type: OrderType;

  /**
   * Статус заказа
   */
  status: OrderStatus;

  /**
   * Подстатус заказа
   */
  subStatus?: OrderSubStatus;

  /**
   * Идентификатор тарифа
   */
  tariffId?: string | null;

  /**
   * Создатель заказа
   */
  creatorId: string;

  /**
   * Предварительная расчетная стоимость (сом)
   */
  initialPrice: number;

  /**
   * Итоговая стоимость заказа (сом)
   */
  finalPrice?: number | null;

  /**
   * Время создания заказа
   */
  createdAt: string;

  /**
   * Время фактического завершения заказа
   */
  completedAt?: string | null;

  /**
   * Использованный шаблон маршрута (если есть)
   */
  routeId?: string | null;

  /**
   * Начальная точка
   */
  startLocationId: string;

  /**
   * Конечная точка
   */
  endLocationId: string;

  /**
   * Промежуточные точки
   */
  additionalStops: string[];

  /**
   * Дополнительные услуги, включенные в заказ
   */
  services: GetOrderServiceDTO[];

  /**
   * Информация о пассажирах в заказе
   */
  passengers: GetPassengerDTO[];

  /**
   * Связанные поездки
   */
  rides?: GetRideDTO[];

  /**
   * Запланированное время выполнения (для предзаказов)
   */
  scheduledTime?: string | null;

  /**
   * Описание
   */
  description?: string | null;

  /**
   * Номер рейса (прилет)
   */
  airFlight?: string | null;

  /**
   * Номер рейса (вылет)
   */
  flyReis?: string | null;
}
