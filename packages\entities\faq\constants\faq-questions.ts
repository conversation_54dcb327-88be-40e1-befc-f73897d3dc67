import type { FAQItemWithCategory } from '../types';

/**
 * Список часто задаваемых вопросов для терминала Compass Transfer
 * Организованы по категориям для лучшей структуризации
 */
export const FAQ_QUESTIONS: FAQItemWithCategory[] = [
  // Общие вопросы
  {
    id: '1',
    questionKey: 'howToOrder.question',
    answerKey: 'howToOrder.answer',
    category: 'general'
  },
  {
    id: '4',
    questionKey: 'multipleStops.question',
    answerKey: 'multipleStops.answer',
    category: 'general'
  },
  {
    id: '7',
    questionKey: 'waitingTime.question',
    answerKey: 'waitingTime.answer',
    category: 'general'
  },
  {
    id: '8',
    questionKey: 'languages.question',
    answerKey: 'languages.answer',
    category: 'general'
  },
  
  // Вопросы по оплате
  {
    id: '2',
    questionKey: 'paymentMethods.question',
    answerKey: 'paymentMethods.answer',
    category: 'payment'
  },
  {
    id: '3',
    questionKey: 'priceCalculation.question',
    answerKey: 'priceCalculation.answer',
    category: 'payment'
  },
  {
    id: '6',
    questionKey: 'cancelOrder.question',
    answerKey: 'cancelOrder.answer',
    category: 'payment'
  },
  
  // Технические вопросы
  {
    id: '5',
    questionKey: 'noDriver.question',
    answerKey: 'noDriver.answer',
    category: 'technical'
  },
  {
    id: '9',
    questionKey: 'technicalIssues.question',
    answerKey: 'technicalIssues.answer',
    category: 'technical'
  },
  
  // Безопасность
  {
    id: '10',
    questionKey: 'safety.question',
    answerKey: 'safety.answer',
    category: 'safety'
  }
];

/**
 * Получить вопросы по категории
 */
export const getFAQByCategory = (category: FAQItemWithCategory['category']) => {
  return FAQ_QUESTIONS.filter(item => item.category === category);
};

/**
 * Получить все категории FAQ
 */
export const getFAQCategories = () => {
  return Array.from(new Set(FAQ_QUESTIONS.map(item => item.category)));
}; 