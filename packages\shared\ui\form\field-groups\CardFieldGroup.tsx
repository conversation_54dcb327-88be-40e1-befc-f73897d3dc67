import clsx from 'clsx';
import React from 'react';
import type { FieldGroupProps } from '@shared/ui/form/dynamic-form/types';

/**
 * Компонент для отображения группы полей формы в виде карточки
 * Каждая группа отображается как отдельная карточка с тенью и скругленными углами
 *
 * @param group - Группа полей
 * @param renderField - Функция рендеринга поля
 */
export const CardFieldGroup: React.FC<FieldGroupProps> = ({ group, renderField }) => {
  // Получаем параметры макета с дефолтными значениями
  const flexDirection = group.layout?.flexDirection || 'row';
  const itemsAlignment = group.layout?.itemsAlignment || 'stretch';
  const gridCols = group.layout?.gridCols || 2;
  const additionalClassName = group.layout?.className || '';

  // Определяем классы для контейнера группы
  const groupContainerClass = clsx(
    'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden',
    additionalClassName
  );

  // Определяем классы для выравнивания элементов
  const getAlignmentClass = (alignment: string) => {
    switch (alignment) {
      case 'start': return 'items-start';
      case 'center': return 'items-center';
      case 'end': return 'items-end';
      case 'stretch': return 'items-stretch';
      default: return 'items-stretch';
    }
  };

  return (
    <div key={group.id} className={`${groupContainerClass}`}>
      {/* Заголовок карточки */}
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium">{group.title}</h3>
        {group.description && (
          <p className="text-sm  mt-1">{group.description}</p>
        )}
      </div>

      {/* Содержимое карточки */}
      <div className="p-6">
        <div
          className={clsx(
            'w-full',
            flexDirection === 'row'
              ? `grid gap-x-6 gap-y-4 grid-cols-${gridCols}`
              : 'flex flex-col gap-y-4',
            getAlignmentClass(itemsAlignment)
          )}
          style={{
            gridTemplateColumns: flexDirection === 'row' ? `repeat(${gridCols}, 1fr)` : undefined
          }}
        >
          {/* Рендерим каждое поле группы */}
          {group.fields.map(field => renderField(field))}
        </div>
      </div>
    </div>
  );
};
