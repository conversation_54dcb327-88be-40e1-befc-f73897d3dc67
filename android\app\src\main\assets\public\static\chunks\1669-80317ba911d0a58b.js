"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1669],{4585:(e,t,r)=>{r.d(t,{f:()=>a});function a(e){if(!e)return"Только что";let t=new Date,r=new Date(e),a=Math.floor((t.getTime()-r.getTime())/6e4),i=Math.floor(a/60),n=Math.floor(i/24);return a<1?"Только что":a<60?"".concat(a," мин назад"):i<24?"".concat(i," ч назад"):n<7?"".concat(n," дн назад"):r.toLocaleDateString("ru-RU")}},12902:(e,t,r)=>{r.d(t,{l:()=>l});var a=r(27736),i=r(80745);class n extends a.vA{async getNotification(e){let t=await this.get("/".concat(e));return this.handleApiResult(t)}async getNotificationSafe(e){let t=await this.get("/".concat(e));return this.handleApiResultSafe(t)}async getNotifications(e){let t=new URLSearchParams;(null==e?void 0:e.size)!==void 0&&t.append("size",e.size.toString()),(null==e?void 0:e.userId)&&t.append("userId",e.userId),(null==e?void 0:e.isRead)!==void 0&&t.append("isRead",e.isRead.toString()),(null==e?void 0:e.dateFrom)&&t.append("dateFrom",e.dateFrom),(null==e?void 0:e.dateTo)&&t.append("dateTo",e.dateTo),(null==e?void 0:e.after)&&t.append("after",e.after),(null==e?void 0:e.type)&&t.append("type",e.type),(null==e?void 0:e.first)!==void 0&&t.append("first",e.first.toString()),(null==e?void 0:e.before)&&t.append("before",e.before),(null==e?void 0:e.last)!==void 0&&t.append("last",e.last.toString());let r=t.toString()?"?".concat(t.toString()):"",a=await this.get(r);return this.handleApiResult(a)}async createNotification(e){let t=await this.post("",e);return this.handleApiResult(t)}async updateNotification(e,t){let r=await this.put("/".concat(e),t);return this.handleApiResult(r)}async deleteNotification(e){let t=await this.delete("/".concat(e));this.handleApiResult(t)}async markAsRead(e){await this.post("read",[e])}async markAllAsRead(e){let t=await this.patch("/user/".concat(e,"/read-all"));return this.handleApiResult(t)}async getUnreadNotifications(e){let t=await this.get("/user/".concat(e,"/unread"));return this.handleApiResult(t)}async deleteById(e){let t=await this.delete("/".concat(e));this.handleApiResult(t)}async markNotificationsAsRead(e){await this.post("/read",e)}async getMyNotifications(e){let t=new URLSearchParams;(null==e?void 0:e.size)!==void 0&&t.append("size",e.size.toString()),(null==e?void 0:e.after)&&t.append("after",e.after);let r=t.toString()?"me?".concat(t.toString()):"me",a=await this.get(r);return this.handleApiResult(a)}constructor(...e){super(...e),this.baseUrl=i.QQ.NOTIFICATION.LIST}}let l=new n},15182:(e,t,r)=>{r.d(t,{_n:()=>a});var a=function(e){return e.Unknown="Unknown",e.OrderCreated="OrderCreated",e.OrderUpdated="OrderUpdated",e.OrderConfirmed="OrderConfirmed",e.OrderCancelled="OrderCancelled",e.OrderCompleted="OrderCompleted",e.RideRequest="RideRequest",e.RideAccepted="RideAccepted",e.RideRejected="RideRejected",e.RideStarted="RideStarted",e.RideCompleted="RideCompleted",e.RideCancelled="RideCancelled",e.RideUpdate="RideUpdate",e.Payment="Payment",e.PaymentReceived="PaymentReceived",e.PaymentFailed="PaymentFailed",e.PaymentRefunded="PaymentRefunded",e.DriverHeading="DriverHeading",e.DriverArrived="DriverArrived",e.DriverAssigned="DriverAssigned",e.DriverCancelled="DriverCancelled",e.DriverNearby="DriverNearby",e.System="System",e.SystemMessage="SystemMessage",e.Maintenance="Maintenance",e.Promo="Promo",e.PromoOffer="PromoOffer",e.Verification="Verification",e.Chat="Chat",e}({})},32326:(e,t,r)=>{r.d(t,{gw:()=>a.gw,k:()=>a.k}),r(15182);var a=r(55484)},39071:(e,t,r)=>{r(27736)},42997:(e,t,r)=>{r.d(t,{CU:()=>n,_J:()=>a});let a=[{key:"order",label:"О заказе",icon:"\uD83D\uDD14",bgColor:"bg-[#FFCD2833]",borderColor:"border-[#1184FF]",textColor:"",badgeColor:"bg-red-500",types:["OrderCreated","OrderUpdated","OrderConfirmed","OrderCancelled","RideRequest","RideAccepted","RideStarted","RideCancelled","DriverAssigned","DriverArrived","DriverHeading"]},{key:"important",label:"Важная информация",icon:"ℹ️",bgColor:"bg-[#0047FF33]",borderColor:"border-[#1184FF]",textColor:"",badgeColor:"bg-red-500",types:["System","SystemMessage","Maintenance","Verification","Payment","PaymentReceived"]},{key:"warning",label:"Предупреждение",icon:"⚠️",bgColor:"bg-[#FF846633]",borderColor:"border-[#1184FF]",textColor:"",badgeColor:"bg-red-500",types:["PaymentFailed","DriverCancelled","RideCancelled","OrderCancelled","RideRejected"]},{key:"completed",label:"Завершенные",icon:"✅",bgColor:"bg-green-50",borderColor:"border-[#1184FF]",textColor:"",badgeColor:"bg-red-500",types:["OrderCompleted","RideCompleted"]}],i={RideRequest:"order",RideAccepted:"order",RideStarted:"order",DriverAssigned:"order",DriverArrived:"order",DriverHeading:"order",OrderCreated:"order",OrderUpdated:"order",OrderConfirmed:"order",RideCompleted:"completed",OrderCompleted:"completed",PaymentReceived:"important",System:"important",SystemMessage:"important",Maintenance:"important",Verification:"important",Payment:"important",RideRejected:"warning",RideCancelled:"warning",OrderCancelled:"warning",DriverCancelled:"warning",PaymentFailed:"warning"},n=e=>i[e]||"order"},45452:(e,t,r)=>{r.d(t,{V:()=>i,M:()=>n});var a=r(7620);let i=(0,a.createContext)(null),n=()=>{let e=(0,a.useContext)(i);if(!e)throw Error("useNotificationContext must be used within NotificationProvider");return e}},51669:(e,t,r)=>{r.d(t,{Ow:()=>o,_n:()=>i._n,CU:()=>a.CU}),r(39071),r(12902),r(92177),r(77914);var a=r(42997);r(45452);var i=r(15182);r(71874),r(32326),r(91599),r(78816);var n=r(54568);r(7620);var l=r(4585),d=r(55484);let o=e=>{let{notification:t,priority:r="order",onClick:a,onGoToRide:i,onGoToOrderDetails:o,onDelete:s,isExpanded:c=!1,onToggleExpanded:p}=e,u=t.orderId||null,m=["RideRequestNotification","RideAcceptedNotification","RideAssignedNotification","RideCancelledNotification","RideCompletedNotification","DriverArrivedNotification","DriverHeadingNotification","RideStartedNotification"].includes(t.type)&&u&&i,g=u&&o,y=((e,t)=>{if(t)return"bg-gray-50 border-gray-200";switch(e){case"order":return"border-blue-200 bg-blue-50";case"important":case"completed":return"border-green-200 bg-green-50";case"warning":return"border-red-200 bg-red-50";default:return"border-gray-200 bg-gray-50"}})(r,t.isRead||!1);return(0,n.jsxs)("div",{className:"\n        flex flex-col gap-4 justify-between p-4 rounded-xl border transition-all bg-[#F0F0F0]\n        ".concat(y,"\n        hover:shadow-md\n      "),children:[(0,n.jsxs)("div",{className:"w-full flex items-center gap-4 cursor-pointer",onClick:()=>a(t),children:[(0,n.jsx)("div",{className:"\n          w-3 h-3 rounded-full flex-shrink-0\n          ".concat(t.isRead?"bg-gray-400":"bg-blue-500","\n        ")}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("h3",{className:"font-medium  truncate",children:t.title}),t.content&&(0,n.jsx)("p",{className:"text-sm  mt-1 line-clamp-2",children:t.content}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Тип: ",(0,d.gw)(t.type)," | Приоритет: ",r?function(e){switch(e){case"order":return"О заказе";case"completed":return"Завершенные";case"important":return"Важные";case"warning":return"Предупреждения";default:return e}}(r):"Неизвестно"]})]}),(m||g||p)&&(0,n.jsxs)("div",{className:"flex gap-2",children:[m&&(0,n.jsx)("button",{onClick:e=>{e.stopPropagation(),i(u,t.rideId||void 0,t.orderType)},className:"\n                  w-10 h-10 rounded-lg transition-colors flex items-center justify-center\n                  ".concat(c?"bg-blue-600 text-white hover:bg-blue-700":"bg-blue-500 text-white hover:bg-blue-600","\n                "),children:(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})}),g&&(0,n.jsx)("button",{onClick:e=>{e.stopPropagation(),o(u,t.rideId||void 0,t.orderType)},className:"\n                  px-2 py-2 rounded-lg transition-colors flex items-center justify-center\n                  ".concat(c?"bg-gray-600 text-white hover:bg-gray-700":"bg-gray-500 text-white hover:bg-gray-600","\n                "),children:"Перейти к заказу"}),p&&(0,n.jsx)("button",{onClick:e=>{e.stopPropagation(),p(t)},className:"\n                  w-10 h-10 rounded-lg transition-colors flex items-center justify-center\n                  ".concat(c?"bg-gray-600 text-white hover:bg-gray-700":"bg-gray-500 text-white hover:bg-gray-600","\n                "),children:(0,n.jsx)("svg",{className:"w-5 h-5 transition-transform ".concat(c?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),(0,n.jsx)("div",{className:"text-sm text-gray-500 flex-shrink-0",children:(0,l.f)(t.createdAt)})]}),c&&(0,n.jsxs)("div",{className:"p-4 bg-white rounded-lg text-sm space-y-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold  mb-1",children:"Полное содержание"}),(0,n.jsx)("p",{children:t.content||"Уважаемый водитель, в вашем районе появился новый заказ. Обратите внимание: – Клиент ожидает быстрое подтверждение – Расстояние до точки подачи — минимальное – Оплата — по стандартному тарифу или выше (если действует повышенный спрос). Чем быстрее вы принимаете заказы, тем выше ваш приоритет в системе и больше шанс получать выгодные поездки. Не забывайте: частый отказ от заказов может повлиять на ваш рейтинг. Спасибо, что работаете с нами!"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold  mb-1",children:"Тип уведомления"}),(0,n.jsx)("p",{children:(0,d.gw)(t.type)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold  mb-1",children:"Статус"}),(0,n.jsx)("p",{children:t.isRead?"Прочитано":"Не прочитано"})]})]})]})]})}},55484:(e,t,r)=>{r.d(t,{gw:()=>i,k:()=>n});var a=r(15182);function i(e){switch(e){case a._n.Unknown:return"Неизвестно";case a._n.OrderCreated:return"Заказ создан";case a._n.OrderUpdated:return"Заказ изменен";case a._n.OrderConfirmed:return"Заказ подтвержден";case a._n.OrderCancelled:return"Заказ отменен";case a._n.OrderCompleted:return"Заказ завершен";case a._n.RideRequest:return"Запрос на поездку";case a._n.RideAccepted:return"Поездка принята";case a._n.RideRejected:return"Поездка отклонена";case a._n.RideStarted:return"Поездка началась";case a._n.RideCompleted:return"Поездка завершена";case a._n.RideCancelled:return"Поездка отменена";case a._n.RideUpdate:return"Обновление информации о поездке";case a._n.Payment:return"Платеж";case a._n.PaymentReceived:return"Платеж получен";case a._n.PaymentFailed:return"Ошибка платежа";case a._n.PaymentRefunded:return"Платеж возвращен";case a._n.DriverHeading:return"Водитель в пути";case a._n.DriverArrived:return"Водитель прибыл";case a._n.DriverAssigned:return"Водитель назначен";case a._n.DriverCancelled:return"Водитель отменил";case a._n.DriverNearby:return"Водитель рядом";case a._n.System:return"Системное уведомление";case a._n.SystemMessage:return"Системное сообщение";case a._n.Maintenance:return"Техническое обслуживание";case a._n.Promo:return"Промо-акция";case a._n.PromoOffer:return"Промо-предложение";case a._n.Verification:return"Верификация";case a._n.Chat:return"Сообщение в чате";default:return""}}function n(){return Object.values(a._n).map(e=>({value:e,label:i(e)}))}},71874:(e,t,r)=>{r.d(t,{fU:()=>n,$Z:()=>l,d1:()=>d,tn:()=>o});var a=r(64942),i=r(32326);let n={title:{create:"Создание уведомления",edit:"Редактирование уведомления"},description:{create:"Заполните информацию для создания уведомления",edit:"Редактирование информации уведомления"},groups:[{id:"default",title:"Основная информация",fields:[{name:"type",label:"Тип уведомления",type:a.V.SELECT,dataType:a.R.OBJECT,required:!0,placeholder:"Выберите тип уведомления",options:(0,i.k)()},{name:"title",label:"Заголовок",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите заголовок",helperText:"Заголовок уведомления"},{name:"content",label:"Содержание",type:a.V.TEXTAREA,dataType:a.R.STRING,required:!1,placeholder:"Введите содержание",helperText:"Текст уведомления"},{name:"orderId",label:"ID заказа",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID заказа",helperText:"Ссылка на связанный заказ"},{name:"id",label:"ID поездки",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID поездки",helperText:"Ссылка на связанную поездку"},{name:"isRead",label:"Прочитано",type:a.V.CHECKBOX,dataType:a.R.BOOLEAN,required:!1,helperText:"Прочитано ли уведомление пользователем"},{name:"userId",label:"ID пользователя",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите ID пользователя",helperText:"Получатель уведомления"}],layout:{gridCols:2,gapX:4,gapY:4}}]},l={title:{create:"Просмотр уведомления",edit:"Редактирование уведомления"},description:{create:"Просмотр информации уведомления",edit:"Редактирование информации уведомления"},groups:[{id:"default",title:"Основная информация",fields:[{name:"id",label:"ID",type:a.V.TEXT,dataType:a.R.STRING,required:!0,disabled:!0},{name:"type",label:"Тип уведомления",type:a.V.SELECT,dataType:a.R.OBJECT,required:!0,placeholder:"Выберите тип уведомления",options:(0,i.k)()},{name:"title",label:"Заголовок",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите заголовок",helperText:"Заголовок уведомления"},{name:"content",label:"Содержание",type:a.V.TEXTAREA,dataType:a.R.STRING,required:!1,placeholder:"Введите содержание",helperText:"Текст уведомления"},{name:"orderId",label:"ID заказа",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID заказа",helperText:"Ссылка на связанный заказ"},{name:"id",label:"ID поездки",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID поездки",helperText:"Ссылка на связанную поездку"},{name:"isRead",label:"Прочитано",type:a.V.CHECKBOX,dataType:a.R.BOOLEAN,required:!1,helperText:"Прочитано ли уведомление пользователем"}],layout:{gridCols:2,gapX:4,gapY:4}}]},d={title:{create:"Создание уведомления",edit:"Редактирование уведомления"},description:{create:"Заполните информацию для создания уведомления",edit:"Редактирование информации уведомления"},groups:[{id:"default",title:"Основная информация",fields:[{name:"type",label:"Тип уведомления",type:a.V.SELECT,dataType:a.R.OBJECT,required:!0,placeholder:"Выберите тип уведомления",options:(0,i.k)()},{name:"title",label:"Заголовок",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите заголовок",helperText:"Заголовок уведомления"},{name:"content",label:"Содержание",type:a.V.TEXTAREA,dataType:a.R.STRING,required:!1,placeholder:"Введите содержание",helperText:"Текст уведомления"},{name:"orderId",label:"ID заказа",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID заказа",helperText:"Ссылка на связанный заказ"},{name:"id",label:"ID поездки",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID поездки",helperText:"Ссылка на связанную поездку"},{name:"isRead",label:"Прочитано",type:a.V.CHECKBOX,dataType:a.R.BOOLEAN,required:!1,helperText:"Прочитано ли уведомление пользователем"}],layout:{gridCols:2,gapX:4,gapY:4}}]},o={title:{create:"Отметить уведомление как прочитанное",edit:"Отметить уведомление как прочитанное"},description:{create:"Укажите ID уведомления для отметки как прочитанное",edit:"Укажите ID уведомления для отметки как прочитанное"},groups:[{id:"default",title:"Информация об уведомлении",fields:[{name:"id",label:"ID уведомления",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите ID уведомления",helperText:"Уникальный идентификатор уведомления"}],layout:{gridCols:1,gapX:4,gapY:4}}]}},77914:(e,t,r)=>{r.d(t,{c:()=>p});var a=r(54568),i=r(27261),n=r.n(i),l=r(7620),d=r(66251),o=r(15182),s=r(32326);let c=(0,l.lazy)(()=>r.e(1523).then(r.bind(r,11523))),p={columns:[{id:"title",label:"Заголовок",accessor:"title",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,d.mJ)((0,s.k)(),[o._n.Unknown])},renderCell:e=>(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-[color:var(--color-primary-50)] text-[color:var(--color-primary)]",children:(0,s.gw)(e.type)})},{id:"content",label:"Содержание",accessor:"content",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","isEmpty","isNotEmpty"]},renderCell:e=>(0,a.jsx)("span",{className:"line-clamp-2",children:e.content||(0,a.jsx)("span",{className:"text-[color:var(--color-neutral-400)]",children:"Нет содержания"})})},{id:"isRead",label:"Прочитано",accessor:"isRead",sortable:!0,filterable:!0,filterConfig:{fieldType:"boolean"},renderCell:e=>(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(e.isRead?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.isRead?"Да":"Нет"})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(n(),{href:"/notifications/".concat(e.id),className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,a.jsx)(c,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"Title",label:"Заголовок",operator:"contains",operatorField:"TitleOp"},{field:"Content",label:"Содержание",operator:"contains",operatorField:"ContentOp"}]},filtersConfig:{availableFilters:[{field:"Type",label:"Тип уведомления",type:"select",options:(0,d.mJ)((0,s.k)(),[o._n.Unknown]).map(e=>({value:e.value,label:e.label}))},{field:"IsRead",label:"Статус прочтения",type:"boolean"}]}}},78816:(e,t,r)=>{r.d(t,{gz:()=>n,o6:()=>l,mm:()=>d});var a=r(75006);let i=a.z.enum(["Unknown","OrderCreated","OrderUpdated","OrderConfirmed","OrderCancelled","OrderCompleted","RideRequest","RideAccepted","RideRejected","RideStarted","RideCompleted","RideCancelled","RideUpdate","Payment","PaymentReceived","PaymentFailed","PaymentRefunded","DriverHeading","DriverArrived","DriverAssigned","DriverCancelled","DriverNearby","System","SystemMessage","Maintenance","Promo","PromoOffer","Verification","Chat"]);a.z.enum(["Home","Work","Airport","Station","Hotel","Restaurant","Shop","Entertainment","Medical","Educational","BusinessCenter","Other"]);let n=a.z.object({type:i,title:a.z.string(),content:a.z.string().nullable().optional(),orderId:a.z.string().uuid().nullable().optional(),rideId:a.z.string().uuid().nullable().optional(),isRead:a.z.boolean().optional(),userId:a.z.string().uuid()}),l=a.z.object({type:i,title:a.z.string(),content:a.z.string().nullable().optional(),orderId:a.z.string().uuid().nullable().optional(),rideId:a.z.string().uuid().nullable().optional(),isRead:a.z.boolean().optional(),id:a.z.string().uuid()}),d=a.z.object({id:a.z.string().uuid()})},91599:(e,t,r)=>{r.d(t,{E:()=>n});var a=r(7620),i=r(12902);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,[t,r]=(0,a.useState)([]),[n,l]=(0,a.useState)(!1),[d,o]=(0,a.useState)(!1),[s,c]=(0,a.useState)(null),[p,u]=(0,a.useState)(!0),[m,g]=(0,a.useState)(0),y=(0,a.useRef)(void 0),h=(0,a.useCallback)(async function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDD04 useNotifications.loadNotifications:",{append:t,lastCursor:y.current}),t?o(!0):(l(!0),y.current=void 0),c(null);let a=await i.l.getMyNotifications({size:e,after:t?y.current:void 0}),n=a.data||[];if(console.log("\uD83D\uDCCA useNotifications результат:",{newCount:n.length,hasNext:a.hasNext,totalCount:a.totalCount,append:t}),t?r(e=>[...e,...n]):r(n),g(a.totalCount||0),u(a.hasNext||!1),n.length>0){let e=n[n.length-1].id;console.log("\uD83C\uDFAF useNotifications обновляем курсор:",e),y.current=e}}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки уведомлений";console.error("❌ useNotifications ошибка:",t),c(e)}finally{l(!1),o(!1)}},[e]),f=(0,a.useCallback)(async()=>{if(!p||d||n)return void console.log("\uD83D\uDEAB useNotifications.loadMore заблокирован:",{hasMore:p,isLoadingMore:d,isLoading:n});console.log("\uD83D\uDCE5 useNotifications.loadMore: загружаем следующую страницу"),await h(!0)},[p,d,n,h]),b=(0,a.useCallback)(async()=>{console.log("\uD83D\uDD04 useNotifications.refresh"),await h(!1)},[h]),R=(0,a.useCallback)(async e=>{try{await i.l.markAsRead(e),r(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),console.log("✅ useNotifications.markAsRead успешно:",e)}catch(e){throw console.error("❌ useNotifications.markAsRead ошибка:",e),e}},[]),v=(0,a.useCallback)(async e=>{try{await i.l.deleteNotification(e),r(t=>t.filter(t=>t.id!==e)),console.log("\uD83D\uDDD1️ useNotifications.deleteNotification успешно:",e)}catch(e){throw console.error("❌ useNotifications.deleteNotification ошибка:",e),e}},[]),C=t.filter(e=>!e.isRead).length;return{notifications:t,isLoading:n,isLoadingMore:d,error:s,hasMore:p,totalCount:m,unreadCount:C,actions:{loadNotifications:h,loadMore:f,refresh:b,markAsRead:R,deleteNotification:v}}}},92177:(e,t,r)=>{r.d(t,{a:()=>n,t:()=>i});var a=r(71874);let i={base:a.d1,create:a.fU,get:a.$Z,read:a.tn},n=a.d1}}]);