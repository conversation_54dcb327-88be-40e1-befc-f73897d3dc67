import { NextResponse, type NextRequest } from 'next/server';

export async function GET() {
  return NextResponse.json({ message: 'Sidebar API endpoint' });
}

export async function POST(request: NextRequest) {
  try {
    const { collapsed } = await request.json();

    // Валидация значения состояния сайдбара
    if (typeof collapsed !== 'boolean') {
      return NextResponse.json(
        { error: 'Invalid collapsed value. Must be boolean' },
        { status: 400 },
      );
    }

    // Создаем ответ с установкой куки
    const response = NextResponse.json({
      success: true,
      collapsed,
      message: 'Sidebar state updated successfully',
    });

    // Устанавливаем куки с состоянием сайдбара (срок действия 1 год)
    response.cookies.set('sidebar-collapsed', collapsed.toString(), {
      httpOnly: false, // Позволяем читать из клиентского JS
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 365, // 1 год
      path: '/',
    });

    return response;
  } catch (_error) {
    return NextResponse.json({ error: 'Failed to update sidebar state' }, { status: 500 });
  }
}
