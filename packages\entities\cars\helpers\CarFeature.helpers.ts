import { CarFeature } from '@entities/cars/enums';

/**
 * Проверяет, является ли значение допустимым значением CarFeature
 * @param {unknown} value Значение для проверки
 * @returns {boolean} true, если значение допустимо
 */
export function isCarFeature(value: unknown): value is CarFeature {
  return Object.values(CarFeature).includes(value as CarFeature);
}
/**
 * Получает метку для значения CarFeature
 * @param {CarFeature} value Значение CarFeature
 * @returns {string} Метка
 */
export function getCarFeatureLabel(value: CarFeature): string {
  switch (value) {
    case CarFeature.AirConditioning:
      return 'Кондиционер';
    case CarFeature.ClimateControl:
      return 'Климат-контроль';
    case CarFeature.LeatherSeats:
      return 'Кожаные сиденья';
    case CarFeature.HeatedSeats:
      return 'Подогрев сидений';
    case CarFeature.Bluetooth:
      return 'Bluetooth';
    case CarFeature.USBPort:
      return 'USB-порт';
    case CarFeature.AuxInput:
      return 'AUX-вход';
    case CarFeature.Navigation:
      return 'Навигация';
    case CarFeature.BackupCamera:
      return 'Камера заднего вида';
    case CarFeature.ParkingSensors:
      return 'Парковочные датчики';
    case CarFeature.Sunroof:
      return 'Люк';
    case CarFeature.PanoramicRoof:
      return 'Панорамная крыша';
    case CarFeature.ThirdRowSeats:
      return 'Третий ряд сидений';
    case CarFeature.ChildSeat:
      return 'Детское кресло';
    case CarFeature.WheelchairAccess:
      return 'Доступ для инвалидных колясок';
    case CarFeature.Wifi:
      return 'Wi-Fi';
    case CarFeature.PremiumAudio:
      return 'Премиальная аудиосистема';
    case CarFeature.AppleCarplay:
      return 'Apple CarPlay';
    case CarFeature.AndroidAuto:
      return 'Android Auto';
    case CarFeature.SmokingAllowed:
      return 'Разрешено курение';
    case CarFeature.PetFriendly:
      return 'Допускаются животные';
    case CarFeature.LuggageCarrier:
      return 'Багажник на крыше';
    case CarFeature.BikeRack:
      return 'Велосипедная стойка';
    default:
      return '';
  }
}
/**
 * Получает массив всех значений CarFeature
 * @returns {CarFeature[]} Массив всех значений
 */
export function getCarFeatureValues(): CarFeature[] {
  return Object.values(CarFeature);
}
/**
 * Получает массив всех меток CarFeature
 * @returns {string[]} Массив всех меток
 */
export function getCarFeatureLabels(): string[] {
  return getCarFeatureValues().map(value => getCarFeatureLabel(value));
}
/**
 * Получает массив объектов { value, label } для CarFeature
 * @returns {{ value: CarFeature, label: string }[]} Массив объектов
 */
export function getCarFeatureOptions(): { value: CarFeature, label: string }[] {
  return getCarFeatureValues().map(value => ({
    value,
    label: getCarFeatureLabel(value)
  }));
}