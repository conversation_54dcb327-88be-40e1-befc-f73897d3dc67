import { FormFieldType, FormFieldDataType, type FormConfig } from '@entities/collections/types/formTypes';
import { getNotificationTypeOptions } from '@entities/notifications/helpers';

/**
 * Конфигурация формы для CreateNotificationDTO
 */
export const CreateNotificationDTOFormConfig: FormConfig = {
  title: {
    create: 'Создание уведомления',
    edit: 'Редактирование уведомления'
  },
  description: {
    create: 'Заполните информацию для создания уведомления',
    edit: 'Редактирование информации уведомления'
  },
  groups: [
  {
    id: "default",
    title: "Основная информация",
    fields: [
      {
        name: "type",
        label: "Тип уведомления",
        type: FormFieldType.SELECT,
        dataType: FormFieldDataType.OBJECT,
        required: true,
        placeholder: "Выберите тип уведомления",
        options: getNotificationTypeOptions()
      },
      {
        name: "title",
        label: "Заголовок",
        type: FormFieldType.TEXT,
        dataType: FormFieldDataType.STRING,
        required: true,
        placeholder: "Введите заголовок",
        helperText: "Заголовок уведомления"
      },
      {
        name: "content",
        label: "Содержание",
        type: FormFieldType.TEXTAREA,
        dataType: FormFieldDataType.STRING,
        required: false,
        placeholder: "Введите содержание",
        helperText: "Текст уведомления"
      },
      {
        name: "orderId",
        label: "ID заказа",
        type: FormFieldType.TEXT,
        dataType: FormFieldDataType.STRING,
        required: false,
        placeholder: "Введите ID заказа",
        helperText: "Ссылка на связанный заказ"
      },
      {
        name: "id",
        label: "ID поездки",
        type: FormFieldType.TEXT,
        dataType: FormFieldDataType.STRING,
        required: false,
        placeholder: "Введите ID поездки",
        helperText: "Ссылка на связанную поездку"
      },
      {
        name: "isRead",
        label: "Прочитано",
        type: FormFieldType.CHECKBOX,
        dataType: FormFieldDataType.BOOLEAN,
        required: false,
        helperText: "Прочитано ли уведомление пользователем"
      },
      {
        name: "userId",
        label: "ID пользователя",
        type: FormFieldType.TEXT,
        dataType: FormFieldDataType.STRING,
        required: true,
        placeholder: "Введите ID пользователя",
        helperText: "Получатель уведомления"
      }
    ],
    layout: {
      gridCols: 2,
      gapX: 4,
      gapY: 4
    }
  }
]
};
/**
 * Получает конфигурацию формы для CreateNotificationDTO
 * @returns {FormConfig<Record<string, unknown>>} Конфигурация формы
 */
export function getCreateNotificationDTOFormConfig(): FormConfig {
  return CreateNotificationDTOFormConfig;
}