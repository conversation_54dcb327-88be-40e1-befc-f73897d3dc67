"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7141],{5992:(e,t,n)=>{n(54568),n(7620),n(3884);var a=n(62117),r=n.n(a);delete r().Icon.Default.prototype._getIconUrl,r().Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"})},20089:(e,t,n)=>{n(54568),n(7620),n(83418),n(42159)},28285:(e,t,n)=>{n(7620)},31290:(e,t,n)=>{n(54568),n(7620),n(83418),n(42159)},35035:(e,t,n)=>{n.d(t,{I:()=>a});let a=(0,n(7620).createContext)(null)},42159:(e,t,n)=>{n(54568),n(7620)},48311:(e,t,n)=>{var a=n(54568),r=n(7620);r.memo(e=>{let{navigationItems:t,activeSection:n,onSectionClick:s,className:o=""}=e;if(t.length<2)return null;let i=(0,r.useCallback)(e=>{s(e)},[s]);return(0,a.jsx)("aside",{className:"min-w-[369px] ".concat(o),children:(0,a.jsxs)("div",{className:"w-full sticky top-0 h-fit overflow-hidden",children:[(0,a.jsx)("div",{className:"px-4 py-3 border-b",children:(0,a.jsxs)("h3",{className:"text-sm font-semibold flex items-center",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})}),"Навигация по форме"]})}),(0,a.jsx)("nav",{className:"p-2 overflow-y-auto",children:(0,a.jsx)("div",{className:"space-y-1",children:t.map((e,t)=>{let r=n===e.id;return(0,a.jsx)(l,{item:e,index:t,isActive:r,onClick:i},e.id)})})})]})})}).displayName="NavigationSidebar";let l=r.memo(e=>{let{item:t,index:n,isActive:l,onClick:s}=e,o=(0,r.useCallback)(()=>{s(t.id)},[s,t.id]);return(0,a.jsx)("button",{type:"button",onClick:o,className:"\n        group w-full text-left px-3 py-2.5 rounded-lg text-sm font-medium\n        transition-all duration-200 ease-in-out\n        flex items-center justify-between cursor-pointer\n        ".concat(l?"bg-[color:var(--color-brand)]/10 shadow-sm":"hover:bg-gray-50/20","\n      "),children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"\n            w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold mr-3\n            transition-colors duration-200\n            ".concat(l?"bg-[color:var(--color-brand)] text-white":"bg-gray-200 text-gray-600 group-hover:bg-gray-300","\n          "),children:n+1}),(0,a.jsx)("span",{className:"truncate",children:t.title})]})})});l.displayName="NavigationButton"},48630:(e,t,n)=>{n(54568),n(7620),n(83418),n(42159)},55668:(e,t,n)=>{n(54568),n(7620),n(83418),n(42159)},57563:(e,t,n)=>{n(54568),n(7620),n(83418),n(42159)},59959:(e,t,n)=>{n.d(t,{k:()=>l});var a=n(7620),r=n(35035);function l(){let e=(0,a.useContext)(r.I);if(!e)throw Error("useSignalR must be used within a SignalRProvider");return e}},62885:(e,t,n)=>{n(54568),n(7620),n(8703),n(83418),n(42159)},77579:(e,t,n)=>{n(54568),n(7620),n(56583),n(83418),n(42159)},79394:(e,t,n)=>{n.d(t,{wh:()=>a.w}),n(53850);var a=n(34763);n(54568),n(7620)},80408:(e,t,n)=>{n(54568),n(90739),n(7620),n(78032),n(59959),n(11447),n(68998),n(57563),n(31290),n(61773),n(56583),n(83418),n(42159),n(5992),n(20089),n(77579),n(62885),n(48630),n(55668),n(86785),n(64942),n(48311)},86785:(e,t,n)=>{n(54568),n(7620),n(83418),n(42159)}}]);