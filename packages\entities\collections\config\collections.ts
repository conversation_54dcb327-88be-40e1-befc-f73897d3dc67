// Типы для компонентов коллекций
import type { ComponentType } from 'react';
import type {
  ListComponentProps,
  ManageComponentProps,
  CustomComponent,
} from '@entities/collections/types/types';
import type { Role } from '@entities/users/enums/Role.enum';

export const COLLECTION_NAMES = [
  'profile',
  'users',
  'cars',
  'locations',
  'services',
  'notifications',
  'orders',
  'tariffs',
  'settings',
] as const;
export type CollectionName = (typeof COLLECTION_NAMES)[number]; // Коллекции с таблицами (исключаем profile)
export const TABLE_COLLECTION_NAMES = [
  'users',
  'cars',
  'locations',
  'services',
  'notifications',
  'orders',
  'tariffs',
] as const;
export type TableCollectionName = (typeof TABLE_COLLECTION_NAMES)[number]; // Типы для компонентов по ролям
export interface RoleBasedComponents {
  /** Компонент для отображения списка элементов коллекции */
  listComponent?: ComponentType<ListComponentProps>;
  /** Компонент для создания нового элемента коллекции */
  manageComponent?: ComponentType<ManageComponentProps>;
  /** Кастомный компонент */
  customComponent?: ComponentType<CustomComponent>;
} // Типы для конфигурации коллекций
export interface CollectionConfig {
  /** Отображаемое название коллекции */
  label: string;
  /** Описание коллекции */
  description: string;
  /** Иконка коллекции */
  icon: string;
  /** Компонент для отображения списка элементов коллекции (по умолчанию) */
  listComponent?: ComponentType<ListComponentProps>;
  /** Компонент для создания нового элемента коллекции (по умолчанию) */
  manageComponent?: ComponentType<ManageComponentProps>;
  /** Кастомный компонент (по умолчанию) */
  customComponent?: ComponentType<CustomComponent>;
  /** Компоненты для конкретных ролей */
  roleComponents?: Partial<Record<Role, RoleBasedComponents>>;
}
// Реестр компонентов (заполняется динамически)
const componentRegistry: Record<string, CollectionConfig> = {}; // Базовая конфигурация коллекций (без компонентов)
const baseCollectionsConfig: Record<
  string,
  Omit<CollectionConfig, 'listComponent' | 'manageComponent' | 'customComponent' | 'roleComponents'>
> = {
  profile: {
    label: 'Мой профиль',
    description: 'Просмотр и управление личным профилем пользователя',
    icon: 'ProfileIcon',
  },
  users: {
    label: 'Пользователи',
    description: 'Управление пользователями системы (водители, клиенты, администраторы)',
    icon: 'UsersIcon',
  },
  cars: {
    label: 'Автомобили',
    description: 'Управление автомобилями компании с их характеристиками',
    icon: 'VehiclesIcon',
  },
  locations: {
    label: 'Локации',
    description: 'Управление локациями (адреса, координаты, типы мест)',
    icon: 'ReferencesIcon',
  },
  services: {
    label: 'Услуги',
    description: 'Управление услугами и их ценами',
    icon: 'TariffsIcon',
  },
  tariffs: {
    label: 'Тарифы',
    description: 'Управление тарифами на перевозки и услуги',
    icon: 'TariffsIcon',
  },
  orders: {
    label: 'Заказы',
    description: 'Управление заказами клиентов на перевозки',
    icon: 'OrdersIcon',
  },
  notifications: {
    label: 'Уведомления',
    description: 'Управление уведомлениями для пользователей',
    icon: 'OrdersIcon',
  },
  settings: {
    label: 'Настройки',
    description: 'Настройки системы и пользовательского интерфейса',
    icon: 'SettingsIcon',
  },
}; // Функция для регистрации компонентов (обычная)

export function registerCollectionComponents(
  collectionName: string,
  components: Partial<
    Pick<CollectionConfig, 'listComponent' | 'manageComponent' | 'customComponent'>
  >,
) {
  const baseConfig = baseCollectionsConfig[collectionName];

  if (baseConfig) {
    componentRegistry[collectionName] = {
      ...baseConfig,
      ...components,
    };
  }
} // Функция для регистрации компонентов для конкретной роли
export function registerCollectionComponentsForRole(
  collectionName: string,
  role: Role,
  components: RoleBasedComponents,
) {
  const baseConfig = baseCollectionsConfig[collectionName];

  if (baseConfig) {
    // Получаем существующую конфигурацию или создаем новую
    const existingConfig = componentRegistry[collectionName] || { ...baseConfig }; // Инициализируем roleComponents если его нет

    if (!existingConfig.roleComponents) {
      existingConfig.roleComponents = {};
    } // Добавляем компоненты для роли
    existingConfig.roleComponents[role] = components; // Сохраняем обновленную конфигурацию
    componentRegistry[collectionName] = existingConfig;
  }
} // Геттер для получения конфигурации
export const collectionsConfig = new Proxy({} as Record<string, CollectionConfig>, {
  get(_target, prop: string) {
    return componentRegistry[prop] || baseCollectionsConfig[prop];
  },
  ownKeys() {
    return Object.keys(baseCollectionsConfig);
  },
  has(_target, prop: string) {
    return prop in baseCollectionsConfig;
  },
  getOwnPropertyDescriptor(_target, prop: string) {
    if (prop in baseCollectionsConfig) {
      return {
        enumerable: true,
        configurable: true,
      };
    }

    return undefined;
  },
}); // Функции для получения компонентов с учетом роли
export function getListComponent(
  collectionName: string,
  role?: Role | null,
): ComponentType<ListComponentProps> | null {
  const collection = collectionsConfig[collectionName]; // Сначала проверяем компонент для конкретной роли

  if (role && collection?.roleComponents?.[role]?.listComponent) {
    return collection.roleComponents[role].listComponent;
  } // Если нет компонента для роли, возвращаем компонент по умолчанию

  return collection?.listComponent || null;
}
export function getCreateComponent(
  collectionName: string,
  role?: Role | null,
): ComponentType<ManageComponentProps> | null {
  const collection = collectionsConfig[collectionName]; // Сначала проверяем компонент для конкретной роли

  if (role && collection?.roleComponents?.[role]?.manageComponent) {
    return collection.roleComponents[role].manageComponent;
  } // Если нет компонента для роли, возвращаем компонент по умолчанию

  return collection?.manageComponent || null;
}
export function getEditComponent(
  collectionName: string,
  role?: Role | null,
): ComponentType<ManageComponentProps> | null {
  const collection = collectionsConfig[collectionName]; // Сначала проверяем компонент для конкретной роли

  if (role && collection?.roleComponents?.[role]?.manageComponent) {
    return collection.roleComponents[role].manageComponent;
  } // Если нет компонента для роли, возвращаем компонент по умолчанию

  return collection?.manageComponent || null;
}
export function getCustomComponent(
  collectionName: string,
  role?: Role | null,
): ComponentType<CustomComponent> | null {
  const collection = collectionsConfig[collectionName]; // Сначала проверяем компонент для конкретной роли

  if (role && collection?.roleComponents?.[role]?.customComponent) {
    return collection.roleComponents[role].customComponent;
  } // Если нет компонента для роли, возвращаем компонент по умолчанию

  return collection?.customComponent || null;
}
