import { LocationType } from '@entities/notifications/enums';

/**
 * Получает текстовое представление типа местоположения
 * @param type Тип местоположения
 * @returns Текстовое представление типа местоположения
 */
export function getLocationTypeLabel(type: LocationType): string {
  switch (type) {
    case LocationType.Home:
      return 'Дом';
    case LocationType.Work:
      return 'Работа';
    case LocationType.Airport:
      return 'Аэропорт';
    case LocationType.Station:
      return 'Вокзал';
    case LocationType.Hotel:
      return 'Отель';
    case LocationType.Restaurant:
      return 'Ресторан';
    case LocationType.Shop:
      return 'Магазин';
    case LocationType.Entertainment:
      return 'Развлекательное заведение';
    case LocationType.Medical:
      return 'Медицинское учреждение';
    case LocationType.Educational:
      return 'Образовательное учреждение';
    case LocationType.BusinessCenter:
      return 'Бизнес-центр';
    case LocationType.Other:
      return 'Другое';
    default:
      return 'Неизвестно';
  }
}
/**
 * Получает список опций для типов местоположений
 * @returns Массив опций для выпадающего списка
 */
export function getLocationTypeOptions() {
  return [
    { value: LocationType.Home, label: getLocationTypeLabel(LocationType.Home) },
    { value: LocationType.Work, label: getLocationTypeLabel(LocationType.Work) },
    { value: LocationType.Airport, label: getLocationTypeLabel(LocationType.Airport) },
    { value: LocationType.Station, label: getLocationTypeLabel(LocationType.Station) },
    { value: LocationType.Hotel, label: getLocationTypeLabel(LocationType.Hotel) },
    { value: LocationType.Restaurant, label: getLocationTypeLabel(LocationType.Restaurant) },
    { value: LocationType.Shop, label: getLocationTypeLabel(LocationType.Shop) },
    { value: LocationType.Entertainment, label: getLocationTypeLabel(LocationType.Entertainment) },
    { value: LocationType.Medical, label: getLocationTypeLabel(LocationType.Medical) },
    { value: LocationType.Educational, label: getLocationTypeLabel(LocationType.Educational) },
    { value: LocationType.BusinessCenter, label: getLocationTypeLabel(LocationType.BusinessCenter) },
    { value: LocationType.Other, label: getLocationTypeLabel(LocationType.Other) },
  ];
}
