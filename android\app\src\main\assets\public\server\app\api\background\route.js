(()=>{var e={};e.id=4626,e.ids=[4626],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6063:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>p});var a=t(48106),n=t(48819),o=t(12050),u=t(65208),i=t(4235);async function p(e){try{let{type:r,value:t}=await e.json();if(!["color","image"].includes(r))return i.NextResponse.json({error:"Неверный тип фона"},{status:400});if("string"!=typeof t||0===t.length)return i.NextResponse.json({error:"Неверное значение фона"},{status:400});let s=await (0,u.UL)();return s.set("driver-background-type",r,{maxAge:31536e3,httpOnly:!1,secure:!0,sameSite:"lax"}),s.set("driver-background-value",t,{maxAge:31536e3,httpOnly:!1,secure:!0,sameSite:"lax"}),i.NextResponse.json({success:!0})}catch(e){return console.error("Ошибка при сохранении фона:",e),i.NextResponse.json({error:"Внутренняя ошибка сервера"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/background/route",pathname:"/api/background",filename:"route",bundlePath:"app/api/background/route"},resolvedPagePath:"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\api\\background\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function g(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80408:()=>{},87032:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,5208,3744],()=>t(6063));module.exports=s})();