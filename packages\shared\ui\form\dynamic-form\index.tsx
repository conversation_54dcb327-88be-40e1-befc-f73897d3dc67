'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React, { useCallback } from 'react';
import { useForm, FormProvider, useFormContext, type FieldValues, type DefaultValues } from 'react-hook-form';
import { useFormNavigation } from '@shared/hooks';
import { FieldGroup } from '@shared/ui/form/dynamic-form/components/FieldGroup';
import { FormActions } from '@shared/ui/form/dynamic-form/components/FormActions';
import { FormErrors } from '@shared/ui/form/dynamic-form/components/FormErrors';
import { useFormOptions } from '@shared/ui/form/dynamic-form/hooks/useFormOptions';
import { renderField } from '@shared/ui/form/dynamic-form/renderers/renderField';
import type { DynamicFormProps } from '@shared/ui/form/dynamic-form/types';
import { normalizeFormErrors } from '@shared/ui/form/dynamic-form/utils/normalizeFormErrors';
import { NavigationSidebar } from '@shared/ui/navigation-sidebar';
import { useManageCollectionContext } from '@entities/collections/context/ManageCollectionContext';
import type { FormField } from '@entities/collections/types/formTypes';



/**
 * Внутренний компонент DynamicForm с доступом к контексту формы
 */
const DynamicFormContent = <T extends FieldValues>({
  config,
  onSubmit,
  isLoading = false,
  mode,
  onCancel,
  fieldOptionsProviders,
  FieldGroupComponent = FieldGroup,
}: Omit<DynamicFormProps<T>, 'initialData' | 'validationSchema'>): React.ReactElement => {
  const { lazyOptions, loadingOptions } = useFormOptions(fieldOptionsProviders);
  const {
    handleSubmit,
    formState: { errors },
  } = useFormContext<T>();

  // Получаем контекст управления коллекцией для доступа к ошибкам валидации
  let validationErrors = null;
  let setValidationErrors = null;
  let collectionName = '';
  
  try {
    const context = useManageCollectionContext();

    validationErrors = context.validationErrors;
    setValidationErrors = context.setValidationErrors;
    collectionName = context.collectionName;
  } catch {
    // Контекст недоступен - форма используется вне ManageCollection
  }

  // Хук для навигации по форме
  const { navigationItems, activeSection, scrollToSection } = useFormNavigation(config);

  const processFormData = (data: unknown): T => {
    return data as T;
  };
  const sortedGroups = [...config.groups].sort((a, b) => (a.order || 0) - (b.order || 0));
  const normalizedErrors = normalizeFormErrors(errors);

  // Функция для подсчета ошибок в группе полей
  const getGroupErrors = (groupId: string) => {
    const group = config.groups.find(g => g.id === groupId);

    if (!group || !group.fields) return { hasErrors: false, errorCount: 0 };

    let errorCount = 0;
    const checkFieldsForErrors = (fields: FormField[]) => {
      fields.forEach(field => {
        if (normalizedErrors[field.name]) {
          errorCount++;
        }
        // Рекурсивно проверяем вложенные поля
        if (field.fields) {
          checkFieldsForErrors(field.fields);
        }
      });
    };

    checkFieldsForErrors(group.fields);

    return { hasErrors: errorCount > 0, errorCount };
  };

  // Обогащаем navigationItems информацией об ошибках
  const enrichedNavigationItems = navigationItems.map(item => {
    const { hasErrors, errorCount } = getGroupErrors(item.id);

    return {
      ...item,
      hasErrors,
      errorCount,
    };
  });

  // Также учитываем ошибки валидации от сервера
  const getValidationErrorsForGroup = (groupId: string) => {
    if (!validationErrors) return { hasServerErrors: false, serverErrorCount: 0 };

    const group = config.groups.find(g => g.id === groupId);

    if (!group || !group.fields) return { hasServerErrors: false, serverErrorCount: 0 };

    let serverErrorCount = 0;
    const checkFieldsForServerErrors = (fields: FormField[]) => {
      fields.forEach(field => {
        // Проверяем как обычные ошибки валидации, так и пользовательские
        if (validationErrors.fieldErrors && validationErrors.fieldErrors[field.name]) {
          serverErrorCount++;
        }
        // Рекурсивно проверяем вложенные поля
        if (field.fields) {
          checkFieldsForServerErrors(field.fields);
        }
      });
    };

    checkFieldsForServerErrors(group.fields);

    return { hasServerErrors: serverErrorCount > 0, serverErrorCount };
  };

  // Финальные navigationItems с учетом всех типов ошибок
  const finalNavigationItems = enrichedNavigationItems.map(item => {
    const { hasServerErrors, serverErrorCount } = getValidationErrorsForGroup(item.id);

    return {
      ...item,
      hasErrors: item.hasErrors || hasServerErrors,
      errorCount: (item.errorCount || 0) + serverErrorCount,
    };
  });

  // Функция для получения детальных ошибок группы для сайдбара
  const getGroupErrorsForSidebar = useCallback((groupId: string) => {
    const group = config.groups.find(g => g.id === groupId);

    if (!group || !group.fields) return { fieldErrors: [] };

    const fieldErrors: Array<{ field: string; message: string }> = [];
    
    const collectFieldErrors = (fields: FormField[], prefix = '') => {
      fields.forEach(field => {
        const fieldPath = prefix ? `${prefix}.${field.name}` : field.name;
        
        // Проверяем ошибки формы
        if (normalizedErrors[field.name]) {
          fieldErrors.push({
            field: field.label || field.name,
            message: normalizedErrors[field.name].message || 'Ошибка валидации',
          });
        }
        
        // Проверяем ошибки валидации от сервера
        if (validationErrors?.fieldErrors && validationErrors.fieldErrors[field.name]) {
          fieldErrors.push({
            field: field.label || field.name,
            message: validationErrors.fieldErrors[field.name],
          });
        }
        
        // Рекурсивно проверяем вложенные поля
        if (field.fields) {
          collectFieldErrors(field.fields, fieldPath);
        }
      });
    };

    collectFieldErrors(group.fields);

    return { fieldErrors };
  }, [config.groups, normalizedErrors, validationErrors]);

  return (
    <form
      onSubmit={e => {
        handleSubmit(data => {
          const processedData = processFormData(data);

          onSubmit(processedData);
        })(e);
      }}
      className="h-full flex flex-col"
    >      
      {/* Кастомный заголовок формы */}
      {('headerComponent' in config) && (config as any).headerComponent && (
        <div className="flex-shrink-0 mb-4">
          {typeof (config as any).headerComponent === 'function' 
            ? React.createElement((config as any).headerComponent)
            : (config as any).headerComponent}
        </div>
      )}
      
      {/* Прокручиваемая область с полями и sidebar */}
      <div className="overflow-y-auto">
        <div className="flex flex-row">
          {/* Основная область с полями формы */}
          <div className="flex-3 w-full">
            <div className="flex flex-col gap-4 border-r">
              {sortedGroups.map(group => {
                // Если у группы есть кастомный компонент, рендерим его вместе с полями группы
                if (group.customComponent) {
                  const CustomComponent = group.customComponent;

                  return (
                    <div id={`form-group-${group.id}`} key={group.id} className="flex flex-col gap-4">
                      {/* Заголовок группы для кастомного компонента */}
                      <div className="flex flex-col border-y px-8 py-2">
                        <h3 className="text-lg font-medium text-[color:var(--color-brand)]">{group.title}</h3>
                        {group.description && (
                          <p className="text-sm">{group.description}</p>
                        )}
                      </div>                  
                      {/* Кастомный компонент */}
                      <div className="px-8 pb-8">
                        <CustomComponent />
                      </div>
                      {/* Рендерим поля группы (например, скрытые поля) */}
                      {group.fields && group.fields.map(field =>
                        renderField({
                          field,
                          errors: normalizedErrors,
                          isLoading,
                          lazyOptions,
                          loadingOptions,
                          fieldOptionsProviders,
                        })
                      )}
                    </div>
                  );
                }

                // Обычная группа полей
                return (
                  <FieldGroupComponent
                    key={group.id}
                    group={group}
                    renderField={(field: FormField) =>
                      renderField({
                        field,
                        errors: normalizedErrors,
                        isLoading,
                        lazyOptions,
                        loadingOptions,
                        fieldOptionsProviders,
                      })
                    }
                  />
                );
              })}
            </div>
          </div>

          {/* Sticky Sidebar для навигации */}
          <aside className="flex-1 w-full sticky top-0 h-fit overflow-hidden">
          {finalNavigationItems.length > 0 && (
            <NavigationSidebar
              navigationItems={finalNavigationItems}
              activeSection={activeSection}
              onSectionClick={scrollToSection}
              getGroupErrors={getGroupErrorsForSidebar}
              validationErrors={validationErrors}
              setValidationErrors={setValidationErrors || undefined}
              collectionName={collectionName}
            />
          )}
          </aside>
          
        </div>
      </div>

    <div className="flex flex-row justify-end gap-4 px-4 py-3 border-t">
      {/* Действия формы */}
      <FormActions mode={mode} isLoading={isLoading} onCancel={onCancel} />
      </div>
    </form>
    
  );
};

export const DynamicForm = <T extends FieldValues>({
  config,
  initialData,
  validationSchema,
  onSubmit,
  isLoading = false,
  mode,
  onCancel,
  fieldOptionsProviders,
  FieldGroupComponent = FieldGroup,
}: DynamicFormProps<T>): React.ReactElement => {
  const methods = useForm<T>({
    resolver: validationSchema ? zodResolver(validationSchema) : undefined,
    defaultValues: (initialData || {}) as DefaultValues<T>,
  });

  return (
    <FormProvider {...methods}>
      <DynamicFormContent
        config={config}
        onSubmit={onSubmit}
        isLoading={isLoading}
        mode={mode}
        onCancel={onCancel}
        fieldOptionsProviders={fieldOptionsProviders}
        FieldGroupComponent={FieldGroupComponent}
      />
    </FormProvider>
  );
};
