import { getCarApiEndpoints } from '@entities/cars/api/endpoints';
import type { CollectionName } from '@entities/collections/config';
import { getLocationApiEndpoints } from '@entities/locations/api/endpoints';
import { getNotificationApiEndpoints } from '@entities/notifications/api/endpoints';
import { getOrderApiEndpoints } from '@entities/order/api/endpoints';
import { getRouteApiEndpoints } from '@entities/routes/api/endpoints';
import { getServiceApiEndpoints } from '@entities/services/api/endpoints';
import { getTariffApiEndpoints } from '@entities/tariff/api/endpoints';
import { getUserApiEndpoints } from '@entities/users/api/endpoints';

/**
 * Интерфейс для полного набора API-эндпоинтов коллекции
 */
export interface ApiEndpoints {
  // Основные CRUD операции
  list?: string;        // GET /Collection - список записей (для таблиц)
  get?: string;         // GET /Collection/{id} - получение записи
  create?: string;      // POST /Collection - создание записи
  update?: string;      // PUT /Collection/{id} - обновление записи
  delete?: string;      // DELETE /Collection/{id} - удаление записи

  // Дополнительные операции
  redirect?: string;    // URL для редиректа, если требуется

  // Специфичные операции (для расширения)
  [key: string]: string | undefined;
}
/**
 * Функция для получения API-эндпоинтов в зависимости от типа коллекции и ID
 * @param collectionName Название коллекции
 * @param id ID записи (для редактирования)
 * @param role Роль пользователя (для коллекции users)
 * @returns API-эндпоинты
 */
export function getApiEndpoints(
  collectionName: CollectionName | string,
  id: string | null,
  role?: string | null
): ApiEndpoints {
  // Режим создания определяется только по id === 'create'
  // id === null означает запрос списка записей (для таблиц)
  const isCreateMode = id === 'create';

  // Определяем эндпоинты в зависимости от коллекции
  switch (collectionName) {
    case 'cars':
      return getCarApiEndpoints(id, isCreateMode);
    case 'locations':
      return getLocationApiEndpoints(id, isCreateMode);
    case 'services':
      return getServiceApiEndpoints(id, isCreateMode);
    case 'notifications':
      return getNotificationApiEndpoints(id, role || null, isCreateMode);
    case 'users':
      return getUserApiEndpoints(id, role || null, isCreateMode);
    case 'orders':
      return getOrderApiEndpoints(id, isCreateMode, 'scheduled', role);
    case 'routes':
      return getRouteApiEndpoints(id, isCreateMode);
    case 'tariffs':
      return getTariffApiEndpoints(id, isCreateMode);
    default:
      // Для неизвестных коллекций возвращаем пустой объект
      if (process.env.NODE_ENV !== 'production') {
        console.warn(`🔍 getApiEndpoints - Неизвестная коллекция: ${collectionName}`);
      }

      return {};
  }
}
/**
 * Функция для получения API endpoint для таблиц (список записей)
 * @param collectionName Название коллекции
 * @param role Роль пользователя (опционально)
 * @returns API endpoint для получения списка записей
 */
export function getTableApiEndpoint(
  collectionName: CollectionName | string,
  role?: string | null
): string {
  // Получаем полный набор endpoints для коллекции
  const endpoints = getApiEndpoints(collectionName, null, role);

  // Возвращаем endpoint для списка или fallback на основные endpoints
  return endpoints.list || endpoints.get || '';
}
/**
 * Функция для получения конкретного API endpoint по операции
 * @param collectionName Название коллекции
 * @param operation Тип операции (list, get, create, update, delete)
 * @param id ID записи (для операций get, update, delete)
 * @param role Роль пользователя (опционально)
 * @returns API endpoint для указанной операции
 */
export function getApiEndpoint(
  collectionName: CollectionName | string,
  operation: keyof ApiEndpoints,
  id?: string | null,
  role?: string | null
): string {
  const endpoints = getApiEndpoints(collectionName, id || null, role || null);

  return endpoints[operation] || '';
}
