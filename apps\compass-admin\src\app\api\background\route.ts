import { NextResponse, type NextRequest } from 'next/server';

export async function GET() {
  return NextResponse.json({ message: 'Background API endpoint' });
}

export async function POST(request: NextRequest) {
  try {
    const { type, value } = await request.json();

    // Валидация типа фона
    if (!['color', 'image'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid background type. Must be "color" or "image"' },
        { status: 400 },
      );
    }

    // Валидация значения
    if (typeof value !== 'string' || value.trim() === '') {
      return NextResponse.json(
        { error: 'Invalid background value. Must be non-empty string' },
        { status: 400 },
      );
    }

    // Дополнительная валидация для типа image
    if (type === 'image') {
      const trimmedValue = value.trim();

      // Проверяем, что это либо локальный путь, либо валидный URL
      if (!trimmedValue.startsWith('/') && !trimmedValue.startsWith('http')) {
        return NextResponse.json(
          { error: 'Image value must be a valid URL or local path' },
          { status: 400 },
        );
      }

      // Если это URL, проверяем его валидность
      if (trimmedValue.startsWith('http')) {
        try {
          new URL(trimmedValue);
        } catch {
          return NextResponse.json({ error: 'Invalid image URL format' }, { status: 400 });
        }
      }
    }

    // Создаем ответ с установкой куки
    const response = NextResponse.json({
      success: true,
      type,
      value,
      message: 'Background settings updated successfully',
    });

    // Устанавливаем куки с настройками фона (срок действия 1 год)
    response.cookies.set('background-type', type, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 365, // 1 год
      path: '/',
    });

    response.cookies.set('background-value', value, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 365, // 1 год
      path: '/',
    });

    return response;
  } catch (_error) {
    return NextResponse.json({ error: 'Failed to update background settings' }, { status: 500 });
  }
}
