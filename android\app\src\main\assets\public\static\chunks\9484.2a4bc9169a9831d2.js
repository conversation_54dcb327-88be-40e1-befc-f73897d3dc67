"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9484],{49484:(s,l,e)=>{e.r(l),e.d(l,{PrivacyPolicyModal:()=>n});var i=e(54568),c=e(41186);e(7620);var d=e(11447);let n=()=>{let s=(0,c.e3)(d.Oo),l=()=>{s({})};return(0,i.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold",children:"Политика конфиденциальности"}),(0,i.jsx)("button",{onClick:l,className:"text-gray-400 hover: transition-colors",children:(0,i.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,i.jsxs)("div",{className:"p-6 space-y-6",children:[(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"mb-3",children:'Настоящая Политика конфиденциальности описывает порядок обработки персональных данных пользователей сервиса ОсОО "Компас Трансфер", именуемого в дальнейшем \xabОператор\xbb, предоставляющего услуги по организации пассажирских перевозок.'})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"1. Общие положения"}),(0,i.jsx)("p",{className:"mb-3",children:"Настоящая Политика конфиденциальности определяет порядок обработки персональных данных пользователей сервиса Compass и является официальным документом Оператора."}),(0,i.jsx)("p",{className:"mb-3",children:"Используя наши услуги, включая мобильное приложение и веб-платформу, вы соглашаетесь с условиями настоящей Политики конфиденциальности."}),(0,i.jsx)("p",{className:"mb-3",children:"Оператор оставляет за собой право вносить изменения в настоящую Политику, о чем пользователи уведомляются посредством публикации новой редакции в мобильном приложении и на сайте Оператора."}),(0,i.jsx)("p",{children:"Пользователь несет ответственность за ознакомление с действующей редакцией Политики при каждом обращении за услугами Оператора."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"2. Какие персональные данные мы собираем"}),(0,i.jsx)("p",{className:"mb-3",children:"Оператор может собирать следующие категории персональных данных:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1 mb-3",children:[(0,i.jsx)("li",{children:"Контактная информация (имя, фамилия, номер телефона, адрес электронной почты)"}),(0,i.jsx)("li",{children:"Данные о местоположении для предоставления услуг по организации перевозок"}),(0,i.jsx)("li",{children:"Информация о поездках, маршрутах и платежах"}),(0,i.jsx)("li",{children:"Техническая информация об устройстве и мобильном приложении"}),(0,i.jsx)("li",{children:"Данные о взаимодействии с нашим сервисом и предпочтениях пользователя"}),(0,i.jsx)("li",{children:"Фотографии профиля (по желанию пользователя)"}),(0,i.jsx)("li",{children:"Платежная информация (номер карты, данные электронных кошельков)"})]}),(0,i.jsx)("p",{children:"Все персональные данные предоставляются пользователем добровольно при регистрации в сервисе и использовании услуг Оператора."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"3. Цели обработки персональных данных"}),(0,i.jsx)("p",{className:"mb-3",children:"Оператор использует собранные персональные данные для:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1 mb-3",children:[(0,i.jsx)("li",{children:"Предоставления и улучшения услуг по организации пассажирских перевозок"}),(0,i.jsx)("li",{children:"Обработки заказов и осуществления платежей"}),(0,i.jsx)("li",{children:"Обеспечения безопасности пользователей и предотвращения мошенничества"}),(0,i.jsx)("li",{children:"Связи с пользователями по вопросам предоставления услуг"}),(0,i.jsx)("li",{children:"Соблюдения правовых требований и законодательства"}),(0,i.jsx)("li",{children:"Анализа и улучшения качества предоставляемых услуг"}),(0,i.jsx)("li",{children:"Предоставления технической поддержки пользователям"}),(0,i.jsx)("li",{children:"Информирования о новых услугах и специальных предложениях"})]}),(0,i.jsx)("p",{children:"Обработка персональных данных осуществляется на основании согласия пользователя и в соответствии с законодательством Кыргызской Республики."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"4. Передача персональных данных третьим лицам"}),(0,i.jsx)("p",{className:"mb-3",children:"Оператор может передавать персональные данные пользователей третьим лицам только в следующих случаях:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1 mb-3",children:[(0,i.jsx)("li",{children:"С явного согласия пользователя на такую передачу"}),(0,i.jsx)("li",{children:"Водителям для выполнения заказа на перевозку (имя, номер телефона, адреса)"}),(0,i.jsx)("li",{children:"Платежным системам для обработки платежей и возвратов"}),(0,i.jsx)("li",{children:"По требованию государственных органов в рамках их компетенции"}),(0,i.jsx)("li",{children:"Партнерам для предоставления услуг (с соблюдением конфиденциальности)"}),(0,i.jsx)("li",{children:"В случае реорганизации или продажи бизнеса Оператора"})]}),(0,i.jsx)("p",{children:"Оператор обязуется обеспечить, чтобы третьи лица соблюдали конфиденциальность переданных им персональных данных."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"5. Защита персональных данных"}),(0,i.jsx)("p",{className:"mb-3",children:"Оператор применяет технические и организационные меры для защиты персональных данных:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1 mb-3",children:[(0,i.jsx)("li",{children:"Шифрование данных при передаче и хранении"}),(0,i.jsx)("li",{children:"Ограничение доступа к персональным данным только уполномоченным сотрудникам"}),(0,i.jsx)("li",{children:"Регулярное обновление систем безопасности и антивирусного программного обеспечения"}),(0,i.jsx)("li",{children:"Обучение сотрудников правилам обработки и защиты персональных данных"}),(0,i.jsx)("li",{children:"Мониторинг и аудит систем безопасности"}),(0,i.jsx)("li",{children:"Использование защищенных серверов и центров обработки данных"}),(0,i.jsx)("li",{children:"Резервное копирование данных для предотвращения их потери"})]}),(0,i.jsx)("p",{children:"Несмотря на принимаемые меры, Оператор не может гарантировать абсолютную безопасность данных при их передаче через интернет."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"6. Права пользователей"}),(0,i.jsx)("p",{className:"mb-3",children:"Пользователи имеют следующие права в отношении своих персональных данных:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1 mb-3",children:[(0,i.jsx)("li",{children:"Получать информацию о обработке своих персональных данных"}),(0,i.jsx)("li",{children:"Требовать исправления неточных или неполных данных"}),(0,i.jsx)("li",{children:"Требовать удаления своих персональных данных"}),(0,i.jsx)("li",{children:"Ограничить обработку своих персональных данных"}),(0,i.jsx)("li",{children:"Отозвать согласие на обработку персональных данных"}),(0,i.jsx)("li",{children:"Получить копию своих персональных данных в структурированном формате"}),(0,i.jsx)("li",{children:"Подать жалобу в надзорный орган по защите персональных данных"})]}),(0,i.jsx)("p",{children:"Для реализации своих прав пользователь может обратиться к Оператору по контактным данным, указанным в настоящей Политике."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"7. Сроки хранения персональных данных"}),(0,i.jsx)("p",{className:"mb-3",children:"Оператор хранит персональные данные пользователей только в течение времени, необходимого для достижения целей обработки или в соответствии с требованиями законодательства Кыргызской Республики."}),(0,i.jsx)("p",{className:"mb-3",children:"Конкретные сроки хранения различных категорий персональных данных:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1 mb-3",children:[(0,i.jsx)("li",{children:"Данные активных пользователей - в течение периода использования сервиса"}),(0,i.jsx)("li",{children:"История поездок - 3 года с момента совершения поездки"}),(0,i.jsx)("li",{children:"Платежная информация - в соответствии с требованиями платежных систем"}),(0,i.jsx)("li",{children:"Данные для налогового учета - в соответствии с налоговым законодательством"})]}),(0,i.jsx)("p",{children:"После истечения срока хранения персональные данные подлежат уничтожению или обезличиванию."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"8. Использование файлов cookie"}),(0,i.jsx)("p",{className:"mb-3",children:"Оператор использует файлы cookie и аналогичные технологии для улучшения работы сервиса и анализа его использования."}),(0,i.jsx)("p",{className:"mb-3",children:"Файлы cookie используются для:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1 mb-3",children:[(0,i.jsx)("li",{children:"Запоминания настроек и предпочтений пользователя"}),(0,i.jsx)("li",{children:"Обеспечения безопасности и предотвращения мошенничества"}),(0,i.jsx)("li",{children:"Анализа использования сервиса и улучшения его функциональности"}),(0,i.jsx)("li",{children:"Персонализации контента и рекламы"})]}),(0,i.jsx)("p",{children:"Пользователь может управлять настройками cookie в своем браузере или мобильном приложении."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"9. Изменения в Политике конфиденциальности"}),(0,i.jsx)("p",{className:"mb-3",children:"Оператор может периодически обновлять настоящую Политику конфиденциальности для отражения изменений в практике обработки данных или законодательстве."}),(0,i.jsx)("p",{className:"mb-3",children:"О существенных изменениях пользователи будут уведомлены через мобильное приложение, по электронной почте или посредством уведомления на сайте."}),(0,i.jsx)("p",{children:"Рекомендуется регулярно просматривать данную страницу для ознакомления с актуальной версией Политики конфиденциальности."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"10. Контактная информация"}),(0,i.jsx)("p",{className:"mb-3",children:"По вопросам обработки персональных данных и реализации своих прав вы можете обратиться к Оператору:"}),(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,i.jsx)("p",{className:"font-semibold mb-2",children:'ОсОО "Компас Трансфер"'}),(0,i.jsx)("p",{className:"mb-1",children:"Адрес: [адрес будет указан позже]"}),(0,i.jsx)("p",{className:"mb-1",children:"Email по вопросам персональных данных: <EMAIL>"}),(0,i.jsx)("p",{className:"mb-1",children:"Общий email: [email будет указан позже]"}),(0,i.jsx)("p",{className:"mb-1",children:"Телефон: +[телефон будет указан позже]"}),(0,i.jsx)("p",{children:"Время работы: Пн-Пт с 9:00 до 18:00"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"11. Заключительные положения"}),(0,i.jsx)("p",{className:"mb-3",children:"Настоящая Политика конфиденциальности регулируется законодательством Кыргызской Республики."}),(0,i.jsx)("p",{className:"mb-3",children:"Все споры, связанные с обработкой персональных данных, подлежат рассмотрению в суде по месту нахождения Оператора."}),(0,i.jsx)("p",{children:"Настоящая Политика вступает в силу с момента ее опубликования и действует до замены новой редакцией."})]})]}),(0,i.jsx)("div",{className:"flex justify-end p-6 border-t border-gray-200",children:(0,i.jsx)("button",{onClick:l,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Закрыть"})})]})})}}}]);