import type { CollectionName } from '@entities/collections/config';

export type { CollectionName };

/**
 * Интерфейсы для пропсов компонентов
 */
export interface ComponentProps {
  /** Название коллекции */
  collectionName: CollectionName;
  /** Идентификатор редактируемого элемента */
  id?: string;
  /** Режим работы компонента */
  mode?: 'view' | 'list' | 'create' | 'edit';
  /** Роль пользователя (для коллекции users) */
  role?: string;
}
/**
 * Интерфейс для пропсов кастомных компонентов
 */
export type CustomComponent = ComponentProps;
/**
 * Интерфейсы для пропсов компонентов
 */
export type ListComponentProps = Omit<ComponentProps, 'mode'> & { mode?: 'list' };
/**
 * Интерфейсы для пропсов компонентов
 */
export type ManageComponentProps = Omit<ComponentProps, 'mode'> & { mode?: 'create' | 'edit' };
