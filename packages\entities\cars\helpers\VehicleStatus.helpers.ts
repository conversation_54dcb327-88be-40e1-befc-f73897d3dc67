import { VehicleStatus } from '@entities/cars/enums';

/**
 * Проверяет, является ли значение допустимым значением VehicleStatus
 * @param {unknown} value Значение для проверки
 * @returns {boolean} true, если значение допустимо
 */
export function isVehicleStatus(value: unknown): value is VehicleStatus {
  return Object.values(VehicleStatus).includes(value as VehicleStatus);
}
/**
 * Получает метку для значения VehicleStatus
 * @param {VehicleStatus} value Значение VehicleStatus
 * @returns {string} Метка
 */
export function getVehicleStatusLabel(value: VehicleStatus): string {
  switch (value) {
    case VehicleStatus.Available:
      return 'Доступен';
    case VehicleStatus.Maintenance:
      return 'На техобслуживании';
    case VehicleStatus.Repair:
      return 'На ремонте';
    case VehicleStatus.Other:
      return 'Другое';
    default:
      return '';
  }
}
/**
 * Получает массив всех значений VehicleStatus
 * @returns {VehicleStatus[]} Массив всех значений
 */
export function getVehicleStatusValues(): VehicleStatus[] {
  return Object.values(VehicleStatus);
}
/**
 * Получает массив всех меток VehicleStatus
 * @returns {string[]} Массив всех меток
 */
export function getVehicleStatusLabels(): string[] {
  return getVehicleStatusValues().map(value => getVehicleStatusLabel(value));
}
/**
 * Получает массив объектов { value, label } для VehicleStatus
 * @returns {{ value: VehicleStatus, label: string }[]} Массив объектов
 */
export function getVehicleStatusOptions(): { value: VehicleStatus, label: string }[] {
  return getVehicleStatusValues().map(value => ({
    value,
    label: getVehicleStatusLabel(value)
  }));
}