import type { ITableConfig } from '@shared/ui/table';
import type { GetCarDTO } from '@entities/cars/interface';
import { GetCarDTOKeysetPaginationResultTableConfig } from '@entities/cars/tables';/**
 * Реестр конфигураций таблиц для сущности cars
 */

export const carsTableConfigs: Record<string, ITableConfig<GetCarDTO>> = {
  GetCarDTOKeysetPaginationResult: GetCarDTOKeysetPaginationResultTableConfig,
};/**
 * Общая конфигурация таблицы для сущности cars
 */
export const carsTableConfig: ITableConfig<GetCarDTO> = GetCarDTOKeysetPaginationResultTableConfig;
