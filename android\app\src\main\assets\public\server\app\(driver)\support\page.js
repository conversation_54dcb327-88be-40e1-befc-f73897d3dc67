(()=>{var e={};e.id=1778,e.ids=[1778],e.modules={68:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>l,tree:()=>u});var t=s(24332),o=s(48819),i=s(67851),n=s.n(i),p=s(97540),a={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>p[e]);s.d(r,a);let u={children:["",{children:["(driver)",{children:["support",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,91090)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\support\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94058)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\support\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},l=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(driver)/support/page",pathname:"/support",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},75:(e,r,s)=>{Promise.resolve().then(s.bind(s,37461))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37461:(e,r,s)=>{"use strict";s.r(r),s.d(r,{SupportPage:()=>o});var t=s(13486);s(60159);let o=()=>(0,t.jsxs)("div",{className:"h-full flex flex-col p-4",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Служба поддержки"}),(0,t.jsx)("p",{children:"Помощь и поддержка"})]}),(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFA7"})}),(0,t.jsx)("h3",{className:"text-lg font-medium  mb-2",children:"Служба поддержки"}),(0,t.jsx)("p",{children:"Раздел в разработке"})]})]})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63123:(e,r,s)=>{Promise.resolve().then(s.bind(s,73087))},73087:(e,r,s)=>{"use strict";s.r(r),s.d(r,{SupportPage:()=>t});let t=(0,s(33952).registerClientReference)(function(){throw Error("Attempted to call SupportPage() from the server but SupportPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\pages\\driver\\support\\SupportPage.tsx","SupportPage")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91090:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i,metadata:()=>n});var t=s(38828),o=s(73087);async function i(){return(0,t.jsx)(o.SupportPage,{})}let n={title:"",description:""}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[191,5208,9455,6596,4680,99,3639,2960,9100,1738],()=>s(68));module.exports=t})();