(()=>{var e={};e.id=9474,e.ids=[9474],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43649:(e,r,t)=>{"use strict";t.r(r),t.d(r,{SettingsPage:()=>p});var s=t(13486),a=t(60159),l=t(80988),o=t(26134),i=t(35925);t(49933),t(49989),t(86157),t(93958),t(2984);var n=t(43888);t(96274),t(18673),t(1106);let d=n.z.object({fullName:n.z.string().min(1,"Полное имя обязательно"),phoneNumber:n.z.string().optional(),email:n.z.string().email("Некорректный email")});d.extend({profile:n.z.object({companyName:n.z.string().min(1,"Название компании обязательно"),companyType:n.z.string().min(1,"Тип компании обязателен"),registrationNumber:n.z.string().optional(),taxIdentifier:n.z.string().optional(),legalAddress:n.z.string().min(1,"Юридический адрес обязателен"),contactEmail:n.z.string().email("Некорректный email").optional(),contactPhone:n.z.string().optional(),website:n.z.string().url("Некорректный URL").optional().or(n.z.literal(""))}).optional()}),d.extend({profile:n.z.object({licenseNumber:n.z.string().min(1,"Номер лицензии обязателен"),licenseCategories:n.z.array(n.z.string()).optional(),licenseIssueDate:n.z.string().optional(),licenseExpiryDate:n.z.string().optional(),dateOfBirth:n.z.string().optional(),birthPlace:n.z.string().optional(),citizenship:n.z.string().optional(),citizenshipCountry:n.z.string().optional(),drivingExperience:n.z.number().optional(),taxIdentifier:n.z.string().optional(),languages:n.z.array(n.z.string()).optional()}).optional()}),t(50534);let c=({currentScale:e=.8,onPreviewChange:r})=>{let[t,l]=(0,a.useState)(e);(0,a.useEffect)(()=>{let e=document.cookie.split("; ").find(e=>e.startsWith("driver-ui-scale="))?.split("=")[1];e&&l(parseFloat(e))},[]);let o=e=>{l(e),r?.(e)};return(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Масштаб интерфейса"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Выберите масштаб:"}),(0,s.jsx)("div",{className:"grid grid-cols-4 gap-3",children:[{value:.7,label:"70%"},{value:.8,label:"80%"},{value:.9,label:"90%"},{value:1,label:"100%"}].map(e=>(0,s.jsx)("button",{onClick:()=>o(e.value),className:`
                  px-4 py-3 text-sm font-medium rounded-lg border-2 transition-all cursor-pointer
                  ${t===e.value?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"}
                `,children:e.label},e.value))})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded-lg",children:[(0,s.jsx)("p",{className:"mb-1",children:"\uD83D\uDCA1 Масштаб влияет на размер всех элементов интерфейса внутри планшета."}),(0,s.jsx)("p",{children:'Изменения будут применены после нажатия кнопки "Применить все" в превью.'})]})]})]})},u=({currentType:e="color",currentValue:r="#f9fafb",onPreviewChange:t})=>{let[l,o]=(0,a.useState)(e),[i,n]=(0,a.useState)(r);(0,a.useEffect)(()=>{let e=document.cookie.split("; ").find(e=>e.startsWith("driver-background-type="))?.split("=")[1],r=document.cookie.split("; ").find(e=>e.startsWith("driver-background-value="))?.split("=")[1];e&&["color","image"].includes(e)&&o(e),r&&n(decodeURIComponent(r))},[]);let d=(e,r)=>{o(e),n(r),t?.({backgroundType:e,backgroundValue:r})};return(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-6 shadow-sm space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Настройки фона"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Настройте фон за планшетом"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Тип фона:"}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("button",{onClick:()=>{o("color"),t?.({backgroundType:"color"})},className:`px-6 py-3 rounded-lg border-2 transition-all font-medium ${"color"===l?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"}`,children:"Цвет"}),(0,s.jsx)("button",{onClick:()=>{o("image"),t?.({backgroundType:"image"})},className:`px-6 py-3 rounded-lg border-2 transition-all font-medium ${"image"===l?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"}`,children:"Изображение"})]})]}),"color"===l&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Выберите цвет фона:"}),(0,s.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg border",children:(0,s.jsx)("div",{className:"grid grid-cols-4 gap-3",children:[{value:"#ffffff",label:"Белый",color:"#ffffff"},{value:"#f9fafb",label:"Очень светлый серый",color:"#f9fafb"},{value:"#f3f4f6",label:"Светлый серый",color:"#f3f4f6"},{value:"#e5e7eb",label:"Серый",color:"#e5e7eb"},{value:"#eff6ff",label:"Светло-синий",color:"#eff6ff"},{value:"#dbeafe",label:"Синий 100",color:"#dbeafe"},{value:"#bfdbfe",label:"Синий 200",color:"#bfdbfe"},{value:"#93c5fd",label:"Синий 300",color:"#93c5fd"},{value:"#f0fdf4",label:"Светло-зеленый",color:"#f0fdf4"},{value:"#dcfce7",label:"Зеленый 100",color:"#dcfce7"},{value:"#bbf7d0",label:"Зеленый 200",color:"#bbf7d0"},{value:"#86efac",label:"Зеленый 300",color:"#86efac"},{value:"#fefce8",label:"Светло-желтый",color:"#fefce8"},{value:"#fef08a",label:"Желтый 100",color:"#fef08a"},{value:"#fde047",label:"Желтый 200",color:"#fde047"},{value:"#facc15",label:"Желтый 300",color:"#facc15"},{value:"#f5f3ff",label:"Светло-фиолетовый",color:"#f5f3ff"},{value:"#ede9fe",label:"Фиолетовый 100",color:"#ede9fe"},{value:"#ddd6fe",label:"Фиолетовый 200",color:"#ddd6fe"},{value:"#c4b5fd",label:"Фиолетовый 300",color:"#c4b5fd"}].map((e,r)=>(0,s.jsx)("button",{onClick:()=>d("color",e.value),className:`
                    w-12 h-12 rounded-lg border-2 transition-all hover:scale-105 cursor-pointer
                    ${i===e.value&&"color"===l?"border-blue-500 ring-2 ring-blue-500/50 scale-105":"border-gray-300 hover:border-gray-400"}
                  `,style:{backgroundColor:e.color},title:e.label},`driver-bg-color-${r}-${e.value}`))})})]}),"image"===l&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Выберите изображение фона:"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4",children:[{value:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop",label:"Горы"},{value:"https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1920&h=1080&fit=crop",label:"Лес"},{value:"https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=1920&h=1080&fit=crop",label:"Озеро"},{value:"https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=1920&h=1080&fit=crop",label:"Небо"}].map((e,r)=>(0,s.jsxs)("button",{onClick:()=>d("image",e.value),className:`
                  relative p-3 rounded-lg border-2 transition-all text-left hover:scale-105 cursor-pointer
                  ${i===e.value&&"image"===l?"border-blue-500 ring-2 ring-blue-500/50 scale-105":"border-gray-300 hover:border-gray-400"}
                `,children:[(0,s.jsx)("div",{className:"w-full h-20 rounded-md mb-2 bg-cover bg-center bg-gray-200",style:{backgroundImage:`url(${e.value})`}}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label})]},`driver-bg-image-${r}-${e.label}`))})]})]})},m=({currentOpacity:e=.95,currentBlur:r=4,currentOverlayColor:t="#ffffff",currentComponentOpacity:l=.9,currentComponentBlur:o=8,onPreviewChange:i})=>{let[n,d]=(0,a.useState)(e),[c,u]=(0,a.useState)(r),[m,p]=(0,a.useState)(t),[b,g]=(0,a.useState)(l),[x,h]=(0,a.useState)(o);(0,a.useEffect)(()=>{let e=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-opacity="))?.split("=")[1],r=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-blur="))?.split("=")[1],t=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-color="))?.split("=")[1];if(e){let r=parseFloat(e);!isNaN(r)&&r>=0&&r<=1&&d(r)}if(r){let e=parseFloat(r);!isNaN(e)&&e>=0&&e<=20&&u(e)}t&&p(decodeURIComponent(t));let s=document.cookie.split("; ").find(e=>e.startsWith("driver-component-opacity="))?.split("=")[1],a=document.cookie.split("; ").find(e=>e.startsWith("driver-component-blur="))?.split("=")[1];if(s){let e=parseFloat(s);!isNaN(e)&&e>=0&&e<=1&&g(e)}if(a){let e=parseFloat(a);!isNaN(e)&&e>=0&&e<=20&&h(e)}},[]);let f=e=>{d(e),i?.({overlayOpacity:e})},v=e=>{u(e),i?.({overlayBlur:e})},y=e=>{p(e),i?.({overlayColor:e})},j=e=>{g(e),i?.({componentOpacity:e})},N=e=>{h(e),i?.({componentBlur:e})};return(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-6 shadow-sm space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Настройки прозрачности и размытия"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Настройте эффекты для планшета и отдельных компонентов"})]}),(0,s.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"\uD83D\uDDA5️ Планшетный контейнер"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Цвет планшета"}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,s.jsx)("input",{type:"color",value:m,onChange:e=>y(e.target.value),className:"w-16 h-10 border border-gray-300 rounded-lg cursor-pointer"}),(0,s.jsx)("input",{type:"text",value:m,onChange:e=>y(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg font-mono text-sm",placeholder:"#ffffff"})]}),(0,s.jsx)("div",{className:"grid grid-cols-10 gap-2",children:["#ffffff","#f8f9fa","#e9ecef","#dee2e6","#000000","#1e3a8a","#7c3aed","#dc2626","#059669","#d97706"].map(e=>(0,s.jsx)("button",{onClick:()=>y(e),className:`w-8 h-8 rounded-lg border-2 transition-all cursor-pointer ${m.toLowerCase()===e.toLowerCase()?"border-blue-500 scale-110 shadow-md":"border-gray-300 hover:border-gray-400"}`,style:{backgroundColor:e},title:e},e))})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["Прозрачность планшета: ",Math.round(100*n),"%"]}),(0,s.jsx)("div",{className:"flex gap-2 flex-wrap",children:[.1,.3,.5,.7,.85,.95,1].map(e=>(0,s.jsxs)("button",{onClick:()=>f(e),className:`px-4 py-3 text-sm rounded-lg border transition-all font-medium cursor-pointer ${.01>Math.abs(n-e)?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"}`,children:[Math.round(100*e),"%"]},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["Размытие фона планшета: ",c,"px"]}),(0,s.jsx)("div",{className:"flex gap-2 flex-wrap",children:[0,2,4,8,12,16,20].map(e=>(0,s.jsxs)("button",{onClick:()=>v(e),className:`px-4 py-3 text-sm rounded-lg border transition-all font-medium cursor-pointer ${c===e?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"}`,children:[e,"px"]},e))})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"\uD83E\uDDE9 Компоненты (Header, Content, Sidebar)"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["Прозрачность компонентов: ",Math.round(100*b),"%"]}),(0,s.jsx)("div",{className:"flex gap-2 flex-wrap",children:[.1,.3,.5,.7,.85,.95,1].map(e=>(0,s.jsxs)("button",{onClick:()=>j(e),className:`px-4 py-3 text-sm rounded-lg border transition-all font-medium cursor-pointer ${.01>Math.abs(b-e)?"bg-green-600 border-green-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"}`,children:[Math.round(100*e),"%"]},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["Размытие фона компонентов: ",x,"px"]}),(0,s.jsx)("div",{className:"flex gap-2 flex-wrap",children:[0,4,8,12,16,20].map(e=>(0,s.jsxs)("button",{onClick:()=>N(e),className:`px-4 py-3 text-sm rounded-lg border transition-all font-medium cursor-pointer ${x===e?"bg-green-600 border-green-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"}`,children:[e,"px"]},e))})]})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600 bg-gray-50 p-4 rounded-lg",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("span",{className:"font-medium",children:"\uD83C\uDFA8 Цвет планшета:"})," Основной цвет планшетного контейнера"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("span",{className:"font-medium",children:"\uD83D\uDDA5️ Планшет:"})," Контролирует прозрачность и размытие всего планшетного контейнера"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("span",{className:"font-medium",children:"\uD83E\uDDE9 Компоненты:"})," Отдельные настройки для Header, Content и Sidebar"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("span",{className:"font-medium",children:"\uD83D\uDCA1 Совет:"})," Комбинируйте настройки для создания уникального стиля интерфейса"]})]})})]})},p=()=>{let{logout:e,isLoggingOut:r}=(0,i.Wn)(),{isInQueue:t,actions:n}=(0,o.o)(),[d,p]=(0,a.useState)(.8),[b,g]=(0,a.useState)("color"),[x,h]=(0,a.useState)("#f9fafb"),[f,v]=(0,a.useState)(.95),[y,j]=(0,a.useState)(4),[N,w]=(0,a.useState)("#ffffff"),[k,C]=(0,a.useState)(.9),[z,S]=(0,a.useState)(8),[P,D]=(0,a.useState)(!1),[q,O]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=document.cookie.split("; ").find(e=>e.startsWith("driver-ui-scale="))?.split("=")[1];e&&p(parseFloat(e));let r=document.cookie.split("; ").find(e=>e.startsWith("driver-background-type="))?.split("=")[1];r&&["color","image"].includes(r)&&g(r);let t=document.cookie.split("; ").find(e=>e.startsWith("driver-background-value="))?.split("=")[1];t&&h(decodeURIComponent(t));let s=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-opacity="))?.split("=")[1];if(s){let e=parseFloat(s);!isNaN(e)&&e>=0&&e<=1&&v(e)}let a=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-blur="))?.split("=")[1];if(a){let e=parseFloat(a);!isNaN(e)&&e>=0&&e<=20&&j(e)}let l=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-color="))?.split("=")[1];l&&w(decodeURIComponent(l));let o=document.cookie.split("; ").find(e=>e.startsWith("driver-component-opacity="))?.split("=")[1];if(o){let e=parseFloat(o);!isNaN(e)&&e>=0&&e<=1&&C(e)}let i=document.cookie.split("; ").find(e=>e.startsWith("driver-component-blur="))?.split("=")[1];if(i){let e=parseFloat(i);!isNaN(e)&&e>=0&&e<=20&&S(e)}},[]);let W=e=>{void 0!==e.scale&&p(e.scale),void 0!==e.backgroundType&&g(e.backgroundType),void 0!==e.backgroundValue&&h(e.backgroundValue),void 0!==e.overlayOpacity&&v(e.overlayOpacity),void 0!==e.overlayBlur&&j(e.overlayBlur),void 0!==e.overlayColor&&w(e.overlayColor),void 0!==e.componentOpacity&&C(e.componentOpacity),void 0!==e.componentBlur&&S(e.componentBlur),D(!0)},E=async()=>{O(!0);try{let e=await fetch("/api/scale",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({scale:d})}),r=await fetch("/api/background",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:b,value:x})}),t=await fetch("/api/overlay",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({opacity:f,blur:y,overlayColor:N,componentOpacity:k,componentBlur:z})});e.ok&&r.ok&&t.ok?(D(!1),setTimeout(()=>{window.location.reload()},1e3)):console.error("Ошибка при сохранении настроек")}catch(e){console.error("Ошибка при сохранении настроек:",e)}finally{O(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.wh,{isVisible:r,message:"Выход из системы..."}),(0,s.jsxs)("div",{className:"h-full flex flex-col p-4 overflow-y-auto",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Настройки"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Настройки приложения и профиля"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,s.jsx)(c,{currentScale:d,onPreviewChange:e=>W({scale:e})}),(0,s.jsx)(u,{currentType:b,currentValue:x,onPreviewChange:e=>W(e)}),(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsx)(m,{currentOpacity:f,currentBlur:y,currentOverlayColor:N,currentComponentOpacity:k,currentComponentBlur:z,onPreviewChange:e=>W(e)})})]}),P&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-900",children:"Есть несохраненные изменения"}),(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Нажмите “Применить все”, чтобы сохранить настройки"})]}),(0,s.jsx)("button",{onClick:E,disabled:q,className:"px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium rounded-lg transition-colors duration-200",children:q?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block"}),"Применяем..."]}):"Применить все"})]})}),(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Безопасность"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{onClick:()=>{e({leaveQueue:n.leaveQueue,isInQueue:t})},disabled:r,className:"w-full flex items-center justify-center px-4 py-3 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-medium rounded-lg transition-colors duration-200",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),t?"Выходим из очереди и системы...":"Выходим из системы..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"Выйти из системы"]})}),t&&(0,s.jsx)("p",{className:"text-sm text-gray-600 text-center",children:"При выходе вы автоматически покинете очередь"})]})]})]})]})}},50611:(e,r,t)=>{"use strict";t.r(r),t.d(r,{SettingsPage:()=>s});let s=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call SettingsPage() from the server but SettingsPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\pages\\driver\\settings\\SettingsPage.tsx","SettingsPage")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59948:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>o});var s=t(38828),a=t(50611);async function l(){return(0,s.jsx)(a.SettingsPage,{})}let o={title:"",description:""}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65445:(e,r,t)=>{Promise.resolve().then(t.bind(t,50611))},71948:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(24332),a=t(48819),l=t(67851),o=t.n(l),i=t(97540),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let d={children:["",{children:["(driver)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59948)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94058)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\settings\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(driver)/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},78597:(e,r,t)=>{Promise.resolve().then(t.bind(t,43649))},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,5208,9455,6596,4680,99,3639,2960,9100,1738,1466],()=>t(71948));module.exports=s})();