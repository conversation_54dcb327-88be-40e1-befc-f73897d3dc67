
import type { ITableConfig } from '@shared/ui/table';
import { carsTableConfig } from '@entities/cars/config/tableConfig';
import { locationsTableConfig } from '@entities/locations/config/tableConfig';
import { notificationTableConfig } from '@entities/notifications/config/tableConfig';
import { orderTableConfig, partnerOrderTableConfig } from '@entities/order/config/tableConfig';
import { servicesTableConfig } from '@entities/services/config/tableConfig';
import { tariffTableConfig } from '@entities/tariff/config/tableConfig';
import { usersTableConfig } from '@entities/users/config/tableConfig';
import { Role } from '@entities/users/enums/Role.enum';
import { getTableApiEndpoint } from './apiEndpointRegistry';// Прямое сопоставление коллекций и ролей с конфигурациями таблиц
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const tableConfigsByRole: Record<string, Record<Role, any>> = {
  orders: {
    [Role.Admin]: orderTableConfig,
    [Role.Operator]: orderTableConfig,
    [Role.Partner]: partnerOrderTableConfig,
    [Role.Customer]: partnerOrderTableConfig,
    [Role.Driver]: partnerOrderTableConfig,
    [Role.Terminal]: orderTableConfig,
    [Role.Unknown]: orderTableConfig,
  },
  cars: {
    [Role.Admin]: carsTableConfig,
    [Role.Operator]: carsTableConfig,
    [Role.Partner]: carsTableConfig,
    [Role.Customer]: carsTableConfig,
    [Role.Driver]: carsTableConfig,
    [Role.Terminal]: carsTableConfig,
    [Role.Unknown]: carsTableConfig,
  },
  users: {
    [Role.Admin]: usersTableConfig,
    [Role.Operator]: usersTableConfig,
    [Role.Partner]: usersTableConfig,
    [Role.Customer]: usersTableConfig,
    [Role.Driver]: usersTableConfig,
    [Role.Terminal]: usersTableConfig,
    [Role.Unknown]: usersTableConfig,
  },
  locations: {
    [Role.Admin]: locationsTableConfig,
    [Role.Operator]: locationsTableConfig,
    [Role.Partner]: locationsTableConfig,
    [Role.Customer]: locationsTableConfig,
    [Role.Driver]: locationsTableConfig,
    [Role.Terminal]: locationsTableConfig,
    [Role.Unknown]: locationsTableConfig,
  },
  notifications: {
    [Role.Admin]: notificationTableConfig,
    [Role.Operator]: notificationTableConfig,
    [Role.Partner]: notificationTableConfig,
    [Role.Customer]: notificationTableConfig,
    [Role.Driver]: notificationTableConfig,
    [Role.Terminal]: notificationTableConfig,
    [Role.Unknown]: notificationTableConfig,
  },
  services: {
    [Role.Admin]: servicesTableConfig,
    [Role.Operator]: servicesTableConfig,
    [Role.Partner]: servicesTableConfig,
    [Role.Customer]: servicesTableConfig,
    [Role.Driver]: servicesTableConfig,
    [Role.Terminal]: servicesTableConfig,
    [Role.Unknown]: servicesTableConfig,
  },
  tariffs: {
    [Role.Admin]: tariffTableConfig,
    [Role.Operator]: tariffTableConfig,
    [Role.Partner]: tariffTableConfig,
    [Role.Customer]: tariffTableConfig,
    [Role.Driver]: tariffTableConfig,
    [Role.Terminal]: tariffTableConfig,
    [Role.Unknown]: tariffTableConfig,
  },
};// Функция для получения конфигурации таблицы с учетом роли

export function getTableConfig<T>(
  collectionName: string,
  role: Role
): ITableConfig<T> {
  const config = tableConfigsByRole[collectionName]?.[role];

  if (!config) {
    throw new Error(`Конфигурация таблицы для коллекции "${collectionName}" и роли "${role}" не найдена`);
  }  // Получаем apiPath из централизованного реестра
  const centralizedApiPath = getTableApiEndpoint(collectionName, role);

  if (!centralizedApiPath) {
    throw new Error(`API endpoint для коллекции "${collectionName}" и роли "${role}" не найден в централизованном реестре`);
  }  // Создаем конфигурацию с дефолтными значениями
  const configWithDefaults: ITableConfig<T> = {
    ...config,
    idField: config.idField || ('id' as keyof T), // Дефолт 'id'
  };

  return configWithDefaults;
}// Обратная совместимость - базовый реестр
export const tableRegistry = {
  cars: carsTableConfig,
  users: usersTableConfig,
  locations: locationsTableConfig,
  notifications: notificationTableConfig,
  orders: orderTableConfig,
  services: servicesTableConfig,
  tariffs: tariffTableConfig,
};
