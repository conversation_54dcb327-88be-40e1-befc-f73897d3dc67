import { cookies } from 'next/headers';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import { TooltipProvider } from '@app/providers/TooltipProvider';
import { DynamicScale } from '@shared/components/DynamicScale';
import { ToastManager } from '@shared/toast/ToastManager';
import '@shared/styles/globals.css';
import 'react-toastify/dist/ReactToastify.css';

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const locale = await getLocale();
  const messages = await getMessages();
  
  // Читаем масштаб из куки на сервере (по умолчанию 0.7)
  const cookieStore = await cookies();
  const scaleCookie = cookieStore.get('ui-scale');
  const scale = scaleCookie ? parseFloat(scaleCookie.value) : 0.7;

  return (
    <html lang={locale}>
      <head>
        <meta name="viewport"/>
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body>
          <DynamicScale scale={scale} />

            <TooltipProvider scale={scale}>
              <NextIntlClientProvider locale={locale} messages={messages}>
                {children}
              </NextIntlClientProvider>
              <ToastManager />
            </TooltipProvider>

      </body>
    </html>
  );
}
