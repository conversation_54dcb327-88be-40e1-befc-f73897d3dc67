'use client';

import React, { lazy, Suspense } from 'react';
// Ленивая загрузка иконок для оптимизации производительности
const DashboardIcon = lazy(() => import('@shared/icons/DashboardIcon'));
const ProfileIcon = lazy(() => import('@shared/icons/ProfileIcon'));
const IdentityCardIcon = lazy(() => import('@shared/icons/IdentityCardIcon'));
const UsersIcon = lazy(() => import('@shared/icons/UsersIcon'));
const VehiclesIcon = lazy(() => import('@shared/icons/VehiclesIcon'));
const TariffsIcon = lazy(() => import('@shared/icons/TariffsIcon'));
const OrdersIcon = lazy(() => import('@shared/icons/OrdersIcon'));
const ReferencesIcon = lazy(() => import('@shared/icons/ReferencesIcon'));

interface IconRendererProps {
  iconName: string;
  className?: string;
  size?: number;
}

/**
 * Компонент для рендеринга иконок по их строковым идентификаторам
 * Используется в дашборде для отображения иконок коллекций
 */
export const IconRenderer: React.FC<IconRendererProps> = ({
  iconName,
  className = 'w-6 h-6',
  size = 24,
}) => {
  // Функция для получения компонента иконки по строковому идентификатору
  const getIconComponent = (name: string) => {
    switch (name) {
      case 'DashboardIcon':
        return <DashboardIcon className={className} size={size} />;
      case 'ProfileIcon':
        return <ProfileIcon className={className} size={size} />;
      case 'IdentityCardIcon':
        return <IdentityCardIcon className={className} size={size} />;
      case 'UsersIcon':
        return <UsersIcon className={className} size={size} />;
      case 'VehiclesIcon':
        return <VehiclesIcon className={className} size={size} />;
      case 'TariffsIcon':
        return <TariffsIcon className={className} size={size} />;
      case 'OrdersIcon':
        return <OrdersIcon className={className} size={size} />;
      case 'ReferencesIcon':
        return <ReferencesIcon className={className} size={size} />;
      default:
        return null;
    }
  };
  // Получаем компонент иконки
  const IconComponent = getIconComponent(iconName);

  // Если иконка не найдена, возвращаем пустой div
  if (!IconComponent) {
    return <div className={className} />;
  }

  // Оборачиваем иконку в Suspense для ленивой загрузки
  return <Suspense fallback={<div className={className} />}>{IconComponent}</Suspense>;
};
