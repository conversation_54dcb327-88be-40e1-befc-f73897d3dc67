(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3457],{14059:(e,r,t)=>{Promise.resolve().then(t.bind(t,31207))},31207:(e,r,t)=>{"use strict";t.r(r),t.d(r,{NotificationsPage:()=>x});var s=t(54568),l=t(62942),n=t(7620),o=t(42997),c=t(45452),i=t(51669);let a=e=>{let{notifications:r,isLoading:t,isLoadingMore:l,error:o,hasMore:c,onLoadMore:a,onNotificationClick:d,onRefresh:x,onGoToRide:u,onGoToOrderDetails:h,onDelete:m,expandedNotificationId:f,onToggleExpanded:p}=e,v=(0,n.useRef)(null),b=(0,n.useRef)(null),j=(0,n.useCallback)(()=>{v.current&&v.current.disconnect(),v.current=new IntersectionObserver(e=>{e[0].isIntersecting&&c&&!l&&!t&&a()},{threshold:.1,rootMargin:"100px"}),b.current&&v.current.observe(b.current)},[c,l,t,a]);return((0,n.useEffect)(()=>(j(),()=>{v.current&&v.current.disconnect()}),[j]),o)?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Ошибка загрузки уведомлений"}),(0,s.jsx)("button",{onClick:x,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Попробовать снова"})]})}):t&&0===r.length?(0,s.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Загрузка..."})}):0===r.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-gray-500",children:"У вас пока нет уведомлений"})}):(0,s.jsxs)("div",{className:"flex flex-col gap-2 overflow-y-auto h-full p-2 rounded-2xl",children:[r.map(e=>(0,s.jsx)(i.Ow,{notification:e,priority:(0,i.CU)(e.type),onClick:d,onGoToRide:u,onGoToOrderDetails:h,onDelete:m,isExpanded:f===e.id,onToggleExpanded:p},e.id)),c&&(0,s.jsx)("div",{ref:b,className:"flex items-center justify-center py-4",children:l?(0,s.jsxs)("div",{className:"flex items-center gap-2 text-gray-500",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,s.jsx)("span",{children:"Загрузка..."})]}):(0,s.jsx)("button",{onClick:a,className:"px-4 py-2 text-blue-600 hover:text-blue-700 transition-colors",children:"Загрузить еще"})}),!c&&r.length>0&&(0,s.jsx)("div",{className:"text-center py-4 text-gray-500 text-sm",children:"Все уведомления загружены"})]})},d=e=>{let{filterKey:r,label:t,icon:l,count:n,isActive:o,bgColor:c,borderColor:i,textColor:a,badgeColor:d,onClick:x}=e;return(0,s.jsx)("div",{onClick:x,className:"\n        relative w-[268px] h-[85px] rounded-lg cursor-pointer transition-all duration-200\n        ".concat(c,"\n        ").concat(o?"border-[1.5px] ".concat(i):"border border-gray-200","\n        hover:shadow-md\n      "),style:{backgroundColor:c.startsWith("bg-[")?c.slice(3,-1):void 0,borderColor:o&&i.startsWith("border-[")?i.slice(7,-1):void 0,borderWidth:o?"1.5px":"1px"},children:(0,s.jsxs)("div",{className:"flex items-center h-full px-4",children:[(0,s.jsx)("div",{className:"text-2xl mr-3",children:l}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("h3",{className:"font-medium text-base ".concat(a),children:t})}),n>0&&(0,s.jsx)("div",{className:"\n            absolute -top-2 -right-2 \n            ".concat(d," text-white \n            rounded-full min-w-[24px] h-6 \n            flex items-center justify-center \n            text-sm font-bold\n            px-2\n          "),children:n>99?"99+":n})]})})},x=()=>{var e;let r=(0,l.useRouter)(),[t,i]=(0,n.useState)("order"),[x,u]=(0,n.useState)(null),{notifications:h,isLoading:m,isLoadingMore:f,error:p,hasMore:v,unreadCount:b,unreadCountsByPriority:j,totalCount:g,actions:y}=(0,c.M)(),N=async e=>{try{await y.deleteNotification(e),console.log("✅ Уведомление удалено:",e)}catch(e){console.error("❌ Ошибка удаления уведомления:",e)}},w=h.filter(e=>(0,o.CU)(e.type)===t);return(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsx)("div",{className:"border-gray-200",children:(0,s.jsx)("div",{className:"w-full flex items-center justify-between",children:(0,s.jsxs)("div",{className:"w-full px-3 py-2 flex flex-row justify-between items-center",children:[(0,s.jsx)("div",{className:"flex flex-row gap-4 items-center",children:(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Уведомления"})}),b>0&&(0,s.jsxs)("div",{className:"bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:[b," новых"]})]})})}),(0,s.jsx)("div",{children:(0,s.jsx)("div",{className:"flex flex-row gap-4 overflow-x-hidden px-3 py-2",children:o._J.map(e=>(0,s.jsx)(d,{filterKey:e.key,label:e.label,icon:e.icon,count:j[e.key],isActive:t===e.key,bgColor:e.bgColor,borderColor:e.borderColor,textColor:e.textColor,badgeColor:e.badgeColor,onClick:()=>i(e.key)},e.key))})}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden p-2",children:0===w.length&&h.length>0&&!m?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD0D"})}),(0,s.jsxs)("h3",{className:"text-lg font-medium  mb-2",children:["Нет уведомлений в категории “",null==(e=o._J.find(e=>e.key===t))?void 0:e.label,"”"]}),(0,s.jsx)("p",{children:"Попробуйте выбрать другую категорию"})]}):(0,s.jsx)(a,{notifications:w,isLoading:m,isLoadingMore:f,error:p,hasMore:v,onLoadMore:y.loadMore,onNotificationClick:e=>{console.log("\uD83D\uDD14 Клик по уведомлению:",e),e.isRead||y.markAsRead(e.id)},onRefresh:y.refresh,onGoToRide:(e,t,s)=>{console.log("\uD83D\uDE97 Переход к поездке:",{orderId:e,rideId:t,orderType:s});let l=s?"&orderType=".concat(s):"",n=t?"/active-ride?orderId=".concat(e,"&rideId=").concat(t).concat(l):"/active-ride?orderId=".concat(e).concat(l);r.push(n)},onGoToOrderDetails:(e,t,s)=>{console.log("\uD83D\uDCCB Переход к детальному просмотру заказа:",{orderId:e,rideId:t,orderType:s});let l=s?"&orderType=".concat(s):"",n=t?"/active-ride?orderId=".concat(e,"&rideId=").concat(t).concat(l):"/active-ride?orderId=".concat(e).concat(l);r.push(n)},onDelete:N,expandedNotificationId:x,onToggleExpanded:e=>{console.log("\uD83D\uDCCB Переключение расширения:",e),u(x===e.id?null:e.id)}})})]})}},62942:(e,r,t)=>{"use strict";var s=t(42418);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},66251:(e,r,t)=>{"use strict";function s(e,r){return Object.values(e).map(e=>({value:e,label:r(e)}))}function l(e,r){return e.filter(e=>!r.includes(e.value))}t.d(r,{mJ:()=>l,wI:()=>s})}},e=>{var r=r=>e(e.s=r);e.O(0,[7261,3766,6467,1669,587,8315,7358],()=>r(14059)),_N_E=e.O()}]);