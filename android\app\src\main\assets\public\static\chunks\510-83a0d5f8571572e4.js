(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[510],{12722:(e,t,s)=>{"use strict";s.d(t,{$z:()=>u,Or:()=>n,Z6:()=>d,aZ:()=>r,eX:()=>i,pL:()=>l,uW:()=>o,wp:()=>c});var a=s(27736);s(62571);let r=async e=>(await a.uE.post("/Order/instant/".concat(e,"/accept-by-driver"))).data,i=async e=>{try{console.log("\uD83D\uDCDD Подтверждаем запланированную поездку водителем:",e);let t=await a.uE.post("/Ride/".concat(e,"/accept-by-driver"));return console.log("✅ Поездка подтверждена водителем:",t.data),t.data}catch(e){throw console.error("❌ Ошибка подтверждения поездки водителем:",e),e}},n=async e=>{try{console.log("\uD83D\uDCE2 Уведомляем о принятии запланированной поездки в сведении:",e);let t=await a.uE.post("/Ride/".concat(e,"/notification-by-driver"));console.log("✅ Уведомление отправлено:",t.data)}catch(e){throw console.error("❌ Ошибка отправки уведомления:",e),e}},l=async e=>(await a.uE.post("/Ride/".concat(e,"/reject-by-driver"))).data,d=async e=>(await a.uE.post("/Ride/".concat(e,"/status/driver-heading-to-client"))).data,c=async e=>(await a.uE.post("/Ride/".concat(e,"/status/driver-arrived"))).data,o=async e=>(await a.uE.post("/Ride/".concat(e,"/status/ride-started"))).data,u=async e=>(await a.uE.post("/Ride/".concat(e,"/status/ride-finished"))).data},44090:(e,t,s)=>{"use strict";s.d(t,{W:()=>i});var a=s(54568);s(7620);var r=s(1839);let i=e=>{let{order:t,onConfirmByDriver:s,onNotifyAcceptance:i,onViewDetails:n,onDriverHeadingToClient:l}=e,d=new Date(t.scheduledTime),c=d.toLocaleDateString("ru-RU",{day:"2-digit",month:"2-digit",year:"2-digit"}),o=d.toLocaleTimeString("ru-RU",{hour:"2-digit",minute:"2-digit"}),u=(()=>{let e=(0,r.Vh)(t.orderStatus),s=t.orderSubStatus?(0,r.l1)(t.orderSubStatus):"";switch(t.orderStatus){case"Scheduled":if("DriverAssigned"===t.orderSubStatus)return{text:"".concat(e," (").concat(s,")"),color:"orange"};return{text:e,color:"blue"};case"InProgress":return{text:"".concat(e," (").concat(s,")"),color:"green"};case"Completed":return{text:e,color:"blue"};case"Cancelled":return{text:e,color:"red"};default:return{text:e,color:"gray"}}})(),h=new Date().getTime(),p=new Date(t.scheduledTime).getTime()-h,g="Scheduled"!==t.orderStatus||"DriverAssigned"!==t.orderSubStatus?[]:"Accepted"===t.status?[{action:"driver-heading",label:"Перейти к активной поездке",color:"green"}]:"Requested"===t.status&&(p<0||p/6e4<=90)?[{action:"accept-by-driver",label:"Еду к клиенту",color:"red"}]:[],v=g.length>0;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg  truncate",children:t.customerName||"-"}),(0,a.jsx)("p",{className:"text-sm mt-1",children:t.customerName?"Контакт в деталях заказа":"-"})]}),(0,a.jsxs)("div",{className:"flex-1 text-center mx-8",children:[(0,a.jsxs)("div",{className:"flex justify-center space-x-8 mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-lg",children:c}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Дата"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-lg",children:o}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Время"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-lg",children:"-"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Рейс"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center flex-col space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-2 ".concat("green"===u.color?"bg-green-500":"orange"===u.color?"bg-orange-500":"blue"===u.color?"bg-blue-500":"red"===u.color?"bg-red-500":"bg-gray-500")}),(0,a.jsx)("span",{className:"text-sm font-medium",children:u.text})]}),p<0&&"DriverAssigned"===t.orderSubStatus&&(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 animate-pulse",children:"⚠️ Просрочено"})]})]}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-blue-600 mr-3 mt-0.5 flex-shrink-0",children:"✈"}),(0,a.jsx)("span",{className:"text-sm  leading-relaxed",children:t.fromAddress||"-"})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-blue-600 mr-3 mt-0.5 flex-shrink-0",children:"\uD83C\uDFE2"}),(0,a.jsx)("span",{className:"text-sm  leading-relaxed",children:t.toAddress||"-"})]})]})})]}),(0,a.jsx)("div",{className:"mt-6 pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex space-x-3",children:[v&&g.map((e,r)=>(0,a.jsx)("button",{onClick:()=>{switch(e.action){case"accept-by-driver":null==s||s(t.id,t.orderId||void 0);break;case"driver-heading":null==l||l(t.id,t.orderId||void 0);break;case"view-active":null==n||n(t.id,t.orderId||void 0)}},className:"px-6 py-2 rounded-lg font-medium transition-colors ".concat("red"===e.color?"bg-red-500 hover:bg-red-600 text-white":"orange"===e.color?"bg-orange-500 hover:bg-orange-600 text-white":"blue"===e.color?"bg-blue-500 hover:bg-blue-600 text-white":"bg-green-500 hover:bg-green-600 text-white"),children:e.label},r)),(0,a.jsx)("button",{onClick:()=>null==n?void 0:n(t.id,t.orderId||void 0),className:"bg-blue-500 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-600 transition-colors",children:"Перейти к детальному заказу"})]})})]})}},62571:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});var a=s(27736),r=s(80745);class i extends a.vA{async getRide(e){let t=await this.get("/".concat(e));return this.handleApiResult(t)}async getRideSafe(e){let t=await this.get("/".concat(e));return this.handleApiResultSafe(t)}async getRides(e){let t=new URLSearchParams;(null==e?void 0:e.first)!==void 0&&t.append("first",e.first.toString()),(null==e?void 0:e.before)&&t.append("before",e.before),(null==e?void 0:e.after)&&t.append("after",e.after),(null==e?void 0:e.last)!==void 0&&t.append("last",e.last.toString()),(null==e?void 0:e.size)!==void 0&&t.append("size",e.size.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.status)&&t.append("status",e.status),(null==e?void 0:e.driverId)&&t.append("driverId",e.driverId),(null==e?void 0:e.passengerId)&&t.append("passengerId",e.passengerId);let s=t.toString()?"?".concat(t.toString()):"",a=await this.get(s);return this.handleApiResult(a)}async getDriverActiveRides(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,s=new URLSearchParams;s.append("DriverId",e),s.append("Size",t.toString()),s.append("Status","Accepted"),s.append("Status","Arrived"),s.append("Status","InProgress");let a=await this.get("my?".concat(s.toString()));return this.handleApiResult(a)}async getDriverScheduledRides(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,s=new URLSearchParams;s.append("driverId",e),s.append("pageSize",t.toString()),s.append("Status","Requested"),s.append("Status","Accepted");let a=await this.get("my/assigned?".concat(s.toString()));return this.handleApiResult(a)}async createRide(e){let t=await this.post("",e);return this.handleApiResult(t)}async updateRide(e,t){let s=await this.put("/".concat(e),t);return this.handleApiResult(s)}async deleteRide(e){let t=await this.delete("/".concat(e));this.handleApiResult(t)}async updateRideStatus(e,t){let s=await this.patch("/".concat(e,"/status"),{status:t});return this.handleApiResult(s)}async acceptRide(e,t){let s=await this.patch("/".concat(e,"/accept"),{driverId:t});return this.handleApiResult(s)}async completeRide(e){let t=await this.patch("/".concat(e,"/complete"));return this.handleApiResult(t)}async cancelRide(e,t){let s=await this.patch("/".concat(e,"/cancel"),{reason:t});return this.handleApiResult(s)}async getDriverQueuePosition(){try{let e=await this.get("/DriverQueue/self");return this.handleApiResultSafe(e)}catch(e){return null}}async joinDriverQueue(){let e=await this.post("/DriverQueue/self");return this.handleApiResult(e)}async leaveDriverQueue(){let e=await this.delete("/DriverQueue/self");this.handleApiResult(e)}async isDriverInQueue(){return null!==await this.getDriverQueuePosition()}async acceptInstantOrder(e){let t=await this.post("/Order/instant/".concat(e,"/accept-by-driver"));return this.handleApiResult(t)}async acceptScheduledRide(e){let t=await this.post("/Ride/".concat(e,"/accept-by-driver"));return this.handleApiResult(t)}async notifyScheduledRideAcceptance(e){let t=await this.post("/Ride/".concat(e,"/notification-by-driver"));this.handleApiResult(t)}async rejectScheduledRide(e){let t=await this.post("/Ride/".concat(e,"/reject-by-driver"));return this.handleApiResult(t)}async driverHeadingToClient(e){let t=await this.post("/Ride/".concat(e,"/status/driver-heading-to-client"));return this.handleApiResult(t)}async driverArrived(e){let t=await this.post("/Ride/".concat(e,"/status/driver-arrived"));return this.handleApiResult(t)}async rideStarted(e){let t=await this.post("/Ride/".concat(e,"/status/ride-started"));return this.handleApiResult(t)}async rideFinished(e){let t=await this.post("/Ride/".concat(e,"/status/ride-finished"));return this.handleApiResult(t)}async rideCancelled(e){let t=await this.post("/Ride/".concat(e,"/status/ride-cancelled"));return this.handleApiResult(t)}async getActiveRides(e){return e?(await this.getDriverActiveRides(e)).data||[]:(await this.getDriverActiveRides("current")).data||[]}async getRideById(e){let t=await this.get("/Ride/".concat(e));return this.handleApiResult(t)}async getScheduledOrderDetails(e){let t=await this.get("/Order/scheduled/".concat(e));return this.handleApiResult(t)}async getOrderDetails(e){let t=await this.get("/Order/".concat(e));return this.handleApiResult(t)}async getOrderDetailsUniversal(e){try{return await this.getScheduledOrderDetails(e)}catch(s){if(s&&"object"==typeof s&&"response"in s){var t;if((null==(t=s.response)?void 0:t.status)===404)return await this.getOrderDetails(e)}throw s}}async getCurrentDriverId(){try{let e=await this.get("/Driver/current"),t=this.handleApiResultSafe(e);return(null==t?void 0:t.driverId)||null}catch(e){return console.error("❌ Ошибка получения ID текущего водителя:",e),null}}async assignDriver(e,t,s){let a={driverId:t};s&&(a.carId=s);let r=await this.patch("/".concat(e,"/assign-driver"),a);return this.handleApiResult(r)}constructor(...e){super(...e),this.baseUrl=r.QQ.RIDE.LIST}}let n=new i},62942:(e,t,s)=>{"use strict";var a=s(42418);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},68327:(e,t,s)=>{"use strict";s.d(t,{P:()=>d,ToastManager:()=>i});var a=s(54568);s(97987),s(84325),s(7620);var r=s(51920);let i=e=>{let{position:t="top-right",autoClose:s=5e3,hideProgressBar:i=!1,closeOnClick:n=!0,pauseOnHover:l=!0,draggable:d=!1,theme:c="colored"}=e;return(0,a.jsx)(r.N9,{position:t,autoClose:s,hideProgressBar:i,closeOnClick:n,pauseOnHover:l,draggable:d,theme:c})},n=e=>{let{message:t}=e;return(0,a.jsx)("div",{className:"toast-message",children:t})},l=e=>{let{message:t}=e;if(t.includes("\n")){let e=t.split("\n");return(0,a.jsxs)("div",{className:"toast-message",children:[e[0],(0,a.jsx)("ul",{className:"mt-2 pl-4 list-disc",children:e.slice(1).map((e,t)=>(0,a.jsx)("li",{className:"text-sm",children:e},t))})]})}return(0,a.jsx)("div",{className:"toast-message",children:t})},d={success:(e,t)=>r.oR.success((0,a.jsx)(n,{message:e}),{icon:!1,position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!1,theme:"colored",...t}),error:(e,t)=>r.oR.error((0,a.jsx)(l,{message:e}),{icon:!1,position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!1,theme:"colored",...t}),info:(e,t)=>r.oR.info(e,{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!1,theme:"colored",...t}),warn:(e,t)=>r.oR.warn(e,{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!1,theme:"colored",...t})}},84325:()=>{}}]);