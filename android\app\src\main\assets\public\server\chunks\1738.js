exports.id=1738,exports.ids=[1738],exports.modules={1106:(e,t,r)=>{"use strict";r.d(t,{AK:()=>d,Vp:()=>o,ZA:()=>s,e5:()=>n,TM:()=>c,Xh:()=>a.X,mm:()=>i,iS:()=>l});var a=r(52646),l=function(e){return e.Pending="Pending",e.Verified="Verified",e.Rejected="Rejected",e.InReview="InReview",e.Expired="Expired",e}({}),s=function(e){return e.RU="RU",e.BY="BY",e.KZ="KZ",e.UA="UA",e.UZ="UZ",e.TJ="TJ",e.KG="KG",e.AM="AM",e.AZ="AZ",e.MD="MD",e.GE="GE",e.OTHER="OTHER",e}({}),i=function(e){return e.Economy="Economy",e.Comfort="Comfort",e.ComfortPlus="ComfortPlus",e.Business="Business",e.Premium="Premium",e.Vip="Vip",e.Luxury="Luxury",e}({}),n=function(e){return e.NationalPassport="NationalPassport",e.InternationalPassport="InternationalPassport",e.IdCard="IdCard",e.ResidencePermit="ResidencePermit",e.RefugeeId="RefugeeId",e.TemporaryId="TemporaryId",e.MilitaryId="MilitaryId",e.ForeignPassport="ForeignPassport",e.DriversLicense="DriversLicense",e}({}),o=function(e){return e.Individual="Individual",e.LLC="LLC",e.Corporation="Corporation",e.Partnership="Partnership",e.Cooperative="Cooperative",e.NonProfit="NonProfit",e.GovernmentEntity="GovernmentEntity",e}({}),d=function(e){return e.Active="Active",e.Inactive="Inactive",e.Suspended="Suspended",e.Blocked="Blocked",e.OnVacation="OnVacation",e}({}),c=function(e){return e.RU="RU",e.EN="EN",e.KK="KK",e.KY="KY",e.UZ="UZ",e.TJ="TJ",e.HY="HY",e.AZ="AZ",e.KA="KA",e.BE="BE",e.UK="UK",e}({})},1251:(e,t,r)=>{"use strict";r.d(t,{_n:()=>a});var a=function(e){return e.Unknown="Unknown",e.OrderCreated="OrderCreated",e.OrderUpdated="OrderUpdated",e.OrderConfirmed="OrderConfirmed",e.OrderCancelled="OrderCancelled",e.OrderCompleted="OrderCompleted",e.RideRequest="RideRequest",e.RideAccepted="RideAccepted",e.RideRejected="RideRejected",e.RideStarted="RideStarted",e.RideCompleted="RideCompleted",e.RideCancelled="RideCancelled",e.RideUpdate="RideUpdate",e.Payment="Payment",e.PaymentReceived="PaymentReceived",e.PaymentFailed="PaymentFailed",e.PaymentRefunded="PaymentRefunded",e.DriverHeading="DriverHeading",e.DriverArrived="DriverArrived",e.DriverAssigned="DriverAssigned",e.DriverCancelled="DriverCancelled",e.DriverNearby="DriverNearby",e.System="System",e.SystemMessage="SystemMessage",e.Maintenance="Maintenance",e.Promo="Promo",e.PromoOffer="PromoOffer",e.Verification="Verification",e.Chat="Chat",e}({})},3775:(e,t,r)=>{"use strict";r.d(t,{JV:()=>s,TM:()=>n,Vh:()=>l,dF:()=>o,mJ:()=>i});var a=r(29457);function l(e){switch(e){case a.Re.Pending:return"Ожидание";case a.Re.Scheduled:return"Запланирован";case a.Re.InProgress:return"В процессе";case a.Re.Completed:return"Завершен";case a.Re.Cancelled:return"Отменен";case a.Re.Expired:return"Истек срок";default:return""}}function s(){return Object.values(a.Re).map(e=>({value:e,label:l(e)}))}function i(e){switch(e){case a.Re.Pending:return"bg-yellow-500/50";case a.Re.Scheduled:return"bg-blue-500/50";case a.Re.InProgress:return"bg-green-500/50";case a.Re.Completed:return"bg-emerald-500/50";case a.Re.Cancelled:return"bg-red-500/50";case a.Re.Expired:return"bg-orange-500/50";default:return"bg-gray-400/50"}}function n(e){switch(e){case a.Re.Pending:return{bg:"bg-yellow-50",text:"text-yellow-700",border:"border-yellow-200"};case a.Re.Scheduled:return{bg:"bg-blue-50",text:"text-blue-700",border:"border-blue-200"};case a.Re.InProgress:return{bg:"bg-green-50",text:"text-green-700",border:"border-green-200"};case a.Re.Completed:return{bg:"bg-emerald-50",text:"text-emerald-700",border:"border-emerald-200"};case a.Re.Cancelled:return{bg:"bg-red-50",text:"text-red-700",border:"border-red-200"};case a.Re.Expired:return{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200"};default:return{bg:"bg-gray-50",text:"text-gray-700",border:"border-gray-200"}}}function o(e){switch(e){case a.Re.Pending:return"Заказы, ожидающие назначения водителя";case a.Re.Scheduled:return"Заказы, запланированные на определенное время";case a.Re.InProgress:return"Заказы, которые выполняются в данный момент";case a.Re.Completed:return"Успешно завершенные заказы";case a.Re.Cancelled:return"Отмененные заказы";case a.Re.Expired:return"⚠️ КРИТИЧНО! Заказы с истекшим сроком - требуют немедленного внимания!";default:return""}}},5224:(e,t,r)=>{"use strict";r.d(t,{fU:()=>s,$Z:()=>i,d1:()=>n,tn:()=>o});var a=r(66616),l=r(64768);let s={title:{create:"Создание уведомления",edit:"Редактирование уведомления"},description:{create:"Заполните информацию для создания уведомления",edit:"Редактирование информации уведомления"},groups:[{id:"default",title:"Основная информация",fields:[{name:"type",label:"Тип уведомления",type:a.V.SELECT,dataType:a.R.OBJECT,required:!0,placeholder:"Выберите тип уведомления",options:(0,l.k)()},{name:"title",label:"Заголовок",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите заголовок",helperText:"Заголовок уведомления"},{name:"content",label:"Содержание",type:a.V.TEXTAREA,dataType:a.R.STRING,required:!1,placeholder:"Введите содержание",helperText:"Текст уведомления"},{name:"orderId",label:"ID заказа",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID заказа",helperText:"Ссылка на связанный заказ"},{name:"id",label:"ID поездки",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID поездки",helperText:"Ссылка на связанную поездку"},{name:"isRead",label:"Прочитано",type:a.V.CHECKBOX,dataType:a.R.BOOLEAN,required:!1,helperText:"Прочитано ли уведомление пользователем"},{name:"userId",label:"ID пользователя",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите ID пользователя",helperText:"Получатель уведомления"}],layout:{gridCols:2,gapX:4,gapY:4}}]},i={title:{create:"Просмотр уведомления",edit:"Редактирование уведомления"},description:{create:"Просмотр информации уведомления",edit:"Редактирование информации уведомления"},groups:[{id:"default",title:"Основная информация",fields:[{name:"id",label:"ID",type:a.V.TEXT,dataType:a.R.STRING,required:!0,disabled:!0},{name:"type",label:"Тип уведомления",type:a.V.SELECT,dataType:a.R.OBJECT,required:!0,placeholder:"Выберите тип уведомления",options:(0,l.k)()},{name:"title",label:"Заголовок",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите заголовок",helperText:"Заголовок уведомления"},{name:"content",label:"Содержание",type:a.V.TEXTAREA,dataType:a.R.STRING,required:!1,placeholder:"Введите содержание",helperText:"Текст уведомления"},{name:"orderId",label:"ID заказа",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID заказа",helperText:"Ссылка на связанный заказ"},{name:"id",label:"ID поездки",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID поездки",helperText:"Ссылка на связанную поездку"},{name:"isRead",label:"Прочитано",type:a.V.CHECKBOX,dataType:a.R.BOOLEAN,required:!1,helperText:"Прочитано ли уведомление пользователем"}],layout:{gridCols:2,gapX:4,gapY:4}}]},n={title:{create:"Создание уведомления",edit:"Редактирование уведомления"},description:{create:"Заполните информацию для создания уведомления",edit:"Редактирование информации уведомления"},groups:[{id:"default",title:"Основная информация",fields:[{name:"type",label:"Тип уведомления",type:a.V.SELECT,dataType:a.R.OBJECT,required:!0,placeholder:"Выберите тип уведомления",options:(0,l.k)()},{name:"title",label:"Заголовок",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите заголовок",helperText:"Заголовок уведомления"},{name:"content",label:"Содержание",type:a.V.TEXTAREA,dataType:a.R.STRING,required:!1,placeholder:"Введите содержание",helperText:"Текст уведомления"},{name:"orderId",label:"ID заказа",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID заказа",helperText:"Ссылка на связанный заказ"},{name:"id",label:"ID поездки",type:a.V.TEXT,dataType:a.R.STRING,required:!1,placeholder:"Введите ID поездки",helperText:"Ссылка на связанную поездку"},{name:"isRead",label:"Прочитано",type:a.V.CHECKBOX,dataType:a.R.BOOLEAN,required:!1,helperText:"Прочитано ли уведомление пользователем"}],layout:{gridCols:2,gapX:4,gapY:4}}]},o={title:{create:"Отметить уведомление как прочитанное",edit:"Отметить уведомление как прочитанное"},description:{create:"Укажите ID уведомления для отметки как прочитанное",edit:"Укажите ID уведомления для отметки как прочитанное"},groups:[{id:"default",title:"Информация об уведомлении",fields:[{name:"id",label:"ID уведомления",type:a.V.TEXT,dataType:a.R.STRING,required:!0,placeholder:"Введите ID уведомления",helperText:"Уникальный идентификатор уведомления"}],layout:{gridCols:1,gapX:4,gapY:4}}]}},5684:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>l.useHeaderDropdowns,V$:()=>s.useDriverHeader,if:()=>a.useBreadcrumbs});var a=r(55577),l=r(46154),s=r(41644)},5777:(e,t,r)=>{"use strict";r(96274)},9019:(e,t,r)=>{"use strict";function a(e,t){return Object.values(e).map(e=>({value:e,label:t(e)}))}function l(e,t){return e.filter(e=>!t.includes(e.value))}r.d(t,{mJ:()=>l,wI:()=>a})},11994:(e,t,r)=>{"use strict";r.d(t,{DriverLocationProvider:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call DriverLocationProvider() from the server but DriverLocationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\app\\providers\\DriverLocationProvider.tsx","DriverLocationProvider")},12431:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var a=r(95124);a.V;let l=a.V},13367:(e,t,r)=>{"use strict";r.d(t,{m:()=>a.m});var a=r(68671)},13510:(e,t,r)=>{"use strict";r.d(t,{Tg:()=>l,AC:()=>s,UG:()=>i,Rv:()=>n,MJ:()=>h,jz:()=>g,Qu:()=>f,JR:()=>b,i5:()=>x,pp:()=>v,yD:()=>y,Pe:()=>C});var a=r(43888);a.z.object({fullName:a.z.string().min(3,{message:"Имя должно содержать не менее 3 символов"}).max(255,{message:"Имя не должно превышать 255 символов"}),phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).regex(/^[+]?[0-9\s()-]{7,}$/,{message:"Введите корректный номер телефона (минимум 7 цифр)"}).optional(),email:a.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"})});let l=a.z.object({email:a.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:a.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:a.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),profile:a.z.object({accessLevel:a.z.string().min(1,{message:"Уровень доступа обязателен"}).max(63,{message:"Уровень доступа не должен превышать 63 символа"}),department:a.z.string().max(127,{message:"Отдел не должен превышать 127 символов"}).nullable(),position:a.z.string().max(127,{message:"Должность не должна превышать 127 символов"}).nullable()})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),s=a.z.object({phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),profile:a.z.object({accessLevel:a.z.string().max(63,{message:"Уровень доступа не должен превышать 63 символа"}),department:a.z.string().max(127,{message:"Отдел не должен превышать 127 символов"}).nullable(),position:a.z.string().max(127,{message:"Должность не должна превышать 127 символов"}).nullable()})}),i=a.z.object({email:a.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:a.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:a.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().max(511,{message:"URL аватара не должен превышать 511 символов"}).nullable().optional(),loyaltyPoints:a.z.number().int({message:"Баллы лояльности должны быть целым числом"})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),n=a.z.object({phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),loyaltyPoints:a.z.number().int({message:"Баллы лояльности должны быть целым числом"})});var o=r(1106);let d=a.z.object({institution:a.z.string().min(1,{message:"Название учебного заведения обязательно"}).max(255,{message:"Название учебного заведения не должно превышать 255 символов"}),degree:a.z.string().max(255,{message:"Полученная степень/квалификация не должна превышать 255 символов"}).nullable(),fieldOfStudy:a.z.string().max(127,{message:"Специальность не должна превышать 127 символов"}).nullable(),startDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата начала обучения должна быть в формате YYYY-MM-DD"}).nullable(),endDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата окончания обучения должна быть в формате YYYY-MM-DD"}).nullable(),isCompleted:a.z.boolean()}),c=a.z.object({number:a.z.string().min(1,{message:"Номер паспорта обязателен"}).max(63,{message:"Номер паспорта не должен превышать 63 символа"}),series:a.z.string().max(2,{message:"Серия паспорта не должна превышать 2 символа"}).nullable().optional(),issueDate:a.z.string().min(1,{message:"Дата выдачи паспорта обязательна"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата выдачи должна быть в формате YYYY-MM-DD"}),issuedBy:a.z.string().min(1,{message:"Кем выдан паспорт обязательно"}).max(31,{message:"Кем выдан паспорт не должен превышать 31 символ"}),page:a.z.number({message:"Страница паспорта должна быть числом"}).int({message:"Страница паспорта должна быть целым числом"}).min(1,{message:"Страница паспорта должна быть больше 0"}).nullable().optional(),expiryDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата окончания срока действия должна быть в формате YYYY-MM-DD"}).nullable().optional(),identityType:a.z.nativeEnum(o.e5,{errorMap:()=>({message:"Выберите тип документа"})})}),u=a.z.object({testName:a.z.string().min(1,{message:"Название пройденного теста обязательно"}).max(127,{message:"Название пройденного теста не должно превышать 127 символов"}),score:a.z.number({message:"Полученный балл должен быть числом"}),maxPossibleScore:a.z.number({message:"Максимально возможный балл должен быть числом"}),passedDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата прохождения теста должна быть в формате YYYY-MM-DD"}),expiryDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата истечения срока действия теста должна быть в формате YYYY-MM-DD"}).nullable(),comments:a.z.string().max(511,{message:"Комментарии к результату не должны превышать 511 символов"}).nullable()}),p=a.z.object({employerName:a.z.string().min(1,{message:"Название предыдущего работодателя обязательно"}).max(127,{message:"Название предыдущего работодателя не должно превышать 127 символов"}),position:a.z.string().min(1,{message:"Должность на предыдущем месте работы обязательна"}).max(127,{message:"Должность на предыдущем месте работы не должна превышать 127 символов"}),startDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата начала работы должна быть в формате YYYY-MM-DD"}),endDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата окончания работы должна быть в формате YYYY-MM-DD"}).nullable(),isCurrent:a.z.boolean(),responsibilities:a.z.string().max(4095,{message:"Основные обязанности не должны превышать 4095 символов"}).nullable()}),m=a.z.object({licenseNumber:a.z.string({required_error:"Номер водительского удостоверения обязателен",invalid_type_error:"Номер водительского удостоверения должен быть строкой"}).min(1,{message:"Номер водительского удостоверения не может быть пустым"}).max(31,{message:"Номер водительского удостоверения не должен превышать 31 си��вол"}),licenseCategories:a.z.array(a.z.string(),{required_error:"Категории прав обязательны",invalid_type_error:"Категории прав должны быть массивом строк"}).min(1,{message:"Выберите хотя бы одну категорию прав"}),licenseIssueDate:a.z.string({required_error:"Дата выдачи прав обязательна",invalid_type_error:"Дата выдачи прав должна быть строкой"}).min(1,{message:"Дата выдачи прав не может быть пустой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата выдачи прав должна быть в формате YYYY-MM-DD"}),licenseExpiryDate:a.z.string({required_error:"Дата окончания прав обязательна",invalid_type_error:"Дата окончания прав должна быть строкой"}).min(1,{message:"Дата окончания прав не может быть пустой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата окончания прав должна быть в формате YYYY-MM-DD"}),dateOfBirth:a.z.string({required_error:"Дата рождения обязательна",invalid_type_error:"Дата рождения должна быть строкой"}).min(1,{message:"Дата рождения не может быть пустой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата рождения должна быть в формате YYYY-MM-DD"}),birthPlace:a.z.string({invalid_type_error:"Место рождения должно быть строкой"}).max(255,{message:"Место рождения не должно превышать 255 символов"}).nullable().optional(),citizenship:a.z.string({required_error:"Гражданство обязательно",invalid_type_error:"Гражданство должно быть строкой"}).min(1,{message:"Гражданство не может быть пустым"}).max(63,{message:"Гражданство не должно превышать 63 символа"}),citizenshipCountry:a.z.nativeEnum(o.ZA,{errorMap:(e,t)=>e.code===a.z.ZodIssueCode.invalid_type?{message:"Некорректная страна гражданства"}:e.code===a.z.ZodIssueCode.invalid_enum_value?{message:`Недопустимая страна гражданства: ${e.received}. Допустимые значения: ${Object.values(o.ZA).join(", ")}`}:{message:"Страна гражданства обязательна"}}),drivingExperience:a.z.number({invalid_type_error:"Опыт вождения должен быть числом"}).int({message:"Опыт вождения должен быть целым числом"}).min(0,{message:"Опыт вождения не может быть отрицательным"}).nullable().optional(),languages:a.z.array(a.z.string(),{required_error:"Языки обязательны",invalid_type_error:"Языки должны быть массивом строк"}).min(1,{message:"Выберите хотя бы один язык"}),taxIdentifier:a.z.string({invalid_type_error:"ИНН должен быть строкой"}).max(31,{message:"ИНН не должен превышать 31 си��вол"}).nullable().optional(),totalRides:a.z.number({required_error:"Общее количество поездок обязательно",invalid_type_error:"Общее количество поездок должно быть числом"}).int({message:"Общее количество поездок должно быть целым числом"}).min(0,{message:"Количество поездок не может быть отрицательным"}).default(0),totalDistance:a.z.number({required_error:"Общее пройденное расстояние обязательно",invalid_type_error:"Общее пройденное расстояние должно быть числом"}).min(0,{message:"Пройденное расстояние не может быть отрицательным"}).default(0),lastRideDate:a.z.string({invalid_type_error:"Дата последней поездки должна быть строкой"}).nullable().optional(),medicalExamDate:a.z.string({invalid_type_error:"Дата медосмотра должна быть строкой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата последнего медосмотра должна быть в формате YYYY-MM-DD"}).nullable().optional(),backgroundCheckDate:a.z.string({invalid_type_error:"Дата проверки биографии должна быть строкой"}).regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата последней проверки биографии должна быть в формате YYYY-MM-DD"}).nullable().optional(),profilePhoto:a.z.string({invalid_type_error:"URL фото профиля должен быть строкой"}).max(511,{message:"URL фото профиля не должен превышать 511 символов"}).nullable().optional(),preferredRideTypes:a.z.array(a.z.string(),{required_error:"Предпочитаемые классы обслуживания обязательны",invalid_type_error:"Предпочитаемые классы обслуживания должны быть массивом строк"}).min(1,{message:"Выберите хотя бы один предпочитаемый класс обслуживания"}),preferredWorkZones:a.z.array(a.z.string(),{invalid_type_error:"Предпочитаемые зоны работы должны быть массивом строк"}).optional(),trainingCompleted:a.z.boolean({required_error:"Статус обучения обязателен",invalid_type_error:"Статус обучения должен быть булевым значением"}).default(!1),passport:c,workExperience:a.z.preprocess(e=>{if("string"==typeof e)try{return JSON.parse(e)}catch{return[]}return e},a.z.array(p,{invalid_type_error:"Опыт работы должен быть массивом объектов"}).optional()),education:a.z.preprocess(e=>{if("string"==typeof e)try{return JSON.parse(e)}catch{return[]}return e},a.z.array(d,{invalid_type_error:"Образование должно быть массивом объектов"}).optional()),testScore:a.z.preprocess(e=>{if("string"==typeof e)try{return JSON.parse(e)}catch{return[]}return e},a.z.array(u,{invalid_type_error:"Результаты тестов должны быть массивом объектов"}).optional())}),h=a.z.object({email:a.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:a.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:a.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),verificationStatus:a.z.nativeEnum(o.iS,{errorMap:()=>({message:"Выберите статус верификации"})}),profile:m,car:a.z.string().uuid({message:"Некорректный ID автомобиля"}).optional()}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]});a.z.object({phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).nullable().optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),verificationStatus:a.z.nativeEnum(o.iS,{errorMap:()=>({message:"Выберите статус верификации"})}),profile:m,car:a.z.string().uuid({message:"Некорректный ID автомобиля"}).nullable().optional()});let g=a.z.object({phoneNumber:a.z.string({invalid_type_error:"Номер телефона должен быть строкой"}).max(63,{message:"Номер телефона не должен превышать 63 символа"}).nullable().optional(),fullName:a.z.string({required_error:"Полное имя обязательно",invalid_type_error:"Полное имя должно быть строкой"}).min(1,{message:"Полное имя не может быть пустым"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string({invalid_type_error:"URL аватара должен быть строкой"}).max(511,{message:"URL аватара не должен превышать 511 символов"}).nullable().optional(),verificationStatus:a.z.nativeEnum(o.iS,{errorMap:(e,t)=>e.code===a.z.ZodIssueCode.invalid_type?{message:"Некорректный статус верификации"}:e.code===a.z.ZodIssueCode.invalid_enum_value?{message:`Недопустимый статус верификации: ${e.received}. Допустимые значения: ${Object.values(o.iS).join(", ")}`}:{message:"Статус верификации обязателен"}}),profile:m}),f=a.z.object({email:a.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:a.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:a.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),isActive:a.z.boolean(),profile:a.z.object({employeeId:a.z.string().min(1,{message:"Табельный номер обязателен"}).max(63,{message:"Табельный номер не должен превышать 63 символа"}),department:a.z.string().min(1,{message:"Отдел обязателен"}).max(127,{message:"Отдел не должен превышать 127 символов"}),position:a.z.string().min(1,{message:"Должность обязательна"}).max(127,{message:"Должность не должна превышать 127 символов"}),hireDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата найма должна быть в формате YYYY-MM-DD"})})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),b=a.z.object({phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),isActive:a.z.boolean(),profile:a.z.object({employeeId:a.z.string().max(63,{message:"Табельный номер не должен превышать 63 символа"}),department:a.z.string().max(127,{message:"Отдел не должен превышать 127 символов"}),position:a.z.string().max(127,{message:"Должность не должна превышать 127 символов"}),hireDate:a.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,{message:"Дата найма должна быть в формате YYYY-MM-DD"})})}),x=a.z.object({email:a.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:a.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:a.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),verificationStatus:a.z.nativeEnum(o.iS,{errorMap:()=>({message:"Выберите статус верификации"})}),profile:a.z.object({companyName:a.z.string().min(1,{message:"Название компании обязательно"}),companyType:a.z.nativeEnum(o.Vp,{errorMap:()=>({message:"Выберите тип компании"})}),registrationNumber:a.z.string().max(31,{message:"Регистрационный номер не должен превышать 31 символ"}).nullable(),taxIdentifier:a.z.string().max(31,{message:"ИНН не должен превышать 31 символ"}).nullable(),legalAddress:a.z.string().min(1,{message:"Юридический адрес обязателен"}).max(255,{message:"Юридический адрес не должен превышать 255 символов"}),contactEmail:a.z.string().max(255,{message:"Контактный email не должен превышать 255 символов"}).email({message:"Введите корректный email"}).nullable(),contactPhone:a.z.string().max(63,{message:"Контактный телефон не должен превышать 63 символа"}).nullable(),website:a.z.string().max(255,{message:"Веб-сайт не должен превышать 255 символов"}).nullable()})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),v=a.z.object({phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),verificationStatus:a.z.nativeEnum(o.iS,{errorMap:()=>({message:"Выберите статус верификации"})}),profile:a.z.object({companyName:a.z.string().min(1,{message:"Название компании обязательно"}),companyType:a.z.nativeEnum(o.Vp,{errorMap:()=>({message:"Выберите тип компании"})}),registrationNumber:a.z.string().max(31,{message:"Регистрационный номер не должен превышать 31 символ"}).nullable(),taxIdentifier:a.z.string().max(31,{message:"ИНН не должен превышать 31 символ"}).nullable(),legalAddress:a.z.string().min(1,{message:"Юридический адрес обязателен"}).max(255,{message:"Юридический адрес не должен превышать 255 символов"}),contactEmail:a.z.string().max(255,{message:"Контактный email не должен превышать 255 символов"}).email({message:"Введите корректный email"}).nullable(),contactPhone:a.z.string().max(63,{message:"Контактный телефон не должен превышать 63 символа"}).nullable(),website:a.z.string().max(255,{message:"Веб-сайт не должен превышать 255 символов"}).nullable()})}),y=a.z.object({email:a.z.string().min(1,{message:"Email обязателен"}).email({message:"Введите корректный email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:a.z.string().min(8,{message:"Пароль должен содержать не менее 8 символов"}),confirmPassword:a.z.string().min(1,{message:"Подтверждение пароля обязательно"}),phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().nullable().optional(),status:a.z.nativeEnum(o.AK,{errorMap:()=>({message:"Выберите статус терминала"})}),locationId:a.z.string().uuid({message:"Некорректный UUID локации"}).optional(),profile:a.z.object({terminalId:a.z.string().min(1,{message:"Идентификатор терминала обязателен"}).max(31,{message:"Идентификатор терминала не должен превышать 31 символ"}),ipAddress:a.z.string().max(45,{message:"IP адрес не должен превышать 45 символов"}).nullable(),deviceModel:a.z.string().max(127,{message:"Модель устройства не должна превышать 127 символов"}).nullable(),osVersion:a.z.string().max(31,{message:"Версия ОС не должна превышать 31 символ"}).nullable(),appVersion:a.z.string().max(31,{message:"Версия приложения не должна превышать 31 символ"}).nullable(),browserInfo:a.z.string().max(127,{message:"Информация о браузере не должна превышать 127 символов"}).nullable(),screenResolution:a.z.string().max(15,{message:"Разрешение экрана не должно превышать 15 символов"}).nullable(),deviceIdentifier:a.z.string().max(31,{message:"Идентификатор устройства не должен превышать 31 символ"}).nullable()})}).refine(e=>e.password===e.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),C=a.z.object({phoneNumber:a.z.string().max(63,{message:"Номер телефона не должен превышать 63 символа"}).optional(),fullName:a.z.string().min(1,{message:"Полное имя обязательно"}).max(255,{message:"Полное имя не должно превышать 255 символов"}),avatarUrl:a.z.string().optional(),status:a.z.nativeEnum(o.AK,{errorMap:()=>({message:"Выберите статус терминала"})}),locationId:a.z.string().uuid({message:"Некорректный UUID локации"}).optional(),profile:a.z.object({terminalId:a.z.string().min(1,{message:"Идентификатор терминала обязателен"}).max(31,{message:"Идентификатор терминала не должен превышать 31 символ"}),ipAddress:a.z.string().max(45,{message:"IP адрес не должен превышать 45 символов"}).nullable(),deviceModel:a.z.string().max(127,{message:"Модель устройства не должна превышать 127 символов"}).nullable(),osVersion:a.z.string().max(31,{message:"Версия ОС не должна превышать 31 символ"}).nullable(),appVersion:a.z.string().max(31,{message:"Версия приложения не должна превышать 31 символ"}).nullable(),browserInfo:a.z.string().max(127,{message:"Информация о браузере не должна превышать 127 символов"}).nullable(),screenResolution:a.z.string().max(15,{message:"Разрешение экрана не должно превышать 15 символов"}).nullable(),deviceIdentifier:a.z.string().max(31,{message:"Идентификатор устройства не должен превышать 31 символ"}).nullable()})});a.z.boolean({required_error:"Статус активности связи обязателен",invalid_type_error:"Статус активности должен быть булевым значением (true/false)"}),a.z.object({carId:a.z.string().uuid("ID автомобиля должен быть валидным UUID").min(1,"ID автомобиля обязателен"),action:a.z.enum(["activate","deactivate"],{errorMap:()=>({message:"Действие должно быть activate или deactivate"})}),reason:a.z.string().max(200,"Причина не должна превышать 200 символов").optional()}),a.z.object({activeCarId:a.z.string().uuid("ID автомобиля должен быть валидным UUID").nullable(),updatedAt:a.z.string().datetime("Дата должна быть в формате ISO 8601").optional()}),a.z.object({availableCarIds:a.z.array(a.z.string().uuid("ID автомобиля должен быть валидным UUID")).min(0,"Список может быть пустым"),activeCarId:a.z.string().uuid("ID автомобиля должен быть валидным UUID").nullable()}),a.z.object({carId:a.z.string().uuid("ID автомобиля должен быть валидным UUID"),driverId:a.z.string().uuid("ID водителя должен быть валидным UUID"),isEligible:a.z.boolean(),reason:a.z.string().optional()}),a.z.object({id:a.z.string().uuid(),driverId:a.z.string().uuid(),carId:a.z.string().uuid(),action:a.z.enum(["activated","deactivated"]),timestamp:a.z.string().datetime(),reason:a.z.string().optional()})},13547:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var a=r(80994);a.G;let l=a.G},13879:(e,t,r)=>{"use strict";r.d(t,{Ow:()=>o,_n:()=>l._n,CU:()=>a.CU}),r(22445),r(93570),r(76575),r(77289);var a=r(70873);r(31071);var l=r(1251);r(5224),r(64768),r(20864),r(51709);var s=r(13486);r(60159);var i=r(96881),n=r(35028);let o=({notification:e,priority:t="order",onClick:r,onGoToRide:a,onGoToOrderDetails:l,onDelete:o,isExpanded:d=!1,onToggleExpanded:c})=>{let u=e.orderId||null,p=["RideRequestNotification","RideAcceptedNotification","RideAssignedNotification","RideCancelledNotification","RideCompletedNotification","DriverArrivedNotification","DriverHeadingNotification","RideStartedNotification"].includes(e.type)&&u&&a,m=u&&l,h=((e,t)=>{if(t)return"bg-gray-50 border-gray-200";switch(e){case"order":return"border-blue-200 bg-blue-50";case"important":case"completed":return"border-green-200 bg-green-50";case"warning":return"border-red-200 bg-red-50";default:return"border-gray-200 bg-gray-50"}})(t,e.isRead||!1);return(0,s.jsxs)("div",{className:`
        flex flex-col gap-4 justify-between p-4 rounded-xl border transition-all bg-[#F0F0F0]
        ${h}
        hover:shadow-md
      `,children:[(0,s.jsxs)("div",{className:"w-full flex items-center gap-4 cursor-pointer",onClick:()=>r(e),children:[(0,s.jsx)("div",{className:`
          w-3 h-3 rounded-full flex-shrink-0
          ${e.isRead?"bg-gray-400":"bg-blue-500"}
        `}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"font-medium  truncate",children:e.title}),e.content&&(0,s.jsx)("p",{className:"text-sm  mt-1 line-clamp-2",children:e.content}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Тип: ",(0,n.gw)(e.type)," | Приоритет: ",t?function(e){switch(e){case"order":return"О заказе";case"completed":return"Завершенные";case"important":return"Важные";case"warning":return"Предупреждения";default:return e}}(t):"Неизвестно"]})]}),(p||m||c)&&(0,s.jsxs)("div",{className:"flex gap-2",children:[p&&(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),a(u,e.rideId||void 0,e.orderType)},className:`
                  w-10 h-10 rounded-lg transition-colors flex items-center justify-center
                  ${d?"bg-blue-600 text-white hover:bg-blue-700":"bg-blue-500 text-white hover:bg-blue-600"}
                `,children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})}),m&&(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),l(u,e.rideId||void 0,e.orderType)},className:`
                  px-2 py-2 rounded-lg transition-colors flex items-center justify-center
                  ${d?"bg-gray-600 text-white hover:bg-gray-700":"bg-gray-500 text-white hover:bg-gray-600"}
                `,children:"Перейти к заказу"}),c&&(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),c(e)},className:`
                  w-10 h-10 rounded-lg transition-colors flex items-center justify-center
                  ${d?"bg-gray-600 text-white hover:bg-gray-700":"bg-gray-500 text-white hover:bg-gray-600"}
                `,children:(0,s.jsx)("svg",{className:`w-5 h-5 transition-transform ${d?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500 flex-shrink-0",children:(0,i.f)(e.createdAt)})]}),d&&(0,s.jsxs)("div",{className:"p-4 bg-white rounded-lg text-sm space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold  mb-1",children:"Полное содержание"}),(0,s.jsx)("p",{children:e.content||"Уважаемый водитель, в вашем районе появился новый заказ. Обратите внимание: – Клиент ожидает быстрое подтверждение – Расстояние до точки подачи — минимальное – Оплата — по стандартному тарифу или выше (если действует повышенный спрос). Чем быстрее вы принимаете заказы, тем выше ваш приоритет в системе и больше шанс получать выгодные поездки. Не забывайте: частый отказ от заказов может повлиять на ваш рейтинг. Спасибо, что работаете с нами!"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold  mb-1",children:"Тип уведомления"}),(0,s.jsx)("p",{children:(0,n.gw)(e.type)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold  mb-1",children:"Статус"}),(0,s.jsx)("p",{children:e.isRead?"Прочитано":"Не прочитано"})]})]})]})]})}},16802:(e,t,r)=>{"use strict";r.d(t,{CollectionHeader:()=>eA});var a=r(13486),l=r(97008),s=r(2984),i=r(60159),n=r(38655),o=r(43888),d=r(95395);o.z.object({make:o.z.string().min(1,{message:"Марка автомобиля обязательна"}).max(63,{message:"Марка автомобиля не должна превышать 63 символа"}),model:o.z.string().min(1,{message:"Модель автомобиля обязательна"}).max(63,{message:"Модель автомобиля не должна превышать 63 символа"}),year:o.z.number({required_error:"Год выпуска обязателен",invalid_type_error:"Год выпуска должен быть числом"}).int({message:"Год выпуска должен быть целым числом"}).min(1900,{message:"Год выпуска должен быть не ранее 1900"}).max(new Date().getFullYear()+1,{message:"Год выпуска не может быть в будущем"}),color:o.z.nativeEnum(d.OU,{errorMap:()=>({message:"Выберите цвет автомобиля"})}),licensePlate:o.z.string().min(1,{message:"Государственный регистрационный знак обязателен"}).max(15,{message:"Государственный регистрационный знак не должен превышать 15 символов"}),type:o.z.nativeEnum(d.$O,{errorMap:()=>({message:"Выберите тип автомобиля"})}),serviceClass:o.z.nativeEnum(d.mm,{errorMap:()=>({message:"Выберите класс обслуживания"})}),status:o.z.nativeEnum(d.zl,{errorMap:()=>({message:"Выберите статус автомобиля"})}),passengerCapacity:o.z.number({required_error:"Пассажировместимость обязательна",invalid_type_error:"Пассажировместимость должна быть числом"}).int({message:"Пассажировместимость должна быть целым числом"}).min(1,{message:"Пассажировместимость должна быть не менее 1"}).max(50,{message:"Пассажировместимость должна быть не более 50"}),features:o.z.array(o.z.nativeEnum(d.Lz)).min(1,{message:"Выберите хотя бы одну дополнительную опцию"}),firstShiftDriver:o.z.string().optional(),secondShiftDriver:o.z.string().optional(),previousFirstShiftDriver:o.z.string().optional(),previousSecondShiftDriver:o.z.string().optional()}),o.z.object({make:o.z.string().min(1,{message:"Марка автомобиля обязательна"}).max(63,{message:"Марка автомобиля не должна превышать 63 символа"}),model:o.z.string().min(1,{message:"Модель автомобиля обязательна"}).max(63,{message:"Модель автомобиля не должна превышать 63 символа"}),year:o.z.number({required_error:"Год выпуска обязателен",invalid_type_error:"Год выпуска должен быть числом"}).int({message:"Год выпуска должен быть целым числом"}).min(1900,{message:"Год выпуска должен быть не ранее 1900"}).max(new Date().getFullYear()+1,{message:"Год выпуска не может быть в будущем"}),color:o.z.nativeEnum(d.OU,{errorMap:()=>({message:"Выберите цвет автомобиля"})}),licensePlate:o.z.string().min(1,{message:"Государственный регистрационный знак обязателен"}).max(15,{message:"Государственный регистрационный знак не должен превышать 15 символов"}),type:o.z.nativeEnum(d.$O,{errorMap:()=>({message:"Выберите тип автомобиля"})}),serviceClass:o.z.nativeEnum(d.mm,{errorMap:()=>({message:"Выберите класс обслуживания"})}),status:o.z.nativeEnum(d.zl,{errorMap:()=>({message:"Выберите статус автомобиля"})}),passengerCapacity:o.z.number({required_error:"Пассажировместимость обязательна",invalid_type_error:"Пассажировместимость должна быть числом"}).int({message:"Пассажировместимость должна быть целым числом"}).min(1,{message:"Пассажировместимость должна быть не менее 1"}).max(50,{message:"Пассажировместимость должна быть не более 50"}),features:o.z.array(o.z.nativeEnum(d.Lz)).min(1,{message:"Выберите хотя бы одну дополнительную опцию"}),id:o.z.string().uuid({message:"Некорректный идентификатор автомобиля"}),firstShiftDriver:o.z.string().optional(),secondShiftDriver:o.z.string().optional(),previousFirstShiftDriver:o.z.string().optional(),previousSecondShiftDriver:o.z.string().optional()}),o.z.boolean({errorMap:()=>({message:"Значение должно быть булевым (true/false)"})}),o.z.object({carId:o.z.string().uuid("ID автомобиля должен быть валидным UUID").min(1,"ID автомобиля обязателен"),driverIds:o.z.array(o.z.string().uuid("ID водителя должен быть валидным UUID")).min(1,"Необходимо указать хотя бы одного водителя").max(2,"Автомобилю можно назначить максимум 2 водителей").refine(e=>new Set(e).size===e.length,"ID водителей не должны повторяться"),isActive:o.z.boolean().default(!0)}),o.z.boolean({errorMap:()=>({message:"Значение должно быть булевым (true/false)"})}),o.z.object({carId:o.z.string().uuid("ID автомобиля должен быть валидным UUID").min(1,"ID автомобиля обязателен"),driverId:o.z.string().uuid("ID водителя должен быть валидным UUID").min(1,"ID водителя обязателен"),updates:o.z.object({startDate:o.z.string().datetime("Дата должна быть в формате ISO 8601").optional(),endDate:o.z.string().datetime("Дата должна быть в формате ISO 8601").optional(),isActive:o.z.boolean({errorMap:()=>({message:"IsActive должен быть булевым значением"})}).optional(),status:o.z.enum(["Active","Inactive","Suspended"],{errorMap:()=>({message:"Статус должен быть Active, Inactive или Suspended"})}).optional(),notes:o.z.string().max(500,"Примечания не должны превышать 500 символов").optional()}).partial()}),o.z.object({carId:o.z.string().uuid("ID автомобиля должен быть валидным UUID").min(1,"ID автомобиля обязателен")}),o.z.object({carId:o.z.string().uuid("ID автомобиля должен быть валидным UUID").optional()}),o.z.string().uuid("UUID должен быть валидным").min(1,"UUID обязателен"),o.z.string().uuid("UUID должен быть валидным").optional(),o.z.object({message:o.z.string(),activeCarId:o.z.string().uuid().nullable(),timestamp:o.z.string().datetime()}),o.z.object({code:o.z.string(),message:o.z.string(),details:o.z.string().optional()});var c=r(46171),u=r(51709);o.z.enum(["Unknown","Instant","Scheduled","Partner","Shuttle","Subscription"]);let p=o.z.enum(["Pending","Scheduled","InProgress","Completed","Cancelled","Expired"]);o.z.enum(["SearchingDriver","DriverAssigned","DriverHeading","DriverArrived","RideStarted","RideFinished","PaymentPending","PaymentCompleted","ReviewPending","CancelledByClient","CancelledByDriver","CancelledBySystem","CancelledByOperator"]);let m=o.z.object({serviceId:o.z.string({required_error:"Выберите услугу"}).uuid({message:"Выберите корректную услугу"}),quantity:o.z.number({required_error:"Укажите количество",invalid_type_error:"Количество должно быть числом"}).int({message:"Количество должно быть целым числом"}).min(1,{message:"Количество должно быть больше 0"}),notes:o.z.string().nullable().optional()}),h=o.z.object({customerId:o.z.string().uuid({message:"Выберите корректного клиента"}).nullable().optional(),firstName:o.z.string({required_error:"Имя пассажира обязательно"}).min(1,{message:"Имя пассажира обязательно"}),lastName:o.z.string().nullable(),isMainPassenger:o.z.boolean({required_error:"Укажите основного пассажира"})});o.z.object({tariffId:o.z.string({required_error:"Выберите тариф"}).uuid({message:"Выберите корректный тариф"}),routeId:o.z.string().uuid({message:"Некорректный маршрут"}).nullable().optional(),startLocationId:o.z.string().uuid({message:"Некорректная начальная точка"}).nullable().optional(),endLocationId:o.z.string().uuid({message:"Некорректная конечная точка"}).nullable().optional(),additionalStops:o.z.array(o.z.string().uuid({message:"Некорректная промежуточная точка"})).default([]),services:o.z.array(m).default([]),initialPrice:o.z.number({required_error:"Укажите стоимость"}).min(0,{message:"Стоимость не может быть отрицательной"}),passengers:o.z.array(h).min(1,{message:"Добавьте хотя бы одного пассажира"}),paymentId:o.z.string().min(1,{message:"Некорректный ID платежа"}).optional()}).refine(e=>{let t=null!==e.routeId&&void 0!==e.routeId&&""!==e.routeId,r=null!==e.startLocationId&&void 0!==e.startLocationId&&""!==e.startLocationId&&null!==e.endLocationId&&void 0!==e.endLocationId&&""!==e.endLocationId;return t||r},{message:"Укажите либо готовый маршрут, либо начальную и конечную точки",path:["routeId"]}).refine(e=>1===e.passengers.filter(e=>e.isMainPassenger).length,{message:"Должен быть ровно один главный пассажир",path:["passengers"]});let g=()=>o.z.string().transform(e=>{if(!e||e.includes("Z")||e.match(/[+-]\d{2}:\d{2}$/))return e;if(e.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)){let[t,r]=e.split("T"),[a,l,s]=t.split("-").map(Number),[i,n]=r.split(":").map(Number);return new Date(a,l-1,s,i,n,0,0).toISOString()}return e}),f=o.z.object({locationId:o.z.string({required_error:"Выберите локацию"}).uuid({message:"Выберите корректную локацию"}),arrivalTime:o.z.string().nullable().optional(),departureTime:o.z.string().nullable().optional()});o.z.object({tariffId:o.z.string({required_error:"Выберите тариф"}).uuid({message:"Выберите корректный тариф"}),routeType:o.z.enum(["template","manual"]).optional(),routeId:o.z.union([o.z.string().uuid({message:"Выберите корректный маршрут"}),o.z.null(),o.z.literal("").transform(()=>null)]).optional(),startLocationId:o.z.union([o.z.string().uuid({message:"Выберите корректную начальную точку"}),o.z.null(),o.z.literal("").transform(()=>null)]).optional(),endLocationId:o.z.union([o.z.string().uuid({message:"Выберите корректную конечную точку"}),o.z.null(),o.z.literal("").transform(()=>null)]).optional(),additionalStops:o.z.array(o.z.string().uuid({message:"Некорректная промежуточная точка"})).default([]),services:o.z.array(m).default([]),initialPrice:o.z.preprocess(e=>{if("string"==typeof e){let t=parseFloat(e);return isNaN(t)?e:t}return e},o.z.number({required_error:"Укажите стоимость",invalid_type_error:"Стоимость должна быть числом"}).min(0,{message:"Стоимость должна быть положительным числом"})),scheduledTime:g().refine(e=>e&&e.length>0,{message:"Выберите дату и время отправки"}),passengers:o.z.array(h).min(1,{message:"Добавьте хотя бы одного пассажира"}),clientType:o.z.enum(["existing","new"],{required_error:"Выберите тип клиента"}).default("new").optional(),customerId:o.z.string().optional(),firstName:o.z.string().default("Нет имени").optional(),lastName:o.z.string().optional(),driverId:o.z.string().optional(),carId:o.z.string().optional(),waypoints:o.z.array(f).optional().default([])}).refine(e=>{if("template"===e.routeType)return null!==e.routeId&&void 0!==e.routeId&&""!==e.routeId;if("manual"===e.routeType)return null!==e.startLocationId&&void 0!==e.startLocationId&&""!==e.startLocationId&&null!==e.endLocationId&&void 0!==e.endLocationId&&""!==e.endLocationId;let t=null!==e.routeId&&void 0!==e.routeId&&""!==e.routeId,r=null!==e.startLocationId&&void 0!==e.startLocationId&&""!==e.startLocationId&&null!==e.endLocationId&&void 0!==e.endLocationId&&""!==e.endLocationId;return t||r},{message:"Укажите либо готовый маршрут, либо начальную и конечную точки",path:["routeId"]});let b=o.z.object({locationId:o.z.string({required_error:"Выберите локацию"}).uuid({message:"Выберите корректную локацию"}),arrivalTime:g().nullable().optional(),departureTime:g().nullable().optional()}).refine(e=>!e.arrivalTime||!e.departureTime||new Date(e.departureTime)>new Date(e.arrivalTime),{message:"Время отправления должно быть позже времени прибытия",path:["departureTime"]});o.z.object({driverId:o.z.string({required_error:"Выберите водителя"}).uuid({message:"Выберите корректного водителя"}),carId:o.z.string({required_error:"Выберите автомобиль"}).uuid({message:"Выберите корректный автомобиль"}),waypoints:o.z.array(b).min(1,{message:"Добавьте хотя бы одну точку маршрута"}).default([])}).partial().extend({rideId:o.z.string({required_error:"ID поездки обязателен"}).uuid({message:"Некорректный ID поездки"})}),o.z.object({tariffId:o.z.string({required_error:"Выберите тариф"}).uuid({message:"Выберите корректный тариф"}),routeId:o.z.string().uuid({message:"Некорректный маршрут"}).nullable().optional(),startLocationId:o.z.string().uuid({message:"Некорректная начальная точка"}).nullable().optional(),endLocationId:o.z.string().uuid({message:"Некорректная конечная точка"}).nullable().optional(),additionalStops:o.z.array(o.z.string().uuid({message:"Некорректная промежуточная точка"})).default([]),services:o.z.array(m).default([]),initialPrice:o.z.number({required_error:"Укажите стоимость"}).min(0,{message:"Стоимость не может быть отрицательной"}),status:p}).refine(e=>!!e.routeId||e.startLocationId&&e.endLocationId,{message:"Необходимо указать либо шаблон маршрута, либо начальную и конечную точки",path:["routeId"]});let x=o.z.object({id:o.z.string().uuid({message:"Некорректный ID пассажира"}),customerId:o.z.string().uuid({message:"Выберите корректного клиента"}).nullable().optional(),firstName:o.z.string({required_error:"Имя пассажира обязательно"}).min(1,{message:"Имя пассажира обязательно"}).max(63,{message:"Имя не должно превышать 63 символа"}),lastName:o.z.string().max(63,{message:"Фамилия не должна превышать 63 символа"}).nullable().optional(),isMainPassenger:o.z.boolean({required_error:"Укажите основного пассажира"})});o.z.object({orderId:o.z.string().uuid({message:"Некорректный ID заказа"}),tariffId:o.z.string({required_error:"Выберите тариф"}).uuid({message:"Выберите корректный тариф"}),routeId:o.z.string().uuid({message:"Некорректный маршрут"}).nullable().optional(),startLocationId:o.z.string().uuid({message:"Некорректная начальная точка"}).nullable().optional(),endLocationId:o.z.string().uuid({message:"Некорректная конечная точка"}).nullable().optional(),additionalStops:o.z.array(o.z.string().uuid({message:"Некорректная промежуточная точка"})).default([]),services:o.z.array(m).default([]),initialPrice:o.z.number({required_error:"Укажите стоимость"}).min(0,{message:"Стоимость не может быть отрицательной"}),scheduledTime:g().refine(e=>e&&e.length>0,{message:"Укажите время"}),passengers:o.z.array(x).min(1,{message:"Добавьте хотя бы одного пассажира"}),status:p}).refine(e=>!!e.routeId||e.startLocationId&&e.endLocationId,{message:"Необходимо указать либо шаблон маршрута, либо начальную и конечную точки",path:["routeId"]}).refine(e=>1===e.passengers.filter(e=>e.isMainPassenger).length,{message:"Должен быть ровно один главный пассажир",path:["passengers"]});let v=o.z.object({name:o.z.string().min(1,{message:"Название услуги обязательно"}).max(127,{message:"Название услуги не должно превышать 127 символов"}).describe("Название услуги"),description:o.z.string().max(255,{message:"Описание услуги не должно превышать 255 символов"}).nullable().describe("Описание услуги (сом)"),price:o.z.number({required_error:"Цена услуги обязательна",invalid_type_error:"Цена услуги должна быть числом"}).positive({message:"Цена услуги должна быть положительным числом"}).describe("Цена услуги (сом)"),isQuantifiable:o.z.boolean({required_error:"Необходимо указать, можно ли указать количество",invalid_type_error:"Поле должно быть логическим значением"}).describe("Можно ли указать количество единиц услуги")}).extend({id:o.z.string().uuid({message:"Некорректный формат UUID"}).describe("Идентификатор услуги")});o.z.object({data:o.z.array(v).describe("Данные услуг"),totalCount:o.z.number().int().describe("Общее количество записей"),pageSize:o.z.number().int().describe("Размер страницы"),hasPrevious:o.z.boolean().describe("Есть ли предыдущая страница"),hasNext:o.z.boolean().describe("Есть ли следующая страница")});var y=r(58387),C=r(13510);c.bo,u.gz,y.El,o.z.object({}),c.e1,u.o6,y.B8,o.z.object({}),C.Tg,C.UG,C.MJ,C.Qu,C.i5,C.yD,C.AC,C.Rv,C.jz,C.JR,C.pp,C.Pe,u.mm;var T=r(96274);r(41101),r(22445),r(50947);r(58631),r(5777);var N=r(49989),w=r.n(N);function S(e){switch(e){case d.OU.Black:return"Черный";case d.OU.White:return"Белый";case d.OU.Silver:return"Серебристый";case d.OU.Gray:return"Серый";case d.OU.Red:return"Красный";case d.OU.Blue:return"Синий";case d.OU.Green:return"Зеленый";case d.OU.Yellow:return"Желтый";case d.OU.Brown:return"Коричневый";case d.OU.Orange:return"Оранжевый";case d.OU.Purple:return"Фиолетовый";case d.OU.Gold:return"Золотой";case d.OU.Other:return"Другой";default:return""}}function R(){return Object.values(d.OU).map(e=>({value:e,label:S(e)}))}function j(e){switch(e){case d.$O.Sedan:return"Седан";case d.$O.Hatchback:return"Хэтчбек";case d.$O.SUV:return"Внедорожник";case d.$O.Minivan:return"Минивэн";case d.$O.Coupe:return"Купе";case d.$O.Cargo:return"Грузовой";case d.$O.Pickup:return"Пикап";default:return""}}function A(){return Object.values(d.$O).map(e=>({value:e,label:j(e)}))}function P(e){switch(e){case d.zl.Available:return"Доступен";case d.zl.Maintenance:return"На техобслуживании";case d.zl.Repair:return"На ремонте";case d.zl.Other:return"Другое";default:return""}}function I(){return Object.values(d.zl).map(e=>({value:e,label:P(e)}))}let D=(0,i.lazy)(()=>Promise.resolve().then(r.bind(r,2065))),z={columns:[{id:"make",label:"Марка",accessor:"make",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"model",label:"Модель",accessor:"model",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"year",label:"Год",accessor:"year",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]}},{id:"licensePlate",label:"Гос. номер",accessor:"licensePlate",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"color",label:"Цвет",accessor:"color",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:R()},renderCell:e=>(0,a.jsx)("span",{children:S(e.color)})},{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:A()},renderCell:e=>(0,a.jsx)("span",{children:j(e.type)})},{id:"status",label:"Статус",accessor:"status",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:I()},renderCell:e=>{let t=P(e.status),r="";switch(e.status){case d.zl.Available:r="bg-green-100 text-green-800";break;case d.zl.Maintenance:r="bg-yellow-100 text-yellow-800";break;case d.zl.Repair:r="bg-red-100 text-red-800";break;default:r="bg-gray-100 text-gray-800"}return(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${r}`,children:t})}},{id:"drivers",label:"Водители",accessor:"drivers",sortable:!1,filterable:!1,renderCell:e=>{let t=e.drivers?.length||0,r="",l="";return 0===t?(r="bg-red-100 text-red-800",l="Нет водителей"):(r=2===t?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800",l=`${t}/2`),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${r}`,children:l})}},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(w(),{href:`/cars/${e.id}`,className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",title:"Редактировать автомобиль (включая водителей)",children:(0,a.jsx)(D,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"Make",label:"Марка",operator:"contains",operatorField:"MakeOp"},{field:"Model",label:"Модель",operator:"contains",operatorField:"ModelOp"},{field:"LicensePlate",label:"Гос. номер",operator:"contains",operatorField:"LicensePlateOp"}]},filtersConfig:{availableFilters:[{field:"Status",label:"Статус",type:"select",options:I().map(e=>({value:e.value,label:e.label}))},{field:"Type",label:"Тип транспорта",type:"select",options:A().map(e=>({value:e.value,label:e.label}))},{field:"Color",label:"Цвет",type:"select",options:R().map(e=>({value:e.value,label:e.label}))},{field:"Year",label:"Год выпуска",type:"number"}]}};var E=r(12431),k=r(77289),O=r(9019);r(22358);let L=(0,i.createContext)(null),M=()=>{let e=(0,i.useContext)(L);if(!e)throw Error("useTooltip must be used within TooltipProvider");return e},U=({content:e,delay:t=300})=>{let{showTooltip:r,hideTooltip:a,scale:l}=M(),s=(0,i.useRef)(null),n=(0,i.useRef)(null),o=(0,i.useRef)(`tooltip-${Math.random().toString(36).substr(2,9)}`),d=(0,i.useCallback)(()=>{n.current&&clearTimeout(n.current),n.current=setTimeout(()=>{if(s.current){let t=s.current.getBoundingClientRect(),a=1/l,i=(t.left+t.width/2)*a,n=t.top*a,d="top";n-100-12>=0?(n-=12,d="top"):t.bottom*a+100+12<=window.innerHeight?(n=t.bottom*a+12,d="bottom"):t.right*a+280+12<=window.innerWidth?(i=t.right*a+12,n=(t.top+t.height/2)*a,d="right"):(i=t.left*a-12,n=(t.top+t.height/2)*a,d="left"),("top"===d||"bottom"===d)&&(i-140<8?i=148:i+140>window.innerWidth-8&&(i=window.innerWidth-140-8)),r(o.current,e,i,n,d)}},t)},[e,t,r]);return{triggerProps:{ref:s,onMouseEnter:d,onMouseLeave:(0,i.useCallback)(()=>{n.current&&clearTimeout(n.current),a(o.current)},[a])}}};var $=r(36249),B=r(29457),V=r(3775);let q=({count:e,label:t,description:r,color:l,onClick:s,isActive:i})=>{let{triggerProps:n}=U({content:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"font-semibold",children:t}),(0,a.jsx)("div",{className:"text-gray-300",children:r}),(0,a.jsxs)("div",{className:"text-gray-400 text-xs",children:["Количество: ",e]}),(0,a.jsx)("div",{className:"text-blue-300 text-xs mt-1",children:"Нажмите для фильтрации"})]}),delay:200});return(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("button",{...n,onClick:s,className:`${l} rounded-full w-12 h-12 flex items-center justify-center border-2 ${i?"border-white shadow-lg ring-2 ring-white/50":"border-white/20"} shadow-lg transition-all duration-200 hover:scale-110 cursor-pointer hover:shadow-xl`,children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:e})})})},_=({filters:e,updateFilters:t,updateSort:r,onApplyTimeSort:l,onFilterWithSort:s})=>{let[n,o]=(0,i.useState)(null),[d,c]=(0,i.useState)(!0),[u,p]=(0,i.useState)(null),m=(0,i.useCallback)(async()=>{try{c(!0),p(null);let e=await $.x.getOrderStats();o(e)}catch(e){console.error("Ошибка при загрузке статистики заказов:",e),p("Не удалось загрузить статистику")}finally{c(!1)}},[]);(0,i.useEffect)(()=>{m()},[m]);let h=a=>{console.log("\uD83D\uDD25 КЛИК ПО КРУЖКУ! Статус:",a);let l=e.find(e=>"status"===e.columnId);if(l&&l.value===a)s?s(null):(r&&r({columnId:"scheduledTime",direction:"desc"},!0),t(e.filter(e=>"status"!==e.columnId)));else if(s)s(a);else{r&&r({columnId:"scheduledTime",direction:"desc"},!0);let l=e.filter(e=>"status"!==e.columnId);l.push({columnId:"status",operator:"equals",value:a}),t(l)}},g=(()=>{let t=e.find(e=>"status"===e.columnId);return t?t.value:null})();if(d)return(0,a.jsx)("div",{className:"flex items-center justify-center gap-4 p-4",children:(0,a.jsx)("div",{className:"animate-pulse flex gap-4",children:Array.from({length:6}).map((e,t)=>(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-300/50 rounded-full"},t))})});if(u||!n)return(0,a.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("div",{className:"text-red-500 text-sm",children:u||"Статистика недоступна"})})});let f=[{key:"pending",status:B.Re.Pending},{key:"scheduled",status:B.Re.Scheduled},{key:"inProgress",status:B.Re.InProgress},{key:"completed",status:B.Re.Completed},{key:"cancelled",status:B.Re.Cancelled},{key:"expired",status:B.Re.Expired}];return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center gap-6",children:f.map(({key:e,status:t})=>(0,a.jsx)(q,{status:t,count:n[e],label:(0,V.Vh)(t),description:(0,V.dF)(t),color:(0,V.mJ)(t),onClick:()=>h(t),isActive:g===t},t))}),l&&(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("button",{onClick:()=>{l&&l()},className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Показать свежие сверху"]})})]})};var X=r(66971);let F=(0,i.lazy)(()=>Promise.resolve().then(r.bind(r,2065))),Y={columns:[{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,O.mJ)((0,X.LP)(),[B.ZN.Unknown])},renderCell:e=>(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-[color:var(--color-primary-50)] text-[color:var(--color-primary)]",children:(0,X.vP)(e.type)})},{id:"status",label:"Статус",accessor:"status",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,X.ah)()},renderCell:e=>{let t=(0,X.TM)(e.status);return(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${t.bg} ${t.text} ${t.border} border`,children:(0,X.Vh)(e.status)})}},{id:"subStatus",label:"Подстатус",accessor:"subStatus",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,X.Z$)()},renderCell:e=>(0,a.jsx)("span",{className:"",children:e.subStatus?(0,X.l1)(e.subStatus):"-"})},{id:"initialPrice",label:"Начальная цена",accessor:"initialPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,a.jsxs)("span",{className:"font-medium",children:[e.initialPrice.toLocaleString("ru-RU")," сом"]})},{id:"finalPrice",label:"Итоговая цена",accessor:"finalPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual","isEmpty","isNotEmpty"]},renderCell:e=>(0,a.jsx)("span",{className:"font-medium",children:e.finalPrice?`${e.finalPrice.toLocaleString("ru-RU")} сом`:"-"})},{id:"scheduledTime",label:"Запланированное время",accessor:"scheduledTime",sortable:!0,filterable:!0,filterConfig:{fieldType:"date",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,a.jsx)("span",{children:e.scheduledTime?new Date(e.scheduledTime).toLocaleString("ru-RU"):"-"})},{id:"completedAt",label:"Дата завершения",accessor:"completedAt",sortable:!0,filterable:!0,filterConfig:{fieldType:"date",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual","isEmpty","isNotEmpty"]},renderCell:e=>(0,a.jsx)("span",{children:e.completedAt?new Date(e.completedAt).toLocaleString("ru-RU"):"-"})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>{let t=e.type.toLowerCase(),r=`/orders/${e.id}?type=${t}`;return(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(w(),{href:r,className:"p-2 text-[color:var(--color-primary)] hover:bg-white rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,a.jsx)(F,{size:20,className:"text-[color:var(--color-primary)]"})})})}}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"}]},filtersConfig:{availableFilters:[{field:"Type",label:"Тип заказа",type:"select",options:(0,O.mJ)((0,X.LP)(),[B.ZN.Unknown]).map(e=>({value:e.value,label:e.label}))},{field:"Status",label:"Статус заказа",type:"select",options:(0,X.ah)().map(e=>({value:e.value,label:e.label}))},{field:"SubStatus",label:"Подстатус заказа",type:"select",options:(0,X.Z$)().map(e=>({value:e.value,label:e.label}))},{field:"InitialPrice",label:"Начальная цена",type:"number"},{field:"FinalPrice",label:"Итоговая цена",type:"number"},{field:"CreatedAt",label:"Дата создания",type:"text"},{field:"CompletedAt",label:"Дата завершения",type:"text"}]},customHeaderComponent:({filters:e,updateFilters:t,updateSort:r,updateFiltersWithSort:l})=>(0,a.jsx)("div",{children:(0,a.jsx)(_,{filters:e,updateFilters:t,updateSort:r,onFilterWithSort:a=>{let s={columnId:"scheduledTime",direction:"desc"},i=e.filter(e=>"status"!==e.columnId);a&&i.push({columnId:"status",operator:"equals",value:a}),l?l(i,s):(r&&r(s,!0),t(i))}})})},H=(0,i.lazy)(()=>Promise.resolve().then(r.bind(r,2065))),G={columns:[{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,O.mJ)((0,X.LP)(),[B.ZN.Unknown])},renderCell:e=>(0,X.vP)(e.type)},{id:"status",label:"Статус",accessor:"status",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,X.ah)()},renderCell:e=>(0,X.Vh)(e.status)},{id:"initialPrice",label:"Стоимость",accessor:"initialPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>`${e.initialPrice.toLocaleString("ru-RU")} сом`},{id:"passengers",label:"Пассажиры",accessor:"passengers",sortable:!1,filterable:!1,renderCell:e=>{let t=e.passengers?.find(e=>e.isMainPassenger);return t?`${t.firstName} ${t.lastName||""}`.trim():"Не указан"}},{id:"scheduledTime",label:"Запланированное время",accessor:"scheduledTime",sortable:!0,filterable:!0,filterConfig:{fieldType:"date",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>e.scheduledTime?new Date(e.scheduledTime).toLocaleDateString("ru-RU"):"—"},{id:"completedAt",label:"Завершен",accessor:"completedAt",sortable:!0,filterable:!0,filterConfig:{fieldType:"date",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual","isEmpty","isNotEmpty"]},renderCell:e=>e.completedAt?new Date(e.completedAt).toLocaleDateString("ru-RU"):"—"},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(w(),{href:`/orders/${e.id}`,className:"p-2 text-[color:var(--color-primary)] hover:bg-white rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",onClick:()=>{let t=e.type.toLowerCase();console.log("\uD83D\uDD17 Клик по заказу в таблице:",{orderId:e.id,originalType:e.type,normalizedType:t}),(0,n.iY)(t),console.log("✅ Тип заказа установлен в состояние:",t)},children:(0,a.jsx)(H,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"}]},filtersConfig:{availableFilters:[{field:"Type",label:"Тип заказа",type:"select",options:(0,O.mJ)((0,X.LP)(),[B.ZN.Unknown]).map(e=>({value:e.value,label:e.label}))},{field:"Status",label:"Статус заказа",type:"select",options:(0,X.ah)().map(e=>({value:e.value,label:e.label}))},{field:"InitialPrice",label:"Стоимость",type:"number"},{field:"CreatedAt",label:"Дата создания",type:"text"},{field:"CompletedAt",label:"Дата завершения",type:"text"}]}},Q=(0,i.lazy)(()=>Promise.resolve().then(r.bind(r,2065))),W={columns:[{id:"name",label:"Название",accessor:"name",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"description",label:"Описание",accessor:"description",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","isEmpty","isNotEmpty"]},renderCell:e=>(0,a.jsx)("span",{children:e.description||(0,a.jsx)("span",{className:"text-[color:var(--color-neutral-400)]",children:"Не указано"})})},{id:"price",label:"Цена (сом)",accessor:"price",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,a.jsx)("span",{className:"font-medium",children:e.price.toLocaleString("ru-RU")})},{id:"isQuantifiable",label:"Количество",accessor:"isQuantifiable",sortable:!0,filterable:!0,filterConfig:{fieldType:"boolean"},renderCell:e=>(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${e.isQuantifiable?"bg-[color:var(--color-success-50)] text-[color:var(--color-success)]":""}`,children:e.isQuantifiable?"Можно указать":"Нельзя указать"})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(w(),{href:`/services/${e.id}`,className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,a.jsx)(Q,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"Name",label:"Название",operator:"contains",operatorField:"NameOp"},{field:"Description",label:"Описание",operator:"contains",operatorField:"DescriptionOp"}]},filtersConfig:{availableFilters:[{field:"Price",label:"Цена",type:"number"},{field:"IsQuantifiable",label:"Можно указать количество",type:"boolean"}]}};var K=r(13547),Z=r(44891),J=r(52646);J.X.Admin,J.X.Operator,J.X.Partner,J.X.Customer,J.X.Driver,J.X.Terminal,J.X.Unknown,J.X.Admin,J.X.Operator,J.X.Partner,J.X.Customer,J.X.Driver,J.X.Terminal,J.X.Unknown,J.X.Admin,Z.L,J.X.Operator,Z.L,J.X.Partner,Z.L,J.X.Customer,Z.L,J.X.Driver,Z.L,J.X.Terminal,Z.L,J.X.Unknown,Z.L,J.X.Admin,E.F,J.X.Operator,E.F,J.X.Partner,E.F,J.X.Customer,E.F,J.X.Driver,E.F,J.X.Terminal,E.F,J.X.Unknown,E.F,J.X.Admin,k.c,J.X.Operator,k.c,J.X.Partner,k.c,J.X.Customer,k.c,J.X.Driver,k.c,J.X.Terminal,k.c,J.X.Unknown,k.c,J.X.Admin,J.X.Operator,J.X.Partner,J.X.Customer,J.X.Driver,J.X.Terminal,J.X.Unknown,J.X.Admin,K.T,J.X.Operator,K.T,J.X.Partner,K.T,J.X.Customer,K.T,J.X.Driver,K.T,J.X.Terminal,K.T,J.X.Unknown,K.T,Z.L,E.F,k.c,K.T;var ee=r(85626);let et=(0,i.createContext)(null);var er=r(18602);let ea=()=>{let{setValue:e,watch:t}=(0,ee.xW)(),{formData:r}=function(){let e=function(){let e=(0,i.useContext)(et);if(!e)throw Error("useManageCollectionContext должен использоваться внутри ManageCollectionProvider");return e}();return{collectionName:e.collectionName,mode:e.mode,role:e.role,id:e.id,formData:e.formData,formConfig:e.formConfig,isLoading:e.isLoading,isSaving:e.isSaving,error:e.error}}(),a=t("firstShiftDriver"),l=t("secondShiftDriver"),[s,n]=(0,i.useState)({}),[o,d]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{(async()=>{if(r?.drivers&&Array.isArray(r.drivers)&&r.drivers.length>0&&!o){console.log("\uD83D\uDE97 Загружаем водителей автомобиля:",r.drivers),d(!0);let t=r.drivers[0],a=r.drivers[1],l=[];t&&l.push(er.Dv.getDriverById(t.driverId).then(r=>{n(e=>({...e,firstShift:r})),e("firstShiftDriver",t.driverId,{shouldValidate:!1}),e("previousFirstShiftDriver",t.driverId,{shouldValidate:!1})}).catch(e=>console.error("Ошибка загрузки водителя 1-й смены:",e))),a&&l.push(er.Dv.getDriverById(a.driverId).then(t=>{n(e=>({...e,secondShift:t})),e("secondShiftDriver",a.driverId,{shouldValidate:!1}),e("previousSecondShiftDriver",a.driverId,{shouldValidate:!1})}).catch(e=>console.error("Ошибка загрузки водителя 2-й смены:",e))),await Promise.all(l)}})()},[r?.id,r?.drivers,o,e]),(0,i.useEffect)(()=>{let e=async(e,t)=>{try{let r=await er.Dv.getDriverById(e);n(e=>({...e,[t]:r}))}catch(t){console.error(`Ошибка загрузки информации о водителе ${e}:`,t)}};a&&a!==s.firstShift?.id&&e(a,"firstShift"),l&&l!==s.secondShift?.id&&e(l,"secondShift")},[a,l,s.firstShift?.id,s.secondShift?.id]),{selectedDrivers:s,assignDriver:(t,r)=>{"first"===r?(t.id===l&&(e("secondShiftDriver","",{shouldValidate:!1}),n(e=>({...e,secondShift:void 0}))),e("firstShiftDriver",t.id,{shouldValidate:!1}),n(e=>({...e,firstShift:t}))):(t.id===a&&(e("firstShiftDriver","",{shouldValidate:!1}),n(e=>({...e,firstShift:void 0}))),e("secondShiftDriver",t.id,{shouldValidate:!1}),n(e=>({...e,secondShift:t})))},removeDriver:t=>{"first"===t?(e("firstShiftDriver","",{shouldValidate:!1}),n(e=>({...e,firstShift:void 0}))):(e("secondShiftDriver","",{shouldValidate:!1}),n(e=>({...e,secondShift:void 0})))},firstShiftDriverId:a,secondShiftDriverId:l}};var el=r(20882);let es=()=>{let[e,t]=(0,i.useState)(""),[r,a]=(0,i.useState)("fullName"),[l,s]=(0,i.useState)([]),[n,o]=(0,i.useState)(!1),[d,c]=(0,i.useState)(!1),[u,p]=(0,i.useState)({}),m=(0,i.useMemo)(()=>u,[JSON.stringify(u)]),h=(0,el.d)(e,300),g=(0,i.useCallback)(async()=>{if(!h||!(h.length<2)){o(!0);try{let e=new URLSearchParams;e.append("Size","20"),h&&h.length>=2&&("fullName"===r?(e.append("FullName",h),e.append("FullNameOp","Contains")):(e.append("PhoneNumber",h),e.append("PhoneNumberOp","Contains"))),e.append("Role","Driver"),Object.entries(m).forEach(([t,r])=>{r&&("DrivingExperience"===t?(e.append("DrivingExperience",r),e.append("DrivingExperienceOp","GreaterThanOrEqual")):e.append(t,r))});let t=await T.uE.get(`/User/Driver?${e.toString()}`);t.data&&Array.isArray(t.data.data)?s(t.data.data):Array.isArray(t.data)?s(t.data):s([])}catch(e){console.error("Ошибка поиска водителей:",e),s([])}finally{o(!1)}}},[h,r,m]);return(0,i.useEffect)(()=>{g()},[g]),(0,i.useEffect)(()=>{(async()=>{o(!0);try{let e=new URLSearchParams;e.append("Size","20"),e.append("Role","Driver");let t=await T.uE.get(`/User/Driver?${e.toString()}`);t.data&&Array.isArray(t.data.data)?s(t.data.data):Array.isArray(t.data)&&s(t.data)}catch(e){console.error("Ошибка загрузки начального списка водителей:",e)}finally{o(!1)}})()},[]),{driversList:l,isLoading:n,searchQuery:e,setSearchQuery:t,searchField:r,setSearchField:a,activeFilters:u,setActiveFilters:p,showFilters:d,setShowFilters:c}},ei=({value:e,onChange:t,options:r,placeholder:l="Выберите...",showMarginTop:s=!0})=>{let[n,o]=(0,i.useState)(!1),d=(0,i.useRef)(null),c=r.find(t=>t.value===e);return(0,i.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&o(!1)};if(n)return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[n]),(0,a.jsxs)("div",{className:"relative",ref:d,children:[(0,a.jsxs)("button",{type:"button",onClick:()=>o(!n),className:`w-full px-3 py-2 text-sm border rounded-md transition-all text-left flex items-center justify-between cursor-pointer backdrop-blur-md ${n?"bg-blue-500/20 border-blue-500/50":"border-white/10 bg-black/20 hover:bg-white/10"} ${s?"mt-2":""}`,children:[(0,a.jsx)("span",{children:c?.label||l}),(0,a.jsx)("span",{className:`text-xs transition-transform ${n?"rotate-180":""}`,children:"▼"})]}),n&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 mt-2 bg-black border border-white/10 rounded-md shadow-lg z-50 p-1",children:r.map((r,l)=>(0,a.jsx)("button",{type:"button",onClick:()=>{t(r.value),o(!1)},className:`w-full px-3 py-2 text-sm text-left hover:bg-white/10 rounded transition-all cursor-pointer ${e===r.value?"bg-blue-500/20 text-blue-400":""} ${l>0?"mt-1":""}`,children:r.label},r.value))})]})},en=({driver:e,shift:t,title:r,onRemove:l})=>(0,a.jsxs)("div",{className:"h-full p-4 flex flex-col overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:r}),e&&(0,a.jsx)("button",{type:"button",onClick:()=>l(t),className:"text-red-500 hover:text-red-700 text-sm cursor-pointer",title:"Удалить водителя",children:"✕"})]}),e?(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-blue-500/20 border border-blue-500/30 flex items-center justify-center text-blue-400 font-medium flex-shrink-0",children:e.fullName.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.fullName}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:e.phoneNumber||"Нет телефона"})]})]}),(0,a.jsxs)("div",{className:"mt-3 space-y-2 text-xs",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Опыт вождения:"}),(0,a.jsx)("span",{className:"text-gray-300",children:e.profile?.drivingExperience?`${e.profile.drivingExperience} лет`:"Нет данных"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Зоны работы:"}),(0,a.jsx)("span",{className:"text-gray-300",children:e.profile?.preferredWorkZones&&e.profile.preferredWorkZones.length>0?e.profile.preferredWorkZones.join(", "):"Нет данных"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Рейтинг:"}),(0,a.jsx)("span",{className:"text-gray-300",children:e.rating?`⭐ ${e.rating.toFixed(1)}`:"Нет данных"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Статус:"}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:`w-2 h-2 rounded-full ${e.online?"bg-green-500":"bg-gray-400"}`}),(0,a.jsx)("span",{className:"text-gray-300",children:e.online?"Онлайн":"Оффлайн"})]})]})]})]}):(0,a.jsxs)("div",{className:"flex-1 flex flex-col items-center justify-center text-gray-400",children:[(0,a.jsx)("div",{className:"w-16 h-16 mb-3 rounded-full bg-white/5"}),(0,a.jsx)("p",{className:"text-sm",children:"Водитель не назначен"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Выберите из списка справа"})]})]});var eo=r(66616);let ed={title:{create:"Создание нового автомобиля",edit:"Редактирование автомобиля"},groups:[{id:"main",title:"Основная информация",order:1,layout:{gridCols:2,gapX:8,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"photoGroup",type:eo.V.GROUP,label:"Фото автомобиля",description:"Загрузите фотографию автомобиля",layout:{flexDirection:"column",gapY:4,itemsAlignment:"center",className:"flex-1 flex flex-col justify-between gap-4"},fields:[{name:"photoUrl",type:eo.V.IMAGE,helperSmallText:"JPG, PNG до 5 МБ",helperBigText:"Загрузите фотографию автомобиля в формате JPG или PNG. Максимальный размер файла - 5 МБ.",className:"w-full h-full"}]},{name:"infoGroup",type:eo.V.GROUP,label:"Информация об автомобиле",description:"Основные данные автомобиля для идентификации в системе",layout:{flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"id",type:eo.V.HIDDEN,defaultValue:""},{name:"make",label:"Марка",type:eo.V.TEXT,placeholder:"Введите марку автомобиля",required:!0,helperSmallText:"Например: Toyota, BMW, Audi",helperBigText:"Марка автомобиля, указанная в техническом паспорте",className:"w-full",maxLength:63},{name:"model",label:"Модель",type:eo.V.TEXT,placeholder:"Введите модель автомобиля",required:!0,helperSmallText:"Например: Camry, X5, A6",helperBigText:"Модель автомобиля, указанная в техническом паспорте",className:"w-full",maxLength:63},{name:"year",label:"Год выпуска",type:eo.V.NUMBER,placeholder:"Введите год выпуска",required:!0,helperSmallText:"Год выпуска автомобиля",helperBigText:"Год выпуска автомобиля, указанный в техническом паспорте",className:"w-full",min:1900,max:new Date().getFullYear()+1},{name:"licensePlate",label:"Государственный номер",type:eo.V.TEXT,placeholder:"Введите гос. номер",required:!0,helperSmallText:"Регистрационный знак",helperBigText:"Государственный регистрационный знак автомобиля",className:"w-full",maxLength:15}]}]},{id:"details",title:"Характеристики",order:2,description:"Технические характеристики и параметры автомобиля",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"center"},fields:[{name:"color",label:"Цвет",type:eo.V.SELECT,placeholder:"Выберите цвет автомобиля",required:!0,options:R(),className:"w-full"},{name:"type",label:"Тип автомобиля",type:eo.V.SELECT,placeholder:"Выберите тип автомобиля",required:!0,options:A(),className:"w-full"},{name:"serviceClass",label:"Класс обслуживания",type:eo.V.SELECT,placeholder:"Выберите класс обслуживания",required:!0,options:[{value:"Economy",label:"Эконом"},{value:"Comfort",label:"Комфорт"},{value:"ComfortPlus",label:"Комфорт+"},{value:"Business",label:"Бизнес"},{value:"Premium",label:"Премиум"},{value:"Vip",label:"VIP"},{value:"Luxury",label:"Люкс"}],className:"w-full"},{name:"status",label:"Статус",type:eo.V.SELECT,placeholder:"Выберите статус автомобиля",required:!0,options:I(),className:"w-full"},{name:"passengerCapacity",label:"Пассажировместимость",type:eo.V.NUMBER,placeholder:"Введите количество мест",required:!0,helperSmallText:"Количество пассажирских мест",helperBigText:"Максимальное количество пассажиров, которое может перевозить автомобиль",className:"w-full",min:1,max:50}]},{id:"features",title:"Дополнительные опции",order:3,description:"Дополнительные опции и оборудование автомобиля",layout:{gridCols:2,gapX:4,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:3,itemsAlignment:"start"},fields:[{name:"features",label:"Дополнительные опции",type:eo.V.MULTISELECT,placeholder:"Выберите опции",required:!0,options:Object.values(d.Lz).map(e=>({value:e,label:function(e){switch(e){case d.Lz.AirConditioning:return"Кондиционер";case d.Lz.ClimateControl:return"Климат-контроль";case d.Lz.LeatherSeats:return"Кожаные сиденья";case d.Lz.HeatedSeats:return"Подогрев сидений";case d.Lz.Bluetooth:return"Bluetooth";case d.Lz.USBPort:return"USB-порт";case d.Lz.AuxInput:return"AUX-вход";case d.Lz.Navigation:return"Навигация";case d.Lz.BackupCamera:return"Камера заднего вида";case d.Lz.ParkingSensors:return"Парковочные датчики";case d.Lz.Sunroof:return"Люк";case d.Lz.PanoramicRoof:return"Панорамная крыша";case d.Lz.ThirdRowSeats:return"Третий ряд сидений";case d.Lz.ChildSeat:return"Детское кресло";case d.Lz.WheelchairAccess:return"Доступ для инвалидных колясок";case d.Lz.Wifi:return"Wi-Fi";case d.Lz.PremiumAudio:return"Премиальная аудиосистема";case d.Lz.AppleCarplay:return"Apple CarPlay";case d.Lz.AndroidAuto:return"Android Auto";case d.Lz.SmokingAllowed:return"Разрешено курение";case d.Lz.PetFriendly:return"Допускаются животные";case d.Lz.LuggageCarrier:return"Багажник на крыше";case d.Lz.BikeRack:return"Велосипедная стойка";default:return""}}(e)})),helperSmallText:"Выберите хотя бы одну опцию",helperBigText:"Дополнительные опции и оборудование, которыми оснащен автомобиль",className:"w-full"}]},{id:"drivers",title:"Водители автомобиля",order:4,description:"Система поддерживает максимум 2 водителей с фиксированными сменами",layout:{gridCols:1,gapX:4,gapY:4},customComponent:()=>{let{driversList:e,isLoading:t,searchQuery:r,setSearchQuery:l,searchField:s,setSearchField:n,activeFilters:o,setActiveFilters:d,showFilters:c,setShowFilters:u}=es(),{selectedDrivers:p,assignDriver:m,removeDriver:h,firstShiftDriverId:g,secondShiftDriverId:f}=ea();return(0,i.useEffect)(()=>{let e=e=>{let t=e.target;!c||t.closest(".filter-panel")||t.closest(".filter-button")||u(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[c,u]),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 h-[700px]",children:[(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Назначенные водители"}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col divide-y rounded-lg border shadow-sm backdrop-blur-md",children:[(0,a.jsx)(en,{driver:p.firstShift,shift:"first",title:"Первая смена (08:00 - 20:00)",onRemove:h}),(0,a.jsx)(en,{driver:p.secondShift,shift:"second",title:"Вторая смена (20:00 - 08:00)",onRemove:h})]})]}),(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Доступные водители"}),(0,a.jsxs)("div",{className:"mb-4 relative",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("div",{className:"w-[150px]",children:(0,a.jsx)(ei,{value:s,onChange:e=>n(e),options:[{value:"fullName",label:"По имени"},{value:"phoneNumber",label:"По телефону"}],placeholder:"Поиск по...",showMarginTop:!1})}),(0,a.jsx)("input",{type:"text",value:r,onChange:e=>l(e.target.value),placeholder:"fullName"===s?"Введите имя...":"Введите телефон...",className:"flex-1 px-3 py-2 text-sm border border-white/10 rounded-md bg-black/20 focus:outline-none focus:border-blue-500/50 hover:bg-white/10 transition-all backdrop-blur-md"}),(0,a.jsx)("button",{type:"button",onClick:()=>u(!c),className:`filter-button px-3 py-2 text-sm border rounded-md transition-all cursor-pointer backdrop-blur-md ${c?"bg-blue-500/20 border-blue-500/50 text-blue-400":"border-white/10 bg-black/20 hover:bg-white/10"}`,title:"Дополнительные фильтры",children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:["\uD83D\uDD27 Фильтры",Object.keys(o).length>0&&(0,a.jsx)("span",{className:"px-1.5 py-0.5 bg-blue-500/30 rounded-full text-xs",children:Object.keys(o).length})]})})]}),c&&(0,a.jsx)("div",{className:"filter-panel absolute right-0 top-full mt-2 w-[400px] p-4 bg-black/80 rounded-lg border border-white/10 z-50 shadow-2xl",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Дополнительные фильтры"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Класс автомобиля"}),(0,a.jsx)(ei,{value:o.VehicleServiceClass||"",onChange:e=>d(t=>({...t,VehicleServiceClass:e})),options:[{value:"",label:"Все"},{value:"Economy",label:"Эконом"},{value:"Comfort",label:"Комфорт"},{value:"ComfortPlus",label:"Комфорт+"},{value:"Business",label:"Бизнес"},{value:"Premium",label:"Премиум"},{value:"Vip",label:"VIP"},{value:"Luxury",label:"Люкс"}],placeholder:"Выберите класс"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Опыт вождения (лет)"}),(0,a.jsx)("input",{type:"number",value:o.DrivingExperience||"",onChange:e=>d(t=>({...t,DrivingExperience:e.target.value})),placeholder:"От",className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Статус"}),(0,a.jsx)(ei,{value:o.Online||"",onChange:e=>d(t=>({...t,Online:e})),options:[{value:"",label:"Все"},{value:"true",label:"Онлайн"},{value:"false",label:"Оффлайн"}],placeholder:"Выберите статус"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Языки"}),(0,a.jsx)(ei,{value:o.Languages||"",onChange:e=>d(t=>({...t,Languages:e})),options:[{value:"",label:"Все"},{value:"ru",label:"Русский"},{value:"kg",label:"Кыргызский"},{value:"en",label:"Английский"}],placeholder:"Выберите язык"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>d({}),className:"px-3 py-1 text-xs border border-white/10 rounded hover:bg-white/10 transition-all cursor-pointer",children:"Очистить"}),(0,a.jsx)("button",{type:"button",onClick:()=>u(!1),className:"px-3 py-1 text-xs bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded hover:bg-blue-500/30 transition-all cursor-pointer",children:"Применить"})]})]})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto space-y-2 border rounded-lg p-2 backdrop-blur-md",children:t?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"}),(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Поиск водителей..."})]}):e.length>0?e.map(e=>(0,a.jsx)("div",{className:"p-3 rounded-lg border hover:bg-white/20 transition-all cursor-pointer",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-400/20 border border-gray-400/30 flex items-center justify-center text-gray-300 text-sm font-medium",children:e.fullName.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.fullName}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:e.phoneNumber}),e.activeCar&&(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.activeCar.make," ",e.activeCar.model," • ",e.activeCar.licensePlate]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,a.jsx)("button",{type:"button",onClick:()=>m(e,"first"),disabled:e.id===g,className:`px-3 py-1 text-xs rounded transition-all ${e.id===g?"bg-gray-600/50 text-gray-400 cursor-not-allowed":"bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 border border-blue-500/30 cursor-pointer"}`,children:e.id===g?"✓ 1-я смена":"1-я смена"}),(0,a.jsx)("button",{type:"button",onClick:()=>m(e,"second"),disabled:e.id===f,className:`px-3 py-1 text-xs rounded transition-all ${e.id===f?"bg-gray-600/50 text-gray-400 cursor-not-allowed":"bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30 cursor-pointer"}`,children:e.id===f?"✓ 2-я смена":"2-я смена"})]})]})},e.id)):(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:(0,a.jsx)("p",{className:"text-sm",children:r?"Водители не найдены":"Начните вводить для поиска"})})})]})]})},fields:[{name:"firstShiftDriver",type:eo.V.HIDDEN},{name:"secondShiftDriver",type:eo.V.HIDDEN},{name:"previousFirstShiftDriver",type:eo.V.HIDDEN},{name:"previousSecondShiftDriver",type:eo.V.HIDDEN}]}],writeOperations:[{api:"delete",endpoint:"/Car/{formData.id}/drivers/{formData.previousFirstShiftDriver}",method:"remove",condition:"onTrue",description:"Удаление предыдущего водителя первой смены",order:1},{api:"delete",endpoint:"/Car/{formData.id}/drivers/{formData.previousSecondShiftDriver}",method:"remove",condition:"onTrue",description:"Удаление предыдущего водителя второй смены",order:2},{api:"post",endpoint:"/Car/{formData.id}/drivers/{formData.firstShiftDriver}",method:"assign",condition:"onTrue",bodyData:{active:!0},description:"Назначение нового водителя первой смены",order:3},{api:"post",endpoint:"/Car/{formData.id}/drivers/{formData.secondShiftDriver}",method:"assign",condition:"onTrue",bodyData:{active:!0},description:"Назначение нового водителя второй смены",order:4}]},ec={create:ed,update:ed};var eu=r(42399),ep=r(76575);eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.MULTISELECT,eo.R.ARRAY,eo.V.MULTISELECT,eo.R.ARRAY,eo.V.NUMBER,eo.R.NUMBER,eo.V.HIDDEN,eo.R.ARRAY;let em={title:{create:"Создание запланированного заказа",edit:"Редактирование запланированного заказа"},description:{create:"Заполните информацию для создания нового запланированного заказа",edit:"Редактирование информации о запланированном заказе"},groups:[{id:"client_selection",title:"Выбор клиента",description:"Выберите клиента для заказа",order:1,collapsed:!1,fields:[{name:"clientType",label:"Тип клиента",type:eo.V.BUTTON_GROUP,dataType:eo.R.STRING,required:!0,options:[{value:"existing",label:"Данные клиента"},{value:"new",label:"Новый клиент"}],defaultValue:"existing",helperText:"Выберите тип клиента"},{name:"customerId",label:"Клиент из базы",type:eo.V.SELECT,dataType:eo.R.STRING,required:!1,placeholder:"Выберите клиента",helperText:"Выберите существующего клиента из базы данных",dynamicReference:{api:"get",endpoint:T.QQ.USER.LIST},dependsOn:{field:"clientType",value:"existing"}},{name:"firstName",label:"Имя пассажира",type:eo.V.TEXT,dataType:eo.R.STRING,required:!0,maxLength:63,placeholder:"Введите имя",helperText:"Имя пассажира",dependsOn:{field:"clientType",value:"new"}},{name:"lastName",label:"Фамилия пассажира",type:eo.V.TEXT,dataType:eo.R.STRING,required:!1,maxLength:63,placeholder:"Введите фамилию",helperText:"Фамилия пассажира (необязательно)",dependsOn:{field:"clientType",value:"new"}},{name:"scheduledTime",label:"Дата и время отправки",type:eo.V.DATETIME,dataType:eo.R.STRING,required:!0,placeholder:"Выберите дату и время",helperText:"Запланированное время начала поездки (прошлое время недоступно)"}],layout:{gridCols:1,gapX:4,gapY:4}},{id:"pricing",title:"Тариф и стоимость",description:"Выберите тариф и укажите стоимость",order:2,fields:[{name:"tariffId",label:"Тариф",type:eo.V.SELECT,dataType:eo.R.STRING,required:!0,placeholder:"Выберите тариф",helperText:"Выберите тариф для расчета стоимости",dynamicReference:{api:"get",endpoint:T.QQ.TARIFF.LIST}},{name:"tariffInfo",label:"Информация о тарифе",type:eo.V.TEXT,dataType:eo.R.STRING,required:!1,readOnly:!0,placeholder:"Выберите тариф для просмотра информации",helperText:"Детали выбранного тарифа",dependsOn:{field:"tariffId",value:"",condition:"notEquals"}},{name:"initialPrice",label:"Предварительная стоимость",type:eo.V.NUMBER,dataType:eo.R.NUMBER,required:!0,placeholder:"Введите стоимость",helperText:"Предварительная расчетная стоимость в сомах",min:0}],layout:{gridCols:1,gapX:4,gapY:4}},{id:"status",title:"Статус заказа",description:"Установите статус заказа",order:3,fields:[{name:"status",label:"Статус заказа",type:eo.V.SELECT,dataType:eo.R.OBJECT,required:!0,placeholder:"Выберите статус",helperText:"Текущий статус заказа",options:(0,X.ah)(),defaultValue:"Pending"}],layout:{gridCols:1,gapX:4,gapY:4}},{id:"route_selection",title:"Выбор маршрута",description:"Выберите готовый маршрут или укажите локации вручную",order:4,fields:[{name:"routeType",label:"Тип маршрута",type:eo.V.BUTTON_GROUP,dataType:eo.R.STRING,required:!0,options:[{value:"template",label:"Готовый маршрут"},{value:"manual",label:"Ручной выбор"}],defaultValue:"template",helperText:"Выберите способ задания маршрута"},{name:"routeId",label:"Готовый маршрут",type:eo.V.SELECT,dataType:eo.R.STRING,required:!1,placeholder:"Выберите готовый маршрут",helperText:"Выберите готовый шаблон маршрута (только маршруты вашего партнера)",dynamicReference:{api:"get",endpoint:T.QQ.ROUTE.LIST,filters:{partnerId:"current_user_partner_id"}},dependsOn:{field:"routeType",value:"template"}},{name:"startLocationId",label:"Начальная точка",type:eo.V.SELECT,dataType:eo.R.STRING,required:!1,placeholder:"Выберите начальную точку",helperText:"Точка отправления",dynamicReference:{api:"get",endpoint:T.QQ.LOCATION.LIST},dependsOn:{field:"routeType",value:"manual"}},{name:"endLocationId",label:"Конечная точка",type:eo.V.SELECT,dataType:eo.R.STRING,required:!1,placeholder:"Выберите конечную точку",helperText:"Точка назначения",dynamicReference:{api:"get",endpoint:T.QQ.LOCATION.LIST},dependsOn:{field:"routeType",value:"manual"}},{name:"additionalStops",label:"Промежуточные остановки",type:eo.V.MULTISELECT,dataType:eo.R.ARRAY,required:!1,placeholder:"Выберите промежуточные остановки",helperText:"Дополнительные остановки по маршруту (работают с обоими типами маршрутов)",dynamicReference:{api:"get",endpoint:T.QQ.LOCATION.LIST}}],layout:{gridCols:1,gapX:4,gapY:4}},{id:"services",title:"Дополнительные услуги",description:"Выберите дополнительные услуги",order:5,fields:[{name:"services",label:"Услуги",type:eo.V.MULTISELECT,dataType:eo.R.ARRAY,required:!1,placeholder:"Выберите дополнительные услуги",helperText:"Дополнительные услуги, включенные в заказ",dynamicReference:{api:"get",endpoint:T.QQ.SERVICE.LIST}}],layout:{gridCols:1,gapX:4,gapY:4}}]},eh=(eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.MULTISELECT,eo.R.ARRAY,eo.V.MULTISELECT,eo.R.ARRAY,eo.V.NUMBER,eo.R.NUMBER,eo.V.SELECT,eo.R.OBJECT,(0,X.ah)(),eo.V.TEXT,eo.R.STRING,eo.V.NUMBER,eo.R.NUMBER,eo.V.TEXT,eo.R.STRING,eo.V.TEXT,eo.R.STRING,eo.V.TEXT,eo.R.STRING,eo.V.TEXT,eo.R.STRING,eo.V.CHECKBOX,eo.R.BOOLEAN,{create:em,update:em}),eg={title:{create:"Создание новой услуги",edit:"Редактирование услуги"},groups:[{id:"main",title:"Информация об услуге",order:1,layout:{gridCols:1,gapY:4,className:"h-full",flexDirection:"column",itemsAlignment:"stretch"},fields:[{name:"name",label:"Название",type:eo.V.TEXT,placeholder:"Введите название услуги",required:!0,helperSmallText:"Название услуги, которое будет отображаться клиентам",helperBigText:"Укажите понятное и краткое название услуги, которое будет отображаться в списке услуг и заказах",className:"w-full",maxLength:127},{name:"description",label:"Описание",type:eo.V.TEXTAREA,placeholder:"Введите описание услуги",helperSmallText:"Подробное описание услуги",helperBigText:"Укажите подробное описание услуги, включая все важные детали и условия предоставления",className:"w-full",maxLength:255}]},{id:"pricing",title:"Стоимость и параметры",order:2,description:"Настройки стоимости и параметров услуги",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"center"},fields:[{name:"price",label:"Цена (сом)",type:eo.V.NUMBER,placeholder:"Введите цену услуги",required:!0,helperSmallText:"Стоимость услуги в сомах",helperBigText:"Укажите стоимость услуги в сомах. Для услуг с переменной стоимостью укажите базовую цену.",className:"w-full",min:0,step:.01},{name:"isQuantifiable",label:"Количественная услуга",type:eo.V.CHECKBOX,helperSmallText:"Можно ли заказать несколько единиц услуги",helperBigText:"Отметьте, если услуга может быть заказана в нескольких экземплярах (например, дополнительный багаж, детское кресло и т.д.)",className:"w-full"}]}]},ef={create:eg,update:eg};var eb=r(25181),ex=r(71483),ev=r(1106);ex.m,eu._,ep.a,eb.b,ex.D[ev.Xh.Admin],ec.create,eu.D.create,ep.t.create,eh.create,ef.create,eb.I.create,ex.D[ev.Xh.Admin],ec.update,eu.D.update,ep.t.get,eh.update,ef.update,eb.I.update;let ey={},eC={profile:{label:"Мой профиль",description:"Просмотр и управление личным профилем пользователя",icon:"ProfileIcon"},users:{label:"Пользователи",description:"Управление пользователями системы (водители, клиенты, администраторы)",icon:"UsersIcon"},cars:{label:"Автомобили",description:"Управление автомобилями компании с их характеристиками",icon:"VehiclesIcon"},locations:{label:"Локации",description:"Управление локациями (адреса, координаты, типы мест)",icon:"ReferencesIcon"},services:{label:"Услуги",description:"Управление услугами и их ценами",icon:"TariffsIcon"},tariffs:{label:"Тарифы",description:"Управление тарифами на перевозки и услуги",icon:"TariffsIcon"},orders:{label:"Заказы",description:"Управление заказами клиентов на перевозки",icon:"OrdersIcon"},notifications:{label:"Уведомления",description:"Управление уведомлениями для пользователей",icon:"OrdersIcon"},settings:{label:"Настройки",description:"Настройки системы и пользовательского интерфейса",icon:"SettingsIcon"}},eT=new Proxy({},{get:(e,t)=>ey[t]||eC[t],ownKeys:()=>Object.keys(eC),has:(e,t)=>t in eC,getOwnPropertyDescriptor(e,t){if(t in eC)return{enumerable:!0,configurable:!0}}});class eN{getButtonText(e){return"edit"===e?"Просмотр":"Редактировать"}handleButtonClick(e){let t="edit"===e.mode?"/profile":"/profile/edit";e.router.push(t)}shouldShowButton(e){return"view"===e||"edit"===e}getAdditionalInfo(e){return null}}class ew{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.openModal(n.bY.SelectUserRoleModal):e.router.push("/users")}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){return null}}class eS{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.openModal(n.bY.SelectOrderTypeModal):e.router.push("/orders")}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){let{orderType:t,mode:r}=e;return"create"!==r&&"edit"!==r?null:"instant"===t?(0,a.jsx)("div",{className:"max-w-4xl h-full p-2 mx-auto hourglass-shape",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"⚡"}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Мгновенный заказ"}),(0,a.jsx)("p",{className:"text-sm text-white",children:"Выполнение как можно скорее"})]})]})}):"scheduled"===t?(0,a.jsx)("div",{className:"max-w-4xl h-full p-2 mx-auto hourglass-shape",children:(0,a.jsx)("div",{className:"flex items-center justify-center gap-2",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Запланированный заказ"}),(0,a.jsx)("p",{className:"text-sm text-white",children:"Выполнение в указанное время"})]})})}):null}}class eR{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.router.push(`/${e.collectionName}/create`):e.router.push(`/${e.collectionName}`)}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){return null}}class ej{static{this.strategies=new Map([["profile",new eN],["users",new ew],["orders",new eS]])}static getStrategy(e){return this.strategies.get(e)||new eR}}let eA=({collectionName:e,mode:t="view",orderType:r})=>{let i=(0,s.useRouter)(),o=(0,l.e3)(n.qf),d=eT[e];if(!d)return(0,a.jsxs)("div",{className:"px-8 py-2",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-red-600",children:"Коллекция не найдена"}),(0,a.jsxs)("p",{className:"text-sm text-red-600",children:['Коллекция "',e,'" не существует.']})]});let c=ej.getStrategy(e),u=c.shouldShowButton(t),p=u?c.getButtonText(t):"",m={mode:t,router:i,openModal:o,collectionName:e,orderType:r},h=c.getAdditionalInfo?.(m);return(0,a.jsxs)("div",{className:"flex justify-between items-start sticky top-0 z-10 border-b border-[color:var(--color-neutral-300)]",children:[(0,a.jsx)("div",{className:"h-full flex flex-row py-2 px-8 skew-right bg-[color:var(--color-brand)]/80",children:(0,a.jsxs)("div",{className:"h-full flex flex-col gap-1",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:d.label}),d.description&&(0,a.jsx)("p",{className:"text-sm text-white",children:d.description})]})}),(0,a.jsx)("div",{className:"h-full flex-1 flex-row",children:h}),u&&(0,a.jsx)("button",{onClick:()=>{c.handleButtonClick(m)},className:"btn-primary flex-shrink-0 flex items-center justify-center h-full skew-left transition-transform duration-200 min-w-sm !rounded-none !px-8",children:(0,a.jsx)("span",{className:"text-xl block font-medium",children:p})})]})}},18602:(e,t,r)=>{"use strict";r.d(t,{Dv:()=>i});var a=r(96274),l=r(50947);class s extends a.vA{async getUser(e){let t=await this.get(`/${e}`);return this.handleApiResult(t)}async getUserSafe(e){let t=await this.get(`/${e}`);return this.handleApiResultSafe(t)}async getUsers(e){let t=new URLSearchParams;e?.first!==void 0&&t.append("first",e.first.toString()),e?.before&&t.append("before",e.before),e?.after&&t.append("after",e.after),e?.last!==void 0&&t.append("last",e.last.toString()),e?.size!==void 0&&t.append("size",e.size.toString()),e?.search&&t.append("search",e.search),e?.role&&t.append("role",e.role);let r=t.toString()?`?${t.toString()}`:"",a=await this.get(r);return this.handleApiResult(a)}async createDriver(e){let t=await this.post("/drivers",e);return this.handleApiResult(t)}async createCustomer(e){let t=await this.post("/customers",e);return this.handleApiResult(t)}async createAdmin(e){let t=await this.post("/admins",e);return this.handleApiResult(t)}async createOperator(e){let t=await this.post("/operators",e);return this.handleApiResult(t)}async createPartner(e){let t=await this.post("/partners",e);return this.handleApiResult(t)}async createTerminal(e){let t=await this.post("/terminals",e);return this.handleApiResult(t)}async updateDriver(e,t){let r=await this.put(`/drivers/${e}`,t);return this.handleApiResult(r)}async updateCustomer(e,t){let r=await this.put(`/customers/${e}`,t);return this.handleApiResult(r)}async updateAdmin(e,t){let r=await this.put(`/admins/${e}`,t);return this.handleApiResult(r)}async updateOperator(e,t){let r=await this.put(`/operators/${e}`,t);return this.handleApiResult(r)}async updatePartner(e,t){let r=await this.put(`/partners/${e}`,t);return this.handleApiResult(r)}async updateTerminal(e,t){let r=await this.put(`/terminals/${e}`,t);return this.handleApiResult(r)}async deleteUser(e){let t=await this.delete(`/${e}`);this.handleApiResult(t)}async getCurrentDriver(){let e=await this.get("Driver/self");return this.handleApiResult(e)}async getSelfProfile(){let e=await this.get("self");return this.handleApiResult(e)}async getTerminalSelfProfile(){let e=await this.get("self/profile");return this.handleApiResult(e)}async getDrivers(e){let t=new URLSearchParams;e?.first!==void 0&&t.append("first",e.first.toString()),e?.before&&t.append("before",e.before),e?.after&&t.append("after",e.after),e?.last!==void 0&&t.append("last",e.last.toString()),e?.size!==void 0&&t.append("size",e.size.toString()),e?.search&&t.append("search",e.search),e?.online!==void 0&&t.append("online",e.online.toString());let r=t.toString()?`Driver?${t.toString()}`:"Driver",a=await this.get(r);return this.handleApiResult(a).data}async getDriversSafe(){let e=await this.get("Driver");return this.handleApiResultSafe(e)}async getDriverById(e){let t=await this.get(`Driver/${e}`);return this.handleApiResult(t)}async getDriverByIdSafe(e){let t=await this.get(`Driver/${e}`);return this.handleApiResultSafe(t)}async getUserByRole(e,t){let r;switch(t.toLowerCase()){case"admin":r=`Admin/${e}`;break;case"customer":r=`Customer/${e}`;break;case"driver":r=`Driver/${e}`;break;case"operator":r=`Operator/${e}`;break;case"partner":r=`Partner/${e}`;break;case"terminal":r=`Terminal/${e}`;break;default:r=`${e}`}let a=await this.get(r);return this.handleApiResult(a)}constructor(...e){super(...e),this.baseUrl=l.QQ.USER.LIST}}let i=new s},20864:(e,t,r)=>{"use strict";r.d(t,{E:()=>s});var a=r(60159),l=r(93570);function s(e=20){let[t,r]=(0,a.useState)([]),[i,n]=(0,a.useState)(!1),[o,d]=(0,a.useState)(!1),[c,u]=(0,a.useState)(null),[p,m]=(0,a.useState)(!0),[h,g]=(0,a.useState)(0),f=(0,a.useRef)(void 0),b=(0,a.useCallback)(async(t=!1)=>{try{console.log("\uD83D\uDD04 useNotifications.loadNotifications:",{append:t,lastCursor:f.current}),t?d(!0):(n(!0),f.current=void 0),u(null);let a=await l.l.getMyNotifications({size:e,after:t?f.current:void 0}),s=a.data||[];if(console.log("\uD83D\uDCCA useNotifications результат:",{newCount:s.length,hasNext:a.hasNext,totalCount:a.totalCount,append:t}),t?r(e=>[...e,...s]):r(s),g(a.totalCount||0),m(a.hasNext||!1),s.length>0){let e=s[s.length-1].id;console.log("\uD83C\uDFAF useNotifications обновляем курсор:",e),f.current=e}}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки уведомлений";console.error("❌ useNotifications ошибка:",t),u(e)}finally{n(!1),d(!1)}},[e]),x=(0,a.useCallback)(async()=>{if(!p||o||i)return void console.log("\uD83D\uDEAB useNotifications.loadMore заблокирован:",{hasMore:p,isLoadingMore:o,isLoading:i});console.log("\uD83D\uDCE5 useNotifications.loadMore: загружаем следующую страницу"),await b(!0)},[p,o,i,b]),v=(0,a.useCallback)(async()=>{console.log("\uD83D\uDD04 useNotifications.refresh"),await b(!1)},[b]),y=(0,a.useCallback)(async e=>{try{await l.l.markAsRead(e),r(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),console.log("✅ useNotifications.markAsRead успешно:",e)}catch(e){throw console.error("❌ useNotifications.markAsRead ошибка:",e),e}},[]),C=(0,a.useCallback)(async e=>{try{await l.l.deleteNotification(e),r(t=>t.filter(t=>t.id!==e)),console.log("\uD83D\uDDD1️ useNotifications.deleteNotification успешно:",e)}catch(e){throw console.error("❌ useNotifications.deleteNotification ошибка:",e),e}},[]),T=t.filter(e=>!e.isRead).length;return{notifications:t,isLoading:i,isLoadingMore:o,error:c,hasMore:p,totalCount:h,unreadCount:T,actions:{loadNotifications:b,loadMore:x,refresh:v,markAsRead:y,deleteNotification:C}}}},22445:(e,t,r)=>{"use strict";r(96274)},24700:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useHeaderDropdowns:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call useHeaderDropdowns() from the server but useHeaderDropdowns is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\header\\hooks\\useHeaderDropdowns.ts","useHeaderDropdowns")},24903:(e,t,r)=>{"use strict";r.d(t,{aN:()=>l,pl:()=>i,OW:()=>o,g0:()=>s,t0:()=>n,UH:()=>d});var a=r(60159);let l=(0,a.createContext)({driverId:null,driverProfile:null,activeRides:[],scheduledRides:[],scheduledRidesPagination:null,isInitialized:!1,isLoading:!0,isLoadingRides:!1,error:null,refreshDriverData:async()=>{},refreshRides:async()=>{}}),s=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useDriverData must be used within DriverDataProvider");return e},i=(0,a.createContext)({location:null,isTracking:!1,error:null,startTracking:()=>{},stopTracking:()=>{},getCurrentPosition:async()=>null}),n=()=>{let e=(0,a.useContext)(i);if(!e)throw Error("useDriverLocation must be used within DriverLocationProvider");return e},o=(0,a.createContext)(null),d=()=>{let e=(0,a.useContext)(o);if(!e)throw Error("useTerminalData должен использоваться внутри TerminalDataProvider");return e}},25181:(e,t,r)=>{"use strict";r.d(t,{I:()=>l,b:()=>s});var a=r(42419);let l={base:a.M,create:a.M,update:a.M},s=a.M},26134:(e,t,r)=>{"use strict";r.d(t,{N:()=>l,o:()=>s});var a=r(60159);let l=(0,a.createContext)(null),s=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useDriverQueueContext must be used within a DriverQueueProvider");return e}},26488:(e,t,r)=>{"use strict";r.d(t,{TerminalLocationsProvider:()=>m});var a=r(13486),l=r(2984),s=r(60159),i=r(91499);r(4191),r(4392),r(37829),r(42399),r(12431),r(89337),r(39314),r(96274),r(46171),r(95124);var n=r(79645);let o={bishkek:"Бишкек",chui:"Чуй",naryn:"Нарын",osh:"Ош",batken:"Баткен",talas:"Талас","jalal-abad":"Жалал-Абад","issyk-kul":"Иссык-Куль"};var d=r(78891),c=r(24903);let u={"bishkek-city":"Бишкек",tokmok:"Токмок",kant:"Кант","kara-balta":"Кара-Балта",shopokov:"Шопоков","osh-city":"Ош",uzgen:"Узген","kara-suu":"Кара-Суу","jalal-abad-city":"Жалал-Абад","tash-kumyr":"Таш-Кумыр",kerben:"Кербен",karakol:"Каракол",balykchy:"Балыкчы","cholpon-ata":"Чолпон-Ата","naryn-city":"Нарын","talas-city":"Талас","batken-city":"Баткен",sulyukta:"Сулюкта"},p={"birinchi-may":"Биринчи Май",leninsky:"Ленинский",oktyabrsky:"Октябрьский",sverdlovsky:"Свердловский","tokmok-central":"Центральный","tokmok-north":"Северный","tokmok-south":"Южный","kant-central":"Центральный","kant-industrial":"Промышленный","kara-balta-central":"Центральный","kara-balta-east":"Восточный","shopokov-central":"Центральный","osh-central":"Центральный","osh-north":"Северный","osh-south":"Южный","osh-east":"Восточный","uzgen-central":"Центральный","uzgen-old":"Старый город","kara-suu-central":"Центральный","kara-suu-bazar":"Базарный","jalal-abad-central":"Центральный","jalal-abad-north":"Северный","jalal-abad-south":"Южный","tash-kumyr-central":"Центральный","tash-kumyr-mining":"Горняцкий","kerben-central":"Центральный","karakol-central":"Центральный","karakol-north":"Северный","karakol-south":"Южный","balykchy-central":"Центральный","balykchy-port":"Портовый","cholpon-ata-central":"Центральный","cholpon-ata-resort":"Курортный","naryn-central":"Центральный","naryn-east":"Восточный","talas-central":"Центральный","talas-north":"Северный","batken-central":"Центральный","sulyukta-central":"Центральный","sulyukta-mining":"Горняцкий"},m=({children:e})=>{let t=(0,l.useRouter)(),r=(0,l.usePathname)(),{terminalLocation:m,isLoading:h}=(0,c.UH)(),{economyTariff:g,isLoading:f}=(0,d.Y)(),[b,x]=(0,s.useState)([]),[v,y]=(0,s.useState)([]),[C,T]=(0,s.useState)(!1),[N,w]=(0,s.useState)(null),[S,R]=(0,s.useState)(null),[j,A]=(0,s.useState)(null),[P,I]=(0,s.useState)(null);(0,s.useEffect)(()=>{if("/"===r||""===r)return void I(null);if(!h&&!f){if(!m)return void t.push("/");g||t.push("/")}},[m,g,h,f,r,t]);let D=(0,s.useCallback)(async e=>{if(C)return void console.log("⚠️ Пропускаем запрос - уже идет загрузка локаций");T(!0),w(null),e&&(R(e),(e.regionSlug||e.city)&&!e.address&&!e.searchQuery&&(A(e),I(e.regionSlug||null)));try{let t={size:10,isActive:e?.isActive??!0,...e?.customFilters};if(e?.popularOnly&&(t.popular1=!0),e?.type&&(t.type=e.type),e?.customFilters?.locationTypes&&Array.isArray(e.customFilters.locationTypes)&&e.customFilters.locationTypes.length>0&&(t.type=e.customFilters.locationTypes,console.log("\uD83C\uDFF7️ Фильтр по типам локации:",e.customFilters.locationTypes)),e?.name&&(t.name=e.name),e?.address&&(t.address=e.address),e?.district&&(t.district=e.district),e?.city&&(t.city=e.city),e?.country&&(t.country=e.country),e?.region&&(t.region=e.region),e?.latitude!==void 0&&(t.latitude=e.latitude),e?.longitude!==void 0&&(t.longitude=e.longitude),e?.popular1!==void 0&&(t.popular1=e.popular1),e?.popular2!==void 0&&(t.popular2=e.popular2),e?.regionSlug&&!e?.city){let r=o[e.regionSlug]||e.regionSlug;t.city=r,console.log("\uD83C\uDFE2 Добавляем фильтр по городу:",e.regionSlug,"→",r)}if(e?.customFilters?.regionSlugs&&Array.isArray(e.customFilters.regionSlugs)){let r=e.customFilters.regionSlugs.map(e=>o[e]||e);r.length>0&&(t.city=r,console.log("\uD83C\uDFE2 Фильтр по регионам:",e.customFilters.regionSlugs,"→",r))}if(e?.customFilters?.citySlugs&&Array.isArray(e.customFilters.citySlugs)&&e.customFilters.citySlugs.length>0){let r=e.customFilters.citySlugs.map(e=>u[e]||e);t.city=r,console.log("\uD83C\uDFD9️ Фильтр по городам:",e.customFilters.citySlugs,"→",r)}if(e?.customFilters?.districtSlugs&&Array.isArray(e.customFilters.districtSlugs)&&e.customFilters.districtSlugs.length>0){let r=e.customFilters.districtSlugs.map(e=>p[e]||e);t.district=r,console.log("\uD83C\uDFD8️ Фильтр по районам:",e.customFilters.districtSlugs,"→",r)}e?.searchQuery&&e.searchQuery.trim()&&!e?.address&&(t.address=e.searchQuery,console.log("\uD83D\uDD0D Добавляем поиск по адресу:",e.searchQuery)),console.log("\uD83C\uDF10 API запрос с параметрами:",t);let r=(await i.Ny.getAll(t)).data||[];y(r);let a=[];(e?.popularOnly||e?.popular1)&&a.push("популярных"),e?.type&&a.push(`типа ${e.type}`),(e?.regionSlug||e?.city)&&a.push(`в городе ${e?.city||e?.regionSlug}`),(e?.searchQuery||e?.address)&&a.push(`по запросу "${e?.address||e?.searchQuery}"`),e?.country&&a.push(`в стране ${e.country}`),e?.region&&a.push(`в регионе ${e.region}`);let l=a.length>0?a.join(", "):"всех";console.log(`🏢 Терминал: загружено ${l} локаций:`,r.length)}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки локаций";console.error("❌ Ошибка загрузки локаций:",t),w(e)}finally{T(!1)}},[]),z=(0,s.useCallback)(async e=>{if(!e.trim())return v;let t=e.toLowerCase();return v.filter(e=>e.name?.toLowerCase().includes(t)||e.address?.toLowerCase().includes(t))},[v]),E=(0,s.useCallback)(e=>v.find(t=>t.id===e),[v]),k=(0,s.useCallback)(async e=>{try{let t=E(e);if(t)return t;return await i.Ny.getById(e)}catch(e){return console.error("❌ Ошибка загрузки локации по ID:",e),null}},[E]),O=(0,s.useCallback)(e=>{x(t=>t.some(t=>t.id===e.id)?t:[...t,e])},[]),L=(0,s.useCallback)(e=>{x(t=>t.filter(t=>t.id!==e))},[]),M=(0,s.useCallback)(()=>{x([])},[]),U=(0,s.useCallback)(async()=>{S&&await D(S)},[S]),$=(0,s.useCallback)(async()=>{j&&await D(j)},[j]);return(0,a.jsx)(n.s.Provider,{value:{selectedLocations:b,addLocation:O,removeLocation:L,clearLocations:M,allLocations:v,isLoadingLocations:C,locationsError:N,currentRegionSlug:P,searchLocations:z,getLocationById:E,fetchLocationById:k,loadLocations:D,reloadLastLocations:U,reloadRegionLocations:$},children:e})}},28412:(e,t,r)=>{"use strict";r.d(t,{C:()=>l});var a=r(72235);let l=e=>{let{scheduledRides:t,scheduledRidesPagination:r,isLoadingRides:l,refreshRides:s,error:i}=(0,a.g0)();return{data:t,pagination:r,isLoading:l,error:i,refetch:s}}},28937:(e,t,r)=>{"use strict";r.d(t,{CollectionHeader:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call CollectionHeader() from the server but CollectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\header\\ui\\CollectionHeader.tsx","CollectionHeader")},29457:(e,t,r)=>{"use strict";r.d(t,{Re:()=>l,lk:()=>s,ZN:()=>a,VQ:()=>i});var a=function(e){return e.Unknown="Unknown",e.Instant="Instant",e.Scheduled="Scheduled",e.Partner="Partner",e.Shuttle="Shuttle",e.Subscription="Subscription",e}({}),l=function(e){return e.Pending="Pending",e.Scheduled="Scheduled",e.InProgress="InProgress",e.Completed="Completed",e.Cancelled="Cancelled",e.Expired="Expired",e}({}),s=function(e){return e.SearchingDriver="SearchingDriver",e.DriverAssigned="DriverAssigned",e.DriverHeading="DriverHeading",e.DriverArrived="DriverArrived",e.RideStarted="RideStarted",e.RideFinished="RideFinished",e.PaymentPending="PaymentPending",e.PaymentCompleted="PaymentCompleted",e.ReviewPending="ReviewPending",e.CancelledByClient="CancelledByClient",e.CancelledByDriver="CancelledByDriver",e.CancelledBySystem="CancelledBySystem",e.CancelledByOperator="CancelledByOperator",e}({}),i=function(e){return e.Requested="Requested",e.Searching="Searching",e.Accepted="Accepted",e.Arrived="Arrived",e.InProgress="InProgress",e.Completed="Completed",e.Cancelled="Cancelled",e}({})},29971:(e,t,r)=>{"use strict";r.d(t,{DriverSidebar:()=>h});var a=r(13486),l=r(2984),s=r(60159),i=r(36893),n=r(31071),o=r(26134),d=r(24903),c=r(95816),u=r(52025),p=r(59152),m=r(82092);let h=({className:e=""})=>{let t=(0,l.useRouter)(),{isInitialized:r,isLoading:h}=(0,d.g0)(),{isActive:g}=function(e={}){let[t,r]=(0,s.useState)(!1),[a,l]=(0,s.useState)(!1),[i,n]=(0,s.useState)(null),[o,d]=(0,s.useState)(null);(0,u.k)();let c=(0,s.useCallback)(async()=>{l(!1),n(null)},[]),p=(0,s.useCallback)(async e=>{r(e),d(new Date().toISOString()),n(null)},[]),m=(0,s.useCallback)(async()=>{await p(!t)},[t,p]);return{isActive:t,isLoading:a,error:i,lastActiveAt:o,toggleActivity:m,setActive:p,refresh:(0,s.useCallback)(async()=>{await c()},[c])}}({autoFetch:!0,syncWithSignalR:!0}),{hasUnreadNotifications:f}=(0,n.M)(),{isInQueue:b,position:x}=(0,o.o)(),{hasActiveRide:v,isLoading:y,forceUpdateActiveRide:C}=(0,c.c7)(),{newScheduledRidesCount:T,totalScheduledRidesCount:N,resetNewRidesCount:w}=(0,m.UF)(),S=(0,s.useRef)(!1),[R,j]=(0,s.useState)(!1),A=(0,s.useRef)(null),P=(0,s.useRef)(0);(0,s.useLayoutEffect)(()=>{r&&!h&&(S.current||(S.current=!0,(async()=>{await C(),j(!0)})()))},[C,r,h]),(0,s.useLayoutEffect)(()=>(v&&b?!A.current&&P.current<3?(P.current+=1,console.log(`⚠️ Конфликт: есть активная поездка, но всё ещё в очереди. Попытка ${P.current}/3...`),A.current=setTimeout(async()=>{try{await C(),setTimeout(async()=>{v&&b&&console.log("\uD83D\uDEAA Конфликт не разрешился - принудительно выходим из очереди")},500)}catch(e){console.error("❌ Ошибка при разрешении конфликта:",e)}finally{A.current=null}},1e3)):P.current>=3&&console.log("\uD83D\uDED1 Превышен лимит попыток разрешения конфликта. Останавливаем попытки."):(P.current=0,A.current&&(clearTimeout(A.current),A.current=null)),()=>{A.current&&(clearTimeout(A.current),A.current=null)}),[v,b,x,C,3]);let I=(0,s.useMemo)(()=>{let e=(0,p.az)(i.Q);return(0,p.Y6)(e,f,T>0)},[f,T]),D=(0,m.Wj)(i.Q),z=e=>{"scheduled-orders"===e.id&&w(),e.path&&t.push(e.path)};return(0,a.jsxs)("aside",{className:`p-2 rounded-2xl w-90 flex flex-col gap-2 overflow-auto ${e}`,children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 flex flex-col gap-2 justify-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Статус активности"}),g&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`
              w-3 h-3 rounded-full
              ${!R||y?"bg-gray-300 animate-pulse":v?"bg-orange-500":b?"bg-blue-500":"bg-gray-400"}
            `}),(0,a.jsx)("span",{className:"text-xs",children:!R||y?"Загрузка...":v?"В активной поездке":b?x?`В очереди #${x}`:"В очереди":"Не в очереди"})]})]}),(0,a.jsx)("nav",{className:"flex-1 overflow-y-auto",children:(0,a.jsx)("ul",{className:"flex flex-col gap-2",children:I.map(e=>{let t=D===e.id,r="scheduled-orders"===e.id;return(0,a.jsx)("li",{children:(0,a.jsxs)("button",{onClick:()=>z(e),className:`
                    flex items-center justify-between w-full p-3 rounded-lg text-left transition-colors bg-[#D9D9D926] h-[120px] cursor-pointer
                    ${t?"bg-blue-50 text-blue-700 border border-blue-200":" hover:bg-gray-50"}
                  `,children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.title}),r&&N>0&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:["Всего: ",N]}),T>0&&(0,a.jsxs)("span",{className:"text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full",children:["+",T," новых"]})]})]}),e.hasNotifications&&(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full"})]})},e.id)})})})]})}},31071:(e,t,r)=>{"use strict";r.d(t,{V:()=>l,M:()=>s});var a=r(60159);let l=(0,a.createContext)(null),s=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useNotificationContext must be used within NotificationProvider");return e}},31496:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>f});var a=r(13486),l=r(49933),s=r(49989),i=r.n(s),n=r(60159),o=r(36893),d=r(52646),c=r(59152),u=r(82092),p=r(18602);let m=({collapsed:e})=>{let[t,r]=(0,n.useState)([]),[l,s]=(0,n.useState)(!0),[i,o]=(0,n.useState)(null),d=(0,n.useCallback)(async()=>{try{console.log("\uD83D\uDD04 Загружаем онлайн водителей..."),s(!0),o(null);let e=await p.Dv.getDrivers({size:50,first:!0,online:!0});console.log("\uD83D\uDFE2 Онлайн водители с сервера:",e),console.log("\uD83D\uDCCA Количество онлайн водителей:",e.length),r(e)}catch(e){console.error("❌ Ошибка загрузки водителей:",e),o("Ошибка загрузки водителей")}finally{s(!1)}},[]);return((0,n.useEffect)(()=>{d()},[d]),(0,n.useEffect)(()=>{let e=setInterval(d,3e4);return()=>clearInterval(e)},[d]),console.log("\uD83C\uDFA8 OnlineDriversList рендер:",{collapsed:e,driversCount:t.length,isLoading:l,error:i}),e)?(console.log("\uD83D\uDCE6 Сайдбар свернут - скрываем водителей"),null):(0,a.jsxs)("div",{className:"px-2 py-4 h-full",children:[(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsxs)("h3",{className:"text-sm font-medium text-gray-300",children:["Водители (",t.length,")"]})}),l?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,a.jsx)("span",{className:"ml-2 text-xs text-gray-400",children:"Загрузка..."})]}):i?(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsx)("p",{className:"text-xs text-red-400",children:i}),(0,a.jsx)("button",{onClick:d,className:"mt-1 text-xs text-blue-400 hover:text-blue-300 transition-colors",children:"Повторить"})]}):0===t.length?(0,a.jsx)("div",{className:"py-2",children:(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Нет онлайн водителей"})}):(0,a.jsx)("div",{className:"space-y-2 overflow-y-auto h-full pb-4 px-1",children:t.map(e=>(0,a.jsxs)("div",{className:"p-2 rounded-md bg-gray-800/50 hover:bg-gray-700/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-white truncate",children:e.fullName}),(0,a.jsx)("p",{className:"text-xs text-gray-400 truncate",children:e.phoneNumber||"Нет телефона"})]}),e.activeCar?.licensePlate&&(0,a.jsx)("div",{className:"ml-2 flex-shrink-0",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-900/50 text-blue-300",children:e.activeCar.licensePlate})})]}),(0,a.jsxs)("div",{className:"flex flex-row justify-between items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full mr-2 ${e.online?"bg-green-500":"bg-gray-500"}`}),(0,a.jsx)("span",{className:`text-xs ${e.online?"text-green-400":"text-gray-400"}`,children:e.online?"Онлайн":"Оффлайн"})]}),void 0!==e.isAvailable&&(0,a.jsx)("span",{className:`text-xs px-2 py-0.5 rounded ${e.isAvailable?"bg-green-900/50 text-green-300":"bg-yellow-900/50 text-yellow-300"}`,children:e.isAvailable?"Доступен":"Занят"})]}),e.rating&&(0,a.jsx)("div",{className:"flex items-center mt-1",children:(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:["⭐ ",e.rating.toFixed(1)]})})]})]},e.id))})]})},h=(0,n.lazy)(()=>Promise.resolve().then(r.bind(r,91325))),g=(0,n.lazy)(()=>Promise.resolve().then(r.bind(r,72244))),f=({userRole:e,initialCollapsed:t=!1,overlayColor:r="#07080A",overlayOpacity:s=.7})=>{let[p,f]=(0,n.useState)(t),b=(0,n.useMemo)(()=>e?(0,c.$r)(o.h,e):[],[e]),x=(0,u.Wj)(b),v=(0,n.useMemo)(()=>e===d.X.Admin||e===d.X.Operator,[e]),y=async()=>{let e=!p;f(e);try{await fetch("/api/sidebar",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({collapsed:e})})}catch(t){console.error("Failed to save sidebar state:",t),f(!e)}};return(0,a.jsxs)("aside",{className:`h-full transition-width duration-300 ease-in-out flex flex-col pt-4 px-4 gap-4 ${p?"w-24":"w-84"}`,style:{transitionProperty:"width"},children:[(0,a.jsxs)("div",{className:"relative flex items-center justify-start h-12 px-2",children:[(0,a.jsx)("div",{className:"flex items-center w-full h-full",children:(0,a.jsx)(i(),{href:"/",className:"relative w-full h-full overflow-hidden transition-all duration-300 ease-in-out cursor-pointer",children:(0,a.jsx)(l.default,{src:"/logo/LogoBig.png",alt:"Compass Transfer",fill:!0,className:"object-contain",sizes:p?"96px":"336px"})})}),(0,a.jsx)("button",{onClick:y,className:"absolute right-[-15] inline-flex items-center rounded-sm border border-[color:var(--color-neutral-300)] p-1 transition-colors duration-300 cursor-pointer",children:p?(0,a.jsx)(n.Suspense,{fallback:(0,a.jsx)("div",{className:"h-5 w-5"}),children:(0,a.jsx)(h,{className:"h-5 w-5"})}):(0,a.jsx)(n.Suspense,{fallback:(0,a.jsx)("div",{className:"h-5 w-5"}),children:(0,a.jsx)(g,{className:"h-5 w-5"})})})]}),(0,a.jsx)("nav",{className:"flex-2 overflow-y-auto py-2 rounded-xl outline outline-offset-[-1px]",style:{backgroundColor:`${r}${Math.round(255*s).toString(16).padStart(2,"0")}`,outlineColor:`${r}${Math.round(255*Math.min(s+.1,1)).toString(16).padStart(2,"0")}`},children:(0,a.jsx)("ul",{className:"flex flex-col gap-1 px-2 py-8",children:b.map(e=>(0,a.jsx)("li",{children:(0,a.jsxs)(i(),{href:e.path,className:`flex items-center p-3 rounded-md transition-colors duration-150 ${x===e.id?"bg-[color:var(--color-brand)]/80":"hover:bg-gray-50/20"}`,style:{willChange:"background-color, color"},children:[(0,a.jsx)("span",{className:"text-current",children:e.icon}),(0,a.jsx)("span",{className:`transition-width duration-300 ease-in-out overflow-hidden whitespace-nowrap ${p?"w-0 opacity-0":"w-auto opacity-100 pl-3"}`,style:{transitionProperty:"width, opacity",willChange:"width, opacity"},children:e.title})]})},e.id))})}),v&&(0,a.jsx)("div",{className:"flex-1 overflow-hidden rounded-t-xl outline outline-offset-[-1px]",style:{backgroundColor:`${r}${Math.round(255*s).toString(16).padStart(2,"0")}`,outlineColor:`${r}${Math.round(255*Math.min(s+.1,1)).toString(16).padStart(2,"0")}`},children:(0,a.jsx)(m,{collapsed:p})})]})}},32005:(e,t,r)=>{"use strict";r.d(t,{C:()=>a.C});var a=r(38013)},32155:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var a=function(e){return e.Home="Home",e.Work="Work",e.Airport="Airport",e.Station="Station",e.Hotel="Hotel",e.Restaurant="Restaurant",e.Shop="Shop",e.Entertainment="Entertainment",e.Medical="Medical",e.Educational="Educational",e.BusinessCenter="BusinessCenter",e.Other="Other",e}({})},32270:(e,t,r)=>{"use strict";r.d(t,{U:()=>i});var a=r(60159),l=r(52025),s=r(28412);function i(){(0,l.k)();let[e,t]=(0,a.useState)(0),{data:r,isLoading:i,error:n,refetch:o}=(0,s.C)({size:50,status:["Requested","Searching","Accepted","Arrived","InProgress"]}),d=(0,a.useCallback)(()=>{t(0)},[]),{hasUpcomingRideWithin1_5Hours:c,hoursUntilNextRide:u,hasOverdueRequestedRides:p,overdueRidesCount:m}=(0,a.useMemo)(()=>{if(!r||0===r.length)return{hasUpcomingRideWithin1_5Hours:!1,hoursUntilNextRide:null,hasOverdueRequestedRides:!1,overdueRidesCount:0};let e=new Date().getTime()+216e5,t=r.map(t=>({...t,timeUntilRide:new Date(t.scheduledTime).getTime()-e})),a=t.filter(e=>e.timeUntilRide>0&&("Requested"===e.status||"Accepted"===e.status)).sort((e,t)=>e.timeUntilRide-t.timeUntilRide),l=t.filter(e=>e.timeUntilRide<=0&&"Requested"===e.status),s=!1,i=null;return a.length>0&&(s=(i=a[0].timeUntilRide/36e5)<=1.5),{hasUpcomingRideWithin1_5Hours:s,hoursUntilNextRide:i,hasOverdueRequestedRides:l.length>0,overdueRidesCount:l.length}},[r]);return{newScheduledRidesCount:e,totalScheduledRidesCount:r.length,hasUpcomingRideWithin1_5Hours:c,hoursUntilNextRide:u,hasOverdueRequestedRides:p,overdueRidesCount:m,isLoading:i,error:n,resetNewRidesCount:d,refetch:o}}},35028:(e,t,r)=>{"use strict";r.d(t,{gw:()=>l,k:()=>s});var a=r(1251);function l(e){switch(e){case a._n.Unknown:return"Неизвестно";case a._n.OrderCreated:return"Заказ создан";case a._n.OrderUpdated:return"Заказ изменен";case a._n.OrderConfirmed:return"Заказ подтвержден";case a._n.OrderCancelled:return"Заказ отменен";case a._n.OrderCompleted:return"Заказ завершен";case a._n.RideRequest:return"Запрос на поездку";case a._n.RideAccepted:return"Поездка принята";case a._n.RideRejected:return"Поездка отклонена";case a._n.RideStarted:return"Поездка началась";case a._n.RideCompleted:return"Поездка завершена";case a._n.RideCancelled:return"Поездка отменена";case a._n.RideUpdate:return"Обновление информации о поездке";case a._n.Payment:return"Платеж";case a._n.PaymentReceived:return"Платеж получен";case a._n.PaymentFailed:return"Ошибка платежа";case a._n.PaymentRefunded:return"Платеж возвращен";case a._n.DriverHeading:return"Водитель в пути";case a._n.DriverArrived:return"Водитель прибыл";case a._n.DriverAssigned:return"Водитель назначен";case a._n.DriverCancelled:return"Водитель отменил";case a._n.DriverNearby:return"Водитель рядом";case a._n.System:return"Системное уведомление";case a._n.SystemMessage:return"Системное сообщение";case a._n.Maintenance:return"Техническое обслуживание";case a._n.Promo:return"Промо-акция";case a._n.PromoOffer:return"Промо-предложение";case a._n.Verification:return"Верификация";case a._n.Chat:return"Сообщение в чате";default:return""}}function s(){return Object.values(a._n).map(e=>({value:e,label:l(e)}))}},35293:(e,t,r)=>{"use strict";r.d(t,{rq:()=>h,PA:()=>n,mp:()=>i});var a=r(1106);let l={[a.Xh.Admin]:"IdentityCardIcon",[a.Xh.Customer]:"ProfileIcon",[a.Xh.Driver]:"VehiclesIcon",[a.Xh.Operator]:"UsersIcon",[a.Xh.Partner]:"TariffsIcon",[a.Xh.Terminal]:"OrdersIcon",[a.Xh.Unknown]:"UsersIcon"},s={[a.Xh.Admin]:"Полный доступ к системе и управлению пользователями",[a.Xh.Customer]:"Клиент, который может заказывать услуги",[a.Xh.Driver]:"Водитель, выполняющий заказы",[a.Xh.Operator]:"Оператор, обрабатывающий заказы и запросы",[a.Xh.Partner]:"Партнер, предоставляющий услуги",[a.Xh.Terminal]:"Терминал для самообслуживания",[a.Xh.Unknown]:"Роль не определена"};function i(e){return l[e]||"UsersIcon"}function n(e){return s[e]||"Роль пользователя в системе"}var o=r(13486),d=r(60159),c=r(85626),u=r(20882),p=r(91499),m=r(79940);let h=()=>{let[e,t]=(0,d.useState)([]),[r,a]=(0,d.useState)(!1),[l,s]=(0,d.useState)(""),[i,n]=(0,d.useState)("address"),[h,g]=(0,d.useState)(!1),[f,b]=(0,d.useState)({}),{watch:x,setValue:v}=(0,c.xW)(),y=x("partnerRoutes")||[],C=(0,d.useMemo)(()=>f,[JSON.stringify(f)]),T=(0,u.d)(l,300),N=(0,d.useCallback)(async()=>{if(!T||!(T.length<2)){a(!0);try{let e={size:100,isActive:!0};T&&T.length>=2&&("address"===i?(e.address=T,e.addressOp="Contains"):"city"===i&&(e.city=T,e.cityOp="Contains")),Object.entries(C).forEach(([t,r])=>{r&&(e[t]=r)});let r=await p.Ny.getLocations(e);t(r.data||[])}catch(e){console.error("Ошибка поиска локаций:",e),t([])}finally{a(!1)}}},[T,i,C]);(0,d.useEffect)(()=>{N()},[N]),(0,d.useEffect)(()=>{(async()=>{a(!0);try{let e=await p.Ny.getLocations({size:100,isActive:!0});t(e.data||[])}catch(e){console.error("Ошибка загрузки начального списка локаций:",e)}finally{a(!1)}})()},[]);let w=e=>{v("partnerRoutes",y.includes(e)?y.filter(t=>t!==e):[...y,e])},S=e.length,R=y.length;return(0,d.useEffect)(()=>{let e=e=>{let t=e.target;!h||t.closest(".filter-panel")||t.closest(".filter-button")||g(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[h]),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("h3",{className:"text-lg font-medium",children:"Доступные локации"}),(0,o.jsxs)("div",{className:"text-sm text-gray-400",children:["Выбрано: ",R," из ",S]})]}),(0,o.jsx)("p",{className:"text-sm text-gray-400",children:"Выберите локации, в которых партнер может принимать заказы. Если не выбрано ни одной локации, партнер сможет работать везде."}),(0,o.jsxs)("div",{className:"mb-4 relative",children:[(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsx)("div",{className:"w-[150px]",children:(0,o.jsxs)("select",{value:i,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 text-sm border border-white/10 rounded-md bg-black/20 focus:outline-none focus:border-blue-500/50 hover:bg-white/10 transition-all backdrop-blur-md cursor-pointer",children:[(0,o.jsx)("option",{value:"address",children:"По адресу"}),(0,o.jsx)("option",{value:"city",children:"По городу"})]})}),(0,o.jsx)("input",{type:"text",value:l,onChange:e=>s(e.target.value),placeholder:"address"===i?"Введите адрес...":"Введите город...",className:"flex-1 px-3 py-2 text-sm border border-white/10 rounded-md bg-black/20 focus:outline-none focus:border-blue-500/50 hover:bg-white/10 transition-all backdrop-blur-md"}),(0,o.jsx)("button",{type:"button",onClick:()=>g(!h),className:`filter-button px-3 py-2 text-sm border rounded-md transition-all cursor-pointer backdrop-blur-md ${h?"bg-blue-500/20 border-blue-500/50 text-blue-400":"border-white/10 bg-black/20 hover:bg-white/10"}`,title:"Дополнительные фильтры",children:(0,o.jsxs)("span",{className:"flex items-center gap-2",children:["\uD83D\uDD27 Фильтры",Object.keys(f).length>0&&(0,o.jsx)("span",{className:"px-1.5 py-0.5 bg-blue-500/30 rounded-full text-xs",children:Object.keys(f).length})]})})]}),h&&(0,o.jsx)("div",{className:"filter-panel absolute right-0 top-full mt-2 w-[400px] p-4 bg-black/80 rounded-lg border border-white/10 z-50 shadow-2xl",children:(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Дополнительные фильтры"}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Тип локации"}),(0,o.jsxs)("select",{value:f.type||"",onChange:e=>b(t=>({...t,type:e.target.value||void 0})),className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2",children:[(0,o.jsx)("option",{value:"",children:"Все"}),(0,o.jsx)("option",{value:"Home",children:"Дом"}),(0,o.jsx)("option",{value:"Work",children:"Работа"}),(0,o.jsx)("option",{value:"Airport",children:"Аэропорт"}),(0,o.jsx)("option",{value:"Station",children:"Вокзал"}),(0,o.jsx)("option",{value:"Hotel",children:"Отель"}),(0,o.jsx)("option",{value:"Restaurant",children:"Ресторан"}),(0,o.jsx)("option",{value:"Shop",children:"Магазин"}),(0,o.jsx)("option",{value:"Entertainment",children:"Развлечения"}),(0,o.jsx)("option",{value:"Medical",children:"Медицина"}),(0,o.jsx)("option",{value:"Educational",children:"Образование"}),(0,o.jsx)("option",{value:"BusinessCenter",children:"Бизнес-центр"}),(0,o.jsx)("option",{value:"Other",children:"Другое"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Город"}),(0,o.jsx)("input",{type:"text",value:f.city||"",onChange:e=>b(t=>({...t,city:e.target.value||void 0})),placeholder:"Введите город",className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Регион"}),(0,o.jsx)("input",{type:"text",value:f.region||"",onChange:e=>b(t=>({...t,region:e.target.value||void 0})),placeholder:"Введите регион",className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Популярность"}),(0,o.jsxs)("select",{value:f.popular1||"",onChange:e=>b(t=>({...t,popular1:e.target.value||void 0})),className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2",children:[(0,o.jsx)("option",{value:"",children:"Все"}),(0,o.jsx)("option",{value:"true",children:"Популярные"}),(0,o.jsx)("option",{value:"false",children:"Обычные"})]})]})]}),(0,o.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,o.jsx)("button",{type:"button",onClick:()=>b({}),className:"px-3 py-1 text-xs border border-white/10 rounded hover:bg-white/10 transition-all cursor-pointer",children:"Очистить"}),(0,o.jsx)("button",{type:"button",onClick:()=>g(!1),className:"px-3 py-1 text-xs bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded hover:bg-blue-500/30 transition-all cursor-pointer",children:"Применить"})]})]})})]}),(0,o.jsx)("div",{className:"max-h-96 overflow-y-auto border border-white/10 rounded-lg bg-black/20 backdrop-blur-md",children:r?(0,o.jsxs)("div",{className:"p-4 text-center text-gray-400",children:[(0,o.jsx)("div",{className:"inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"}),(0,o.jsx)("p",{className:"mt-2 text-sm",children:"Поиск локаций..."})]}):e.length>0?(0,o.jsx)("div",{className:"p-2 space-y-1",children:e.map(e=>{let t=y.includes(e.id);return(0,o.jsx)("div",{onClick:()=>w(e.id),className:`p-3 rounded-lg border transition-all cursor-pointer hover:bg-white/10 ${t?"bg-blue-500/20 border-blue-500/50 text-blue-400":"border-white/10 hover:border-white/20"}`,children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("h4",{className:"font-medium text-sm",children:e.address}),(0,o.jsx)("span",{className:"text-xs px-2 py-0.5 bg-gray-500/20 rounded-full",children:e.type?(0,m.al)(e.type):"Локация"})]}),(0,o.jsxs)("p",{className:"text-xs text-gray-500",children:[e.city,", ",e.region]})]}),t&&(0,o.jsx)("div",{className:"text-blue-400 text-lg",children:"✓"})]})},e.id)})}):(0,o.jsx)("div",{className:"p-4 text-center text-gray-400",children:(0,o.jsx)("p",{className:"text-sm",children:l?"Локации не найдены":"Начните вводить для поиска"})})})]})}},36249:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var a=r(96274),l=r(50947);class s extends a.vA{async getOrder(e){let t=await this.get(`/${e}`);return this.handleApiResult(t)}async getInstantOrder(e){let t=await this.get(`instant/${e}`);return this.handleApiResult(t)}async getScheduledOrder(e){let t=await this.get(`scheduled/${e}`);return this.handleApiResult(t)}async getOrderSafe(e){let t=await this.get(`/${e}`);return this.handleApiResultSafe(t)}async getMyParticipantOrder(e){let t=await this.get(`/Order/my/participant/${e}`);return this.handleApiResult(t)}async getOrders(e){let t=new URLSearchParams;e?.First!==void 0&&t.append("First",e.First.toString()),e?.Before&&t.append("Before",e.Before),e?.After&&t.append("After",e.After),e?.Last!==void 0&&t.append("Last",e.Last.toString()),e?.Size!==void 0&&t.append("Size",e.Size.toString()),e?.Type?.length&&e.Type.forEach(e=>t.append("Type",e)),e?.status?.length&&e.status.forEach(e=>t.append("status",e)),e?.statusOp&&t.append("statusOp",e.statusOp),e?.SubStatus?.length&&e.SubStatus.forEach(e=>t.append("SubStatus",e)),e?.CreatorId&&t.append("CreatorId",e.CreatorId),e?.CustomerId&&t.append("CustomerId",e.CustomerId),e?.CreatedAt&&t.append("CreatedAt",e.CreatedAt),e?.CreatedAtOp&&t.append("CreatedAtOp",e.CreatedAtOp),e?.CompletedAt&&t.append("CompletedAt",e.CompletedAt),e?.CompletedAtOp&&t.append("CompletedAtOp",e.CompletedAtOp),e?.ScheduledTime&&t.append("ScheduledTime",e.ScheduledTime),e?.ScheduledTimeOp&&t.append("ScheduledTimeOp",e.ScheduledTimeOp),e?.Services?.length&&e.Services.forEach(e=>t.append("Services",e)),e?.AirFlight&&t.append("AirFlight",e.AirFlight),e?.AirFlightOp&&t.append("AirFlightOp",e.AirFlightOp),e?.["FTS.Plain"]&&t.append("FTS.Plain",e["FTS.Plain"]),e?.["FTS.Query"]&&t.append("FTS.Query",e["FTS.Query"]),e?.SortBy&&t.append("SortBy",e.SortBy),e?.SortOrder&&t.append("SortOrder",e.SortOrder);let r=t.toString()?`?${t.toString()}`:"",a=await this.get(r);return this.handleApiResult(a)}async createInstantOrder(e){let t=await this.post("instant/by-operator",e);return this.handleApiResult(t)}async createInstantOrderByTerminal(e){let t=await this.post("instant/by-terminal",e);return this.handleApiResult(t)}async createScheduledOrder(e){let t=await this.post("scheduled",e);return this.handleApiResult(t)}async updateInstantOrder(e,t){let r=await this.put(`instant/${e}`,t);return this.handleApiResult(r)}async updateScheduledOrder(e,t){let r=await this.put(`scheduled/${e}`,t);return this.handleApiResult(r)}async deleteOrder(e){let t=await this.delete(`/${e}`);this.handleApiResult(t)}async cancelOrder(e,t){let r=await this.patch(`/${e}/cancel`,t?{reason:t}:{});return this.handleApiResult(r)}async acceptOrder(e,t){let r=await this.patch(`/${e}/accept`,{driverId:t});return this.handleApiResult(r)}async completeOrder(e){let t=await this.patch(`/${e}/complete`);return this.handleApiResult(t)}async updatePassengers(e,t){let r=await this.put(`${e}/passengers`,t);return this.handleApiResult(r)}async createScheduledRide(e,t){let r=await this.post(`scheduled/${e}/ride`,t);return this.handleApiResult(r)}async getPotentialDrivers(e){let t=await this.get(`${e}/PotentialDrivers`);return this.handleApiResult(t)}async getOrderStats(){let e=await this.get("stats");return this.handleApiResult(e)}constructor(...e){super(...e),this.baseUrl=l.QQ.ORDER.LIST}}let i=new s},36893:(e,t,r)=>{"use strict";r.d(t,{h:()=>u,Q:()=>p});var a=r(13486),l=r(60159);let s=(0,l.lazy)(()=>Promise.resolve().then(r.bind(r,35235))),i=(0,l.lazy)(()=>Promise.resolve().then(r.bind(r,29383))),n=(0,l.lazy)(()=>Promise.resolve().then(r.bind(r,27766))),o=(0,l.lazy)(()=>Promise.resolve().then(r.bind(r,2262))),d=(0,l.lazy)(()=>Promise.resolve().then(r.bind(r,24986))),c=(0,l.lazy)(()=>Promise.resolve().then(r.bind(r,71015))),u=[{id:"dashboard",title:"Панель управления",path:"/",icon:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(s,{className:"w-6 h-6"})})},{id:"users",title:"Пользователи",path:"/users",icon:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(i,{className:"w-6 h-6"})})},{id:"cars",title:"Автомобили",path:"/cars",icon:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(n,{className:"w-6 h-6"})})},{id:"locations",title:"Локации",path:"/locations",icon:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(c,{className:"w-6 h-6"})})},{id:"services",title:"Услуги",path:"/services",icon:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(o,{className:"w-6 h-6"})})},{id:"tariffs",title:"Тарифы",path:"/tariffs",icon:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(o,{className:"w-6 h-6"})})},{id:"orders",title:"Заказы",path:"/orders",icon:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(d,{className:"w-6 h-6"})})},{id:"notifications",title:"Уведомления",path:"/notifications",icon:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(d,{className:"w-6 h-6"})})}],p=[{id:"orders",title:"Заказы",path:"/",icon:"\uD83D\uDCC5"},{id:"scheduled-orders",title:"Запланированные заказы",path:"/scheduled-orders",icon:"\uD83D\uDCC5"},{id:"notifications",title:"Уведомления",path:"/notifications",icon:"\uD83D\uDD14"},{id:"my-car",title:"Мои автомобили",path:"/my-car",icon:"\uD83D\uDE97"},{id:"settings",title:"Настройки",path:"/settings",icon:"⚙️"},{id:"support",title:"Служба поддержки",path:"/support",icon:"\uD83C\uDFA7"},{id:"statistics",title:"Моя статистика",path:"/statistics",icon:"\uD83D\uDCCA"}]},37829:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var a=r(66616),l=r(39314);let s={title:{create:"Создание новой локации",edit:"Редактирование локации"},groups:[{id:"main",title:"Основная информация",order:1,layout:{gridCols:2,gapX:8,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"infoGroup",type:a.V.GROUP,label:"Информация о локации",description:"Основные данные локации для идентификации в системе",layout:{flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"name",label:"Название",type:a.V.TEXT,placeholder:"Введите название локации",required:!0,helperSmallText:"Например: Аэропорт Манас, Отель Хаятт",helperBigText:"Название места, которое будет отображаться в системе",className:"w-full",maxLength:127},{name:"type",label:"Тип локации",type:a.V.SELECT,placeholder:"Выберите тип локации",required:!0,options:(0,l._n)(),helperSmallText:"Тип места",helperBigText:"Категория места для классификации в системе",className:"w-full"}]},{name:"addressGroup",type:a.V.GROUP,label:"Адрес",description:"Адресная информация о локации",layout:{flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"address",label:"Адрес",type:a.V.TEXT,placeholder:"Введите адрес",required:!0,helperSmallText:"Полный адрес",helperBigText:"Полный адрес локации, включая улицу и номер дома",className:"w-full",maxLength:255},{name:"district",label:"Округ/район",type:a.V.TEXT,placeholder:"Введите округ или район",required:!1,helperSmallText:"Округ или район (необязательно)",helperBigText:"Административный округ или район, где находится локация",className:"w-full",maxLength:63},{name:"city",label:"Город",type:a.V.TEXT,placeholder:"Введите город",required:!0,helperSmallText:"Город или населенный пункт",helperBigText:"Город или населенный пункт, где находится локация",className:"w-full",maxLength:63},{name:"region",label:"Область/регион",type:a.V.TEXT,placeholder:"Введите область или регион",required:!0,helperSmallText:"Область или регион",helperBigText:"Область или регион, где находится локация",className:"w-full",maxLength:63},{name:"country",label:"Страна",type:a.V.TEXT,placeholder:"Введите страну",required:!0,helperSmallText:"Страна",helperBigText:"Страна, в которой находится локация",className:"w-full",maxLength:63}]}]},{id:"coordinates",title:"Местоположение на карте",order:2,description:"Выберите точное местоположение на интерактивной карте",layout:{gridCols:1,gapX:6,gapY:4,className:"h-full",flexDirection:"column",maxItemsPerRow:1,itemsAlignment:"start"},fields:[{name:"mapLocationPicker",label:"Интерактивная карта",type:a.V.MAP_LOCATION_PICKER,required:!0,helperSmallText:"Кликните на карту для выбора точного местоположения",helperBigText:"Выберите мес��оположение на карте. Координаты будут автоматически сохранены.",className:"w-full"},{name:"latitude",label:"Широта",type:a.V.HIDDEN,required:!0},{name:"longitude",label:"Долгота",type:a.V.HIDDEN,required:!0}]},{id:"settings",title:"Настройки",order:3,description:"Настройки активности и популярности локации",layout:{gridCols:1,gapX:6,gapY:4,className:"h-full",flexDirection:"column",maxItemsPerRow:1,itemsAlignment:"start"},fields:[{name:"isActive",label:"Активная локация",type:a.V.CHECKBOX,required:!0,helperSmallText:"Активна ли локация",helperBigText:"Определяет, доступна ли локация для использования в системе",className:"w-full",defaultValue:!0},{name:"popular1",label:"Популярная локация 1",type:a.V.CHECKBOX,required:!1,helperSmallText:"Отметить как популярную локацию (категория 1)",helperBigText:"Популярные локации отображаются в приоритетном порядке",className:"w-full"},{name:"popular2",label:"Популярная локация 2",type:a.V.CHECKBOX,required:!1,helperSmallText:"Отметить как популярную локацию (категория 2)",helperBigText:"Популярные локации отображаются в приоритетном порядке",className:"w-full"}]}]}},38013:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var a=r(96274),l=r(50947);class s extends a.vA{async getRide(e){let t=await this.get(`/${e}`);return this.handleApiResult(t)}async getRideSafe(e){let t=await this.get(`/${e}`);return this.handleApiResultSafe(t)}async getRides(e){let t=new URLSearchParams;e?.first!==void 0&&t.append("first",e.first.toString()),e?.before&&t.append("before",e.before),e?.after&&t.append("after",e.after),e?.last!==void 0&&t.append("last",e.last.toString()),e?.size!==void 0&&t.append("size",e.size.toString()),e?.search&&t.append("search",e.search),e?.status&&t.append("status",e.status),e?.driverId&&t.append("driverId",e.driverId),e?.passengerId&&t.append("passengerId",e.passengerId);let r=t.toString()?`?${t.toString()}`:"",a=await this.get(r);return this.handleApiResult(a)}async getDriverActiveRides(e,t=100){let r=new URLSearchParams;r.append("DriverId",e),r.append("Size",t.toString()),r.append("Status","Accepted"),r.append("Status","Arrived"),r.append("Status","InProgress");let a=await this.get(`my?${r.toString()}`);return this.handleApiResult(a)}async getDriverScheduledRides(e,t=50){let r=new URLSearchParams;r.append("driverId",e),r.append("pageSize",t.toString()),r.append("Status","Requested"),r.append("Status","Accepted");let a=await this.get(`my/assigned?${r.toString()}`);return this.handleApiResult(a)}async createRide(e){let t=await this.post("",e);return this.handleApiResult(t)}async updateRide(e,t){let r=await this.put(`/${e}`,t);return this.handleApiResult(r)}async deleteRide(e){let t=await this.delete(`/${e}`);this.handleApiResult(t)}async updateRideStatus(e,t){let r=await this.patch(`/${e}/status`,{status:t});return this.handleApiResult(r)}async acceptRide(e,t){let r=await this.patch(`/${e}/accept`,{driverId:t});return this.handleApiResult(r)}async completeRide(e){let t=await this.patch(`/${e}/complete`);return this.handleApiResult(t)}async cancelRide(e,t){let r=await this.patch(`/${e}/cancel`,{reason:t});return this.handleApiResult(r)}async getDriverQueuePosition(){try{let e=await this.get("/DriverQueue/self");return this.handleApiResultSafe(e)}catch(e){return null}}async joinDriverQueue(){let e=await this.post("/DriverQueue/self");return this.handleApiResult(e)}async leaveDriverQueue(){let e=await this.delete("/DriverQueue/self");this.handleApiResult(e)}async isDriverInQueue(){return null!==await this.getDriverQueuePosition()}async acceptInstantOrder(e){let t=await this.post(`/Order/instant/${e}/accept-by-driver`);return this.handleApiResult(t)}async acceptScheduledRide(e){let t=await this.post(`/Ride/${e}/accept-by-driver`);return this.handleApiResult(t)}async notifyScheduledRideAcceptance(e){let t=await this.post(`/Ride/${e}/notification-by-driver`);this.handleApiResult(t)}async rejectScheduledRide(e){let t=await this.post(`/Ride/${e}/reject-by-driver`);return this.handleApiResult(t)}async driverHeadingToClient(e){let t=await this.post(`/Ride/${e}/status/driver-heading-to-client`);return this.handleApiResult(t)}async driverArrived(e){let t=await this.post(`/Ride/${e}/status/driver-arrived`);return this.handleApiResult(t)}async rideStarted(e){let t=await this.post(`/Ride/${e}/status/ride-started`);return this.handleApiResult(t)}async rideFinished(e){let t=await this.post(`/Ride/${e}/status/ride-finished`);return this.handleApiResult(t)}async rideCancelled(e){let t=await this.post(`/Ride/${e}/status/ride-cancelled`);return this.handleApiResult(t)}async getActiveRides(e){return e?(await this.getDriverActiveRides(e)).data||[]:(await this.getDriverActiveRides("current")).data||[]}async getRideById(e){let t=await this.get(`/Ride/${e}`);return this.handleApiResult(t)}async getScheduledOrderDetails(e){let t=await this.get(`/Order/scheduled/${e}`);return this.handleApiResult(t)}async getOrderDetails(e){let t=await this.get(`/Order/${e}`);return this.handleApiResult(t)}async getOrderDetailsUniversal(e){try{return await this.getScheduledOrderDetails(e)}catch(t){if(t&&"object"==typeof t&&"response"in t&&t.response?.status===404)return await this.getOrderDetails(e);throw t}}async getCurrentDriverId(){try{let e=await this.get("/Driver/current"),t=this.handleApiResultSafe(e);return t?.driverId||null}catch(e){return console.error("❌ Ошибка получения ID текущего водителя:",e),null}}async assignDriver(e,t,r){let a={driverId:t};r&&(a.carId=r);let l=await this.patch(`/${e}/assign-driver`,a);return this.handleApiResult(l)}constructor(...e){super(...e),this.baseUrl=l.QQ.RIDE.LIST}}let i=new s},38567:(e,t,r)=>{"use strict";r.d(t,{TerminalTariffProvider:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call TerminalTariffProvider() from the server but TerminalTariffProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\app\\providers\\TerminalTariffProvider.tsx","TerminalTariffProvider")},38765:(e,t,r)=>{"use strict";r.d(t,{SignalRProvider:()=>d});var a=r(13486),l=r(60159),s=r(95863),i=r(63497);class n{registerHandler(e,t){this.handlers.has(e)||this.handlers.set(e,[]),this.handlers.get(e).push(t)}handleNotification(e,t){console.log("\uD83D\uDCE2 Обработка уведомления:",{type:e,data:t});let r=this.handlers.get(e);r&&r.forEach(e=>e(t)),this.showToast(e,t)}showToast(e,t){let r=t.title||"Уведомление",a=t.content||"";switch(e){case"RideRequest":case"RideRequestNotification":case"DriverHeadingNotification":i.P.info(`🚗 ${r}: ${a}`);break;case"RideAccepted":case"RideAcceptedNotification":case"OrderConfirmedNotification":i.P.success(`✅ ${r}: ${a}`);break;case"RideCancelled":case"RideCancelledNotification":case"OrderCancelledNotification":i.P.warn(`❌ ${r}: ${a}`);break;case"RideStarted":case"RideStartedNotification":i.P.success(`🚗 ${r}: ${a}`);break;case"DriverArrivedNotification":i.P.success(`🏁 ${r}: ${a}`);break;case"DriverCancelledNotification":case"RideRejectedNotification":i.P.error(`❌ ${r}: ${a}`);break;case"OrderCompletedNotification":i.P.success(`🎉 ${r}: ${a}`);break;case"RideStartedNotification":i.P.info(`🚀 ${r}: ${a}`);break;case"PaymentNotification":i.P.info(`💳 ${r}: ${a}`);break;case"PaymentReceivedNotification":i.P.success(`💰 ${r}: ${a}`);break;case"PaymentFailedNotification":i.P.error(`💸 ${r}: ${a}`);break;default:i.P.info(`📢 ${r}: ${a}`)}}constructor(){this.handlers=new Map}}let o=new n,d=({children:e,accessToken:t})=>{let[r,i]=(0,l.useState)(null),[n,d]=(0,l.useState)(!1),[c,u]=(0,l.useState)(!1),[p,m]=(0,l.useState)(null),h=(0,l.useRef)(new Map),g=(0,l.useCallback)(()=>{["RideRequestNotification","RideAcceptedNotification","RideAssignedNotification","RideCancelledNotification","RideCompletedNotification","DriverActivityUpdated","DriverArrivedNotification","DriverHeadingNotification","DriverCancelledNotification","OrderConfirmedNotification","OrderCancelledNotification","OrderCompletedNotification","RideRejectedNotification","RideStartedNotification","PaymentNotification","PaymentReceivedNotification","PaymentFailedNotification"].forEach(e=>{let t=h.current.get(e)||[];t.push(t=>{console.log(`WebSocket уведомление [${e}]:`,t),o.handleNotification(e,t)}),h.current.set(e,t)}),console.log("Подписка на уведомления активирована")},[]),f=(0,l.useCallback)(async()=>{try{if(u(!0),m(null),!t)throw Error("JWT токен не найден");g();let e=`ws://api.compass.local:3030/hubs/wshub?access_token=${t}`,r=new WebSocket(e);r.onopen=()=>{console.log("WebSocket подключен"),r.send('{"protocol":"json","version":1}\x1e'),i(r),d(!0),u(!1)},r.onclose=e=>{console.log("WebSocket соединение закрыто:",e),d(!1),i(null)},r.onerror=e=>{console.error("Ошибка WebSocket:",e),m("Ошибка подключения к WebSocket"),u(!1)},r.onmessage=e=>{try{console.log("Получено сообщение WebSocket:",e.data);let t=e.data.replace(/\x1e$/,"");if(!t||"{}"===t)return void console.log("Handshake успешно завершен");if(t.includes('"error"')){let e=JSON.parse(t);console.error("Ошибка от сервера:",e),m(e.error||"Ошибка сервера"),d(!1),i(null);return}let r=JSON.parse(t);if(1===r.type&&r.target&&r.arguments){let e=r.target,t=r.arguments[0];if(console.log(`Обработка события [${e}]:`,t),h.current.has(e)){let r=h.current.get(e)||[];console.log(`ВЫЗЫВАЮ ${r.length} обработчиков для ${e}`),r.forEach(e=>{e(t)})}else console.log(`НЕТ ОБРАБОТЧИКОВ для события ${e}`)}}catch(t){console.error("Ошибка парсинга сообщения WebSocket:",t,"Данные:",e.data)}}}catch(e){m(e instanceof Error?e.message:"Ошибка подключения"),u(!1)}},[t,g]),b=(0,l.useCallback)(async()=>{r&&(r.close(),i(null),d(!1),h.current.clear())},[r]),x=(0,l.useCallback)((e,t)=>{let r=h.current.get(e)||[];r.push(t),h.current.set(e,r)},[]),v=(0,l.useCallback)((e,t)=>{let r=(h.current.get(e)||[]).filter(e=>e!==t);r.length>0?h.current.set(e,r):h.current.delete(e)},[]);(0,l.useEffect)(()=>{!t||n||c||(console.log("Автоматическое подключение к WebSocket..."),f().catch(e=>{console.error("Ошибка автоподключения к WebSocket:",e)}))},[t,n,c,f]),(0,l.useEffect)(()=>{n?console.log("WebSocket подключен и готов к работе"):p&&console.error("Ошибка WebSocket:",p)},[n,p]);let y={connection:r,isConnected:n,isConnecting:c,error:p,connect:f,disconnect:b,on:x,off:v};return n||p?p?(0,a.jsx)(s.I.Provider,{value:y,children:(0,a.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-red-500 text-lg mb-2",children:"Ошибка подключения"}),(0,a.jsx)("div",{children:p})]})})}):(0,a.jsx)(s.I.Provider,{value:y,children:e}):(0,a.jsx)(s.I.Provider,{value:y,children:(0,a.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"}),(0,a.jsx)("div",{children:c?"Подключение к серверу...":"Инициализация..."})]})})})}},39314:(e,t,r)=>{"use strict";r.d(t,{_n:()=>a._n,al:()=>a.al}),r(58836);var a=r(79940)},41101:(e,t,r)=>{"use strict";r(96274)},41644:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useDriverHeader:()=>n});var a=r(57305),l=r(95395);let s={[l.$O.Sedan]:"/car/sedan.png",[l.$O.Hatchback]:"/car/hatchback.png",[l.$O.SUV]:"/car/suv.png",[l.$O.Minivan]:"/car/minivan.png",[l.$O.Coupe]:"/car/coupe.png",[l.$O.Cargo]:"/car/cargo.png",[l.$O.Pickup]:"/car/pickup.png"},i="/car/HongQi E-QM5.png";function n(){var e;let{profile:t,isLoading:r,error:l}=(0,a.Z)(),n=t?.activeCar,o=t?{id:t.id,firstName:t.fullName.split(" ")[0]||"Водитель",lastName:t.fullName.split(" ").slice(1).join(" ")||"",fullName:t.fullName,phone:t.phoneNumber||"Телефон не указан",email:t.email,avatar:t.avatarUrl||void 0,vehicle:n?{licensePlate:n.licensePlate,model:n.model,make:n.make,year:n.year,color:n.color,category:n.serviceClass||"Economy",bodyType:n.type||"Economy"}:void 0}:null,d=(e=o?.vehicle?.bodyType)&&s[e]||i,c=o?.fullName||"Водитель",u=o?.phone||"Телефон не указан",p=o?.firstName?.[0]||"В",m=!!o?.vehicle;return{profile:o,isLoading:r,error:l,isActive:!!t?.online,carImagePath:d,displayName:c,displayPhone:u,avatarInitials:p,hasVehicleInfo:m}}},41925:(e,t,r)=>{"use strict";r.d(t,{SignalRProvider:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call SignalRProvider() from the server but SignalRProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\app\\providers\\SignalRProvider.tsx","SignalRProvider")},42399:(e,t,r)=>{"use strict";r.d(t,{D:()=>l,_:()=>s});var a=r(37829);let l={base:a.F,create:a.F,update:a.F},s=a.F},42419:(e,t,r)=>{"use strict";r.d(t,{M:()=>s});var a=r(66616),l=r(88330);let s={title:{create:"Создание нового тарифа",edit:"Редактирование тарифа"},groups:[{id:"main",title:"Основная информация",order:1,layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"name",label:"Название",type:a.V.TEXT,required:!0,placeholder:"Введите название тарифа",helperSmallText:"Название тарифа (Эконом, Комфорт)",helperBigText:"Укажите понятное и краткое название тарифа, которое будет отображаться клиентам",className:"w-full",maxLength:127},{name:"serviceClass",label:"Класс обслуживания",type:a.V.SELECT,required:!0,placeholder:"Выберите класс обслуживания",options:(0,l.cx)(),helperSmallText:"Класс обслуживания",helperBigText:"Выберите класс обслуживания, который соответствует данному тарифу",className:"w-full"}]},{id:"pricing",title:"Стоимость",order:2,description:"Настройки стоимости тарифа",layout:{gridCols:2,gapX:4,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"center"},fields:[{name:"basePrice",label:"Базовая стоимость",type:a.V.NUMBER,required:!0,placeholder:"Введите базовую стоимость",helperSmallText:"Базовая стоимость подачи",helperBigText:"Базовая стоимость подачи / минимальная стоимость",className:"w-full",min:0,step:.01},{name:"minutePrice",label:"Стоимость минуты",type:a.V.NUMBER,required:!0,placeholder:"Введите стоимость минуты",helperSmallText:"Стоимость минуты поездки",helperBigText:"Стоимость одной минуты поездки по данному тарифу",className:"w-full",min:0,step:.01},{name:"minimumPrice",label:"Минимальная стоимость",type:a.V.NUMBER,required:!0,placeholder:"Введите минимальную стоимость",helperSmallText:"Минимальная стоимость поездки",helperBigText:"Минимальная стоимость поездки по данному тарифу",className:"w-full",min:0,step:.01},{name:"perKmPrice",label:"Стоимость километра",type:a.V.NUMBER,required:!0,placeholder:"Введите стоимость километра",helperSmallText:"Стоимость километра поездки (сом)",helperBigText:"Стоимость одного километра поездки по данному тарифу",className:"w-full",min:0,step:.01}]}]}},42429:(e,t,r)=>{"use strict";r.d(t,{LP:()=>s,vP:()=>l});var a=r(29457);function l(e){switch(e){case a.ZN.Unknown:return"Неизвестно";case a.ZN.Instant:return"Мгновенный";case a.ZN.Scheduled:return"Запланированный";case a.ZN.Partner:return"Партнерский";case a.ZN.Shuttle:return"Шаттл";case a.ZN.Subscription:return"Подписка";default:return""}}function s(){return Object.values(a.ZN).map(e=>({value:e,label:l(e)}))}},44891:(e,t,r)=>{"use strict";r.d(t,{L:()=>l});var a=r(56840);a.l;let l=a.l},45788:(e,t,r)=>{"use strict";r.d(t,{Header:()=>N});var a=r(13486),l=r(60159),s=r.n(l),i=r(5684),n=r(49989),o=r.n(n);let d=()=>{let e=(0,i.if)();return 0===e.length?null:(0,a.jsx)("nav",{"aria-label":"Breadcrumbs",children:(0,a.jsx)("ol",{className:"flex items-center space-x-1 text-sm",children:e.map((t,r)=>{let l=r===e.length-1;return(0,a.jsxs)("li",{className:"flex items-center",children:[r>0&&(0,a.jsx)("span",{className:"mx-1",children:"/"}),l?(0,a.jsx)("span",{className:"font-medium text-[color:var(--color-brand)]",children:t.label}):(0,a.jsx)(o(),{href:t.path,className:"hover:text-[color:var(--color-primary)] transition-colors duration-150",children:t.label})]},t.path)})})})};var c=r(2984),u=r(96881),p=r(31071),m=r(64768),h=r(13879);let g=(0,l.forwardRef)(({isOpen:e},t)=>{let r=(0,c.useRouter)(),s=(0,l.useRef)(null),i=(0,l.useRef)(null),{notifications:n,isLoading:o,isLoadingMore:d,hasMore:g,unreadCount:f,actions:b}=(0,p.M)(),x=(0,l.useCallback)(()=>{s.current&&s.current.disconnect(),s.current=new IntersectionObserver(e=>{e[0].isIntersecting&&g&&!d&&!o&&n.length>0&&(console.log("\uD83D\uDD04 Intersection Observer: загружаем больше уведомлений"),b.loadMore())},{threshold:.1,rootMargin:"20px"}),i.current&&g&&s.current.observe(i.current)},[g,d,o,b,n.length]);(0,l.useEffect)(()=>(e&&x(),()=>{s.current&&s.current.disconnect()}),[x,e]);let v=async e=>{try{if(e.isRead||await b.markAsRead(e.id),e.orderId){let t=e.orderType?.toLowerCase()||"instant";r.push(`/orders/${e.orderId}?type=${t}`)}else e.rideId&&r.push(`/rides/${e.rideId}`)}catch(e){}};return e?(0,a.jsxs)("div",{ref:t,role:"menu","aria-hidden":!1,className:"absolute right-0 top-[calc(100%+1.5rem)] w-80 rounded-md overflow-hidden shadow-lg border border-[color:var(--color-neutral-300)]  z-40 transition-all duration-200 transform origin-top-right opacity-100 scale-100 animate-fadeIn backdrop-blur-md bg-black/90",children:[(0,a.jsxs)("div",{className:"p-3 border-b border-[color:var(--color-neutral-300)] flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-medium",children:"Уведомления"}),f>0&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300 rounded-full",children:f})]}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:o&&0===n.length?(0,a.jsx)("div",{className:"p-4 text-center",children:"Загрузка уведомлений..."}):0===n.length?(0,a.jsx)("div",{className:"p-4 text-center",children:"Нет уведомлений"}):(0,a.jsxs)(a.Fragment,{children:[n.map(e=>(0,a.jsx)("div",{onClick:()=>v(e),className:`
                    p-3 border-b border-[color:var(--color-neutral-300)] 
                    hover:bg-[color:var(--color-surface-variant)] 
                    dark:hover:bg-[color:var(--color-neutral-800)]/50 
                    transition-colors duration-200 cursor-pointer
                    ${e.isRead?"opacity-70":""}
                  `,children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:`
                      flex-shrink-0 rounded-full p-2 transition-colors duration-200 text-lg
                      ${function(e,t){let r=t?"50":"100";switch(e){case h._n.RideRequest:case h._n.RideAccepted:return`bg-blue-${r} text-blue-700 dark:bg-blue-900/30 dark:text-blue-300`;case h._n.RideCompleted:case h._n.OrderCompleted:return`bg-green-${r} text-green-700 dark:bg-green-900/30 dark:text-green-300`;case h._n.PaymentReceived:return`bg-emerald-${r} text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300`;case h._n.PaymentFailed:case h._n.OrderCancelled:case h._n.RideCancelled:return`bg-red-${r} text-red-700 dark:bg-red-900/30 dark:text-red-300`;case h._n.System:case h._n.SystemMessage:return`bg-gray-${r}  dark:bg-gray-900/30 dark:text-gray-300`;default:return`bg-blue-${r} text-blue-700 dark:bg-blue-900/30 dark:text-blue-300`}}(e.type,e.isRead||!1)}
                    `,children:function(e){switch(e){case h._n.RideRequest:return"\uD83D\uDE97";case h._n.RideAccepted:return"✅";case h._n.RideCompleted:return"\uD83C\uDFC1";case h._n.OrderCompleted:return"\uD83C\uDF89";case h._n.DriverArrived:return"\uD83D\uDCCD";case h._n.DriverHeading:return"\uD83D\uDE99";case h._n.DriverAssigned:return"\uD83D\uDC64";case h._n.PaymentReceived:return"\uD83D\uDCB0";case h._n.PaymentFailed:return"\uD83D\uDCB8";case h._n.OrderCancelled:case h._n.RideCancelled:return"❌";case h._n.System:case h._n.SystemMessage:return"⚙️";default:return"\uD83D\uDCE2"}}(e.type)}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col gap-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,a.jsx)("p",{className:`
                          text-sm font-medium truncate
                          ${e.isRead?"":"text-[color:var(--color-text-primary)]"}
                        `,children:e.title}),!e.isRead&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-1"})]}),e.content&&(0,a.jsx)("p",{className:"text-xs  transition-colors duration-200 line-clamp-2",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-xs text-[color:var(--color-text-tertiary)] transition-colors duration-200",children:(0,u.f)(e.createdAt)}),(0,a.jsx)("span",{className:"text-xs text-[color:var(--color-text-tertiary)] bg-[color:var(--color-neutral-100)] dark:bg-[color:var(--color-neutral-800)] px-2 py-1 rounded",children:(0,m.gw)(e.type)})]})]})]})},e.id)),g&&(0,a.jsx)("div",{ref:i,className:"flex items-center justify-center py-3",children:d?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,a.jsx)("span",{className:"text-xs",children:"Загрузка..."})]}):(0,a.jsx)("button",{onClick:()=>b.loadMore(),className:"text-xs text-[color:var(--color-primary)] hover:text-[color:var(--color-primary-hover)] transition-colors",children:"Загрузить еще"})}),!g&&n.length>0&&(0,a.jsx)("div",{className:"text-center py-2 text-[color:var(--color-text-tertiary)] text-xs",children:"Все уведомления загружены"})]})})]}):null});g.displayName="NotificationsDropdown";var f=r(96274),b=r(5448),x=r(18673),v=r(80988);let y=(0,l.forwardRef)(({isOpen:e},t)=>{let[r,s]=(0,l.useState)(!1),i=async()=>{try{s(!0);let e=fetch(`/api${f.QQ.AUTH.LOGOUT}`,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include"}),t=await (0,b.n)(e);if(t.ok)window.location.href=f.vc.AUTH.LOGIN;else{let e="Ошибка при выходе из системы";try{e=(await t.json()).message||e}catch{e=`Ошибка при выходе из системы: ${t.status} ${t.statusText}`}x.P.error(e)}}catch(t){let e=t instanceof Error?t.message:"Произошла непредвиденная ошибка при выходе из системы";x.P.error(e)}finally{s(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.wh,{isVisible:r,message:"Выход из системы..."}),(0,a.jsxs)("div",{ref:t,role:"menu","aria-hidden":!e,className:`absolute right-0 top-[calc(100%+1.5rem)] w-56 rounded-md overflow-hidden shadow-lg 
            border border-[color:var(--color-neutral-300)]  z-40 transition-all duration-200 transform origin-top-right 
            bg-black/50 backdrop-blur-md
            ${e?"opacity-100 scale-100 pointer-events-auto":"opacity-0 scale-95 pointer-events-none"}`,children:[(0,a.jsxs)("div",{className:"p-3 border-b border-[color:var(--color-neutral-300)]",children:[(0,a.jsx)("p",{className:"font-medium",children:"Администратор"}),(0,a.jsx)("p",{className:"text-sm  transition-colors duration-200",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o(),{href:f.vc.PROFILE.SELF,role:"menuitem",className:"block w-full px-4 py-2 text-sm hover:bg-gray-50/20 transition-colors duration-200",children:"Мой профиль"}),(0,a.jsx)(o(),{href:"/settings",role:"menuitem",className:"block w-full px-4 py-2 text-sm hover:bg-gray-50/20 transition-colors duration-200",children:"Настройки"}),(0,a.jsx)("button",{type:"button",role:"menuitem",onClick:i,disabled:r,className:"block w-full text-left px-4 py-2 text-sm text-[color:var(--color-error)] hover:bg-red-50/20 transition-colors duration-200 cursor-pointer",children:"Выйти"})]})]})]})});y.displayName="ProfileDropdown";let C=s().lazy(()=>Promise.resolve().then(r.bind(r,84368))),T=s().lazy(()=>Promise.resolve().then(r.bind(r,97376))),N=()=>{let{notificationsOpen:e,profileOpen:t,notificationsRef:r,profileRef:s,notificationsButtonRef:n,profileButtonRef:o,toggleNotifications:c,toggleProfile:u}=(0,i.Nu)();return(0,a.jsx)("header",{className:"h-16 px-8 py-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-full",children:[(0,a.jsxs)("div",{className:"flex flex-col items-start justify-start",children:[(0,a.jsx)("h1",{className:"text-xl font-medium",children:"Админ-панель"}),(0,a.jsx)(d,{})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{ref:n,onClick:c,className:"p-2 rounded-full hover:bg-[color:var(--color-surface-variant)] active:bg-[color:var(--color-surface-variant)]/80 transition-colors duration-200 relative cursor-pointer","aria-label":"Уведомления",children:[(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(C,{className:"w-6 h-6"})}),(0,a.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-error rounded-full"})]}),(0,a.jsx)(g,{ref:r,isOpen:e})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{ref:o,onClick:u,className:"flex items-center justify-center p-2 rounded-full hover:bg-[color:var(--color-surface-variant)] active:bg-[color:var(--color-surface-variant)]/80 transition-colors duration-200 cursor-pointer","aria-label":"Профиль",style:{width:"40px",height:"40px"},children:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-6 h-6"}),children:(0,a.jsx)(T,{className:"w-6 h-6 text-primary"})})}),(0,a.jsx)(y,{ref:s,isOpen:t})]})]})]})})}},46154:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useHeaderDropdowns:()=>l});var a=r(60159);let l=()=>{let[e,t]=(0,a.useState)(!1),[r,l]=(0,a.useState)(!1),s=(0,a.useRef)(null),i=(0,a.useRef)(null),n=(0,a.useRef)(null),o=(0,a.useRef)(null),d=(0,a.useRef)(null);return(0,a.useEffect)(()=>{let a=a=>{e&&i.current&&!i.current.contains(a.target)&&o.current&&!o.current.contains(a.target)&&t(!1),r&&n.current&&!n.current.contains(a.target)&&d.current&&!d.current.contains(a.target)&&l(!1)};return document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[e,r]),{notificationsOpen:e,profileOpen:r,searchRef:s,notificationsRef:i,profileRef:n,notificationsButtonRef:o,profileButtonRef:d,toggleNotifications:()=>{t(!e),e||l(!1)},toggleProfile:()=>{l(!r),r||t(!1)}}}},46171:(e,t,r)=>{"use strict";r.d(t,{e1:()=>i,bo:()=>s});var a=r(43888);let l=a.z.enum(["Home","Work","Airport","Station","Hotel","Restaurant","Shop","Entertainment","Medical","Educational","BusinessCenter","Other"]),s=a.z.object({type:l.describe("Тип локации"),name:a.z.string().min(1,{message:"Название локации обязательно"}).max(127,{message:"Название локации не должно превышать 127 символов"}).describe("Название места (например, Аэропорт Манас)"),address:a.z.string().min(1,{message:"Адрес обязателен"}).max(255,{message:"Адрес не должен превышать 255 символов"}).describe("Адрес"),district:a.z.string().max(63,{message:"Округ/район не должен превышать 63 символа"}).nullable().optional().describe("Округ/район"),city:a.z.string().min(1,{message:"Город обязателен"}).max(63,{message:"Город не должен превышать 63 символа"}).describe("Город/Населенный пункт"),country:a.z.string().min(1,{message:"Страна обязательна"}).max(63,{message:"Страна не должна превышать 63 символа"}).describe("Страна"),region:a.z.string().min(1,{message:"Область/регион о��язательна"}).max(63,{message:"Область/регион не должна превышать 63 символа"}).describe("Область/регион"),latitude:a.z.number().min(-90,{message:"Широта должна быть не менее -90"}).max(90,{message:"Широта должна быть не более 90"}).describe("Географическая широта"),longitude:a.z.number().min(-180,{message:"Долгота должна быть не менее -180"}).max(180,{message:"Долгота должна быть не более 180"}).describe("Географическая долгота"),isActive:a.z.boolean().describe("Активна ли локация"),popular1:a.z.boolean().optional().describe("Популярная локация 1"),popular2:a.z.boolean().optional().describe("Популярная локация 2")}),i=s.extend({id:a.z.string().uuid({message:"Некорректный формат UUID"}).describe("Идентификатор локации")});a.z.object({data:a.z.array(i).describe("Данные локаций"),totalCount:a.z.number().int().describe("Общее количество записей"),pageSize:a.z.number().int().describe("Размер страницы"),hasPrevious:a.z.boolean().describe("Есть ли предыдущая страница"),hasNext:a.z.boolean().describe("Есть ли следующая страница")}),a.z.object({fromId:a.z.string().uuid({message:"Некорректный формат UUID для fromId"}).describe("Идентификатор начальной локации"),toId:a.z.string().uuid({message:"Некорректный формат UUID для toId"}).describe("Идентификатор конечной локации"),tariffId:a.z.string().uuid({message:"Некорректный формат UUID для tariffId"}).describe("Идентификатор тарифа"),basePrice:a.z.number().min(0,{message:"Базовая цена должна быть не менее 0"}).describe("Базовая цена")})},46420:(e,t,r)=>{"use strict";r.d(t,{NotificationProvider:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\app\\providers\\NotificationProvider.tsx","NotificationProvider")},49567:(e,t,r)=>{"use strict";r.d(t,{DriverSidebar:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call DriverSidebar() from the server but DriverSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\sidebar\\ui\\DriverSidebar.tsx","DriverSidebar")},51709:(e,t,r)=>{"use strict";r.d(t,{gz:()=>s,o6:()=>i,mm:()=>n});var a=r(43888);let l=a.z.enum(["Unknown","OrderCreated","OrderUpdated","OrderConfirmed","OrderCancelled","OrderCompleted","RideRequest","RideAccepted","RideRejected","RideStarted","RideCompleted","RideCancelled","RideUpdate","Payment","PaymentReceived","PaymentFailed","PaymentRefunded","DriverHeading","DriverArrived","DriverAssigned","DriverCancelled","DriverNearby","System","SystemMessage","Maintenance","Promo","PromoOffer","Verification","Chat"]);a.z.enum(["Home","Work","Airport","Station","Hotel","Restaurant","Shop","Entertainment","Medical","Educational","BusinessCenter","Other"]);let s=a.z.object({type:l,title:a.z.string(),content:a.z.string().nullable().optional(),orderId:a.z.string().uuid().nullable().optional(),rideId:a.z.string().uuid().nullable().optional(),isRead:a.z.boolean().optional(),userId:a.z.string().uuid()}),i=a.z.object({type:l,title:a.z.string(),content:a.z.string().nullable().optional(),orderId:a.z.string().uuid().nullable().optional(),rideId:a.z.string().uuid().nullable().optional(),isRead:a.z.boolean().optional(),id:a.z.string().uuid()}),n=a.z.object({id:a.z.string().uuid()})},52177:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,75636,23)),Promise.resolve().then(r.bind(r,92157)),Promise.resolve().then(r.bind(r,86332)),Promise.resolve().then(r.bind(r,69539)),Promise.resolve().then(r.bind(r,78951)),Promise.resolve().then(r.bind(r,38765)),Promise.resolve().then(r.bind(r,57999)),Promise.resolve().then(r.bind(r,26488)),Promise.resolve().then(r.bind(r,66542)),Promise.resolve().then(r.bind(r,55577)),Promise.resolve().then(r.bind(r,41644)),Promise.resolve().then(r.bind(r,46154)),Promise.resolve().then(r.bind(r,16802)),Promise.resolve().then(r.bind(r,84932)),Promise.resolve().then(r.bind(r,45788)),Promise.resolve().then(r.bind(r,65023)),Promise.resolve().then(r.bind(r,29971)),Promise.resolve().then(r.bind(r,31496))},52646:(e,t,r)=>{"use strict";r.d(t,{X:()=>a});var a=function(e){return e.Unknown="Unknown",e.Customer="Customer",e.Admin="Admin",e.Driver="Driver",e.Operator="Operator",e.Partner="Partner",e.Terminal="Terminal",e}({})},52849:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,56082,23)),Promise.resolve().then(r.bind(r,87019)),Promise.resolve().then(r.bind(r,11994)),Promise.resolve().then(r.bind(r,82604)),Promise.resolve().then(r.bind(r,46420)),Promise.resolve().then(r.bind(r,41925)),Promise.resolve().then(r.bind(r,77809)),Promise.resolve().then(r.bind(r,78715)),Promise.resolve().then(r.bind(r,38567)),Promise.resolve().then(r.bind(r,93055)),Promise.resolve().then(r.bind(r,81718)),Promise.resolve().then(r.bind(r,24700)),Promise.resolve().then(r.bind(r,28937)),Promise.resolve().then(r.bind(r,70123)),Promise.resolve().then(r.bind(r,94081)),Promise.resolve().then(r.bind(r,61938)),Promise.resolve().then(r.bind(r,49567)),Promise.resolve().then(r.bind(r,81021))},55577:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useBreadcrumbs:()=>i});var a=r(2984),l=r(60159),s=r(36893);let i=()=>{let e=(0,a.usePathname)();return(0,l.useMemo)(()=>{if(!e||"/"===e)return[];let t=e.split("/").filter(Boolean),r=[],a="";return t.forEach((e,l)=>{a+=`/${e}`;let i=s.h.find(e=>e.path===a),n=l===t.length-1;i?r.push({label:i.title,path:i.path,isActive:n}):r.push({label:e,path:a,isActive:n})}),r},[e])}},56840:(e,t,r)=>{"use strict";r.d(t,{l:()=>p});var a=r(13486),l=r(49989),s=r.n(l),i=r(60159),n=r(38655),o=r(9019),d=r(1106),c=r(86157);let u=(0,i.lazy)(()=>Promise.resolve().then(r.bind(r,2065))),p={columns:[{id:"fullName",label:"Имя",accessor:"fullName",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","endsWith","equals","notEquals"]}},{id:"email",label:"Email",accessor:"email",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]}},{id:"role",label:"Роль",accessor:"role",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,o.mJ)((0,c.nu)(),[d.Xh.Unknown])},renderCell:e=>(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-[color:var(--color-primary-50)] text-[color:var(--color-primary)]",children:(0,c.r4)(e.role)})},{id:"phoneNumber",label:"Телефон",accessor:"phoneNumber",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","isEmpty","isNotEmpty"]},renderCell:e=>(0,a.jsx)("span",{children:e.phoneNumber||(0,a.jsx)("span",{className:"text-[color:var(--color-neutral-400)]",children:"Не указан"})})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(s(),{href:`/users/${e.id}`,className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",onClick:()=>{(0,n.$A)(e.role)},children:(0,a.jsx)(u,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"FullName",label:"Имя",operator:"contains",operatorField:"FullNameOp"},{field:"Email",label:"Email",operator:"contains",operatorField:"EmailOp"},{field:"PhoneNumber",label:"Телефон",operator:"contains",operatorField:"PhoneNumberOp"}]},filtersConfig:{availableFilters:[{field:"Email",label:"Email",type:"text"},{field:"FullName",label:"Полное имя",type:"text"},{field:"PhoneNumber",label:"Телефон",type:"text"},{field:"Role",label:"Роль",type:"select",options:(0,o.mJ)((0,c.nu)(),[d.Xh.Unknown]).map(e=>({value:e.value,label:e.label}))},{field:"Online",label:"Онлайн",type:"boolean"}]}}},57305:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(24903);let l=()=>{let{driverProfile:e,isInitialized:t,isLoading:r,error:l,refreshDriverData:s}=(0,a.g0)();return{profile:e,isLoading:r,isInitialized:t,error:l,refreshProfile:s}}},57999:(e,t,r)=>{"use strict";r.d(t,{TerminalDataProvider:()=>o});var a=r(13486),l=r(60159),s=r(91499),i=r(97127),n=r(24903);let o=({children:e})=>{let[t,r]=(0,l.useState)(null),[o,d]=(0,l.useState)(!1),[c,u]=(0,l.useState)(null),[p,m]=(0,l.useState)(null),[h,g]=(0,l.useState)(!1),[f,b]=(0,l.useState)(null),x=(0,l.useCallback)(async()=>{d(!0),u(null);try{let e=await i.Dv.getTerminalSelfProfile();r(e)}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки профиля пользователя";console.error("❌ Ошибка загрузки профиля пользователя:",t),u(e)}finally{d(!1)}},[]),v=(0,l.useCallback)(async e=>{g(!0),b(null);try{let t=await s.Ny.getById(e);if(!t)throw Error("Локация терминала не найдена");m(t)}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки локации терминала";console.error("❌ Ошибка загрузки локации терминала:",t),b(e)}finally{g(!1)}},[]),y=(0,l.useCallback)(async()=>{await x()},[x]),C=(0,l.useCallback)(async()=>{t?.locationId&&await v(t.locationId)},[t?.locationId,v]),T=(0,l.useCallback)(async()=>{await x(),t?.locationId&&await v(t.locationId)},[x,t?.locationId,v]);(0,l.useEffect)(()=>{x()},[x]),(0,l.useEffect)(()=>{t?.locationId&&!o&&v(t.locationId)},[t?.locationId,o,v]);let N=o||h;return(0,a.jsx)(n.OW.Provider,{value:{profile:t,isProfileLoading:o,profileError:c,terminalLocation:p,isLocationLoading:h,locationError:f,isLoading:N,error:c||f,refetchProfile:y,refetchLocation:C,refetchAll:T},children:e})}},58387:(e,t,r)=>{"use strict";r.d(t,{B8:()=>i,El:()=>s});var a=r(43888);let l=a.z.enum(["Economy","Comfort","ComfortPlus","Business","Premium","Vip","Luxury"]);a.z.object({fromId:a.z.string().uuid(),toId:a.z.string().uuid(),tariffId:a.z.string().uuid(),basePrice:a.z.number()});let s=a.z.object({name:a.z.string().min(1,{message:"Название тарифа обязательно"}).max(127,{message:"Название тарифа не должно превышат�� 127 символов"}).describe("Название тарифа (Эконом, Комфорт)"),serviceClass:l.describe("Класс обслуживания"),basePrice:a.z.number().min(0,{message:"Базовая стоимость не может быть отрицательной"}).describe("Базовая стоимость подачи / минимальная стоимость"),minutePrice:a.z.number().min(0,{message:"Стоимость минуты не может быть отрицательной"}).describe("Стоимость минуты поездки"),minimumPrice:a.z.number().min(0,{message:"Минимальная стоимость не может быть отрицательной"}).describe("Минимальная стоимость поездки по тарифу"),perKmPrice:a.z.number().min(0,{message:"Стоимость километра не может быть отрицательной"}).describe("Стоимость километра поездки (сом)")}),i=s.extend({id:a.z.string().uuid({message:"Некорректный формат UUID"}).describe("Идентификатор тарифа")});a.z.object({data:a.z.lazy(()=>i).optional(),totalCount:a.z.number().int().optional(),pageSize:a.z.number().int().optional(),hasPrevious:a.z.boolean().optional(),hasNext:a.z.boolean().optional()})},58631:(e,t,r)=>{"use strict";r(96274)},58836:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var a=r(32155);function l(e){return({[a.i.Home]:"blue",[a.i.Work]:"green",[a.i.Airport]:"purple",[a.i.Station]:"orange",[a.i.Hotel]:"red",[a.i.Restaurant]:"indigo",[a.i.Shop]:"blue",[a.i.Entertainment]:"green",[a.i.Medical]:"red",[a.i.Educational]:"orange",[a.i.BusinessCenter]:"purple",[a.i.Other]:"gray"})[e]||"blue"}},59152:(e,t,r)=>{"use strict";r.d(t,{Y6:()=>s,az:()=>i,$r:()=>l});var a=r(52646);function l(e,t){return e.filter(e=>{if(["dashboard"].includes(e.id))return!0;switch(t){case a.X.Admin:return!0;case a.X.Partner:return["orders","tariffs"].includes(e.id);case a.X.Driver:return["orders","profile"].includes(e.id);case a.X.Operator:return["orders","users","drivers","tariffs","cars","services","notifications","locations"].includes(e.id);case a.X.Terminal:return["orders","profile"].includes(e.id);default:return!1}})}function s(e,t,r=!1){return e.map(e=>"notifications"===e.id?{...e,hasNotifications:t}:"scheduled-orders"===e.id?{...e,hasNotifications:r}:e)}function i(e,t={}){let{showScheduledOrders:r=!0,showStatistics:a=!0,showSupport:l=!0}=t;return e.filter(e=>{switch(e.id){case"scheduled-orders":return r;case"statistics":return a;case"support":return l;default:return!0}})}},61938:(e,t,r)=>{"use strict";r.d(t,{ModalManagerComponent:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call ModalManagerComponent() from the server but ModalManagerComponent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\modals\\index.tsx","ModalManagerComponent")},64768:(e,t,r)=>{"use strict";r.d(t,{gw:()=>a.gw,k:()=>a.k}),r(1251);var a=r(35028)},65023:(e,t,r)=>{"use strict";r.d(t,{ModalManagerComponent:()=>C});var a=r(13486),l=r(97008),s=r(18187),i=r(38655),n=r(60159),o=r(52025),d=r(63497);let c=()=>{let e=(0,o.k)(),[t,r]=(0,n.useState)(null),[a,l]=(0,n.useState)(!1),s=(0,n.useCallback)(t=>{console.log("\uD83D\uDE97 ПОЛУЧЕН ЗАПРОС НА ПОЕЗДКУ В ХУКЕ:",t),console.log("\uD83D\uDE97 ТЕКУЩЕЕ СОСТОЯНИЕ signalR.isConnected:",e.isConnected);let a=t.data?.waypoints||[],s=a[0]?.location,i=a[a.length-1]?.location,n={rideId:t.rideId,orderId:t.orderId,orderType:t.orderType||"Instant",customerName:t.customerName||"Неизвестный клиент",fromAddress:s?.address||s?.name||"Адрес не указан",toAddress:i?.address||i?.name||"Адрес не указан",price:t.price,distance:t.distance};console.log("\uD83C\uDFAF ОБРАБОТАННЫЕ ДАННЫЕ ДЛЯ МОДАЛКИ:",n),console.log("\uD83C\uDFAF УСТАНАВЛИВАЮ showModal = true"),r(n),l(!0),console.log("\uD83C\uDFAF ПОСЛЕ УСТАНОВКИ - currentRequest:",n),console.log("\uD83C\uDFAF ПОСЛЕ УСТАНОВКИ - showModal: true"),d.P.info(`Новый заказ: ${n.fromAddress} → ${n.toAddress}`)},[e.isConnected]);(0,n.useEffect)(()=>{if(e.isConnected)return e.on("RideRequestNotification",s),()=>{e.off("RideRequestNotification",s)}},[e,e.isConnected,s]);let i=(0,n.useCallback)(()=>{l(!1),r(null)},[]),c=(0,n.useCallback)(()=>{i()},[i]),u=(0,n.useCallback)(()=>{i()},[i]);return{currentRequest:t,showModal:a,closeModal:i,handleAccept:c,handleReject:u}},u=(0,s.default)(()=>r.e(4722).then(r.bind(r,94722)).then(e=>({default:e.CardPaymentModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/CardPaymentModal"]}}),p=(0,s.default)(()=>r.e(5853).then(r.bind(r,45853)).then(e=>({default:e.CompanyDetailsModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/CompanyDetailsModal"]}}),m=(0,s.default)(()=>r.e(9671).then(r.bind(r,49671)).then(e=>({default:e.FAQModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/FAQModal"]}}),h=(0,s.default)(()=>r.e(21).then(r.bind(r,80021)).then(e=>({default:e.PotentialDriversModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/PotentialDriversModal"]}}),g=(0,s.default)(()=>r.e(1680).then(r.bind(r,61680)).then(e=>({default:e.PrivacyPolicyModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/PrivacyPolicyModal"]}}),f=(0,s.default)(()=>r.e(4803).then(r.bind(r,94803)).then(e=>({default:e.PublicOfferModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/PublicOfferModal"]}}),b=(0,s.default)(()=>r.e(5971).then(r.bind(r,85971)).then(e=>({default:e.QRPaymentModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/QRPaymentModal"]}}),x=(0,s.default)(()=>r.e(4309).then(r.bind(r,4309)).then(e=>({default:e.RideRequestModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/RideRequestModal"]}}),v=(0,s.default)(()=>r.e(4790).then(r.bind(r,74790)).then(e=>({default:e.SelectOrderTypeModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/SelectOrderTypeModal"]}}),y=(0,s.default)(()=>r.e(9533).then(r.bind(r,89533)).then(e=>({default:e.SelectUserRoleModal})),{loadableGenerated:{modules:["..\\..\\..\\packages\\widgets\\modals\\index.tsx -> ./ui/SelectUserRoleModal"]}}),C=()=>{let e=(0,l.e3)(i.Gw),t=(0,l.e3)(i.Qx),r=(0,l.e3)(i.Oo),{currentRequest:s,showModal:n,closeModal:o,handleAccept:d,handleReject:C}=c(),T=e.length>0;if(n&&s)return(0,a.jsx)(x,{requestData:s,isOpen:n,onClose:o,onAccept:d,onReject:C});if(!T)return null;switch(t){case i.bY.SelectUserRoleModal:return(0,a.jsx)(y,{});case i.bY.SelectOrderTypeModal:return(0,a.jsx)(v,{onClose:()=>r(void 0)});case i.bY.QRPaymentModal:return(0,a.jsx)(b,{});case i.bY.CardPaymentModal:return(0,a.jsx)(u,{});case i.bY.PotentialDriversModal:return(0,a.jsx)(h,{});case i.bY.CompanyDetailsModal:return(0,a.jsx)(p,{});case i.bY.PublicOfferModal:return(0,a.jsx)(f,{});case i.bY.PrivacyPolicyModal:return(0,a.jsx)(g,{});case i.bY.FAQModal:return(0,a.jsx)(m,{});default:return null}}},66542:(e,t,r)=>{"use strict";r.d(t,{TerminalTariffProvider:()=>u});var a=r(13486),l=r(60159);r(13367);var s=r(78891),i=r(96274),n=r(68671);let o=()=>{let[e,t]=(0,l.useState)([]),[r,a]=(0,l.useState)(!0),[s,o]=(0,l.useState)(null),[d,c]=(0,l.useState)(null);return(0,l.useEffect)(()=>{(async()=>{try{a(!0),o(null),console.log("\uD83D\uDD04 Загружаем тарифы...");let e=await i.uE.get(i.QQ.TARIFF.LIST);if(e.data?.data){let r=e.data.data;t(r);let a=r.find(e=>{let t=e.name?.toLowerCase()||"";return e.serviceClass===n.m.Economy||t.includes("эконом")||t.includes("economy")||t.includes("базовый")})||r[0]||null;c(a),console.log("\uD83D\uDCE6 Загружено тарифов:",r.length),console.log("\uD83C\uDFAF Найден эконом тариф:",a?.name||"не найден")}else t([]),c(null)}catch(e){console.error("❌ Ошибка загрузки тарифов:",e),o("Ошибка загрузки тарифов. Попробуйте обновить страницу."),t([]),c(null)}finally{a(!1)}})()},[]),{tariffs:e,isLoading:r,error:s,economyTariff:d}};r(58387),r(25181),r(13547),r(42419),r(80994),r(88330),r(58631);var d=r(50947);class c extends i.vA{async getTariff(e){let t=await this.get(`/${e}`);return this.handleApiResult(t)}async getTariffSafe(e){let t=await this.get(`/${e}`);return this.handleApiResultSafe(t)}async getTariffs(e){let t=new URLSearchParams;e?.first!==void 0&&t.append("first",e.first.toString()),e?.before&&t.append("before",e.before),e?.after&&t.append("after",e.after),e?.last!==void 0&&t.append("last",e.last.toString()),e?.size!==void 0&&t.append("size",e.size.toString()),e?.search&&t.append("search",e.search),e?.isActive!==void 0&&t.append("isActive",e.isActive.toString());let r=t.toString()?`?${t.toString()}`:"",a=await this.get(r);return this.handleApiResult(a)}async createTariff(e){let t=await this.post("",e);return this.handleApiResult(t)}async updateTariff(e,t){let r=await this.put(`/${e}`,t);return this.handleApiResult(r)}async deleteTariff(e){let t=await this.delete(`/${e}`);this.handleApiResult(t)}async toggleTariffStatus(e,t){let r=await this.patch(`/${e}/status`,{isActive:t});return this.handleApiResult(r)}async calculatePrice(e,t,r){let a=await this.post(`/${e}/calculate`,{distance:t,duration:r});return this.handleApiResult(a)}constructor(...e){super(...e),this.baseUrl=d.QQ.TARIFF.LIST}}new c;let u=({children:e})=>{let{tariffs:t,isLoading:r,error:l,economyTariff:i}=o();return(0,a.jsx)(s.C.Provider,{value:{economyTariff:i,tariffs:t,isLoading:r,error:l},children:e})}},66971:(e,t,r)=>{"use strict";r.d(t,{TM:()=>l.TM,Vh:()=>l.Vh,ah:()=>l.JV,l1:()=>i,Z$:()=>n,vP:()=>a.vP,LP:()=>a.LP,jY:()=>o});var a=r(42429),l=r(3775),s=r(29457);function i(e){switch(e){case s.lk.SearchingDriver:return"Поиск водителя";case s.lk.DriverAssigned:return"Водитель назначен";case s.lk.DriverHeading:return"Водитель в пути";case s.lk.DriverArrived:return"Водитель прибыл";case s.lk.RideStarted:return"Поездка началась";case s.lk.RideFinished:return"Поездка завершена";case s.lk.PaymentPending:return"Ожидание оплаты";case s.lk.PaymentCompleted:return"Оплата завершена";case s.lk.ReviewPending:return"Ожидание отзыва";case s.lk.CancelledByClient:return"Отменено клиентом";case s.lk.CancelledByDriver:return"Отменено водителем";case s.lk.CancelledBySystem:return"Отменено системой";case s.lk.CancelledByOperator:return"Отменено оператором";default:return""}}function n(){return Object.values(s.lk).map(e=>({value:e,label:i(e)}))}function o(e){switch(e){case s.VQ.Requested:return"Запрошена";case s.VQ.Searching:return"Поиск водителя";case s.VQ.Accepted:return"Принята водителем";case s.VQ.Arrived:return"Водитель прибыл";case s.VQ.InProgress:return"В процессе";case s.VQ.Completed:return"Завершена";case s.VQ.Cancelled:return"Отменена";default:return""}}},68290:(e,t,r)=>{"use strict";r.d(t,{e:()=>n});var a=r(96274),l=r(75111),s=r(50947);class i extends a.vA{async getLocation(e){let t=await this.get(`/${e}`);return this.handleApiResult(t)}async getLocationSafe(e){let t=await this.get(`/${e}`);return this.handleApiResultSafe(t)}async getById(e){return this.getLocation(e)}async getAll(e){return this.getLocations(e)}async getPopularLocations(e){let t={size:100,popular1:!0,isActive:!0};return e?.trim()&&(t.ftsPlain=e.trim(),delete t.popular1),this.getLocations(t)}async getLocations(e){let t=new URLSearchParams;e?.first!==void 0&&t.append("first",e.first.toString()),e?.before&&t.append("before",e.before),e?.after&&t.append("after",e.after),e?.last!==void 0&&t.append("last",e.last.toString()),e?.size!==void 0&&t.append("size",e.size.toString()),e?.popular1!==void 0&&t.append("Popular1",e.popular1.toString()),e?.popular2!==void 0&&t.append("Popular2",e.popular2.toString()),e?.isActive!==void 0&&t.append("IsActive",e.isActive.toString()),e?.ftsPlain&&t.append("FTS.Plain",e.ftsPlain),e?.type&&(Array.isArray(e.type)?e.type.forEach(e=>t.append("Type",e)):t.append("Type",e.type)),e?.city&&(Array.isArray(e.city)?e.city.forEach(e=>t.append("City",e)):t.append("City",e.city),t.append("CityOp",e.cityOp||"Equals")),e?.address&&(Array.isArray(e.address)?e.address.forEach(e=>t.append("Address",e)):t.append("Address",e.address),t.append("AddressOp",e.addressOp||"StartsWith")),e?.district&&(Array.isArray(e.district)?e.district.forEach(e=>t.append("District",e)):t.append("District",e.district),t.append("DistrictOp",e.districtOp||"Equals")),e?.region&&(Array.isArray(e.region)?e.region.forEach(e=>t.append("Region",e)):t.append("Region",e.region),t.append("RegionOp",e.regionOp||"Equals"));let r=t.toString()?`?${t.toString()}`:"",a=await this.get(r);return this.handleApiResult(a)}async createLocation(e){let t=await this.post("",e);return this.handleApiResult(t)}async updateLocation(e,t){let r=await this.put(`/${e}`,t);return this.handleApiResult(r)}async deleteLocation(e){let t=await this.delete(`/${e}`);this.handleApiResult(t)}async updateCurrentLocation(e){console.log("\uD83D\uDCE1 Отправка координат водителя на сервер:",e);let t=await (0,l.$P)("/Location/CurrentLocation/self",{latitude:e.latitude,longitude:e.longitude});this.handleApiResult(t),console.log("✅ Координаты водителя успешно отправлены на сервер")}async getActiveDrivers(e){let t=await this.get("/GIS/ActiveDrivers",{params:{LatFrom:e.latFrom,LatTo:e.latTo,LongFrom:e.longFrom,LongTo:e.longTo}});return this.handleApiResult(t)}constructor(...e){super(...e),this.baseUrl=s.QQ.LOCATION.LIST}}let n=new i},68671:(e,t,r)=>{"use strict";r.d(t,{m:()=>a});var a=function(e){return e.Economy="Economy",e.Comfort="Comfort",e.ComfortPlus="ComfortPlus",e.Business="Business",e.Premium="Premium",e.Vip="Vip",e.Luxury="Luxury",e}({})},69539:(e,t,r)=>{"use strict";r.d(t,{DriverQueueProvider:()=>c});var a=r(13486),l=r(60159),s=r(63497),i=r(96274);class n extends i.vA{async getMyPosition(){try{let e=await this.get("self");return this.handleApiResultSafe(e)}catch(e){return e instanceof Error&&e.message.includes("404")?console.warn("⚠️ DriverQueue API endpoint не найден (404). Возможно, функция очереди отключена на сервере."):console.error("❌ Ошибка получения позиции в очереди:",e),null}}async join(){try{let e=await this.post("self");return this.handleApiResult(e)}catch(e){if(e instanceof Error&&e.message.includes("404"))throw Error("Функция очереди временно недоступна. Обратитесь к администратору.");throw e}}async leave(){try{let e=await this.delete("self");this.handleApiResult(e)}catch(e){if(e instanceof Error&&e.message.includes("404"))return void console.warn("⚠️ DriverQueue API endpoint не найден (404). Считаем что водитель уже не в очереди.");throw e}}constructor(...e){super(...e),this.baseUrl="/DriverQueue"}}let o=new n;var d=r(26134);let c=({children:e,refreshInterval:t=3e4})=>{let[r,i]=(0,l.useState)(!1),[n,c]=(0,l.useState)(null),[u,p]=(0,l.useState)(null),[m,h]=(0,l.useState)(!1),[g,f]=(0,l.useState)(!1),[b,x]=(0,l.useState)(null),v=(0,l.useCallback)(async()=>{try{h(!0),x(null),console.log("\uD83D\uDD0D Проверяем статус очереди через rideService...");let e=await o.getMyPosition();e?(i(!0),c(e.position),p(e.joinedAt),console.log("✅ Водитель в очереди, позиция:",e.position)):(i(!1),c(null),p(null),console.log("ℹ️ Водитель не в очереди"))}catch(t){let e=t instanceof Error?t.message:"Ошибка проверки статуса очереди";console.error("❌ Ошибка проверки статуса очереди через rideService:",t),x(e)}finally{h(!1)}},[]),y=(0,l.useCallback)(async()=>{try{f(!0),x(null),console.log("\uD83D\uDEB6‍♂️ Встаем в очередь через rideService...");let e=await o.join();i(!0),p(e.joinedAt),await v(),s.P.success("Вы встали в очередь"),console.log("✅ Успешно встали в очередь")}catch(t){let e=t instanceof Error?t.message:"Ошибка входа в очередь";console.error("❌ Ошибка входа в очередь через rideService:",t),x(e),s.P.error(e)}finally{f(!1)}},[v]),C=(0,l.useCallback)(async()=>{try{f(!0),x(null),console.log("\uD83D\uDEAA Выходим из очереди через rideService..."),await o.leave(),i(!1),c(null),p(null),s.P.success("Вы вышли из очереди"),console.log("✅ Успешно вышли из очереди")}catch(t){let e=t instanceof Error?t.message:"Ошибка выхода из очереди";console.error("❌ Ошибка выхода из очереди через rideService:",t),x(e),s.P.error(e)}finally{f(!1)}},[]),T=(0,l.useCallback)(async()=>{await v()},[v]);return(0,l.useEffect)(()=>{v()},[v]),(0,l.useEffect)(()=>{if(t>0&&r){let e=setInterval(v,t);return()=>clearInterval(e)}},[t,r,v]),(0,a.jsx)(d.N.Provider,{value:{isInQueue:r,position:n,joinedAt:u,isLoading:m,isActionLoading:g,error:b,actions:{joinQueue:y,leaveQueue:C,refresh:T}},children:e})}},70123:(e,t,r)=>{"use strict";r.d(t,{DriverHeader:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call DriverHeader() from the server but DriverHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\header\\ui\\DriverHeader.tsx","DriverHeader")},70873:(e,t,r)=>{"use strict";r.d(t,{CU:()=>s,_J:()=>a});let a=[{key:"order",label:"О заказе",icon:"\uD83D\uDD14",bgColor:"bg-[#FFCD2833]",borderColor:"border-[#1184FF]",textColor:"",badgeColor:"bg-red-500",types:["OrderCreated","OrderUpdated","OrderConfirmed","OrderCancelled","RideRequest","RideAccepted","RideStarted","RideCancelled","DriverAssigned","DriverArrived","DriverHeading"]},{key:"important",label:"Важная информация",icon:"ℹ️",bgColor:"bg-[#0047FF33]",borderColor:"border-[#1184FF]",textColor:"",badgeColor:"bg-red-500",types:["System","SystemMessage","Maintenance","Verification","Payment","PaymentReceived"]},{key:"warning",label:"Предупреждение",icon:"⚠️",bgColor:"bg-[#FF846633]",borderColor:"border-[#1184FF]",textColor:"",badgeColor:"bg-red-500",types:["PaymentFailed","DriverCancelled","RideCancelled","OrderCancelled","RideRejected"]},{key:"completed",label:"Завершенные",icon:"✅",bgColor:"bg-green-50",borderColor:"border-[#1184FF]",textColor:"",badgeColor:"bg-red-500",types:["OrderCompleted","RideCompleted"]}],l={RideRequest:"order",RideAccepted:"order",RideStarted:"order",DriverAssigned:"order",DriverArrived:"order",DriverHeading:"order",OrderCreated:"order",OrderUpdated:"order",OrderConfirmed:"order",RideCompleted:"completed",OrderCompleted:"completed",PaymentReceived:"important",System:"important",SystemMessage:"important",Maintenance:"important",Verification:"important",Payment:"important",RideRejected:"warning",RideCancelled:"warning",OrderCancelled:"warning",DriverCancelled:"warning",PaymentFailed:"warning"},s=e=>l[e]||"order"},71483:(e,t,r)=>{"use strict";r.d(t,{D:()=>s,m:()=>i});var a=r(1106),l=r(85787);let s={[a.Xh.Admin]:l.g9,[a.Xh.Customer]:l.Ht,[a.Xh.Driver]:l.bU,[a.Xh.Operator]:l.FS,[a.Xh.Partner]:l.nb,[a.Xh.Terminal]:l.Z8,[a.Xh.Unknown]:l.hF},i=l.hF},72235:(e,t,r)=>{"use strict";r.d(t,{rq:()=>l.rq,g0:()=>a.g0}),r(97127),r(57305),r(60159),r(18602),r(85787),r(71483),r(44891);var a=r(24903);r(1106),r(86157),r(13510),r(56840);var l=r(35293)},76575:(e,t,r)=>{"use strict";r.d(t,{a:()=>s,t:()=>l});var a=r(5224);let l={base:a.d1,create:a.fU,get:a.$Z,read:a.tn},s=a.d1},77289:(e,t,r)=>{"use strict";r.d(t,{c:()=>u});var a=r(13486),l=r(49989),s=r.n(l),i=r(60159),n=r(9019),o=r(1251),d=r(64768);let c=(0,i.lazy)(()=>Promise.resolve().then(r.bind(r,2065))),u={columns:[{id:"title",label:"Заголовок",accessor:"title",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,n.mJ)((0,d.k)(),[o._n.Unknown])},renderCell:e=>(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-[color:var(--color-primary-50)] text-[color:var(--color-primary)]",children:(0,d.gw)(e.type)})},{id:"content",label:"Содержание",accessor:"content",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","isEmpty","isNotEmpty"]},renderCell:e=>(0,a.jsx)("span",{className:"line-clamp-2",children:e.content||(0,a.jsx)("span",{className:"text-[color:var(--color-neutral-400)]",children:"Нет содержания"})})},{id:"isRead",label:"Прочитано",accessor:"isRead",sortable:!0,filterable:!0,filterConfig:{fieldType:"boolean"},renderCell:e=>(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${e.isRead?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:e.isRead?"Да":"Нет"})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(s(),{href:`/notifications/${e.id}`,className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,a.jsx)(c,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"Title",label:"Заголовок",operator:"contains",operatorField:"TitleOp"},{field:"Content",label:"Содержание",operator:"contains",operatorField:"ContentOp"}]},filtersConfig:{availableFilters:[{field:"Type",label:"Тип уведомления",type:"select",options:(0,n.mJ)((0,d.k)(),[o._n.Unknown]).map(e=>({value:e.value,label:e.label}))},{field:"IsRead",label:"Статус прочтения",type:"boolean"}]}}},77809:(e,t,r)=>{"use strict";r.d(t,{TerminalDataProvider:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call TerminalDataProvider() from the server but TerminalDataProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\app\\providers\\TerminalDataProvider.tsx","TerminalDataProvider")},78715:(e,t,r)=>{"use strict";r.d(t,{TerminalLocationsProvider:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call TerminalLocationsProvider() from the server but TerminalLocationsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\app\\providers\\TerminalLocationsProvider.tsx","TerminalLocationsProvider")},78891:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,Y:()=>s});var a=r(60159);let l=(0,a.createContext)(void 0),s=()=>{let e=(0,a.useContext)(l);if(void 0===e)throw Error("useTerminalTariff must be used within a TerminalTariffProvider");return e}},78951:(e,t,r)=>{"use strict";r.d(t,{NotificationProvider:()=>c});var a=r(13486),l=r(60159),s=r(52025),i=r(70873),n=r(31071);let o={RideRequest:1,OrderCreated:1,RideAccepted:2,DriverAssigned:3,DriverArrived:4,DriverHeading:4,RideStarted:5,OrderConfirmed:5,OrderUpdated:5,RideCompleted:10,OrderCompleted:10,RideCancelled:9,OrderCancelled:9,DriverCancelled:8,RideRejected:7,PaymentReceived:100,PaymentFailed:100,System:100,SystemMessage:100,Maintenance:100,Verification:100,Payment:100};var d=r(20864);let c=({children:e})=>{let t=(0,s.k)(),{notifications:r,isLoading:c,isLoadingMore:u,error:p,hasMore:m,totalCount:h,unreadCount:g,actions:{loadNotifications:f,loadMore:b,refresh:x,markAsRead:v,deleteNotification:y}}=(0,d.E)(20),[C,T]=(0,l.useState)(0);console.log("\uD83D\uDD04 NotificationProvider состояние:",{notificationsCount:r.length,isLoading:c,isLoadingMore:u,hasMore:m,totalCount:h,unreadCount:g,realTimeUnreadCount:C});let N=(0,l.useCallback)(()=>(function(e){let t=new Map,r=[];e.forEach(e=>{if(!e.orderId||o[e.type]>=100)return void r.push(e);t.has(e.orderId)||t.set(e.orderId,[]),t.get(e.orderId).push(e)});let a=[];return t.forEach(e=>{let t=e.sort((e,t)=>{let r=o[e.type]||0,a=o[t.type]||0;return r!==a?a-r:new Date(t.createdAt||0).getTime()-new Date(e.createdAt||0).getTime()});a.push(t[0])}),a.push(...r),a.sort((e,t)=>new Date(t.createdAt||0).getTime()-new Date(e.createdAt||0).getTime())})(r),[r]),w=(0,l.useCallback)(()=>{let e=N(),t={order:0,completed:0,important:0,warning:0},r=0;return e.forEach(e=>{if(!e.isRead){let a=(0,i.CU)(e.type);t[a]++,r++}}),{priorityCounts:t,totalUnread:r,totalCount:e.length}},[N]),S=(0,l.useCallback)(async e=>{try{await v(e),T(e=>Math.max(0,e-1)),console.log("✅ NotificationProvider.markAsRead успешно:",e)}catch(e){console.error("❌ NotificationProvider.markAsRead ошибка:",e)}},[v]),R=(0,l.useCallback)(async e=>{try{let t=r.find(t=>t.id===e),a=t&&!t.isRead;await y(e),a&&T(e=>Math.max(0,e-1)),console.log("\uD83D\uDDD1️ NotificationProvider.deleteNotification успешно:",e)}catch(e){console.error("❌ NotificationProvider.deleteNotification ошибка:",e)}},[y,r]);(0,l.useEffect)(()=>{if(t.connection&&t.isConnected){let e=e=>{console.log("\uD83D\uDCE8 NotificationProvider: получено новое уведомление через SignalR"),T(e=>e+1),x()},r=["RideRequestNotification","RideAcceptedNotification","RideAssignedNotification","RideCancelledNotification","RideCompletedNotification","DriverActivityUpdated","DriverArrivedNotification","DriverHeadingNotification","DriverCancelledNotification","OrderConfirmedNotification","OrderCancelledNotification","OrderCompletedNotification","RideRejectedNotification","RideStartedNotification","PaymentNotification","PaymentReceivedNotification","PaymentFailedNotification"];return r.forEach(r=>{t.on(r,e)}),()=>{r.forEach(r=>{t.off(r,e)})}}},[t,x]),(0,l.useEffect)(()=>{console.log("\uD83D\uDE80 NotificationProvider: автоматическая загрузка уведомлений"),f(!1)},[f]);let j=w(),A={notifications:N(),hasUnreadNotifications:g+C>0,unreadCount:g+C,unreadCountsByPriority:j.priorityCounts,isLoading:c,isLoadingMore:u,error:p,hasMore:m,totalCount:j.totalCount,originalTotalCount:h,actions:{loadMore:b,refresh:x,markAsRead:S,deleteNotification:R,markAllAsRead:async()=>{console.log("TODO: Implement markAllAsRead")},markAllAsReadByPriority:async e=>{console.log("TODO: Implement markAllAsReadByPriority")},loadMoreByPriority:e=>{console.log("TODO: Implement loadMoreByPriority")},loadMoreIfNeeded:()=>{console.log("TODO: Implement loadMoreIfNeeded")},markAsReadById:S,markAsReadByType:async e=>{console.log("TODO: Implement markAsReadByType")}}};return(0,a.jsx)(n.V.Provider,{value:A,children:e})}},79645:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,I:()=>s});var a=r(60159);let l=(0,a.createContext)({selectedLocations:[],addLocation:()=>{},removeLocation:()=>{},clearLocations:()=>{},allLocations:[],isLoadingLocations:!1,locationsError:null,currentRegionSlug:null,searchLocations:async()=>[],getLocationById:()=>void 0,fetchLocationById:async()=>null,loadLocations:async e=>{},reloadLastLocations:async()=>{},reloadRegionLocations:async()=>{}}),s=()=>(0,a.useContext)(l)},79940:(e,t,r)=>{"use strict";r.d(t,{_n:()=>s,al:()=>l});var a=r(89337);function l(e){switch(e){case a.i.Home:return"Дом";case a.i.Work:return"Работа";case a.i.Airport:return"Аэропорт";case a.i.Station:return"Вокзал";case a.i.Hotel:return"Отель";case a.i.Restaurant:return"Ресторан";case a.i.Shop:return"Магазин";case a.i.Entertainment:return"Развлекательное заведение";case a.i.Medical:return"Медицинское учреждение";case a.i.Educational:return"Образовательное учреждение";case a.i.BusinessCenter:return"Бизнес-центр";case a.i.Other:return"Другое";default:return""}}function s(){return Object.values(a.i).map(e=>({value:e,label:l(e)}))}},80994:(e,t,r)=>{"use strict";r.d(t,{G:()=>d});var a=r(13486),l=r(49989),s=r.n(l),i=r(60159),n=r(88330);let o=(0,i.lazy)(()=>Promise.resolve().then(r.bind(r,2065))),d={columns:[{id:"name",label:"Название",accessor:"name",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"serviceClass",label:"Класс обслуживания",accessor:"serviceClass",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,n.cx)()},renderCell:e=>(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-[color:var(--color-primary-50)] text-[color:var(--color-primary)]",children:(0,n.mE)(e.serviceClass)})},{id:"basePrice",label:"Базовая стоимость",accessor:"basePrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,a.jsxs)("span",{className:"font-medium",children:[e.basePrice.toLocaleString("ru-RU")," сом"]})},{id:"minutePrice",label:"Стоимость минуты",accessor:"minutePrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,a.jsxs)("span",{className:"font-medium",children:[e.minutePrice.toLocaleString("ru-RU")," сом"]})},{id:"minimumPrice",label:"Минимальная стоимость",accessor:"minimumPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,a.jsxs)("span",{className:"font-medium",children:[e.minimumPrice.toLocaleString("ru-RU")," сом"]})},{id:"perKmPrice",label:"Стоимость километра",accessor:"perKmPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,a.jsxs)("span",{className:"font-medium",children:[e.perKmPrice.toLocaleString("ru-RU")," сом/км"]})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(s(),{href:`/tariffs/${e.id}`,className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,a.jsx)(o,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"Name",label:"Название",operator:"contains",operatorField:"NameOp"}]},filtersConfig:{availableFilters:[{field:"ServiceClass",label:"Класс обслуживания",type:"select",options:(0,n.cx)().map(e=>({value:e.value,label:e.label}))},{field:"BasePrice",label:"Базовая стоимость",type:"number"},{field:"MinutePrice",label:"Стоимость минуты",type:"number"},{field:"MinimumPrice",label:"Минимальная стоимость",type:"number"},{field:"PerKmPrice",label:"Стоимость километра",type:"number"}]}}},81021:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\sidebar\\ui\\Sidebar.tsx","Sidebar")},81718:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useDriverHeader:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call useDriverHeader() from the server but useDriverHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\header\\hooks\\useDriverHeader.ts","useDriverHeader")},82092:(e,t,r)=>{"use strict";r.d(t,{Wj:()=>s,UF:()=>i.U});var a=r(2984),l=r(60159);let s=e=>{let[t,r]=(0,l.useState)(""),s=(0,a.usePathname)();return(0,l.useEffect)(()=>{let t=e.find(e=>s===e.path||s.startsWith(`${e.path}/`));t&&r(t.id)},[s,e]),t};r(52025);var i=r(32270)},82604:(e,t,r)=>{"use strict";r.d(t,{DriverQueueProvider:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call DriverQueueProvider() from the server but DriverQueueProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\app\\providers\\DriverQueueProvider.tsx","DriverQueueProvider")},84932:(e,t,r)=>{"use strict";r.d(t,{DriverHeader:()=>d});var a=r(13486);r(60159);var l=r(5684);let s=({className:e=""})=>(0,a.jsx)("header",{className:`bg-white shadow-sm border-b border-gray-200 ${e}`,children:(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"flex flex-row items-center gap-4 justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col items-start space-x-4 p-6",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse mb-2"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full animate-pulse"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 animate-pulse"})]})]})]}),(0,a.jsxs)("div",{className:"hidden md:flex flex-col items-start space-y-2",children:[(0,a.jsx)("div",{className:"h-5 bg-gray-200 rounded w-20 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-28 animate-pulse"})]}),(0,a.jsx)("div",{className:"hidden lg:block",children:(0,a.jsx)("div",{className:"w-48 h-24 bg-gray-200 rounded animate-pulse"})})]})})});var i=r(49933);let n=({profile:e,displayName:t,displayPhone:r,avatarInitials:l,isActive:s,className:n=""})=>(0,a.jsxs)("div",{className:`flex-1 flex flex-col items-start space-x-4 p-6 ${n}`,children:[(0,a.jsx)("div",{className:"text-sm text-gray-500 font-medium mb-2",children:"Водитель"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center overflow-hidden",children:e?.avatar?(0,a.jsx)(i.default,{src:e.avatar,alt:"Аватар водителя",width:64,height:64,className:"w-full h-full rounded-full object-cover",onError:()=>{console.warn("Ошибка загрузки аватара:",e.avatar)},placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="}):(0,a.jsx)("span",{className:"text-white text-xl font-bold",children:l})}),(0,a.jsx)("div",{className:`
            absolute -bottom-1 -right-1 w-5 h-5 border-2 border-white rounded-full
            ${s?"bg-green-500":"bg-gray-400"}
          `})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold  w-[300px]",children:t}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:r})]})]})]}),o=({vehicle:e,carImagePath:t,hasVehicleInfo:r,className:l=""})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:`flex-1 hidden md:flex flex-col items-start space-y-1 ${l}`,children:r&&e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-lg font-bold",children:e.licensePlate}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.category," - ",e.bodyType]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.make," ",e.model]})]}):(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Автомобиля нет"})}),(0,a.jsx)("div",{className:"hidden lg:block relative flex-2 h-[196px]",children:r&&e?(0,a.jsx)(i.default,{src:"/car/HongQi E-QM5.png",alt:`${e.make} ${e.model}`,fill:!0,style:{objectFit:"contain"},onError:()=>{console.warn("Ошибка загрузки изображения автомобиля:",t)}}):(0,a.jsx)("div",{className:"text-6xl",children:"\uD83D\uDE97"})})]}),d=({className:e=""})=>{let{profile:t,isLoading:r,isActive:i,carImagePath:d,displayName:c,displayPhone:u,avatarInitials:p,hasVehicleInfo:m}=(0,l.V$)();return r?(0,a.jsx)(s,{className:e}):(0,a.jsx)("header",{className:`rounded-2xl ${e}`,children:(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(n,{profile:t,displayName:c,displayPhone:u,avatarInitials:p,isActive:i}),(0,a.jsx)(o,{vehicle:t?.vehicle||null,carImagePath:d,hasVehicleInfo:m})]})})})}},85787:(e,t,r)=>{"use strict";r.d(t,{g9:()=>s,hF:()=>l,Ht:()=>i,bU:()=>u,FS:()=>p,nb:()=>h,Z8:()=>g});var a=r(66616);let l={title:{create:"Создание нового пользователя",edit:"Редактирование пользователя"},groups:[{id:"main",title:"Основная информация",order:1,layout:{gridCols:2,gapX:8,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"photoGroup",type:a.V.GROUP,label:"Фото профиля",description:"Загрузите фотографию пользователя",layout:{flexDirection:"column",gapY:4,itemsAlignment:"center",className:"flex-1 flex flex-col justify-between gap-4"},fields:[{name:"avatarUrl",type:a.V.IMAGE,helperSmallText:"JPG, PNG до 5 МБ",helperBigText:"Загрузите фотографию пользователя в формате JPG или PNG. Максимальный размер файла - 5 МБ. Рекомендуемое соотношение сторон 1:1.",className:"w-full h-full"}]},{name:"contactGroup",type:a.V.GROUP,label:"Контактная информация",description:"Основные контактные данные пользователя для связи и идентификации в системе",layout:{flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"email",label:"Email",type:a.V.EMAIL,placeholder:"Введите email пользователя",required:!0,helperSmallText:"Email для входа",helperBigText:"Будет использоваться для входа в систему и восстановления пароля. Должен быть уникальным."},{name:"fullName",label:"Полное имя",type:a.V.TEXT,placeholder:"Введите полное имя пользователя",required:!0,helperSmallText:"ФИО пользователя",helperBigText:"Полное имя пользователя, которое будет отображаться в системе"},{name:"phoneNumber",label:"Телефон",type:a.V.PHONE,placeholder:"Введите номер телефона",required:!1,helperSmallText:"Телефон для связи",helperBigText:"Контактный телефон для связи с пользователем. Используется для уведомлений и подтверждений."}]}]},{id:"security",title:"Безопасность",order:2,description:"Настройки безопасности и доступа администратора",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"center"},fields:[{name:"password",label:"Пароль",type:a.V.PASSWORD,placeholder:"Введите пароль",required:!0,helperSmallText:"Минимум 8 символов",helperBigText:`Требования к паролю:
- Минимум 8 символов
- Хотя бы одна заглавная буква
- Хотя бы одна строчная буква
- Хотя бы одна цифра
- Хотя бы один специальный символ

Пароль должен содержать не менее 8 символов, включая заглавные и строчные буквы, цифры и специальные символы для обеспечения безопасности.`},{name:"confirmPassword",label:"Подтверждение пароля",type:a.V.PASSWORD,placeholder:"Повторите пароль",required:!0,isServiceField:!0,helperSmallText:"Повторите пароль",helperBigText:"Введите пароль повторно для подтверждения. Это поле используется только для проверки правильности ввода и не отправляется на сервер."}]}]},s={...l,title:{create:"Создание нового администратора",edit:"Редактирование администратора"},description:{create:"Заполните информацию для создания нового администратора системы",edit:"Редактирование информации администратора системы"},groups:[{...l.groups[0],description:"Основные данные и контактная информация администратора",fields:[...l.groups[0].fields]},{...l.groups[1],description:"Настройки безопасности и доступа администратора",fields:[...l.groups[1].fields,{name:"profile.accessLevel",label:"Уровень доступа",type:a.V.SELECT,placeholder:"Выберите уровень доступа",required:!0,options:[{value:"full",label:"Полный доступ"},{value:"limited",label:"Ограниченный доступ"},{value:"readonly",label:"Только чтение"}],helperSmallText:"Права доступа",helperBigText:`Определяет уровень прав и доступных функций администратора в системе. От этого зависит, какие действия может выполнять пользователь.

Доступные варианты:
- Полный доступ: все функции системы без ограничений
- Ограниченный доступ: базовые функции администрирования
- Только чтение: просмотр данных без возможности изменения`,className:"flex-1"}]},{id:"employment",title:"Информация о работе",order:3,description:"Данные о должности и трудоустройстве",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"profile.department",label:"Отдел",type:a.V.TEXT,placeholder:"Введите отдел",helperSmallText:"Отдел",helperBigText:"Отдел, в котором работает администратор"},{name:"profile.position",label:"Должность",type:a.V.TEXT,placeholder:"Введите должность",helperSmallText:"Должность",helperBigText:"Должность администратора в компании"},{name:"profile.hireDate",label:"Дата найма",type:a.V.DATE,helperSmallText:"Дата найма",helperBigText:"Дата начала работы администратора в компании"},{name:"profile.employeeId",label:"Табельный номер",type:a.V.TEXT,placeholder:"Введите табельный номер",helperSmallText:"Табельный номер",helperBigText:"Уникальный идентификатор сотрудника в системе"}]}]},i={...l,title:{create:"Создание нового клиента",edit:"Редактирование клиента"},description:{create:"Заполните информацию для создания нового клиента в системе",edit:"Редактирование информации клиента в системе"},groups:[{...l.groups[0],description:"Основные данные и контактная информация клиента",fields:[...l.groups[0].fields]},{...l.groups[1],description:"Настройки безопасности и доступа клиента",fields:[...l.groups[1].fields]},{id:"customerProfile",title:"Профиль клиента",order:3,description:"Дополнительная информация и настройки клиента",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"loyaltyPoints",label:"Баллы лояльности",type:a.V.NUMBER,placeholder:"Введите количество баллов",min:0,helperSmallText:"Баллы",helperBigText:"Количество накопленных баллов лояльности клиента"},{name:"phantom",label:"Фантомный аккаунт",type:a.V.CHECKBOX,helperSmallText:"Фантомный",helperBigText:"Отметьте, если это фантомный аккаунт для тестирования"}]}]};var n=r(96274),o=r(39314),d=r(58836),c=r(86157);let u={...l,title:{create:"Создание нового водителя",edit:"Редактирование водителя"},description:{create:"Заполните информацию для создания нового водителя в системе",edit:"Редактирование информации водителя в системе"},groups:[{...l.groups[0],description:"Основные данные и контактная информация водителя",fields:[...l.groups[0].fields]},{...l.groups[1],description:"Настройки безопасности и доступа водителя",fields:[...l.groups[1].fields]},{id:"driverBasic",title:"Основная информация водителя",order:3,description:"Статус и основные параметры водителя",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"verificationStatus",label:"Статус верификации",type:a.V.SELECT,placeholder:"Выберите статус верификации",required:!0,options:[{value:"Pending",label:"Ожидает проверки"},{value:"Verified",label:"Проверен и подтвержден"},{value:"Rejected",label:"Отклонен"},{value:"InReview",label:"На рассмотрении"},{value:"Expired",label:"Истек срок действия"}],helperSmallText:"Статус проверки",helperBigText:"Текущий статус проверки документов и данных водителя"}]},{id:"driverPassport",title:"Паспортные данные",order:4,description:"Данные документа, удостоверяющего личность водителя",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"passportNumberGroup",type:a.V.GROUP,label:"Серия и номер",description:"Тип документа, серия, номер и кем выдан документ, удостоверяющий личность",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.passport.identityType",label:"Тип документа",type:a.V.SELECT,placeholder:"Выберите тип документа",required:!0,options:[{value:"NationalPassport",label:"Внутренний паспорт"},{value:"InternationalPassport",label:"Загранпаспорт"},{value:"IdCard",label:"Идентификационная карта"},{value:"ResidencePermit",label:"Вид на жительство"},{value:"RefugeeId",label:"Удостоверение беженца"},{value:"TemporaryId",label:"Временное удостоверение личности"},{value:"MilitaryId",label:"Военный билет"},{value:"ForeignPassport",label:"Иностранный паспорт"},{value:"DriversLicense",label:"Водительское удостоверение"}],helperSmallText:"Тип документа",helperBigText:"Тип документа, удостоверяющего личность водителя"},{name:"profile.passport.series",label:"Серия паспорта",type:a.V.TEXT,placeholder:"Введите серию паспорта",helperSmallText:"Серия документа",helperBigText:"Серия документа, удостоверяющего личность",pattern:"^\\d{4}$",helperText:"Серия паспорта должна состоять из 4 цифр"},{name:"profile.passport.number",label:"Номер паспорта",type:a.V.TEXT,placeholder:"Введите номер паспорта",required:!0,helperSmallText:"Номер документа",helperBigText:"Номер документа, удостоверяющего личность",pattern:"^\\d{6}$",helperText:"Номер паспорта должен состоять из 6 цифр"},{name:"profile.passport.issuedBy",label:"Кем выдан паспорт",type:a.V.TEXT,placeholder:"Введите кем выдан паспорт",required:!0,helperSmallText:"Кем выдан",helperBigText:"Орган, выдавший документ, удостоверяющий личность"}]},{name:"passportDatesGroup",type:a.V.GROUP,label:"Даты и выдача",description:"Даты выдачи и окончания срока действия документа",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.passport.issueDate",label:"Дата выдачи паспорта",type:a.V.DATE,required:!0,helperSmallText:"Дата выдачи",helperBigText:"Дата выдачи документа, удостоверяющего личность"},{name:"profile.passport.expiryDate",label:"Дата окончания срока действия",type:a.V.DATE,helperSmallText:"Срок действия",helperBigText:"Дата окончания срока действия документа (если применимо)"},{name:"profile.passport.page",label:"Страница паспорта",type:a.V.NUMBER,placeholder:"Введите страницу паспорта",helperSmallText:"Страница",helperBigText:"Страница паспорта (если применимо)",min:1}]}]},{id:"driverLicense",title:"Водительское удостоверение",order:5,description:"Данные водительского удостоверения",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"licenseNumberGroup",type:a.V.GROUP,label:"Номер удостоверения",description:"Номер водительского удостоверения и категории прав",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.licenseNumber",label:"Номер водительского удостоверения",type:a.V.TEXT,placeholder:"Введите номер ВУ",required:!0,helperSmallText:"Номер ВУ",helperBigText:"Номер водительского удостоверения"},{name:"profile.licenseCategories",label:"Категории прав",type:a.V.MULTISELECT,placeholder:"Выберите категории прав",required:!0,options:[{value:"A",label:"A - Мотоциклы"},{value:"B",label:"B - Легковые автомобили"},{value:"C",label:"C - Грузовые автомобили"},{value:"D",label:"D - Автобусы"},{value:"E",label:"E - Прицепы"},{value:"M",label:"M - Мопеды"},{value:"Tb",label:"Tb - Троллейбусы"},{value:"Tm",label:"Tm - Трамваи"}],helperSmallText:"Категории ВУ",helperBigText:"Категории транспортных средств в водительском удостоверении"}]},{name:"licenseDatesGroup",type:a.V.GROUP,label:"Даты выдачи и окончания",description:"Даты выдачи и окончания срока действия водительского удостоверения",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.licenseIssueDate",label:"Дата выдачи прав",type:a.V.DATE,required:!0,helperSmallText:"Дата выдачи",helperBigText:"Дата выдачи водительского удостоверения"},{name:"profile.licenseExpiryDate",label:"Дата окончания прав",type:a.V.DATE,required:!0,helperSmallText:"Срок действия",helperBigText:"Дата окончания срока действия водительского удостоверения"}]}]},{id:"driverPersonalInfo",title:"Личная информация",order:6,description:"Личные данные водителя",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"birthInfoGroup",type:a.V.GROUP,label:"Данные о рождении",description:"Дата и место рождения водителя",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.dateOfBirth",label:"Дата рождения",type:a.V.DATE,required:!0,helperSmallText:"Дата рождения",helperBigText:"Дата рождения водителя"},{name:"profile.birthPlace",label:"Место рождения",type:a.V.TEXT,placeholder:"Введите место рождения",helperSmallText:"Место рождения",helperBigText:"Место рождения водителя (если применимо)"}]},{name:"citizenshipGroup",type:a.V.GROUP,label:"Гражданство",description:"Гражданство и страна гражданства водителя",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.citizenship",label:"Гражданство",type:a.V.SELECT,placeholder:"Выберите гражданство",required:!0,options:(0,c.H)(),helperSmallText:"Гражданство",helperBigText:"Гражданство водителя"},{name:"profile.citizenshipCountry",label:"Страна гражданства",type:a.V.SELECT,placeholder:"Выберите страну гражданства",required:!0,options:(0,c.H)(),helperSmallText:"Страна гражданства",helperBigText:"Страна гражданства водителя"}]}]},{id:"driverAdditionalInfo",title:"Дополнительная информация",order:7,description:"Опыт вождения и налоговые данные водителя",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"profile.drivingExperience",label:"Опыт вождения (лет)",type:a.V.NUMBER,placeholder:"Введите опыт вождения",min:0,helperSmallText:"Опыт вождения",helperBigText:"Опыт вождения в годах"},{name:"profile.taxIdentifier",label:"ИНН",type:a.V.TEXT,placeholder:"Введите ИНН",helperSmallText:"ИНН",helperBigText:"Идентификационный номер налогоплательщика (если применимо)"}]},{id:"driverCar",title:"Назначение автомобиля",order:8,description:"Назначение автомобиля для водителя и управление его активностью",layout:{gridCols:1,gapX:4,gapY:4,className:"h-full",flexDirection:"column",maxItemsPerRow:1,itemsAlignment:"stretch"},fields:[]},{id:"driverPreferences",title:"Предпочтения и языки",order:9,description:"Языки и предпочтения водителя",layout:{gridCols:3,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"languagesGroup",type:a.V.GROUP,label:"Языки общения",description:"Языки, на которых может общаться водитель с пассажирами",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.languages",label:"Языки",type:a.V.MULTISELECT,placeholder:"Выберите языки",required:!0,options:(0,c.DL)(),helperSmallText:"Языки общения",helperBigText:"Языки, на которых может общаться водитель",showNavigateButton:!1,multiselectDataType:"static"}]},{name:"rideTypesGroup",type:a.V.GROUP,label:"Классы обслуживания",description:"Предпочитаемые классы обслуживания для работы водителя",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.preferredRideTypes",label:"Предпочитаемые классы обслуживания",type:a.V.MULTISELECT,placeholder:"Выберите классы обслуживания",required:!0,options:(0,c.qd)(),helperSmallText:"Классы обслуживания",helperBigText:"Предпочитаемые классы обслуживания для водителя",showNavigateButton:!1,multiselectDataType:"static"}]},{name:"workZonesGroup",type:a.V.GROUP,label:"Зоны работы",description:"Предпочитаемые зоны работы водителя в городе",layout:{gridCols:2,flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"profile.preferredWorkZones",label:"Предпочитаемые зоны работы",type:a.V.MULTISELECT,placeholder:"Выберите зоны работы",helperSmallText:"Зоны работы",helperBigText:"Предпочитаемые зоны работы водителя (необязательно)",dynamicReference:{api:"get",endpoint:n.QQ.LOCATION.LIST},showNavigateButton:!0,multiselectDataType:"dynamic",colorFieldName:"type",getColorFromValue:d.b,getDescriptionFromValue:e=>(0,o.al)(e)}]}]}]},p={...l,title:{create:"Создание нового оператора",edit:"Редактирование оператора"},description:{create:"Заполните информацию для создания нового оператора в системе",edit:"Редактирование информации оператора в системе"},groups:[{...l.groups[0],description:"Основные данные и контактная информация оператора",fields:[...l.groups[0].fields]},{...l.groups[1],description:"Настройки безопасности и доступа оператора",fields:[...l.groups[1].fields]},{id:"operatorProfile",title:"Профиль оператора",order:3,description:"Информация о работе и статусе оператора",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"isActive",label:"Активен",type:a.V.CHECKBOX,helperSmallText:"Статус",helperBigText:"Отметьте, если оператор активен и может принимать заказы"},{name:"profile.employeeId",label:"Табельный номер",type:a.V.TEXT,placeholder:"Введите табельный номер",required:!0,helperSmallText:"Табельный номер",helperBigText:"Уникальный идентификатор сотрудника в системе"},{name:"profile.department",label:"Отдел",type:a.V.TEXT,placeholder:"Введите отдел",required:!0,helperSmallText:"Отдел",helperBigText:"Отдел, в котором работает оператор"},{name:"profile.position",label:"Должность",type:a.V.TEXT,placeholder:"Введите должность",required:!0,helperSmallText:"Должность",helperBigText:"Должность оператора в компании"},{name:"profile.hireDate",label:"Дата найма",type:a.V.DATE,required:!0,helperSmallText:"Дата найма",helperBigText:"Дата начала работы оператора в компании"}]}]};var m=r(72235);let h={...l,title:{create:"Создание нового партнера",edit:"Редактирование партнера"},description:{create:"Заполните информацию для создания нового партнера в системе",edit:"Редактирование информации партнера в системе"},groups:[{...l.groups[0],description:"Основные данные и контактная информация партнера",fields:[...l.groups[0].fields||[]]},{...l.groups[1],description:"Настройки безопасности и доступа партнера",fields:[...l.groups[1].fields||[]]},{id:"companyInfo",title:"Информация о компании",order:3,description:"Основная информация о компании-партнере",layout:{gridCols:3,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:3,itemsAlignment:"stretch"},fields:[{name:"profile.companyName",label:"Название компании",type:a.V.TEXT,placeholder:"Введите название компании",required:!0,helperSmallText:"Название",helperBigText:"Полное официальное название компании-партнера",className:"flex-1 col-span-2"},{name:"verificationStatus",label:"Статус верификации",type:a.V.SELECT,placeholder:"Выберите статус верификации",required:!0,options:[{value:"Pending",label:"Ожидает проверки"},{value:"Verified",label:"Проверен и подтвержден"},{value:"Rejected",label:"Отклонен"},{value:"InReview",label:"На рассмотрении"},{value:"Expired",label:"Истек срок действия"}],helperSmallText:"Статус проверки",helperBigText:"Текущий статус проверки документов и данных партнера",className:"flex-1"},{name:"profile.companyType",label:"Тип компании",type:a.V.SELECT,placeholder:"Выберите тип компании",required:!0,options:[{value:"LLC",label:"ООО"},{value:"JSC",label:"АО"},{value:"IE",label:"ИП"},{value:"SE",label:"Самозанятый"},{value:"PJSC",label:"ПАО"},{value:"NGO",label:"НКО"},{value:"GP",label:"ГУП"},{value:"Foreign",label:"Иностранная компания"}],helperSmallText:"Организационно-правовая форма",helperBigText:"Организационно-правовая форма компании-партнера",className:"flex-1"},{name:"profile.registrationNumber",label:"Регистрационный номер",type:a.V.TEXT,placeholder:"Введите регистрационный номер",helperSmallText:"ОГРН/ОГРНИП",helperBigText:"Основной государственный регистрационный номер компании",className:"flex-1"},{name:"profile.taxIdentifier",label:"ИНН",type:a.V.TEXT,placeholder:"Введите ИНН",helperSmallText:"ИНН",helperBigText:"Идентификационный номер налогоплательщика",pattern:"^\\d{10}|\\d{12}$",helperText:"ИНН должен содержать 10 цифр для юр. лиц или 12 цифр для ИП и физ. лиц",className:"flex-1"},{name:"profile.legalAddress",label:"Юридический адрес",type:a.V.TEXT,placeholder:"Введите юридический адрес",required:!0,helperSmallText:"Юр. адрес",helperBigText:"Официальный юридический адрес компании-партнера",className:"flex-1 col-span-3"}]},{id:"contactInfo",title:"Контактная информация",order:4,description:"Контактные данные компании-партнера",layout:{gridCols:3,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:3,itemsAlignment:"stretch"},fields:[{name:"profile.contactEmail",label:"Контактный email",type:a.V.EMAIL,placeholder:"Введите контактный email",helperSmallText:"Email",helperBigText:"Основной email для связи с компанией-партнером",className:"flex-1"},{name:"profile.contactPhone",label:"Контактный телефон",type:a.V.PHONE,placeholder:"Введите контактный телефон",helperSmallText:"Телефон",helperBigText:"Основной телефон для связи с компанией-партнером",className:"flex-1"},{name:"profile.website",label:"Веб-сайт",type:a.V.TEXT,placeholder:"Введите веб-сайт",helperSmallText:"Сайт",helperBigText:"Официальный веб-сайт компании-партнера",className:"flex-1"}]},{id:"partnerRoutes",title:"Маршруты партнера",order:5,description:"Управление локациями, доступными для данного партнера",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"column",maxItemsPerRow:1,itemsAlignment:"stretch"},customComponent:m.rq,fields:[{name:"partnerRoutes",type:a.V.HIDDEN}]}],writeOperations:[{api:"put",endpoint:"/User/Partner/self/routes",method:"update",condition:"always",bodyData:{routes:"partnerRoutes"},description:"Обновление привязанных маршрутов партнера",order:1}]},g={...l,title:{create:"Создание нового терминала",edit:"Редактирование терминала"},description:{create:"Заполните информацию для создания нового терминала в системе",edit:"Редактирование информации терминала в системе"},groups:[{...l.groups[0],description:"Основные данные и контактная информация терминала",fields:[...l.groups[0].fields]},{...l.groups[1],description:"Настройки безопасности и доступа терминала",fields:[...l.groups[1].fields]},{id:"terminalProfile",title:"Профиль терминала",order:3,description:"Техническая информация и настройки терминала",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"status",label:"Статус",type:a.V.SELECT,placeholder:"Выберите статус терминала",required:!0,options:[{value:"Active",label:"Активен"},{value:"Inactive",label:"Неактивен"},{value:"Maintenance",label:"На обслуживании"},{value:"Offline",label:"Офлайн"},{value:"Error",label:"Ошибка"}],helperSmallText:"Статус терминала",helperBigText:"Текущий статус работы терминала в системе"},{name:"locationId",label:"Местоположение",type:a.V.SELECT,placeholder:"Выберите местоположение",required:!0,dynamicReference:{api:"get",endpoint:n.QQ.LOCATION.LIST,filters:[{field:"status",operator:"eq",value:"Active"}],sort:[{field:"name",direction:"asc"}],limit:50},helperSmallText:"Местоположение",helperBigText:"Местоположение, где установлен терминал"},{name:"profile.terminalId",label:"Идентификатор терминала",type:a.V.TEXT,placeholder:"Введите идентификатор терминала",required:!0,helperSmallText:"ID терминала",helperBigText:"Уникальный идентификатор терминала в системе"},{name:"profile.ipAddress",label:"IP адрес",type:a.V.TEXT,placeholder:"Введите IP адрес",helperSmallText:"IP адрес",helperBigText:"IP адрес терминала в сети"},{name:"profile.deviceModel",label:"Модель устройства",type:a.V.TEXT,placeholder:"Введите модель устройства",helperSmallText:"Модель",helperBigText:"Модель устройства терминала"},{name:"profile.osVersion",label:"Версия ОС",type:a.V.TEXT,placeholder:"Введите версию ОС",helperSmallText:"ОС",helperBigText:"Версия операционной системы терминала"},{name:"profile.appVersion",label:"Версия приложения",type:a.V.TEXT,placeholder:"Введите версию приложения",helperSmallText:"Приложение",helperBigText:"Версия установленного приложения на терминале"},{name:"profile.browserInfo",label:"Информация о браузере",type:a.V.TEXT,placeholder:"Введите информацию о браузере",helperSmallText:"Браузер",helperBigText:"Информация о браузере, используемом терминалом"},{name:"profile.screenResolution",label:"Разрешение экрана",type:a.V.TEXT,placeholder:"Введите разрешение экрана",helperSmallText:"Разрешение",helperBigText:"Разрешение экрана терминала (например, 1920x1080)"},{name:"profile.deviceIdentifier",label:"Идентификатор устройства",type:a.V.TEXT,placeholder:"Введите идентификатор устройства",helperSmallText:"Device ID",helperBigText:"Уникальный идентификатор устройства терминала"}]}]}},86157:(e,t,r)=>{"use strict";r.d(t,{H:()=>i,DL:()=>o,nu:()=>s,qd:()=>n,r4:()=>l});var a=r(1106);function l(e){switch(e){case a.Xh.Unknown:return"Не определена";case a.Xh.Customer:return"Клиент";case a.Xh.Admin:return"Администратор";case a.Xh.Driver:return"Водитель";case a.Xh.Operator:return"Оператор";case a.Xh.Partner:return"Партнер";case a.Xh.Terminal:return"Терминал";default:return"Неизвестная роль"}}function s(){return Object.values(a.Xh).map(e=>({value:e,label:function(e){switch(e){case a.Xh.Unknown:return"Не определена";case a.Xh.Customer:return"Клиент";case a.Xh.Admin:return"Администратор";case a.Xh.Driver:return"Водитель";case a.Xh.Operator:return"Оператор";case a.Xh.Partner:return"Партнер";case a.Xh.Terminal:return"Терминал";default:return""}}(e)}))}function i(){return Object.values(a.ZA).map(e=>({value:e,label:function(e){switch(e){case a.ZA.RU:return"Россия";case a.ZA.BY:return"Беларусь";case a.ZA.KZ:return"Казахстан";case a.ZA.UA:return"Украина";case a.ZA.UZ:return"Узбекистан";case a.ZA.TJ:return"Таджикистан";case a.ZA.KG:return"Кыргызстан";case a.ZA.AM:return"Армения";case a.ZA.AZ:return"Азербайджан";case a.ZA.MD:return"Молдова";case a.ZA.GE:return"Грузия";case a.ZA.OTHER:return"Другое";default:return""}}(e)}))}function n(){return Object.values(a.mm).map(e=>({value:e,label:function(e){switch(e){case a.mm.Economy:return"Эконом";case a.mm.Comfort:return"Комфорт";case a.mm.ComfortPlus:return"Комфорт+";case a.mm.Business:return"Бизнес";case a.mm.Premium:return"Премиум";case a.mm.Vip:return"VIP";case a.mm.Luxury:return"Люкс";default:return""}}(e),description:function(e){switch(e){case a.mm.Economy:return"Базовый уровень комфорта";case a.mm.Comfort:return"Повышенный комфорт";case a.mm.ComfortPlus:return"Улучшенный комфорт";case a.mm.Business:return"Бизнес-класс обслуживания";case a.mm.Premium:return"Максимальный комфорт и сервис";case a.mm.Vip:return"VIP обслуживание";case a.mm.Luxury:return"Люкс класс";default:return""}}(e)}))}function o(){return Object.values(a.TM).map(e=>({value:e,label:function(e){switch(e){case a.TM.RU:return"Русский";case a.TM.EN:return"Английский";case a.TM.KK:return"Казахский";case a.TM.KY:return"Кыргызский";case a.TM.UZ:return"Узбекский";case a.TM.TJ:return"Таджикский";case a.TM.HY:return"Армянский";case a.TM.AZ:return"Азербайджанский";case a.TM.KA:return"Грузинский";case a.TM.BE:return"Белорусский";case a.TM.UK:return"Украинский";default:return""}}(e),description:function(e){switch(e){case a.TM.RU:return"Официальный язык";case a.TM.EN:return"Международный язык";case a.TM.KK:return"Тюркский язык";case a.TM.KY:return"Государственный язык";case a.TM.UZ:return"Тюркский язык";case a.TM.TJ:return"Персидский язык";case a.TM.HY:return"Индоевропейский язык";case a.TM.AZ:return"Тюркский язык";case a.TM.KA:return"Картвельский язык";case a.TM.BE:case a.TM.UK:return"Славянский язык";default:return""}}(e)}))}},86332:(e,t,r)=>{"use strict";r.d(t,{DriverLocationProvider:()=>n});var a=r(13486),l=r(60159),s=r(68290),i=r(24903);let n=({children:e})=>{let[t,r]=(0,l.useState)(null),[n,o]=(0,l.useState)(!1),[d,c]=(0,l.useState)(null),u=(0,l.useRef)(null),p=(0,l.useRef)(null),m=(0,l.useRef)(null),h=(0,l.useCallback)(async()=>{if(null===u.current){if(console.log("\uD83D\uDE80 DriverLocationProvider: запуск отслеживания геолокации"),o(!0),c(null),"permissions"in navigator&&navigator.permissions.query({name:"geolocation"}).then(e=>{"denied"===e.state&&(console.error("\uD83D\uDEAB\uD83D\uDEAB\uD83D\uDEAB ГЕОЛОКАЦИЯ ЗАБЛОКИРОВАНА! \uD83D\uDEAB\uD83D\uDEAB\uD83D\uDEAB"),console.error("\uD83D\uDC46 РАЗРЕШИТЕ ГЕОЛОКАЦИЮ В БРАУЗЕРЕ! \uD83D\uDC46"),console.error("\uD83D\uDD12 Нажмите на иконку замка в адресной строке"))}).catch(e=>{console.error("❌ Ошибка проверки разрешений:",e)}),navigator.geolocation)try{u.current=navigator.geolocation.watchPosition(e=>{let t=p.current,a=e.coords.heading||0;t&&(Math.abs(e.coords.latitude-t.latitude)>1e-5||Math.abs(e.coords.longitude-t.longitude)>1e-5)&&(a=((e,t,r,a)=>{let l=(a-t)*Math.PI/180,s=e*Math.PI/180,i=r*Math.PI/180,n=Math.sin(l)*Math.cos(i),o=Math.cos(s)*Math.sin(i)-Math.sin(s)*Math.cos(i)*Math.cos(l);return(180*Math.atan2(n,o)/Math.PI+360)%360})(t.latitude,t.longitude,e.coords.latitude,e.coords.longitude),console.log("\uD83E\uDDED Вычислено направление движения:",a,"градусов"));let l={latitude:e.coords.latitude,longitude:e.coords.longitude,speed:e.coords.speed||0,heading:a,accuracy:e.coords.accuracy,timestamp:e.timestamp};m.current=p.current,r(l),p.current=l},async e=>{console.error("HTML5 Geolocation ошибка:",e),console.error("\uD83D\uDEAB ГЕОЛОКАЦИЯ ЗАБЛОКИРОВАНА! Разрешите геолокацию в браузере для точных координат!"),c("Геолокация заблокирована. Разрешите в настройках браузера."),o(!1)},{enableHighAccuracy:!0,timeout:6e4,maximumAge:0});return}catch(e){console.error("HTML5 Geolocation недоступен:",e)}console.error("❌ HTML5 Geolocation недоступен в этом браузере"),c("Геолокация недоступна в этом браузере"),o(!1)}},[]),g=(0,l.useCallback)(()=>{null!==u.current&&(navigator.geolocation.clearWatch(u.current),u.current=null),o(!1),console.log("\uD83D\uDED1 Отслеживание геолокации остановлено")},[]),f=(0,l.useCallback)(()=>new Promise(e=>{if(!("geolocation"in navigator)){console.error("❌ Geolocation API не поддерживается"),e(null);return}console.log("\uD83C\uDFAF Запрос текущей позиции с максимальной точностью..."),navigator.geolocation.getCurrentPosition(t=>{let r={latitude:t.coords.latitude,longitude:t.coords.longitude,speed:t.coords.speed||0,heading:t.coords.heading||0,accuracy:t.coords.accuracy,timestamp:t.timestamp};console.log("\uD83C\uDFAF Текущая позиция получена:",r),e(r)},t=>{console.error("❌ Ошибка получения текущей позиции:",t),e(p.current)},{enableHighAccuracy:!0,timeout:6e4,maximumAge:0})}),[]);return(0,l.useEffect)(()=>(console.log("\uD83D\uDE80 DriverLocationProvider: автозапуск отслеживания геолокации"),h(),()=>{console.log("\uD83D\uDED1 DriverLocationProvider: остановка отслеживания при размонтировании"),g()}),[h,g]),(0,l.useEffect)(()=>{if(console.log("\uD83D\uDCE1 DriverLocationProvider: проверка условий отправки координат",{hasLocation:!!t,isTracking:n,location:t}),!t||!n)return void console.log("❌ DriverLocationProvider: условия не выполнены, пропускаем отправку");console.log("✅ DriverLocationProvider: начинаем отправку координат на сервер");let e=async()=>{try{console.log("\uD83D\uDCE1 DriverLocationProvider: отправляем координаты",{latitude:t.latitude,longitude:t.longitude}),await s.e.updateCurrentLocation({latitude:t.latitude,longitude:t.longitude}),console.log("✅ DriverLocationProvider: координаты успешно отправлены")}catch(e){console.error("❌ DriverLocationProvider: ошибка отправки координат на сервер:",e)}};e();let r=setInterval(()=>{console.log("⏰ DriverLocationProvider: периодическая отправка координат"),e()},3e4);return()=>{console.log("\uD83D\uDD0C DriverLocationProvider: очистка интервала отправки координат"),clearInterval(r)}},[t,n]),(0,a.jsx)(i.pl.Provider,{value:{location:t,isTracking:n,error:d,startTracking:h,stopTracking:g,getCurrentPosition:f},children:e})}},87019:(e,t,r)=>{"use strict";r.d(t,{DriverDataProvider:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call DriverDataProvider() from the server but DriverDataProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\app\\providers\\DriverDataProvider.tsx","DriverDataProvider")},88330:(e,t,r)=>{"use strict";r.d(t,{mE:()=>l,cx:()=>s});var a=r(13367);function l(e){switch(e){case a.m.Economy:return"Эконом";case a.m.Comfort:return"Комфорт";case a.m.ComfortPlus:return"Комфорт+";case a.m.Business:return"Бизнес";case a.m.Premium:return"Премиум";case a.m.Vip:return"VIP";case a.m.Luxury:return"Люкс";default:return""}}function s(){return Object.values(a.m).map(e=>({value:e,label:l(e)}))}},89337:(e,t,r)=>{"use strict";r.d(t,{i:()=>a.i});var a=r(32155)},91499:(e,t,r)=>{"use strict";r.d(t,{Ny:()=>a.e}),r(41101);var a=r(68290)},92157:(e,t,r)=>{"use strict";r.d(t,{DriverDataProvider:()=>o});var a=r(13486),l=r(60159),s=r(32005),i=r(97127),n=r(24903);let o=({children:e})=>{let[t,r]=(0,l.useState)(null),[o,d]=(0,l.useState)(null),[c,u]=(0,l.useState)([]),[p,m]=(0,l.useState)([]),[h,g]=(0,l.useState)(null),[f,b]=(0,l.useState)(!1),[x,v]=(0,l.useState)(!0),[y,C]=(0,l.useState)(!1),[T,N]=(0,l.useState)(null),w=(0,l.useRef)(!1),S=(0,l.useRef)(!1),R=(0,l.useCallback)(async()=>{try{v(!0),N(null);let e=await i.Dv.getCurrentDriver();if(e&&e.id)r(e.id),d(e);else throw Error("Не удалось получить данные водителя")}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки данных водителя";console.error("❌ Ошибка централизованной загрузки данных водителя:",t),N(e)}finally{v(!1),b(!0)}},[]),j=(0,l.useCallback)(async()=>{if(t&&!S.current)try{S.current=!0,C(!0),N(null);let[e,r]=await Promise.all([s.C.getDriverActiveRides(t,100),s.C.getDriverScheduledRides(t,50)]),a=e?.data||[],l=r?.data||[],i=r?{totalCount:r.totalCount,pageSize:r.pageSize,hasPrevious:r.hasPrevious,hasNext:r.hasNext}:null;u(a),m(l),g(i)}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки данных поездок";console.error("❌ Ошибка централизованной загрузки поездок:",t),N(e)}finally{C(!1),S.current=!1}},[t]),A=(0,l.useCallback)(async()=>{console.log("\uD83D\uDD04 Обновление данных водителя..."),await R()},[R]),P=(0,l.useCallback)(async()=>{S.current=!1,await j()},[j]);return(0,l.useEffect)(()=>{w.current||(w.current=!0,R())},[R]),(0,l.useEffect)(()=>{t&&f&&!x&&j()},[t,f,x,j]),(0,a.jsx)(n.aN.Provider,{value:{driverId:t,driverProfile:o,activeRides:c,scheduledRides:p,scheduledRidesPagination:h,isInitialized:f,isLoading:x,isLoadingRides:y,error:T,refreshDriverData:A,refreshRides:P},children:e})}},93055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useBreadcrumbs:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call useBreadcrumbs() from the server but useBreadcrumbs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\header\\hooks\\useBreadcrumbs.ts","useBreadcrumbs")},93570:(e,t,r)=>{"use strict";r.d(t,{l:()=>i});var a=r(96274),l=r(50947);class s extends a.vA{async getNotification(e){let t=await this.get(`/${e}`);return this.handleApiResult(t)}async getNotificationSafe(e){let t=await this.get(`/${e}`);return this.handleApiResultSafe(t)}async getNotifications(e){let t=new URLSearchParams;e?.size!==void 0&&t.append("size",e.size.toString()),e?.userId&&t.append("userId",e.userId),e?.isRead!==void 0&&t.append("isRead",e.isRead.toString()),e?.dateFrom&&t.append("dateFrom",e.dateFrom),e?.dateTo&&t.append("dateTo",e.dateTo),e?.after&&t.append("after",e.after),e?.type&&t.append("type",e.type),e?.first!==void 0&&t.append("first",e.first.toString()),e?.before&&t.append("before",e.before),e?.last!==void 0&&t.append("last",e.last.toString());let r=t.toString()?`?${t.toString()}`:"",a=await this.get(r);return this.handleApiResult(a)}async createNotification(e){let t=await this.post("",e);return this.handleApiResult(t)}async updateNotification(e,t){let r=await this.put(`/${e}`,t);return this.handleApiResult(r)}async deleteNotification(e){let t=await this.delete(`/${e}`);this.handleApiResult(t)}async markAsRead(e){await this.post("read",[e])}async markAllAsRead(e){let t=await this.patch(`/user/${e}/read-all`);return this.handleApiResult(t)}async getUnreadNotifications(e){let t=await this.get(`/user/${e}/unread`);return this.handleApiResult(t)}async deleteById(e){let t=await this.delete(`/${e}`);this.handleApiResult(t)}async markNotificationsAsRead(e){await this.post("/read",e)}async getMyNotifications(e){let t=new URLSearchParams;e?.size!==void 0&&t.append("size",e.size.toString()),e?.after&&t.append("after",e.after);let r=t.toString()?`me?${t.toString()}`:"me",a=await this.get(r);return this.handleApiResult(a)}constructor(...e){super(...e),this.baseUrl=l.QQ.NOTIFICATION.LIST}}let i=new s},94058:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>el});var a=r(38828),l=r(87019),s=r(46420),i=r(82604),n=r(11994);r(77809),r(78715),r(38567);var o=r(41925),d=r(65208);async function c(e){try{let t=(await (0,d.UL)()).get(e);return t?.value||null}catch(t){return console.error(`❌ Ошибка при получении куки ${e}:`,t),null}}r(94081);var u=r(70123);r(61365),r(14299),r(28937);var p=r(54462);let m=(0,p.lh)(),h=(0,p.lh)(),g=(0,p.lh)(),f=(0,p.lh)(),b=(0,p.y$)(!1).on(m,(e,t)=>t),x=(0,p.y$)({}).on(g,(e,{key:t,loading:r})=>({...e,[t]:r})).on(f,()=>({})).map(e=>Object.values(e).some(e=>e)),v=(0,p.y$)(!1);(0,p.XM)({source:[b,x],fn:([e,t])=>e||t,target:v});let y=(0,p.y$)(1200).on(h,(e,t)=>t),C=(0,p.lh)(),T=(0,p.EH)(async()=>{let e=Date.now()-N,t=Math.max(0,y.getState()-e);t>0&&await new Promise(e=>setTimeout(e,t))}),N=0;m.watch(e=>{e&&(N=Date.now())}),(0,p.XM)({source:C,target:T}),(0,p.XM)({source:T.doneData,fn:()=>!1,target:m});var w=function(e){return e.ContactFormModal="CONTACT_FORM_MODAL",e.ImageViewerModal="IMAGE_VIEWER_MODAL",e.MediaSelectorModal="MEDIA_SELECTOR_MODAL",e.HistoryViewerModal="HISTORY_VIEWER_MODAL",e.SelectUserRoleModal="SELECT_USER_ROLE_MODAL",e.SelectOrderTypeModal="SELECT_ORDER_TYPE_MODAL",e.NotificationCenterModal="NOTIFICATION_CENTER_MODAL",e.RideRequestModal="RIDE_REQUEST_MODAL",e.QRPaymentModal="QR_PAYMENT_MODAL",e.CardPaymentModal="CARD_PAYMENT_MODAL",e.PotentialDriversModal="POTENTIAL_DRIVERS_MODAL",e.CompanyDetailsModal="COMPANY_DETAILS_MODAL",e.PublicOfferModal="PUBLIC_OFFER_MODAL",e.PrivacyPolicyModal="PRIVACY_POLICY_MODAL",e.FAQModal="FAQ_MODAL",e}({});let S=(0,p.y$)({}),R=(0,p.y$)([]),j=(0,p.y$)(null),A=(0,p.lh)(),P=(0,p.lh)(),I=(0,p.lh)();S.on(P,(e,t)=>t);let D=(0,p.EH)(()=>{});R.on(A,(e,t)=>{if("object"==typeof t&&null!==t&&"type"in t){let{type:r}=t;return r?[...e,r]:e}return t?[...e,t]:e}),R.on(I,(e,t={})=>0===e.length||t.allClose?[]:e.slice(0,-1)),(0,p.XM)({source:I,fn:()=>({}),target:P}),j.on(R,(e,t)=>0===t.length?null:t[t.length-1]),(0,p.XM)({source:I,target:D});let z=(0,p.y$)(null),E=(0,p.lh)();z.on(E,(e,t)=>t),z.reset(I);let k=(0,p.y$)(null),O=(0,p.lh)();k.on(O,(e,t)=>t),k.reset(I);let L=(0,p.y$)(null),M=(0,p.lh)();L.on(M,(e,t)=>t),L.reset(I);let U=(0,p.y$)(null),$=(0,p.lh)(),B=(0,p.lh)();U.on($,(e,t)=>t),U.reset(B);let V=(0,p.y$)(null),q=(0,p.lh)();V.on(q,(e,t)=>t),V.reset(I);let _=(0,p.y$)(null),X=(0,p.lh)(),F=(0,p.lh)();_.on(X,(e,t)=>t),_.reset(F);let Y=(0,p.lh)();(0,p.XM)({source:Y,fn:({modalType:e,params:t})=>({type:e,params:t||{}}),target:A});let H=(0,p.lh)();(0,p.XM)({source:H,fn:e=>({modalType:"POTENTIAL_DRIVERS_MODAL",params:{orderId:e}}),target:Y});let G=(0,p.lh)();(0,p.XM)({source:G,fn:()=>({modalType:"SELECT_ORDER_TYPE_MODAL",params:{}}),target:Y});let Q=(0,p.lh)();(0,p.XM)({source:Q,fn:({userId:e})=>({modalType:"SELECT_USER_ROLE_MODAL",params:{userId:e}}),target:Y}),(0,p.XM)({source:A,fn:e=>"object"==typeof e&&null!==e&&"params"in e&&e.params||{},target:P});class W{getButtonText(e){return"edit"===e?"Просмотр":"Редактировать"}handleButtonClick(e){let t="edit"===e.mode?"/profile":"/profile/edit";e.router.push(t)}shouldShowButton(e){return"view"===e||"edit"===e}getAdditionalInfo(e){return null}}class K{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.openModal(w.SelectUserRoleModal):e.router.push("/users")}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){return null}}class Z{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.openModal(w.SelectOrderTypeModal):e.router.push("/orders")}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){let{orderType:t,mode:r}=e;return"create"!==r&&"edit"!==r?null:"instant"===t?(0,a.jsx)("div",{className:"max-w-4xl h-full p-2 mx-auto hourglass-shape",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"⚡"}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Мгновенный заказ"}),(0,a.jsx)("p",{className:"text-sm text-white",children:"Выполнение как можно скорее"})]})]})}):"scheduled"===t?(0,a.jsx)("div",{className:"max-w-4xl h-full p-2 mx-auto hourglass-shape",children:(0,a.jsx)("div",{className:"flex items-center justify-center gap-2",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Запланированный заказ"}),(0,a.jsx)("p",{className:"text-sm text-white",children:"Выполнение в указанное время"})]})})}):null}}class J{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.router.push(`/${e.collectionName}/create`):e.router.push(`/${e.collectionName}`)}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){return null}}class ee{static{this.strategies=new Map([["profile",new W],["users",new K],["orders",new Z]])}static getStrategy(e){return this.strategies.get(e)||new J}}r(93055),r(24700),r(81718);var et=function(e){return e.Sedan="Sedan",e.Hatchback="Hatchback",e.SUV="SUV",e.Minivan="Minivan",e.Coupe="Coupe",e.Cargo="Cargo",e.Pickup="Pickup",e}({});et.Sedan,et.Hatchback,et.SUV,et.Minivan,et.Coupe,et.Cargo,et.Pickup;var er=r(61938);r(81021);var ea=r(49567);async function el({children:e}){let t=await c(".AspNetCore.Identity.Application"),r=await c("driver-background-type")||"color",d=await c("driver-background-value")||"#f9fafb";parseFloat(await c("driver-ui-scale")||"0.8");let p=await c("driver-overlay-opacity")||"0.95",m=await c("driver-overlay-blur")||"4",h=await c("driver-overlay-color")||"#ffffff",g=parseFloat(p),f=parseFloat(m),b=await c("driver-component-opacity")||"0.9",x=await c("driver-component-blur")||"8",v=parseFloat(b),y=parseFloat(x),C=(e=>{let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:{r:255,g:255,b:255}})(h),T={backgroundColor:`rgba(255, 255, 255, ${v})`,backdropFilter:`blur(${y}px)`,WebkitBackdropFilter:`blur(${y}px)`};return(0,a.jsx)(o.SignalRProvider,{accessToken:t||void 0,children:(0,a.jsx)(l.DriverDataProvider,{children:(0,a.jsx)(n.DriverLocationProvider,{children:(0,a.jsx)(s.NotificationProvider,{children:(0,a.jsx)(i.DriverQueueProvider,{refreshInterval:3e4,children:(0,a.jsxs)("div",{className:"mx-auto my-auto flex overflow-hidden w-full h-full",style:{..."image"===r?{backgroundImage:`url(${d})`,backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat",backgroundAttachment:"fixed"}:{backgroundColor:d}},children:[(0,a.jsxs)("div",{className:"mx-auto my-auto w-full h-full flex flex-row gap-4 overflow-hidden p-5",style:{backgroundColor:`rgba(${C.r}, ${C.g}, ${C.b}, ${g})`,backdropFilter:`blur(${f}px)`,WebkitBackdropFilter:`blur(${f}px)`,transformOrigin:"center center"},children:[(0,a.jsxs)("div",{className:"h-full flex-1 flex flex-col gap-4 overflow-hidden",children:[(0,a.jsx)("div",{className:"rounded-2xl",style:T,children:(0,a.jsx)(u.DriverHeader,{})}),(0,a.jsx)("main",{className:"h-ful flex flex-col flex-1 rounded-2xl overflow-hidden",style:{...T,backgroundColor:`rgba(249, 249, 249, ${v})`},children:e})]}),(0,a.jsx)("div",{className:"rounded-2xl overflow-auto",style:T,children:(0,a.jsx)(ea.DriverSidebar,{})})]}),(0,a.jsx)(er.ModalManagerComponent,{})]})})})})})})}},94081:(e,t,r)=>{"use strict";r.d(t,{Header:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\header\\ui\\Header.tsx","Header")},95124:(e,t,r)=>{"use strict";r.d(t,{V:()=>u});var a=r(13486),l=r(49989),s=r.n(l),i=r(60159),n=r(9019),o=r(89337),d=r(39314);let c=(0,i.lazy)(()=>Promise.resolve().then(r.bind(r,2065))),u={columns:[{id:"name",label:"Название",accessor:"name",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","endsWith","equals","notEquals"]}},{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,n.wI)(o.i,d.al)},renderCell:e=>(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium",children:(0,d.al)(e.type)})},{id:"address",label:"Адрес",accessor:"address",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"city",label:"Город",accessor:"city",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]}},{id:"district",label:"Округ/район",accessor:"district",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]},renderCell:e=>(0,a.jsx)("span",{children:e.district||"-"})},{id:"region",label:"Область/регион",accessor:"region",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]}},{id:"country",label:"Страна",accessor:"country",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]}},{id:"isActive",label:"Активна",accessor:"isActive",sortable:!0,filterable:!0,filterConfig:{fieldType:"boolean"},renderCell:e=>(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Активна":"Неактивна"})},{id:"popular",label:"Популярность",accessor:"id",sortable:!1,filterable:!0,filterConfig:{fieldType:"boolean"},renderCell:e=>(0,a.jsxs)("div",{className:"flex gap-1",children:[e.popular1&&(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"П1"}),e.popular2&&(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:"П2"}),!e.popular1&&!e.popular2&&(0,a.jsx)("span",{className:"text-gray-400",children:"-"})]})},{id:"coordinates",label:"Координаты",accessor:"id",sortable:!1,filterable:!1,renderCell:e=>(0,a.jsxs)("span",{children:[e.latitude.toFixed(6),", ",e.longitude.toFixed(6)]})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,a.jsx)("div",{className:"flex justify-end gap-2",children:(0,a.jsx)(s(),{href:`/locations/${e.id}`,className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,a.jsx)(c,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"Name",label:"Название",operator:"contains",operatorField:"NameOp"},{field:"Address",label:"Адрес",operator:"contains",operatorField:"AddressOp"},{field:"City",label:"Город",operator:"contains",operatorField:"CityOp"},{field:"District",label:"Округ/район",operator:"contains",operatorField:"DistrictOp"},{field:"Region",label:"Область/регион",operator:"contains",operatorField:"RegionOp"}]},filtersConfig:{availableFilters:[{field:"Type",label:"Тип локации",type:"select",options:(0,d._n)().map(e=>({value:e.value,label:e.label}))},{field:"IsActive",label:"Активна",type:"boolean"},{field:"Country",label:"Страна",type:"text"},{field:"Region",label:"Область/регион",type:"text"},{field:"Popular1",label:"Популярная локация 1",type:"boolean"},{field:"Popular2",label:"Популярная локация 2",type:"boolean"}]}}},95395:(e,t,r)=>{"use strict";r.d(t,{OU:()=>a,Lz:()=>n,mm:()=>s,zl:()=>i,$O:()=>l});var a=function(e){return e.Black="Black",e.White="White",e.Silver="Silver",e.Gray="Gray",e.Red="Red",e.Blue="Blue",e.Green="Green",e.Yellow="Yellow",e.Brown="Brown",e.Orange="Orange",e.Purple="Purple",e.Gold="Gold",e.Other="Other",e}({}),l=function(e){return e.Sedan="Sedan",e.Hatchback="Hatchback",e.SUV="SUV",e.Minivan="Minivan",e.Coupe="Coupe",e.Cargo="Cargo",e.Pickup="Pickup",e}({}),s=function(e){return e.Economy="Economy",e.Comfort="Comfort",e.ComfortPlus="ComfortPlus",e.Business="Business",e.Premium="Premium",e.Vip="Vip",e.Luxury="Luxury",e}({}),i=function(e){return e.Available="Available",e.Maintenance="Maintenance",e.Repair="Repair",e.Other="Other",e}({}),n=function(e){return e.AirConditioning="AirConditioning",e.ClimateControl="ClimateControl",e.LeatherSeats="LeatherSeats",e.HeatedSeats="HeatedSeats",e.Bluetooth="Bluetooth",e.USBPort="USBPort",e.AuxInput="AuxInput",e.Navigation="Navigation",e.BackupCamera="BackupCamera",e.ParkingSensors="ParkingSensors",e.Sunroof="Sunroof",e.PanoramicRoof="PanoramicRoof",e.ThirdRowSeats="ThirdRowSeats",e.ChildSeat="ChildSeat",e.WheelchairAccess="WheelchairAccess",e.Wifi="Wifi",e.PremiumAudio="PremiumAudio",e.AppleCarplay="AppleCarplay",e.AndroidAuto="AndroidAuto",e.SmokingAllowed="SmokingAllowed",e.PetFriendly="PetFriendly",e.LuggageCarrier="LuggageCarrier",e.BikeRack="BikeRack",e}({})},95816:(e,t,r)=>{"use strict";r.d(t,{Hq:()=>g,c7:()=>h});var a=r(2984),l=r(60159),s=r(72235);let i=null,n=[],o=!1,d=[],c=e=>{i=e,n.forEach(t=>t(e))},u=e=>{o=e,d.forEach(t=>t(e))},p=e=>(d.push(e),()=>{d=d.filter(t=>t!==e)}),m=e=>(n.push(e),()=>{n=n.filter(t=>t!==e)}),h=()=>{let e=(0,a.useRouter)(),[t,r]=(0,l.useState)(i),[n,d]=(0,l.useState)(o),{driverId:h,activeRides:g,isInitialized:f,isLoadingRides:b,refreshRides:x}=(0,s.g0)();(0,l.useEffect)(()=>{let e=m(e=>{r(e)}),t=p(e=>{d(e)});return o&&!n&&d(!0),()=>{e(),t()}},[n]),(0,l.useEffect)(()=>{if(f&&g.length>0){let e=g[0],r={orderId:e.orderId||e.id,rideId:e.id,status:e.status,ride:e};t&&t.rideId===r.rideId||c(r)}else f&&0===g.length&&t&&(console.log("\uD83D\uDDD1️ Нет активных поездок в централизованных данных, очищаем локальное состояние"),c(null))},[g,f,t]);let v=(0,l.useCallback)(async()=>(f&&h?(d(!0),u(!0)):console.log("⏳ Ждем инициализации водителя для проверки активных поездок..."),t),[t,h,f]),y=(0,l.useCallback)((e,t)=>{console.log("\uD83D\uDE80 setActiveRideData вызван - устанавливаем активную поездку глобально");let r={orderId:e,rideId:t,status:"Accepted",ride:null};c(r),console.log("✅ Активная поездка установлена глобально:",r)},[]),C=(0,l.useCallback)(async()=>(await x(),await v()),[v,x]),T=(0,l.useCallback)(()=>{console.log("\uD83D\uDDD1️ clearActiveRide вызван - очищаем активную поездку глобально"),localStorage.removeItem("activeRide"),c(null),console.log("✅ Активная поездка очищена глобально")},[]),N=(0,l.useCallback)(()=>{if(t){let r=t.orderType?`&orderType=${t.orderType}`:"",a=`/active-ride/?orderId=${t.orderId}&rideId=${t.rideId}${r}`;e.push(a)}},[t,e]);return{activeRide:t,hasActiveRide:!!t,isLoading:b,isInitialized:n,isRideActive:e=>["Accepted","DriverHeading","Arrived","InProgress"].includes(e),checkActiveRides:v,setActiveRideData:y,clearActiveRide:T,navigateToActiveRide:N,forceUpdateActiveRide:C}},g=(e,t)=>{console.log("\uD83C\uDF0D setActiveRideGlobally вызван:",{orderId:e,rideId:t}),c({orderId:e,rideId:t,status:"Accepted",ride:null}),console.log("✅ Активная поездка установлена глобально через прямой вызов")}},96881:(e,t,r)=>{"use strict";function a(e){if(!e)return"Только что";let t=new Date,r=new Date(e),a=Math.floor((t.getTime()-r.getTime())/6e4),l=Math.floor(a/60),s=Math.floor(l/24);return a<1?"Только что":a<60?`${a} мин назад`:l<24?`${l} ч назад`:s<7?`${s} дн назад`:r.toLocaleDateString("ru-RU")}r.d(t,{f:()=>a})},97127:(e,t,r)=>{"use strict";r.d(t,{Dv:()=>a.Dv}),r(5777);var a=r(18602)}};