"use strict";exports.id=4803,exports.ids=[4803],exports.modules={94803:(s,e,l)=>{l.r(e),l.d(e,{PublicOfferModal:()=>r});var i=l(13486),c=l(97008);l(60159);var d=l(38655);let r=()=>{let s=(0,c.e3)(d.Oo),e=()=>{s({})};return(0,i.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold",children:"Публичная оферта"}),(0,i.jsx)("button",{onClick:e,className:"text-gray-400 hover: transition-colors",children:(0,i.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,i.jsxs)("div",{className:"p-6 space-y-6",children:[(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"mb-3",children:'Настоящий документ является публичной офертой (предложением) ОсОО "Компас Трансфер", именуемой в дальнейшем \xabИсполнитель\xbb, на заключение договора об оказании информационных услуг по организации пассажирских перевозок на нижеследующих условиях.'})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"1. Общие положения"}),(0,i.jsx)("p",{className:"mb-3",children:"Настоящая публичная оферта определяет порядок предоставления информационных услуг по организации пассажирских перевозок и является официальным предложением (офертой) Исполнителя, адресованным неопределенному кругу лиц, заключить договор на оказание информационных услуг на условиях, предусмотренных настоящей офертой."}),(0,i.jsx)("p",{className:"mb-3",children:"Акцептом настоящей оферты является осуществление Заказчиком полной или частичной оплаты услуг Исполнителя, что означает полное и безоговорочное принятие Заказчиком условий настоящей оферты. С момента акцепта настоящей оферты договор на оказание информационных услуг считается заключенным между Исполнителем и Заказчиком."}),(0,i.jsx)("p",{className:"mb-3",children:"Исполнитель оставляет за собой право вносить изменения в настоящую оферту, о чем Заказчик уведомляется посредством публикации новой редакции оферты на сайте Исполнителя. Изменения вступают в силу с момента публикации, если иной срок не предусмотрен в новой редакции оферты."}),(0,i.jsx)("p",{children:"Заказчик несет ответственность за ознакомление с действующей редакцией оферты при каждом обращении за услугами Исполнителя."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"2. Предмет соглашения"}),(0,i.jsx)("p",{className:"mb-3",children:"Предметом настоящего соглашения является предоставление Заказчику информационных услуг по организации пассажирских перевозок, включая:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1 mb-3",children:[(0,i.jsx)("li",{children:"Поиск и подбор транспортного средства"}),(0,i.jsx)("li",{children:"Расчет стоимости поездки"}),(0,i.jsx)("li",{children:"Организация взаимодействия между пассажиром и водителем"}),(0,i.jsx)("li",{children:"Обработка платежей"}),(0,i.jsx)("li",{children:"Предоставление информации о маршрутах и тарифах"})]}),(0,i.jsx)("p",{children:"Исполнитель обязуется предоставить информационные услуги с надлежащим качеством и в соответствии с описанием, размещенным в мобильном приложении и на сайте Исполнителя."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"3. Стоимость и описание услуг"}),(0,i.jsx)("p",{className:"mb-3",children:"Стоимость каждой поездки определяется автоматически на основе тарифов, действующих на момент заказа, с учетом расстояния, времени поездки и других факторов."}),(0,i.jsx)("p",{className:"mb-3",children:"Описание услуг, включая тарифы и условия предоставления, размещается в мобильном приложении и на сайте Исполнителя."}),(0,i.jsx)("p",{children:"Оплата услуг осуществляется наличными водителю или безналичным способом через мобильное приложение."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"4. Условия предоставления услуг"}),(0,i.jsx)("p",{className:"mb-3",children:"Исполнитель обязуется обеспечить функционирование информационного сервиса и предоставить возможность заказа транспортного средства через мобильное приложение."}),(0,i.jsx)("p",{className:"mb-3",children:"Исполнитель не несет ответственности за качество интернет-соединения Заказчика и не может гарантировать бесперебойный доступ к сервису."}),(0,i.jsx)("p",{children:"Исполнитель не несет ответственности за действия водителей, являющихся независимыми исполнителями услуг по перевозке пассажиров."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"5. Условия оплаты"}),(0,i.jsx)("p",{className:"mb-3",children:"Оплата за предоставляемые услуги осуществляется в соответствии с тарифами, указанными в мобильном приложении."}),(0,i.jsx)("p",{className:"mb-3",children:"Заказчик может выбрать один из предложенных способов оплаты: наличными водителю или безналичным способом через мобильное приложение."}),(0,i.jsx)("p",{children:"Оплата считается произведенной с момента подтверждения платежа в системе или передачи наличных средств водителю."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"6. Политика отмены заказов"}),(0,i.jsx)("p",{className:"mb-3",children:"Заказчик имеет право отменить заказ до прибытия водителя без дополнительной оплаты."}),(0,i.jsx)("p",{className:"mb-3",children:"В случае отмены заказа после прибытия водителя к месту подачи, с Заказчика может взиматься плата за отмену в соответствии с действующими тарифами."}),(0,i.jsx)("p",{children:"Возврат денежных средств при безналичной оплате производится в течение 5-10 рабочих дней на банковскую карту или электронный кошелек Заказчика."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"7. Ответственность сторон"}),(0,i.jsx)("p",{className:"mb-3",children:"Исполнитель несет ответственность за качество предоставляемых информационных услуг в пределах стоимости заказанной поездки."}),(0,i.jsx)("p",{className:"mb-3",children:"Исполнитель не несет ответственности за действия водителей, качество транспортных средств, соблюдение правил дорожного движения и безопасность поездки."}),(0,i.jsx)("p",{className:"mb-3",children:"Заказчик несет ответственность за предоставление достоверной информации при оформлении заказа и соблюдение правил пользования сервисом."}),(0,i.jsx)("p",{children:"Ответственность Исполнителя ограничивается суммой, фактически оплаченной Заказчиком за соответствующую поездку."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"8. Персональные данные"}),(0,i.jsx)("p",{className:"mb-3",children:"Исполнитель обязуется обеспечить конфиденциальность персональных данных Заказчика в соответствии с законодательством Кыргызской Республики."}),(0,i.jsx)("p",{children:"Подробная информация об обработке персональных данных содержится в Политике конфиденциальности Исполнителя."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"9. Заключительные положения"}),(0,i.jsx)("p",{className:"mb-3",children:"Все споры и разногласия, возникающие в связи с исполнением настоящего договора, подлежат урегулированию путем переговоров."}),(0,i.jsx)("p",{className:"mb-3",children:"В случае невозможности урегулирования споров путем переговоров, они подлежат рассмотрению в суде по месту нахождения Исполнителя."}),(0,i.jsx)("p",{className:"mb-3",children:"Настоящая оферта регулируется законодательством Кыргызской Республики."}),(0,i.jsx)("p",{children:"Настоящая оферта вступает в силу с момента ее опубликования и действует до момента ее отзыва или изменения Исполнителем."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold  mb-3",children:"10. Реквизиты"}),(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,i.jsx)("p",{className:"font-semibold mb-2",children:'ОсОО "Компас Трансфер"'}),(0,i.jsx)("p",{className:"mb-1",children:"Адрес: [адрес будет указан позже]"}),(0,i.jsx)("p",{className:"mb-1",children:"ИНН: [ИНН будет указан позже]"}),(0,i.jsx)("p",{className:"mb-1",children:"ОКПО: [ОКПО будет указан позже]"}),(0,i.jsx)("p",{className:"mb-1",children:"Телефон: +[телефон будет указан позже]"}),(0,i.jsx)("p",{children:"Email: [email будет указан позже]"})]})]})]}),(0,i.jsx)("div",{className:"flex justify-end p-6 border-t border-gray-200",children:(0,i.jsx)("button",{onClick:e,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Закрыть"})})]})})}}};