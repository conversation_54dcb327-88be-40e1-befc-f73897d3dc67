"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2634],{11447:(e,r,t)=>{t.d(r,{$A:()=>p,Gw:()=>o,OT:()=>u,Oo:()=>i,Qx:()=>s,bY:()=>a,iY:()=>y,jB:()=>f,qf:()=>c,t0:()=>E});var n=t(52906);t(68998);var a=function(e){return e.ContactFormModal="CONTACT_FORM_MODAL",e.ImageViewerModal="IMAGE_VIEWER_MODAL",e.MediaSelectorModal="MEDIA_SELECTOR_MODAL",e.HistoryViewerModal="HISTORY_VIEWER_MODAL",e.SelectUserRoleModal="SELECT_USER_ROLE_MODAL",e.SelectOrderTypeModal="SELECT_ORDER_TYPE_MODAL",e.NotificationCenterModal="NOTIFICATION_CENTER_MODAL",e.RideRequestModal="RIDE_REQUEST_MODAL",e.QRPaymentModal="QR_PAYMENT_MODAL",e.CardPaymentModal="CARD_PAYMENT_MODAL",e.PotentialDriversModal="POTENTIAL_DRIVERS_MODAL",e.CompanyDetailsModal="COMPANY_DETAILS_MODAL",e.PublicOfferModal="PUBLIC_OFFER_MODAL",e.PrivacyPolicyModal="PRIVACY_POLICY_MODAL",e.FAQModal="FAQ_MODAL",e}({});let u=(0,n.y$)({}),o=(0,n.y$)([]),s=(0,n.y$)(null),c=(0,n.lh)(),l=(0,n.lh)(),i=(0,n.lh)();u.on(l,(e,r)=>r);let m=(0,n.EH)(()=>{});o.on(c,(e,r)=>{if("object"==typeof r&&null!==r&&"type"in r){let{type:t}=r;return t?[...e,t]:e}return r?[...e,r]:e}),o.on(i,function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return 0===e.length||r.allClose?[]:e.slice(0,-1)}),(0,n.XM)({source:i,fn:()=>({}),target:l}),s.on(o,(e,r)=>0===r.length?null:r[r.length-1]),(0,n.XM)({source:i,target:m});let M=(0,n.y$)(null),d=(0,n.lh)();M.on(d,(e,r)=>r),M.reset(i);let A=(0,n.y$)(null),T=(0,n.lh)();A.on(T,(e,r)=>r),A.reset(i);let E=(0,n.y$)(null),f=(0,n.lh)();E.on(f,(e,r)=>r),E.reset(i);let h=(0,n.y$)(null),p=(0,n.lh)(),O=(0,n.lh)();h.on(p,(e,r)=>r),h.reset(O);let R=(0,n.y$)(null),P=(0,n.lh)();R.on(P,(e,r)=>r),R.reset(i);let _=(0,n.y$)(null),y=(0,n.lh)(),C=(0,n.lh)();_.on(y,(e,r)=>r),_.reset(C);let D=(0,n.lh)();(0,n.XM)({source:D,fn:e=>{let{modalType:r,params:t}=e;return{type:r,params:t||{}}},target:c});let L=(0,n.lh)();(0,n.XM)({source:L,fn:e=>({modalType:"POTENTIAL_DRIVERS_MODAL",params:{orderId:e}}),target:D});let I=(0,n.lh)();(0,n.XM)({source:I,fn:()=>({modalType:"SELECT_ORDER_TYPE_MODAL",params:{}}),target:D});let v=(0,n.lh)();(0,n.XM)({source:v,fn:e=>{let{userId:r}=e;return{modalType:"SELECT_USER_ROLE_MODAL",params:{userId:r}}},target:D}),(0,n.XM)({source:c,fn:e=>"object"==typeof e&&null!==e&&"params"in e&&e.params||{},target:l})},24723:(e,r,t)=>{t.d(r,{AK:()=>l,Vp:()=>c,ZA:()=>u,e5:()=>s,TM:()=>i,Xh:()=>n.X,mm:()=>o,iS:()=>a});var n=t(37780),a=function(e){return e.Pending="Pending",e.Verified="Verified",e.Rejected="Rejected",e.InReview="InReview",e.Expired="Expired",e}({}),u=function(e){return e.RU="RU",e.BY="BY",e.KZ="KZ",e.UA="UA",e.UZ="UZ",e.TJ="TJ",e.KG="KG",e.AM="AM",e.AZ="AZ",e.MD="MD",e.GE="GE",e.OTHER="OTHER",e}({}),o=function(e){return e.Economy="Economy",e.Comfort="Comfort",e.ComfortPlus="ComfortPlus",e.Business="Business",e.Premium="Premium",e.Vip="Vip",e.Luxury="Luxury",e}({}),s=function(e){return e.NationalPassport="NationalPassport",e.InternationalPassport="InternationalPassport",e.IdCard="IdCard",e.ResidencePermit="ResidencePermit",e.RefugeeId="RefugeeId",e.TemporaryId="TemporaryId",e.MilitaryId="MilitaryId",e.ForeignPassport="ForeignPassport",e.DriversLicense="DriversLicense",e}({}),c=function(e){return e.Individual="Individual",e.LLC="LLC",e.Corporation="Corporation",e.Partnership="Partnership",e.Cooperative="Cooperative",e.NonProfit="NonProfit",e.GovernmentEntity="GovernmentEntity",e}({}),l=function(e){return e.Active="Active",e.Inactive="Inactive",e.Suspended="Suspended",e.Blocked="Blocked",e.OnVacation="OnVacation",e}({}),i=function(e){return e.RU="RU",e.EN="EN",e.KK="KK",e.KY="KY",e.UZ="UZ",e.TJ="TJ",e.HY="HY",e.AZ="AZ",e.KA="KA",e.BE="BE",e.UK="UK",e}({})},37780:(e,r,t)=>{t.d(r,{X:()=>n});var n=function(e){return e.Unknown="Unknown",e.Customer="Customer",e.Admin="Admin",e.Driver="Driver",e.Operator="Operator",e.Partner="Partner",e.Terminal="Terminal",e}({})},55115:(e,r,t)=>{t.d(r,{H:()=>o,DL:()=>c,nu:()=>u,qd:()=>s,r4:()=>a});var n=t(24723);function a(e){switch(e){case n.Xh.Unknown:return"Не определена";case n.Xh.Customer:return"Клиент";case n.Xh.Admin:return"Администратор";case n.Xh.Driver:return"Водитель";case n.Xh.Operator:return"Оператор";case n.Xh.Partner:return"Партнер";case n.Xh.Terminal:return"Терминал";default:return"Неизвестная роль"}}function u(){return Object.values(n.Xh).map(e=>({value:e,label:function(e){switch(e){case n.Xh.Unknown:return"Не определена";case n.Xh.Customer:return"Клиент";case n.Xh.Admin:return"Администратор";case n.Xh.Driver:return"Водитель";case n.Xh.Operator:return"Оператор";case n.Xh.Partner:return"Партнер";case n.Xh.Terminal:return"Терминал";default:return""}}(e)}))}function o(){return Object.values(n.ZA).map(e=>({value:e,label:function(e){switch(e){case n.ZA.RU:return"Россия";case n.ZA.BY:return"Беларусь";case n.ZA.KZ:return"Казахстан";case n.ZA.UA:return"Украина";case n.ZA.UZ:return"Узбекистан";case n.ZA.TJ:return"Таджикистан";case n.ZA.KG:return"Кыргызстан";case n.ZA.AM:return"Армения";case n.ZA.AZ:return"Азербайджан";case n.ZA.MD:return"Молдова";case n.ZA.GE:return"Грузия";case n.ZA.OTHER:return"Другое";default:return""}}(e)}))}function s(){return Object.values(n.mm).map(e=>({value:e,label:function(e){switch(e){case n.mm.Economy:return"Эконом";case n.mm.Comfort:return"Комфорт";case n.mm.ComfortPlus:return"Комфорт+";case n.mm.Business:return"Бизнес";case n.mm.Premium:return"Премиум";case n.mm.Vip:return"VIP";case n.mm.Luxury:return"Люкс";default:return""}}(e),description:function(e){switch(e){case n.mm.Economy:return"Базовый уровень комфорта";case n.mm.Comfort:return"Повышенный комфорт";case n.mm.ComfortPlus:return"Улучшенный комфорт";case n.mm.Business:return"Бизнес-класс обслуживания";case n.mm.Premium:return"Максимальный комфорт и сервис";case n.mm.Vip:return"VIP обслуживание";case n.mm.Luxury:return"Люкс класс";default:return""}}(e)}))}function c(){return Object.values(n.TM).map(e=>({value:e,label:function(e){switch(e){case n.TM.RU:return"Русский";case n.TM.EN:return"Английский";case n.TM.KK:return"Казахский";case n.TM.KY:return"Кыргызский";case n.TM.UZ:return"Узбекский";case n.TM.TJ:return"Таджикский";case n.TM.HY:return"Армянский";case n.TM.AZ:return"Азербайджанский";case n.TM.KA:return"Грузинский";case n.TM.BE:return"Белорусский";case n.TM.UK:return"Украинский";default:return""}}(e),description:function(e){switch(e){case n.TM.RU:return"Официальный язык";case n.TM.EN:return"Международный язык";case n.TM.KK:return"Тюркский язык";case n.TM.KY:return"Государственный язык";case n.TM.UZ:return"Тюркский язык";case n.TM.TJ:return"Персидский язык";case n.TM.HY:return"Индоевропейский язык";case n.TM.AZ:return"Тюркский язык";case n.TM.KA:return"Картвельский язык";case n.TM.BE:case n.TM.UK:return"Славянский язык";default:return""}}(e)}))}},68998:(e,r,t)=>{var n=t(52906);let a=(0,n.lh)(),u=(0,n.lh)(),o=(0,n.lh)(),s=(0,n.lh)(),c=(0,n.y$)(!1).on(a,(e,r)=>r),l=(0,n.y$)({}).on(o,(e,r)=>{let{key:t,loading:n}=r;return{...e,[t]:n}}).on(s,()=>({})).map(e=>Object.values(e).some(e=>e)),i=(0,n.y$)(!1);(0,n.XM)({source:[c,l],fn:e=>{let[r,t]=e;return r||t},target:i});let m=(0,n.y$)(1200).on(u,(e,r)=>r),M=(0,n.lh)(),d=(0,n.EH)(async()=>{let e=Date.now()-A,r=Math.max(0,m.getState()-e);r>0&&await new Promise(e=>setTimeout(e,r))}),A=0;a.watch(e=>{e&&(A=Date.now())}),(0,n.XM)({source:M,target:d}),(0,n.XM)({source:d.doneData,fn:()=>!1,target:a})},78032:(e,r,t)=>{t.d(r,{d:()=>a});var n=t(7620);function a(e,r){let[t,a]=(0,n.useState)(e);return(0,n.useEffect)(()=>{let t=setTimeout(()=>a(e),r);return()=>clearTimeout(t)},[e,r]),t}}}]);