'use client';

import React, { createContext, useContext, useState, useCallback, type ReactNode } from 'react';
import { createPortal } from 'react-dom';

interface TooltipData {
  id: string;
  content: ReactNode;
  x: number;
  y: number;
  placement: 'top' | 'bottom' | 'left' | 'right';
  visible: boolean;
}

interface TooltipContextType {
  showTooltip: (id: string, content: ReactNode, x: number, y: number, placement?: 'top' | 'bottom' | 'left' | 'right') => void;
  hideTooltip: (id: string) => void;
  scale: number; // Добавляем scale в контекст
}

const TooltipContext = createContext<TooltipContextType | null>(null);

export const useTooltip = () => {
  const context = useContext(TooltipContext);

  if (!context) {
    throw new Error('useTooltip must be used within TooltipProvider');
  }

  return context;
};

interface TooltipProviderProps {
  children: ReactNode;
  scale?: number; // Масштаб от DynamicScale
}

export const TooltipProvider: React.FC<TooltipProviderProps> = ({ children, scale = 1 }) => {
  const [tooltips, setTooltips] = useState<Map<string, TooltipData>>(new Map());

  const showTooltip = useCallback((id: string, content: ReactNode, x: number, y: number, placement: 'top' | 'bottom' | 'left' | 'right' = 'top') => {
    setTooltips(prev => {
      const newMap = new Map(prev);

      newMap.set(id, { id, content, x, y, placement, visible: true });

      return newMap;
    });
  }, []);

  const hideTooltip = useCallback((id: string) => {
    setTooltips(prev => {
      const newMap = new Map(prev);
      const tooltip = newMap.get(id);

      if (tooltip) {
        newMap.set(id, { ...tooltip, visible: false });
        // Удаляем через небольшую задержку для анимации
        setTimeout(() => {
          setTooltips(current => {
            const updatedMap = new Map(current);

            updatedMap.delete(id);

            return updatedMap;
          });
        }, 200);
      }

      return newMap;
    });
  }, []);

  return (
    <TooltipContext.Provider value={{ showTooltip, hideTooltip, scale }}>
      {children}
      {/* Портал для рендера тултипов на самом высоком уровне */}
      {typeof window !== 'undefined' && Array.from(tooltips.values()).map(tooltip => {
        // Вычисляем позицию и трансформацию в зависимости от placement
        let style: React.CSSProperties = {
          maxWidth: '280px',
          zIndex: 9999,
          transformOrigin: '0 0',
        };

        let arrowStyle: React.CSSProperties = {
          width: '8px',
          height: '8px',
          position: 'absolute',
        };

        const inv = 1 / scale;
        const baseX = tooltip.x;
        const baseY = tooltip.y;

        switch (tooltip.placement) {
          case 'top':
            style = {
              ...style,
              left: `${baseX - 140}px`, // 280/2
              top: `${baseY}px`,
              transform: `translateY(-100%)`,
            };
            arrowStyle = {
              ...arrowStyle,
              left: '50%',
              top: '100%',
              transform: 'translateX(-50%) rotate(45deg)',
              marginTop: '-4px',
            };
            break;
          case 'bottom':
            style = {
              ...style,
              left: `${baseX - 140}px`,
              top: `${baseY}px`,
              // no translateX needed
            };
            arrowStyle = {
              ...arrowStyle,
              left: '50%',
              bottom: '100%',
              transform: 'translateX(-50%) rotate(45deg)',
              marginBottom: '-4px',
            };
            break;
          case 'right':
            style = {
              ...style,
              left: `${baseX}px`,
              top: `${baseY - 50}px`, // height/2
            };
            arrowStyle = {
              ...arrowStyle,
              right: '100%',
              top: '50%',
              transform: 'translateY(-50%) rotate(45deg)',
              marginRight: '-4px',
            };
            break;
          case 'left':
            style = {
              ...style,
              left: `${baseX - 280}px`,
              top: `${baseY - 50}px`,
            };
            arrowStyle = {
              ...arrowStyle,
              left: '100%',
              top: '50%',
              transform: 'translateY(-50%) rotate(45deg)',
              marginLeft: '-4px',
            };
            break;
        }

        return createPortal(
          <div
            key={tooltip.id}
            className={`fixed px-3 py-2 bg-gray-900 text-white text-xs rounded-lg transition-opacity duration-200 pointer-events-none ${
              tooltip.visible ? 'opacity-100' : 'opacity-0'
            }`}
            style={style}
          >
            {tooltip.content}
            {/* Стрелочка */}
            <div 
              className="absolute bg-gray-900"
              style={arrowStyle}
            />
          </div>,
          document.body
        );
      })}
    </TooltipContext.Provider>
  );
}; 