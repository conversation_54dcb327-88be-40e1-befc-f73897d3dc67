"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8371],{18371:(e,t,s)=>{s.r(t),s.d(t,{QRPaymentModal:()=>w});var a=s(54568),n=s(41186),r=s(61773),l=s(7620);function i(e){return Math.PI/180*e}var c=s(52906);let o=(0,c.lh)(),d=(0,c.lh)(),u=(0,c.y$)(null).on(o,(e,t)=>t).on(d,()=>null);(0,c.lh)();var m=s(11447),x=s(29988),h=s(94455),g=s(8426),f=s(59959),b=s(8703),p=s(27736);class j extends p.vA{async generateOptimaQR(e,t){let s={sum:e,note:t};console.log("\uD83D\uDD27 Отправляем запрос в Optima API (финальная версия):",s);let a=await this.post("/Optima/generate-qr",s);return this.handleApiResult(a)}async getPaymentStatus(e){let t=await this.get("/status/".concat(e));return this.handleApiResult(t)}async cancelPayment(e){let t=await this.delete("/".concat(e));this.handleApiResult(t)}constructor(...e){super(...e),this.baseUrl="/Payment"}}let y=new j,N=()=>{let[e,t]=(0,l.useState)({status:"idle"}),s=(0,f.k)(),a=(0,l.useRef)(void 0),n=(0,l.useCallback)(s=>{console.log("\uD83D\uDCB0 Получено уведомление о платеже:",s);let{paymentId:a,transactionId:n}=s.data;"waiting"===e.status&&e.transactionId===n&&t({status:"completed",paymentId:a,transactionId:n,sum:e.sum})},[e]);(0,l.useEffect)(()=>{if(s.isConnected&&"waiting"===e.status)return s.on("PaymentReceivedNotification",n),()=>{s.off("PaymentReceivedNotification",n)}},[s.isConnected,e.status,n]),(0,l.useEffect)(()=>{if("waiting"===e.status)return a.current=setTimeout(()=>{t({status:"expired",transactionId:e.transactionId}),b.P.error("Время ожидания платежа истекло")},3e5),()=>{a.current&&clearTimeout(a.current)}},[e]);let r=(0,l.useCallback)(async(e,s)=>{t({status:"generating",sum:e});try{console.log("\uD83D\uDE80 Генерация QR с параметрами:",{sum:e,note:s});let a=await y.generateOptimaQR(e,s);t({status:"waiting",transactionId:a.transactionId,qrBase64:a.qrBase64,sum:e}),console.log("✅ QR-код сгенерирован:",{transactionId:a.transactionId,sum:e})}catch(s){console.error("❌ Ошибка генерации QR:",s);let e=s instanceof Error?s.message:"Не удалось сгенерировать QR-код";t({status:"failed",error:e}),b.P.error(e)}},[]),i=(0,l.useCallback)(async()=>{if("waiting"===e.status)try{await y.cancelPayment(e.transactionId),t({status:"idle"})}catch(e){console.error("❌ Ошибка отмены платежа:",e),t({status:"idle"})}else t({status:"idle"})},[e]),c=(0,l.useCallback)(()=>{a.current&&clearTimeout(a.current),t({status:"idle"})},[]);return(0,l.useEffect)(()=>()=>{a.current&&clearTimeout(a.current)},[]),{state:e,generateQR:r,cancelPayment:i,reset:c}},w=()=>{let{state:e,generateQR:t,cancelPayment:s,reset:c}=N(),{selectedLocations:o}=(0,x.I)(),{economyTariff:d}=(0,h.Y)(),{terminalLocation:f}=(0,g.UH)(),[b,p]=(0,l.useState)(!1),j=(0,n.e3)(u),y=function(e,t){if(!e||!t||0===t.length)return 0;let s=0,a={latitude:e.latitude,longitude:e.longitude};console.log("\uD83D\uDDFA️ Начинаем расчет маршрута:"),console.log("\uD83D\uDCCD Стартовая точка:",e.address,"(".concat(a.latitude,", ").concat(a.longitude,")"));for(let e=0;e<t.length;e++){let n=t[e];if(n.latitude&&n.longitude){let t={latitude:n.latitude,longitude:n.longitude},r=function(e,t){let s=i(t.latitude-e.latitude),a=i(t.longitude-e.longitude),n=Math.sin(s/2)*Math.sin(s/2)+Math.cos(i(e.latitude))*Math.cos(i(t.latitude))*Math.sin(a/2)*Math.sin(a/2);return 2*Math.atan2(Math.sqrt(n),Math.sqrt(1-n))*6371}(a,t);console.log("\uD83D\uDCCD Точка ".concat(e+1,": ").concat(n.address," (").concat(n.latitude,", ").concat(n.longitude,")")),console.log("\uD83D\uDCCF Расстояние от предыдущей точки: ".concat(r.toFixed(2)," км")),s+=r,a=t}}return console.log("\uD83C\uDFAF Общее расстояние маршрута: ".concat(s.toFixed(2)," км")),s}(f,o),w=d?function(e,t){let s=Math.round(100*t)/100,a=Math.round(s*e.perKmPrice*100)/100,n=Math.round((e.basePrice+a)*100)/100,r=Math.round(Math.max(n,e.minimumPrice));return{distance:s,basePrice:e.basePrice,perKmPrice:e.perKmPrice,distancePrice:a,subtotal:n,minimumPrice:e.minimumPrice,finalPrice:r,isMinimumApplied:r===Math.round(e.minimumPrice)}}({basePrice:d.basePrice,perKmPrice:d.perKmPrice||0,minimumPrice:d.minimumPrice},y):null,v=(null==w?void 0:w.finalPrice)||(null==d?void 0:d.basePrice)||0;(0,l.useEffect)(()=>{!b&&v>0&&(p(!0),t(v,"Мгновенный заказ"))},[b,v,o.length,t]),(0,l.useEffect)(()=>{"completed"===e.status&&j&&(console.log("✅ Платеж успешно завершен:",e.paymentId),j(e.paymentId),setTimeout(()=>{(0,m.Oo)(),c()},2e3))},[e,j,c]);let P=async()=>{await s(),(0,m.Oo)(),c()};return(0,a.jsx)("div",{className:"fixed inset-0 black/80 backdrop-blur-md flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(()=>{switch(e.status){case"generating":return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Генерация QR-кода..."}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Подготавливаем платеж на сумму ",Math.round(e.sum)," KGS"]})]});case"waiting":return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Сканируйте QR-код для оплаты"}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-2xl shadow-lg mb-6 inline-block",children:(0,a.jsx)(r.default,{src:"data:image/png;base64,".concat(e.qrBase64),alt:"QR код для оплаты",width:300,height:300,className:"mx-auto"})}),(0,a.jsx)("div",{className:"bg-blue-50 p-4 rounded-xl mb-4",children:(0,a.jsxs)("p",{className:"text-xl font-semibold text-blue-800",children:["Сумма к оплате: ",Math.round(e.sum)," KGS"]})}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Откройте приложение банка и отсканируйте QR-код"}),(0,a.jsxs)("div",{className:"flex items-center justify-center text-orange-600",children:[(0,a.jsx)("div",{className:"animate-pulse w-3 h-3 bg-orange-500 rounded-full mr-2"}),"Ожидаем подтверждение платежа..."]})]});case"completed":return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-12 h-12 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-green-600 mb-2",children:"Платеж успешно завершен!"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["Сумма: ",Math.round(e.sum)," KGS"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Создаем ваш заказ..."})]});case"failed":return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-red-600 mb-2",children:"Ошибка платежа"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e.error})]});case"expired":return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-12 h-12 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-yellow-600 mb-2",children:"Время истекло"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Время ожидания платежа истекло"})]});default:return(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Подготовка к оплате..."})})}})(),(0,a.jsxs)("div",{className:"flex gap-4 mt-8",children:[("waiting"===e.status||"failed"===e.status||"expired"===e.status)&&(0,a.jsx)("button",{onClick:P,className:"flex-1 px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors",children:"Отменить"}),"failed"===e.status&&(0,a.jsx)("button",{onClick:()=>{c(),p(!1)},className:"flex-1 px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors",children:"Попробовать снова"})]})]})})}}}]);