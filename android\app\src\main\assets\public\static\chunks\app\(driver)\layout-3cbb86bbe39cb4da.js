(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2205],{600:(e,t,a)=>{"use strict";a.d(t,{Sidebar:()=>g});var r=a(54568),l=a(61773),s=a(27261),i=a.n(s),n=a(7620),o=a(27512),c=a(37780),d=a(8037),u=a(80980),m=a(47510);let p=e=>{let{collapsed:t}=e,[a,l]=(0,n.useState)([]),[s,i]=(0,n.useState)(!0),[o,c]=(0,n.useState)(null),d=(0,n.useCallback)(async()=>{try{console.log("\uD83D\uDD04 Загружаем онлайн водителей..."),i(!0),c(null);let e=await m.Dv.getDrivers({size:50,first:!0,online:!0});console.log("\uD83D\uDFE2 Онлайн водители с сервера:",e),console.log("\uD83D\uDCCA Количество онлайн водителей:",e.length),l(e)}catch(e){console.error("❌ Ошибка загрузки водителей:",e),c("Ошибка загрузки водителей")}finally{i(!1)}},[]);return((0,n.useEffect)(()=>{d()},[d]),(0,n.useEffect)(()=>{let e=setInterval(d,3e4);return()=>clearInterval(e)},[d]),console.log("\uD83C\uDFA8 OnlineDriversList рендер:",{collapsed:t,driversCount:a.length,isLoading:s,error:o}),t)?(console.log("\uD83D\uDCE6 Сайдбар свернут - скрываем водителей"),null):(0,r.jsxs)("div",{className:"px-2 py-4 h-full",children:[(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsxs)("h3",{className:"text-sm font-medium text-gray-300",children:["Водители (",a.length,")"]})}),s?(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,r.jsx)("span",{className:"ml-2 text-xs text-gray-400",children:"Загрузка..."})]}):o?(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsx)("p",{className:"text-xs text-red-400",children:o}),(0,r.jsx)("button",{onClick:d,className:"mt-1 text-xs text-blue-400 hover:text-blue-300 transition-colors",children:"Повторить"})]}):0===a.length?(0,r.jsx)("div",{className:"py-2",children:(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Нет онлайн водителей"})}):(0,r.jsx)("div",{className:"space-y-2 overflow-y-auto h-full pb-4 px-1",children:a.map(e=>{var t;return(0,r.jsxs)("div",{className:"p-2 rounded-md bg-gray-800/50 hover:bg-gray-700/50 transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-white truncate",children:e.fullName}),(0,r.jsx)("p",{className:"text-xs text-gray-400 truncate",children:e.phoneNumber||"Нет телефона"})]}),(null==(t=e.activeCar)?void 0:t.licensePlate)&&(0,r.jsx)("div",{className:"ml-2 flex-shrink-0",children:(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-900/50 text-blue-300",children:e.activeCar.licensePlate})})]}),(0,r.jsxs)("div",{className:"flex flex-row justify-between items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full mr-2 ".concat(e.online?"bg-green-500":"bg-gray-500")}),(0,r.jsx)("span",{className:"text-xs ".concat(e.online?"text-green-400":"text-gray-400"),children:e.online?"Онлайн":"Оффлайн"})]}),void 0!==e.isAvailable&&(0,r.jsx)("span",{className:"text-xs px-2 py-0.5 rounded ".concat(e.isAvailable?"bg-green-900/50 text-green-300":"bg-yellow-900/50 text-yellow-300"),children:e.isAvailable?"Доступен":"Занят"})]}),e.rating&&(0,r.jsx)("div",{className:"flex items-center mt-1",children:(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:["⭐ ",e.rating.toFixed(1)]})})]})]},e.id)})})]})},h=(0,n.lazy)(()=>Promise.resolve().then(a.bind(a,92971))),f=(0,n.lazy)(()=>Promise.resolve().then(a.bind(a,96430))),g=e=>{let{userRole:t,initialCollapsed:a=!1,overlayColor:s="#07080A",overlayOpacity:m=.7}=e,[g,b]=(0,n.useState)(a),v=(0,n.useMemo)(()=>t?(0,d.$r)(o.h,t):[],[t]),x=(0,u.Wj)(v),y=(0,n.useMemo)(()=>t===c.X.Admin||t===c.X.Operator,[t]),N=async()=>{let e=!g;b(e);try{await fetch("/api/sidebar",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({collapsed:e})})}catch(t){console.error("Failed to save sidebar state:",t),b(!e)}};return(0,r.jsxs)("aside",{className:"h-full transition-width duration-300 ease-in-out flex flex-col pt-4 px-4 gap-4 ".concat(g?"w-24":"w-84"),style:{transitionProperty:"width"},children:[(0,r.jsxs)("div",{className:"relative flex items-center justify-start h-12 px-2",children:[(0,r.jsx)("div",{className:"flex items-center w-full h-full",children:(0,r.jsx)(i(),{href:"/",className:"relative w-full h-full overflow-hidden transition-all duration-300 ease-in-out cursor-pointer",children:(0,r.jsx)(l.default,{src:"/logo/LogoBig.png",alt:"Compass Transfer",fill:!0,className:"object-contain",sizes:g?"96px":"336px"})})}),(0,r.jsx)("button",{onClick:N,className:"absolute right-[-15] inline-flex items-center rounded-sm border border-[color:var(--color-neutral-300)] p-1 transition-colors duration-300 cursor-pointer",children:g?(0,r.jsx)(n.Suspense,{fallback:(0,r.jsx)("div",{className:"h-5 w-5"}),children:(0,r.jsx)(h,{className:"h-5 w-5"})}):(0,r.jsx)(n.Suspense,{fallback:(0,r.jsx)("div",{className:"h-5 w-5"}),children:(0,r.jsx)(f,{className:"h-5 w-5"})})})]}),(0,r.jsx)("nav",{className:"flex-2 overflow-y-auto py-2 rounded-xl outline outline-offset-[-1px]",style:{backgroundColor:"".concat(s).concat(Math.round(255*m).toString(16).padStart(2,"0")),outlineColor:"".concat(s).concat(Math.round(255*Math.min(m+.1,1)).toString(16).padStart(2,"0"))},children:(0,r.jsx)("ul",{className:"flex flex-col gap-1 px-2 py-8",children:v.map(e=>(0,r.jsx)("li",{children:(0,r.jsxs)(i(),{href:e.path,className:"flex items-center p-3 rounded-md transition-colors duration-150 ".concat(x===e.id?"bg-[color:var(--color-brand)]/80":"hover:bg-gray-50/20"),style:{willChange:"background-color, color"},children:[(0,r.jsx)("span",{className:"text-current",children:e.icon}),(0,r.jsx)("span",{className:"transition-width duration-300 ease-in-out overflow-hidden whitespace-nowrap ".concat(g?"w-0 opacity-0":"w-auto opacity-100 pl-3"),style:{transitionProperty:"width, opacity",willChange:"width, opacity"},children:e.title})]})},e.id))})}),y&&(0,r.jsx)("div",{className:"flex-1 overflow-hidden rounded-t-xl outline outline-offset-[-1px]",style:{backgroundColor:"".concat(s).concat(Math.round(255*m).toString(16).padStart(2,"0")),outlineColor:"".concat(s).concat(Math.round(255*Math.min(m+.1,1)).toString(16).padStart(2,"0"))},children:(0,r.jsx)(p,{collapsed:g})})]})}},3141:(e,t,a)=>{"use strict";a.d(t,{TerminalTariffProvider:()=>u});var r=a(54568),l=a(7620);a(9039);var s=a(94455),i=a(27736),n=a(90683);let o=()=>{let[e,t]=(0,l.useState)([]),[a,r]=(0,l.useState)(!0),[s,o]=(0,l.useState)(null),[c,d]=(0,l.useState)(null);return(0,l.useEffect)(()=>{(async()=>{try{var e;r(!0),o(null),console.log("\uD83D\uDD04 Загружаем тарифы...");let a=await i.uE.get(i.QQ.TARIFF.LIST);if(null==(e=a.data)?void 0:e.data){let e=a.data.data;t(e);let r=e.find(e=>{var t;let a=(null==(t=e.name)?void 0:t.toLowerCase())||"";return e.serviceClass===n.m.Economy||a.includes("эконом")||a.includes("economy")||a.includes("базовый")})||e[0]||null;d(r),console.log("\uD83D\uDCE6 Загружено тарифов:",e.length),console.log("\uD83C\uDFAF Найден эконом тариф:",(null==r?void 0:r.name)||"не найден")}else t([]),d(null)}catch(e){console.error("❌ Ошибка загрузки тарифов:",e),o("Ошибка загрузки тарифов. Попробуйте обновить страницу."),t([]),d(null)}finally{r(!1)}})()},[]),{tariffs:e,isLoading:a,error:s,economyTariff:c}};a(77566),a(14701),a(83549),a(19189),a(75170),a(95559),a(34915);var c=a(80745);class d extends i.vA{async getTariff(e){let t=await this.get("/".concat(e));return this.handleApiResult(t)}async getTariffSafe(e){let t=await this.get("/".concat(e));return this.handleApiResultSafe(t)}async getTariffs(e){let t=new URLSearchParams;(null==e?void 0:e.first)!==void 0&&t.append("first",e.first.toString()),(null==e?void 0:e.before)&&t.append("before",e.before),(null==e?void 0:e.after)&&t.append("after",e.after),(null==e?void 0:e.last)!==void 0&&t.append("last",e.last.toString()),(null==e?void 0:e.size)!==void 0&&t.append("size",e.size.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.isActive)!==void 0&&t.append("isActive",e.isActive.toString());let a=t.toString()?"?".concat(t.toString()):"",r=await this.get(a);return this.handleApiResult(r)}async createTariff(e){let t=await this.post("",e);return this.handleApiResult(t)}async updateTariff(e,t){let a=await this.put("/".concat(e),t);return this.handleApiResult(a)}async deleteTariff(e){let t=await this.delete("/".concat(e));this.handleApiResult(t)}async toggleTariffStatus(e,t){let a=await this.patch("/".concat(e,"/status"),{isActive:t});return this.handleApiResult(a)}async calculatePrice(e,t,a){let r=await this.post("/".concat(e,"/calculate"),{distance:t,duration:a});return this.handleApiResult(r)}constructor(...e){super(...e),this.baseUrl=c.QQ.TARIFF.LIST}}new d;let u=e=>{let{children:t}=e,{tariffs:a,isLoading:l,error:i,economyTariff:n}=o();return(0,r.jsx)(s.C.Provider,{value:{economyTariff:n,tariffs:a,isLoading:l,error:i},children:t})}},5449:(e,t,a)=>{"use strict";a.d(t,{SignalRProvider:()=>c});var r=a(54568),l=a(7620),s=a(35035),i=a(68327);class n{registerHandler(e,t){this.handlers.has(e)||this.handlers.set(e,[]),this.handlers.get(e).push(t)}handleNotification(e,t){console.log("\uD83D\uDCE2 Обработка уведомления:",{type:e,data:t});let a=this.handlers.get(e);a&&a.forEach(e=>e(t)),this.showToast(e,t)}showToast(e,t){let a=t.title||"Уведомление",r=t.content||"";switch(e){case"RideRequest":case"RideRequestNotification":case"DriverHeadingNotification":i.P.info("\uD83D\uDE97 ".concat(a,": ").concat(r));break;case"RideAccepted":case"RideAcceptedNotification":case"OrderConfirmedNotification":i.P.success("✅ ".concat(a,": ").concat(r));break;case"RideCancelled":case"RideCancelledNotification":case"OrderCancelledNotification":i.P.warn("❌ ".concat(a,": ").concat(r));break;case"RideStarted":case"RideStartedNotification":i.P.success("\uD83D\uDE97 ".concat(a,": ").concat(r));break;case"DriverArrivedNotification":i.P.success("\uD83C\uDFC1 ".concat(a,": ").concat(r));break;case"DriverCancelledNotification":case"RideRejectedNotification":i.P.error("❌ ".concat(a,": ").concat(r));break;case"OrderCompletedNotification":i.P.success("\uD83C\uDF89 ".concat(a,": ").concat(r));break;case"RideStartedNotification":i.P.info("\uD83D\uDE80 ".concat(a,": ").concat(r));break;case"PaymentNotification":i.P.info("\uD83D\uDCB3 ".concat(a,": ").concat(r));break;case"PaymentReceivedNotification":i.P.success("\uD83D\uDCB0 ".concat(a,": ").concat(r));break;case"PaymentFailedNotification":i.P.error("\uD83D\uDCB8 ".concat(a,": ").concat(r));break;default:i.P.info("\uD83D\uDCE2 ".concat(a,": ").concat(r))}}constructor(){this.handlers=new Map}}let o=new n,c=e=>{let{children:t,accessToken:a}=e,[i,n]=(0,l.useState)(null),[c,d]=(0,l.useState)(!1),[u,m]=(0,l.useState)(!1),[p,h]=(0,l.useState)(null),f=(0,l.useRef)(new Map),g=(0,l.useCallback)(()=>{["RideRequestNotification","RideAcceptedNotification","RideAssignedNotification","RideCancelledNotification","RideCompletedNotification","DriverActivityUpdated","DriverArrivedNotification","DriverHeadingNotification","DriverCancelledNotification","OrderConfirmedNotification","OrderCancelledNotification","OrderCompletedNotification","RideRejectedNotification","RideStartedNotification","PaymentNotification","PaymentReceivedNotification","PaymentFailedNotification"].forEach(e=>{let t=f.current.get(e)||[];t.push(t=>{console.log("WebSocket уведомление [".concat(e,"]:"),t),o.handleNotification(e,t)}),f.current.set(e,t)}),console.log("Подписка на уведомления активирована")},[]),b=(0,l.useCallback)(async()=>{try{if(m(!0),h(null),!a)throw Error("JWT токен не найден");g();let e="".concat("ws://api.compass.local:3030/hubs/wshub","?access_token=").concat(a),t=new WebSocket(e);t.onopen=()=>{console.log("WebSocket подключен"),t.send('{"protocol":"json","version":1}\x1e'),n(t),d(!0),m(!1)},t.onclose=e=>{console.log("WebSocket соединение закрыто:",e),d(!1),n(null)},t.onerror=e=>{console.error("Ошибка WebSocket:",e),h("Ошибка подключения к WebSocket"),m(!1)},t.onmessage=e=>{try{console.log("Получено сообщение WebSocket:",e.data);let t=e.data.replace(/\x1e$/,"");if(!t||"{}"===t)return void console.log("Handshake успешно завершен");if(t.includes('"error"')){let e=JSON.parse(t);console.error("Ошибка от сервера:",e),h(e.error||"Ошибка сервера"),d(!1),n(null);return}let a=JSON.parse(t);if(1===a.type&&a.target&&a.arguments){let e=a.target,t=a.arguments[0];if(console.log("Обработка события [".concat(e,"]:"),t),f.current.has(e)){let a=f.current.get(e)||[];console.log("ВЫЗЫВАЮ ".concat(a.length," обработчиков для ").concat(e)),a.forEach(e=>{e(t)})}else console.log("НЕТ ОБРАБОТЧИКОВ для события ".concat(e))}}catch(t){console.error("Ошибка парсинга сообщения WebSocket:",t,"Данные:",e.data)}}}catch(e){h(e instanceof Error?e.message:"Ошибка подключения"),m(!1)}},[a,g]),v=(0,l.useCallback)(async()=>{i&&(i.close(),n(null),d(!1),f.current.clear())},[i]),x=(0,l.useCallback)((e,t)=>{let a=f.current.get(e)||[];a.push(t),f.current.set(e,a)},[]),y=(0,l.useCallback)((e,t)=>{let a=(f.current.get(e)||[]).filter(e=>e!==t);a.length>0?f.current.set(e,a):f.current.delete(e)},[]);(0,l.useEffect)(()=>{!a||c||u||(console.log("Автоматическое подключение к WebSocket..."),b().catch(e=>{console.error("Ошибка автоподключения к WebSocket:",e)}))},[a,c,u,b]),(0,l.useEffect)(()=>{c?console.log("WebSocket подключен и готов к работе"):p&&console.error("Ошибка WebSocket:",p)},[c,p]);let N={connection:i,isConnected:c,isConnecting:u,error:p,connect:b,disconnect:v,on:x,off:y};return c||p?p?(0,r.jsx)(s.I.Provider,{value:N,children:(0,r.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-red-500 text-lg mb-2",children:"Ошибка подключения"}),(0,r.jsx)("div",{children:p})]})})}):(0,r.jsx)(s.I.Provider,{value:N,children:t}):(0,r.jsx)(s.I.Provider,{value:N,children:(0,r.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"}),(0,r.jsx)("div",{children:u?"Подключение к серверу...":"Инициализация..."})]})})})}},8037:(e,t,a)=>{"use strict";a.d(t,{Y6:()=>s,az:()=>i,$r:()=>l});var r=a(37780);function l(e,t){return e.filter(e=>{if(["dashboard"].includes(e.id))return!0;switch(t){case r.X.Admin:return!0;case r.X.Partner:return["orders","tariffs"].includes(e.id);case r.X.Driver:return["orders","profile"].includes(e.id);case r.X.Operator:return["orders","users","drivers","tariffs","cars","services","notifications","locations"].includes(e.id);case r.X.Terminal:return["orders","profile"].includes(e.id);default:return!1}})}function s(e,t){let a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return e.map(e=>"notifications"===e.id?{...e,hasNotifications:t}:"scheduled-orders"===e.id?{...e,hasNotifications:a}:e)}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{showScheduledOrders:a=!0,showStatistics:r=!0,showSupport:l=!0}=t;return e.filter(e=>{switch(e.id){case"scheduled-orders":return a;case"statistics":return r;case"support":return l;default:return!0}})}},9039:(e,t,a)=>{"use strict";a.d(t,{m:()=>r.m});var r=a(90683)},12719:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,73970,23)),Promise.resolve().then(a.bind(a,72617)),Promise.resolve().then(a.bind(a,28128)),Promise.resolve().then(a.bind(a,54994)),Promise.resolve().then(a.bind(a,60916)),Promise.resolve().then(a.bind(a,5449)),Promise.resolve().then(a.bind(a,78171)),Promise.resolve().then(a.bind(a,44146)),Promise.resolve().then(a.bind(a,3141)),Promise.resolve().then(a.bind(a,81745)),Promise.resolve().then(a.bind(a,61889)),Promise.resolve().then(a.bind(a,92801)),Promise.resolve().then(a.bind(a,23690)),Promise.resolve().then(a.bind(a,20820)),Promise.resolve().then(a.bind(a,57870)),Promise.resolve().then(a.bind(a,45935)),Promise.resolve().then(a.bind(a,32037)),Promise.resolve().then(a.bind(a,600))},14701:(e,t,a)=>{"use strict";a.d(t,{I:()=>l,b:()=>s});var r=a(19189);let l={base:r.M,create:r.M,update:r.M},s=r.M},15493:(e,t,a)=>{"use strict";a.d(t,{F:()=>s});var r=a(64942),l=a(37556);let s={title:{create:"Создание новой локации",edit:"Редактирование локации"},groups:[{id:"main",title:"Основная информация",order:1,layout:{gridCols:2,gapX:8,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"infoGroup",type:r.V.GROUP,label:"Информация о локации",description:"Основные данные локации для идентификации в системе",layout:{flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"name",label:"Название",type:r.V.TEXT,placeholder:"Введите название локации",required:!0,helperSmallText:"Например: Аэропорт Манас, Отель Хаятт",helperBigText:"Название места, которое будет отображаться в системе",className:"w-full",maxLength:127},{name:"type",label:"Тип локации",type:r.V.SELECT,placeholder:"Выберите тип локации",required:!0,options:(0,l._n)(),helperSmallText:"Тип места",helperBigText:"Категория места для классификации в системе",className:"w-full"}]},{name:"addressGroup",type:r.V.GROUP,label:"Адрес",description:"Адресная информация о локации",layout:{flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"address",label:"Адрес",type:r.V.TEXT,placeholder:"Введите адрес",required:!0,helperSmallText:"Полный адрес",helperBigText:"Полный адрес локации, включая улицу и номер дома",className:"w-full",maxLength:255},{name:"district",label:"Округ/район",type:r.V.TEXT,placeholder:"Введите округ или район",required:!1,helperSmallText:"Округ или район (необязательно)",helperBigText:"Административный округ или район, где находится локация",className:"w-full",maxLength:63},{name:"city",label:"Город",type:r.V.TEXT,placeholder:"Введите город",required:!0,helperSmallText:"Город или населенный пункт",helperBigText:"Город или населенный пункт, где находится локация",className:"w-full",maxLength:63},{name:"region",label:"Область/регион",type:r.V.TEXT,placeholder:"Введите область или регион",required:!0,helperSmallText:"Область или регион",helperBigText:"Область или регион, где находится локация",className:"w-full",maxLength:63},{name:"country",label:"Страна",type:r.V.TEXT,placeholder:"Введите страну",required:!0,helperSmallText:"Страна",helperBigText:"Страна, в которой находится локация",className:"w-full",maxLength:63}]}]},{id:"coordinates",title:"Местоположение на карте",order:2,description:"Выберите точное местоположение на интерактивной карте",layout:{gridCols:1,gapX:6,gapY:4,className:"h-full",flexDirection:"column",maxItemsPerRow:1,itemsAlignment:"start"},fields:[{name:"mapLocationPicker",label:"Интерактивная карта",type:r.V.MAP_LOCATION_PICKER,required:!0,helperSmallText:"Кликните на карту для выбора точного местоположения",helperBigText:"Выберите мес��оположение на карте. Координаты будут автоматически сохранены.",className:"w-full"},{name:"latitude",label:"Широта",type:r.V.HIDDEN,required:!0},{name:"longitude",label:"Долгота",type:r.V.HIDDEN,required:!0}]},{id:"settings",title:"Настройки",order:3,description:"Настройки активности и популярности локации",layout:{gridCols:1,gapX:6,gapY:4,className:"h-full",flexDirection:"column",maxItemsPerRow:1,itemsAlignment:"start"},fields:[{name:"isActive",label:"Активная локация",type:r.V.CHECKBOX,required:!0,helperSmallText:"Активна ли локация",helperBigText:"Определяет, доступна ли локация для использования в системе",className:"w-full",defaultValue:!0},{name:"popular1",label:"Популярная локация 1",type:r.V.CHECKBOX,required:!1,helperSmallText:"Отметить как популярную локацию (категория 1)",helperBigText:"Популярные локации отображаются в приоритетном порядке",className:"w-full"},{name:"popular2",label:"Популярная локация 2",type:r.V.CHECKBOX,required:!1,helperSmallText:"Отметить как популярную локацию (категория 2)",helperBigText:"Популярные локации отображаются в приоритетном порядке",className:"w-full"}]}]}},19189:(e,t,a)=>{"use strict";a.d(t,{M:()=>s});var r=a(64942),l=a(95559);let s={title:{create:"Создание нового тарифа",edit:"Редактирование тарифа"},groups:[{id:"main",title:"Основная информация",order:1,layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"name",label:"Название",type:r.V.TEXT,required:!0,placeholder:"Введите название тарифа",helperSmallText:"Название тарифа (Эконом, Комфорт)",helperBigText:"Укажите понятное и краткое название тарифа, которое будет отображаться клиентам",className:"w-full",maxLength:127},{name:"serviceClass",label:"Класс обслуживания",type:r.V.SELECT,required:!0,placeholder:"Выберите класс обслуживания",options:(0,l.cx)(),helperSmallText:"Класс обслуживания",helperBigText:"Выберите класс обслуживания, который соответствует данному тарифу",className:"w-full"}]},{id:"pricing",title:"Стоимость",order:2,description:"Настройки стоимости тарифа",layout:{gridCols:2,gapX:4,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"center"},fields:[{name:"basePrice",label:"Базовая стоимость",type:r.V.NUMBER,required:!0,placeholder:"Введите базовую стоимость",helperSmallText:"Базовая стоимость подачи",helperBigText:"Базовая стоимость подачи / минимальная стоимость",className:"w-full",min:0,step:.01},{name:"minutePrice",label:"Стоимость минуты",type:r.V.NUMBER,required:!0,placeholder:"Введите стоимость минуты",helperSmallText:"Стоимость минуты поездки",helperBigText:"Стоимость одной минуты поездки по данному тарифу",className:"w-full",min:0,step:.01},{name:"minimumPrice",label:"Минимальная стоимость",type:r.V.NUMBER,required:!0,placeholder:"Введите минимальную стоимость",helperSmallText:"Минимальная стоимость поездки",helperBigText:"Минимальная стоимость поездки по данному тарифу",className:"w-full",min:0,step:.01},{name:"perKmPrice",label:"Стоимость километра",type:r.V.NUMBER,required:!0,placeholder:"Введите стоимость километра",helperSmallText:"Стоимость километра поездки (сом)",helperBigText:"Стоимость одного километра поездки по данному тарифу",className:"w-full",min:0,step:.01}]}]}},20820:(e,t,a)=>{"use strict";a.d(t,{DriverHeader:()=>c});var r=a(54568);a(7620);var l=a(88154);let s=e=>{let{className:t=""}=e;return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 ".concat(t),children:(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"flex flex-row items-center gap-4 justify-between",children:[(0,r.jsxs)("div",{className:"flex flex-col items-start space-x-4 p-6",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse mb-2"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full animate-pulse"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 animate-pulse"})]})]})]}),(0,r.jsxs)("div",{className:"hidden md:flex flex-col items-start space-y-2",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-20 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-28 animate-pulse"})]}),(0,r.jsx)("div",{className:"hidden lg:block",children:(0,r.jsx)("div",{className:"w-48 h-24 bg-gray-200 rounded animate-pulse"})})]})})})};var i=a(61773);let n=e=>{let{profile:t,displayName:a,displayPhone:l,avatarInitials:s,isActive:n,className:o=""}=e;return(0,r.jsxs)("div",{className:"flex-1 flex flex-col items-start space-x-4 p-6 ".concat(o),children:[(0,r.jsx)("div",{className:"text-sm text-gray-500 font-medium mb-2",children:"Водитель"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center overflow-hidden",children:(null==t?void 0:t.avatar)?(0,r.jsx)(i.default,{src:t.avatar,alt:"Аватар водителя",width:64,height:64,className:"w-full h-full rounded-full object-cover",onError:()=>{console.warn("Ошибка загрузки аватара:",t.avatar)},placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="}):(0,r.jsx)("span",{className:"text-white text-xl font-bold",children:s})}),(0,r.jsx)("div",{className:"\n            absolute -bottom-1 -right-1 w-5 h-5 border-2 border-white rounded-full\n            ".concat(n?"bg-green-500":"bg-gray-400","\n          ")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold  w-[300px]",children:a}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:l})]})]})]})},o=e=>{let{vehicle:t,carImagePath:a,hasVehicleInfo:l,className:s=""}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex-1 hidden md:flex flex-col items-start space-y-1 ".concat(s),children:l&&t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-lg font-bold",children:t.licensePlate}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[t.category," - ",t.bodyType]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[t.make," ",t.model]})]}):(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Автомобиля нет"})}),(0,r.jsx)("div",{className:"hidden lg:block relative flex-2 h-[196px]",children:l&&t?(0,r.jsx)(i.default,{src:"/car/HongQi E-QM5.png",alt:"".concat(t.make," ").concat(t.model),fill:!0,style:{objectFit:"contain"},onError:()=>{console.warn("Ошибка загрузки изображения автомобиля:",a)}}):(0,r.jsx)("div",{className:"text-6xl",children:"\uD83D\uDE97"})})]})},c=e=>{let{className:t=""}=e,{profile:a,isLoading:i,isActive:c,carImagePath:d,displayName:u,displayPhone:m,avatarInitials:p,hasVehicleInfo:h}=(0,l.V$)();return i?(0,r.jsx)(s,{className:t}):(0,r.jsx)("header",{className:"rounded-2xl ".concat(t),children:(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(n,{profile:a,displayName:u,displayPhone:m,avatarInitials:p,isActive:c}),(0,r.jsx)(o,{vehicle:(null==a?void 0:a.vehicle)||null,carImagePath:d,hasVehicleInfo:h})]})})})}},23690:(e,t,a)=>{"use strict";a.d(t,{CollectionHeader:()=>eR});var r=a(54568),l=a(41186),s=a(62942),i=a(7620),n=a(11447),o=a(75006),c=a(45901);o.z.object({make:o.z.string().min(1,{message:"Марка автомобиля обязательна"}).max(63,{message:"Марка автомобиля не должна превышать 63 символа"}),model:o.z.string().min(1,{message:"Модель автомобиля обязательна"}).max(63,{message:"Модель автомобиля не должна превышать 63 символа"}),year:o.z.number({required_error:"Год выпуска обязателен",invalid_type_error:"Год выпуска должен быть числом"}).int({message:"Год выпуска должен быть целым числом"}).min(1900,{message:"Год выпуска должен быть не ранее 1900"}).max(new Date().getFullYear()+1,{message:"Год выпуска не может быть в будущем"}),color:o.z.nativeEnum(c.OU,{errorMap:()=>({message:"Выберите цвет автомобиля"})}),licensePlate:o.z.string().min(1,{message:"Государственный регистрационный знак обязателен"}).max(15,{message:"Государственный регистрационный знак не должен превышать 15 символов"}),type:o.z.nativeEnum(c.$O,{errorMap:()=>({message:"Выберите тип автомобиля"})}),serviceClass:o.z.nativeEnum(c.mm,{errorMap:()=>({message:"Выберите класс обслуживания"})}),status:o.z.nativeEnum(c.zl,{errorMap:()=>({message:"Выберите статус автомобиля"})}),passengerCapacity:o.z.number({required_error:"Пассажировместимость обязательна",invalid_type_error:"Пассажировместимость должна быть числом"}).int({message:"Пассажировместимость должна быть целым числом"}).min(1,{message:"Пассажировместимость должна быть не менее 1"}).max(50,{message:"Пассажировместимость должна быть не более 50"}),features:o.z.array(o.z.nativeEnum(c.Lz)).min(1,{message:"Выберите хотя бы одну дополнительную опцию"}),firstShiftDriver:o.z.string().optional(),secondShiftDriver:o.z.string().optional(),previousFirstShiftDriver:o.z.string().optional(),previousSecondShiftDriver:o.z.string().optional()}),o.z.object({make:o.z.string().min(1,{message:"Марка автомобиля обязательна"}).max(63,{message:"Марка автомобиля не должна превышать 63 символа"}),model:o.z.string().min(1,{message:"Модель автомобиля обязательна"}).max(63,{message:"Модель автомобиля не должна превышать 63 символа"}),year:o.z.number({required_error:"Год выпуска обязателен",invalid_type_error:"Год выпуска должен быть числом"}).int({message:"Год выпуска должен быть целым числом"}).min(1900,{message:"Год выпуска должен быть не ранее 1900"}).max(new Date().getFullYear()+1,{message:"Год выпуска не может быть в будущем"}),color:o.z.nativeEnum(c.OU,{errorMap:()=>({message:"Выберите цвет автомобиля"})}),licensePlate:o.z.string().min(1,{message:"Государственный регистрационный знак обязателен"}).max(15,{message:"Государственный регистрационный знак не должен превышать 15 символов"}),type:o.z.nativeEnum(c.$O,{errorMap:()=>({message:"Выберите тип автомобиля"})}),serviceClass:o.z.nativeEnum(c.mm,{errorMap:()=>({message:"Выберите класс обслуживания"})}),status:o.z.nativeEnum(c.zl,{errorMap:()=>({message:"Выберите статус автомобиля"})}),passengerCapacity:o.z.number({required_error:"Пассажировместимость обязательна",invalid_type_error:"Пассажировместимость должна быть числом"}).int({message:"Пассажировместимость должна быть целым числом"}).min(1,{message:"Пассажировместимость должна быть не менее 1"}).max(50,{message:"Пассажировместимость должна быть не более 50"}),features:o.z.array(o.z.nativeEnum(c.Lz)).min(1,{message:"Выберите хотя бы одну дополнительную опцию"}),id:o.z.string().uuid({message:"Некорректный идентификатор автомобиля"}),firstShiftDriver:o.z.string().optional(),secondShiftDriver:o.z.string().optional(),previousFirstShiftDriver:o.z.string().optional(),previousSecondShiftDriver:o.z.string().optional()}),o.z.boolean({errorMap:()=>({message:"Значение должно быть булевым (true/false)"})}),o.z.object({carId:o.z.string().uuid("ID автомобиля должен быть валидным UUID").min(1,"ID автомобиля обязателен"),driverIds:o.z.array(o.z.string().uuid("ID водителя должен быть валидным UUID")).min(1,"Необходимо указать хотя бы одного водителя").max(2,"Автомобилю можно назначить максимум 2 водителей").refine(e=>new Set(e).size===e.length,"ID водителей не должны повторяться"),isActive:o.z.boolean().default(!0)}),o.z.boolean({errorMap:()=>({message:"Значение должно быть булевым (true/false)"})}),o.z.object({carId:o.z.string().uuid("ID автомобиля должен быть валидным UUID").min(1,"ID автомобиля обязателен"),driverId:o.z.string().uuid("ID водителя должен быть валидным UUID").min(1,"ID водителя обязателен"),updates:o.z.object({startDate:o.z.string().datetime("Дата должна быть в формате ISO 8601").optional(),endDate:o.z.string().datetime("Дата должна быть в формате ISO 8601").optional(),isActive:o.z.boolean({errorMap:()=>({message:"IsActive должен быть булевым значением"})}).optional(),status:o.z.enum(["Active","Inactive","Suspended"],{errorMap:()=>({message:"Статус должен быть Active, Inactive или Suspended"})}).optional(),notes:o.z.string().max(500,"Примечания не должны превышать 500 символов").optional()}).partial()}),o.z.object({carId:o.z.string().uuid("ID автомобиля должен быть валидным UUID").min(1,"ID автомобиля обязателен")}),o.z.object({carId:o.z.string().uuid("ID автомобиля должен быть валидным UUID").optional()}),o.z.string().uuid("UUID должен быть валидным").min(1,"UUID обязателен"),o.z.string().uuid("UUID должен быть валидным").optional(),o.z.object({message:o.z.string(),activeCarId:o.z.string().uuid().nullable(),timestamp:o.z.string().datetime()}),o.z.object({code:o.z.string(),message:o.z.string(),details:o.z.string().optional()});var d=a(89751),u=a(78816);o.z.enum(["Unknown","Instant","Scheduled","Partner","Shuttle","Subscription"]);let m=o.z.enum(["Pending","Scheduled","InProgress","Completed","Cancelled","Expired"]);o.z.enum(["SearchingDriver","DriverAssigned","DriverHeading","DriverArrived","RideStarted","RideFinished","PaymentPending","PaymentCompleted","ReviewPending","CancelledByClient","CancelledByDriver","CancelledBySystem","CancelledByOperator"]);let p=o.z.object({serviceId:o.z.string({required_error:"Выберите услугу"}).uuid({message:"Выберите корректную услугу"}),quantity:o.z.number({required_error:"Укажите количество",invalid_type_error:"Количество должно быть числом"}).int({message:"Количество должно быть целым числом"}).min(1,{message:"Количество должно быть больше 0"}),notes:o.z.string().nullable().optional()}),h=o.z.object({customerId:o.z.string().uuid({message:"Выберите корректного клиента"}).nullable().optional(),firstName:o.z.string({required_error:"Имя пассажира обязательно"}).min(1,{message:"Имя пассажира обязательно"}),lastName:o.z.string().nullable(),isMainPassenger:o.z.boolean({required_error:"Укажите основного пассажира"})});o.z.object({tariffId:o.z.string({required_error:"Выберите тариф"}).uuid({message:"Выберите корректный тариф"}),routeId:o.z.string().uuid({message:"Некорректный маршрут"}).nullable().optional(),startLocationId:o.z.string().uuid({message:"Некорректная начальная точка"}).nullable().optional(),endLocationId:o.z.string().uuid({message:"Некорректная конечная точка"}).nullable().optional(),additionalStops:o.z.array(o.z.string().uuid({message:"Некорректная промежуточная точка"})).default([]),services:o.z.array(p).default([]),initialPrice:o.z.number({required_error:"Укажите стоимость"}).min(0,{message:"Стоимость не может быть отрицательной"}),passengers:o.z.array(h).min(1,{message:"Добавьте хотя бы одного пассажира"}),paymentId:o.z.string().min(1,{message:"Некорректный ID платежа"}).optional()}).refine(e=>{let t=null!==e.routeId&&void 0!==e.routeId&&""!==e.routeId,a=null!==e.startLocationId&&void 0!==e.startLocationId&&""!==e.startLocationId&&null!==e.endLocationId&&void 0!==e.endLocationId&&""!==e.endLocationId;return t||a},{message:"Укажите либо готовый маршрут, либо начальную и конечную точки",path:["routeId"]}).refine(e=>1===e.passengers.filter(e=>e.isMainPassenger).length,{message:"Должен быть ровно один главный пассажир",path:["passengers"]});let f=()=>o.z.string().transform(e=>{if(!e||e.includes("Z")||e.match(/[+-]\d{2}:\d{2}$/))return e;if(e.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)){let[t,a]=e.split("T"),[r,l,s]=t.split("-").map(Number),[i,n]=a.split(":").map(Number);return new Date(r,l-1,s,i,n,0,0).toISOString()}return e}),g=o.z.object({locationId:o.z.string({required_error:"Выберите локацию"}).uuid({message:"Выберите корректную локацию"}),arrivalTime:o.z.string().nullable().optional(),departureTime:o.z.string().nullable().optional()});o.z.object({tariffId:o.z.string({required_error:"Выберите тариф"}).uuid({message:"Выберите корректный тариф"}),routeType:o.z.enum(["template","manual"]).optional(),routeId:o.z.union([o.z.string().uuid({message:"Выберите корректный маршрут"}),o.z.null(),o.z.literal("").transform(()=>null)]).optional(),startLocationId:o.z.union([o.z.string().uuid({message:"Выберите корректную начальную точку"}),o.z.null(),o.z.literal("").transform(()=>null)]).optional(),endLocationId:o.z.union([o.z.string().uuid({message:"Выберите корректную конечную точку"}),o.z.null(),o.z.literal("").transform(()=>null)]).optional(),additionalStops:o.z.array(o.z.string().uuid({message:"Некорректная промежуточная точка"})).default([]),services:o.z.array(p).default([]),initialPrice:o.z.preprocess(e=>{if("string"==typeof e){let t=parseFloat(e);return isNaN(t)?e:t}return e},o.z.number({required_error:"Укажите стоимость",invalid_type_error:"Стоимость должна быть числом"}).min(0,{message:"Стоимость должна быть положительным числом"})),scheduledTime:f().refine(e=>e&&e.length>0,{message:"Выберите дату и время отправки"}),passengers:o.z.array(h).min(1,{message:"Добавьте хотя бы одного пассажира"}),clientType:o.z.enum(["existing","new"],{required_error:"Выберите тип клиента"}).default("new").optional(),customerId:o.z.string().optional(),firstName:o.z.string().default("Нет имени").optional(),lastName:o.z.string().optional(),driverId:o.z.string().optional(),carId:o.z.string().optional(),waypoints:o.z.array(g).optional().default([])}).refine(e=>{if("template"===e.routeType)return null!==e.routeId&&void 0!==e.routeId&&""!==e.routeId;if("manual"===e.routeType)return null!==e.startLocationId&&void 0!==e.startLocationId&&""!==e.startLocationId&&null!==e.endLocationId&&void 0!==e.endLocationId&&""!==e.endLocationId;let t=null!==e.routeId&&void 0!==e.routeId&&""!==e.routeId,a=null!==e.startLocationId&&void 0!==e.startLocationId&&""!==e.startLocationId&&null!==e.endLocationId&&void 0!==e.endLocationId&&""!==e.endLocationId;return t||a},{message:"Укажите либо готовый маршрут, либо начальную и конечную точки",path:["routeId"]});let b=o.z.object({locationId:o.z.string({required_error:"Выберите локацию"}).uuid({message:"Выберите корректную локацию"}),arrivalTime:f().nullable().optional(),departureTime:f().nullable().optional()}).refine(e=>!e.arrivalTime||!e.departureTime||new Date(e.departureTime)>new Date(e.arrivalTime),{message:"Время отправления должно быть позже времени прибытия",path:["departureTime"]});o.z.object({driverId:o.z.string({required_error:"Выберите водителя"}).uuid({message:"Выберите корректного водителя"}),carId:o.z.string({required_error:"Выберите автомобиль"}).uuid({message:"Выберите корректный автомобиль"}),waypoints:o.z.array(b).min(1,{message:"Добавьте хотя бы одну точку маршрута"}).default([])}).partial().extend({rideId:o.z.string({required_error:"ID поездки обязателен"}).uuid({message:"Некорректный ID поездки"})}),o.z.object({tariffId:o.z.string({required_error:"Выберите тариф"}).uuid({message:"Выберите корректный тариф"}),routeId:o.z.string().uuid({message:"Некорректный маршрут"}).nullable().optional(),startLocationId:o.z.string().uuid({message:"Некорректная начальная точка"}).nullable().optional(),endLocationId:o.z.string().uuid({message:"Некорректная конечная точка"}).nullable().optional(),additionalStops:o.z.array(o.z.string().uuid({message:"Некорректная промежуточная точка"})).default([]),services:o.z.array(p).default([]),initialPrice:o.z.number({required_error:"Укажите стоимость"}).min(0,{message:"Стоимость не может быть отрицательной"}),status:m}).refine(e=>!!e.routeId||e.startLocationId&&e.endLocationId,{message:"Необходимо указать либо шаблон маршрута, либо начальную и конечную точки",path:["routeId"]});let v=o.z.object({id:o.z.string().uuid({message:"Некорректный ID пассажира"}),customerId:o.z.string().uuid({message:"Выберите корректного клиента"}).nullable().optional(),firstName:o.z.string({required_error:"Имя пассажира обязательно"}).min(1,{message:"Имя пассажира обязательно"}).max(63,{message:"Имя не должно превышать 63 символа"}),lastName:o.z.string().max(63,{message:"Фамилия не должна превышать 63 символа"}).nullable().optional(),isMainPassenger:o.z.boolean({required_error:"Укажите основного пассажира"})});o.z.object({orderId:o.z.string().uuid({message:"Некорректный ID заказа"}),tariffId:o.z.string({required_error:"Выберите тариф"}).uuid({message:"Выберите корректный тариф"}),routeId:o.z.string().uuid({message:"Некорректный маршрут"}).nullable().optional(),startLocationId:o.z.string().uuid({message:"Некорректная начальная точка"}).nullable().optional(),endLocationId:o.z.string().uuid({message:"Некорректная конечная точка"}).nullable().optional(),additionalStops:o.z.array(o.z.string().uuid({message:"Некорректная промежуточная точка"})).default([]),services:o.z.array(p).default([]),initialPrice:o.z.number({required_error:"Укажите стоимость"}).min(0,{message:"Стоимость не может быть отрицательной"}),scheduledTime:f().refine(e=>e&&e.length>0,{message:"Укажите время"}),passengers:o.z.array(v).min(1,{message:"Добавьте хотя бы одного пассажира"}),status:m}).refine(e=>!!e.routeId||e.startLocationId&&e.endLocationId,{message:"Необходимо указать либо шаблон маршрута, либо начальную и конечную точки",path:["routeId"]}).refine(e=>1===e.passengers.filter(e=>e.isMainPassenger).length,{message:"Должен быть ровно один главный пассажир",path:["passengers"]});let x=o.z.object({name:o.z.string().min(1,{message:"Название услуги обязательно"}).max(127,{message:"Название услуги не должно превышать 127 символов"}).describe("Название услуги"),description:o.z.string().max(255,{message:"Описание услуги не должно превышать 255 символов"}).nullable().describe("Описание услуги (сом)"),price:o.z.number({required_error:"Цена услуги обязательна",invalid_type_error:"Цена услуги должна быть числом"}).positive({message:"Цена услуги должна быть положительным числом"}).describe("Цена услуги (сом)"),isQuantifiable:o.z.boolean({required_error:"Необходимо указать, можно ли указать количество",invalid_type_error:"Поле должно быть логическим значением"}).describe("Можно ли указать количество единиц услуги")}).extend({id:o.z.string().uuid({message:"Некорректный формат UUID"}).describe("Идентификатор услуги")});o.z.object({data:o.z.array(x).describe("Данные услуг"),totalCount:o.z.number().int().describe("Общее количество записей"),pageSize:o.z.number().int().describe("Размер страницы"),hasPrevious:o.z.boolean().describe("Есть ли предыдущая страница"),hasNext:o.z.boolean().describe("Есть ли следующая страница")});var y=a(77566),N=a(61670);d.bo,u.gz,y.El,o.z.object({}),d.e1,u.o6,y.B8,o.z.object({}),N.Tg,N.UG,N.MJ,N.Qu,N.i5,N.yD,N.AC,N.Rv,N.jz,N.JR,N.pp,N.Pe,u.mm;var j=a(27736);a(25671),a(39071),a(80745);a(34915),a(52795);var S=a(27261),w=a.n(S);function C(e){switch(e){case c.OU.Black:return"Черный";case c.OU.White:return"Белый";case c.OU.Silver:return"Серебристый";case c.OU.Gray:return"Серый";case c.OU.Red:return"Красный";case c.OU.Blue:return"Синий";case c.OU.Green:return"Зеленый";case c.OU.Yellow:return"Желтый";case c.OU.Brown:return"Коричневый";case c.OU.Orange:return"Оранжевый";case c.OU.Purple:return"Фиолетовый";case c.OU.Gold:return"Золотой";case c.OU.Other:return"Другой";default:return""}}function T(){return Object.values(c.OU).map(e=>({value:e,label:C(e)}))}function A(e){switch(e){case c.$O.Sedan:return"Седан";case c.$O.Hatchback:return"Хэтчбек";case c.$O.SUV:return"Внедорожник";case c.$O.Minivan:return"Минивэн";case c.$O.Coupe:return"Купе";case c.$O.Cargo:return"Грузовой";case c.$O.Pickup:return"Пикап";default:return""}}function R(){return Object.values(c.$O).map(e=>({value:e,label:A(e)}))}function I(e){switch(e){case c.zl.Available:return"Доступен";case c.zl.Maintenance:return"На техобслуживании";case c.zl.Repair:return"На ремонте";case c.zl.Other:return"Другое";default:return""}}function P(){return Object.values(c.zl).map(e=>({value:e,label:I(e)}))}let k=(0,i.lazy)(()=>Promise.resolve().then(a.bind(a,11523))),z={columns:[{id:"make",label:"Марка",accessor:"make",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"model",label:"Модель",accessor:"model",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"year",label:"Год",accessor:"year",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]}},{id:"licensePlate",label:"Гос. номер",accessor:"licensePlate",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"color",label:"Цвет",accessor:"color",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:T()},renderCell:e=>(0,r.jsx)("span",{children:C(e.color)})},{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:R()},renderCell:e=>(0,r.jsx)("span",{children:A(e.type)})},{id:"status",label:"Статус",accessor:"status",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:P()},renderCell:e=>{let t=I(e.status),a="";switch(e.status){case c.zl.Available:a="bg-green-100 text-green-800";break;case c.zl.Maintenance:a="bg-yellow-100 text-yellow-800";break;case c.zl.Repair:a="bg-red-100 text-red-800";break;default:a="bg-gray-100 text-gray-800"}return(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(a),children:t})}},{id:"drivers",label:"Водители",accessor:"drivers",sortable:!1,filterable:!1,renderCell:e=>{var t;let a=(null==(t=e.drivers)?void 0:t.length)||0,l="",s="";return 0===a?(l="bg-red-100 text-red-800",s="Нет водителей"):(l=2===a?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800",s="".concat(a,"/").concat(2)),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(l),children:s})}},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,r.jsx)("div",{className:"flex justify-end gap-2",children:(0,r.jsx)(w(),{href:"/cars/".concat(e.id),className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",title:"Редактировать автомобиль (включая водителей)",children:(0,r.jsx)(k,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"Make",label:"Марка",operator:"contains",operatorField:"MakeOp"},{field:"Model",label:"Модель",operator:"contains",operatorField:"ModelOp"},{field:"LicensePlate",label:"Гос. номер",operator:"contains",operatorField:"LicensePlateOp"}]},filtersConfig:{availableFilters:[{field:"Status",label:"Статус",type:"select",options:P().map(e=>({value:e.value,label:e.label}))},{field:"Type",label:"Тип транспорта",type:"select",options:R().map(e=>({value:e.value,label:e.label}))},{field:"Color",label:"Цвет",type:"select",options:T().map(e=>({value:e.value,label:e.label}))},{field:"Year",label:"Год выпуска",type:"number"}]}};var E=a(50385),D=a(77914),O=a(66251);a(97509);let L=(0,i.createContext)(null),U=()=>{let e=(0,i.useContext)(L);if(!e)throw Error("useTooltip must be used within TooltipProvider");return e},q=e=>{let{content:t,delay:a=300}=e,{showTooltip:r,hideTooltip:l,scale:s}=U(),n=(0,i.useRef)(null),o=(0,i.useRef)(null),c=(0,i.useRef)("tooltip-".concat(Math.random().toString(36).substr(2,9))),d=(0,i.useCallback)(()=>{o.current&&clearTimeout(o.current),o.current=setTimeout(()=>{if(n.current){let e=n.current.getBoundingClientRect(),a=1/s,l=(e.left+e.width/2)*a,i=e.top*a,o="top";i-100-12>=0?(i-=12,o="top"):e.bottom*a+100+12<=window.innerHeight?(i=e.bottom*a+12,o="bottom"):e.right*a+280+12<=window.innerWidth?(l=e.right*a+12,i=(e.top+e.height/2)*a,o="right"):(l=e.left*a-12,i=(e.top+e.height/2)*a,o="left"),("top"===o||"bottom"===o)&&(l-140<8?l=148:l+140>window.innerWidth-8&&(l=window.innerWidth-140-8)),r(c.current,t,l,i,o)}},a)},[t,a,r]);return{triggerProps:{ref:n,onMouseEnter:d,onMouseLeave:(0,i.useCallback)(()=>{o.current&&clearTimeout(o.current),l(c.current)},[l])}}};var B=a(77963),M=a(12691),F=a(38749);let V=e=>{let{count:t,label:a,description:l,color:s,onClick:i,isActive:n}=e,{triggerProps:o}=q({content:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"font-semibold",children:a}),(0,r.jsx)("div",{className:"text-gray-300",children:l}),(0,r.jsxs)("div",{className:"text-gray-400 text-xs",children:["Количество: ",t]}),(0,r.jsx)("div",{className:"text-blue-300 text-xs mt-1",children:"Нажмите для фильтрации"})]}),delay:200});return(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("button",{...o,onClick:i,className:"".concat(s," rounded-full w-12 h-12 flex items-center justify-center border-2 ").concat(n?"border-white shadow-lg ring-2 ring-white/50":"border-white/20"," shadow-lg transition-all duration-200 hover:scale-110 cursor-pointer hover:shadow-xl"),children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:t})})})},X=e=>{let{filters:t,updateFilters:a,updateSort:l,onApplyTimeSort:s,onFilterWithSort:n}=e,[o,c]=(0,i.useState)(null),[d,u]=(0,i.useState)(!0),[m,p]=(0,i.useState)(null),h=(0,i.useCallback)(async()=>{try{u(!0),p(null);let e=await B.x.getOrderStats();c(e)}catch(e){console.error("Ошибка при загрузке статистики заказов:",e),p("Не удалось загрузить статистику")}finally{u(!1)}},[]);(0,i.useEffect)(()=>{h()},[h]);let f=e=>{console.log("\uD83D\uDD25 КЛИК ПО КРУЖКУ! Статус:",e);let r=t.find(e=>"status"===e.columnId);if(r&&r.value===e)n?n(null):(l&&l({columnId:"scheduledTime",direction:"desc"},!0),a(t.filter(e=>"status"!==e.columnId)));else if(n)n(e);else{l&&l({columnId:"scheduledTime",direction:"desc"},!0);let r=t.filter(e=>"status"!==e.columnId);r.push({columnId:"status",operator:"equals",value:e}),a(r)}},g=(()=>{let e=t.find(e=>"status"===e.columnId);return e?e.value:null})();if(d)return(0,r.jsx)("div",{className:"flex items-center justify-center gap-4 p-4",children:(0,r.jsx)("div",{className:"animate-pulse flex gap-4",children:Array.from({length:6}).map((e,t)=>(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-300/50 rounded-full"},t))})});if(m||!o)return(0,r.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("div",{className:"text-red-500 text-sm",children:m||"Статистика недоступна"})})});let b=[{key:"pending",status:M.Re.Pending},{key:"scheduled",status:M.Re.Scheduled},{key:"inProgress",status:M.Re.InProgress},{key:"completed",status:M.Re.Completed},{key:"cancelled",status:M.Re.Cancelled},{key:"expired",status:M.Re.Expired}];return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center gap-6",children:b.map(e=>{let{key:t,status:a}=e;return(0,r.jsx)(V,{status:a,count:o[t],label:(0,F.Vh)(a),description:(0,F.dF)(a),color:(0,F.mJ)(a),onClick:()=>f(a),isActive:g===a},a)})}),s&&(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("button",{onClick:()=>{s&&s()},className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Показать свежие сверху"]})})]})};var _=a(1839);let Q=(0,i.lazy)(()=>Promise.resolve().then(a.bind(a,11523))),G={columns:[{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,O.mJ)((0,_.LP)(),[M.ZN.Unknown])},renderCell:e=>(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-[color:var(--color-primary-50)] text-[color:var(--color-primary)]",children:(0,_.vP)(e.type)})},{id:"status",label:"Статус",accessor:"status",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,_.ah)()},renderCell:e=>{let t=(0,_.TM)(e.status);return(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(t.bg," ").concat(t.text," ").concat(t.border," border"),children:(0,_.Vh)(e.status)})}},{id:"subStatus",label:"Подстатус",accessor:"subStatus",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,_.Z$)()},renderCell:e=>(0,r.jsx)("span",{className:"",children:e.subStatus?(0,_.l1)(e.subStatus):"-"})},{id:"initialPrice",label:"Начальная цена",accessor:"initialPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,r.jsxs)("span",{className:"font-medium",children:[e.initialPrice.toLocaleString("ru-RU")," сом"]})},{id:"finalPrice",label:"Итоговая цена",accessor:"finalPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual","isEmpty","isNotEmpty"]},renderCell:e=>(0,r.jsx)("span",{className:"font-medium",children:e.finalPrice?"".concat(e.finalPrice.toLocaleString("ru-RU")," сом"):"-"})},{id:"scheduledTime",label:"Запланированное время",accessor:"scheduledTime",sortable:!0,filterable:!0,filterConfig:{fieldType:"date",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,r.jsx)("span",{children:e.scheduledTime?new Date(e.scheduledTime).toLocaleString("ru-RU"):"-"})},{id:"completedAt",label:"Дата завершения",accessor:"completedAt",sortable:!0,filterable:!0,filterConfig:{fieldType:"date",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual","isEmpty","isNotEmpty"]},renderCell:e=>(0,r.jsx)("span",{children:e.completedAt?new Date(e.completedAt).toLocaleString("ru-RU"):"-"})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>{let t=e.type.toLowerCase(),a="/orders/".concat(e.id,"?type=").concat(t);return(0,r.jsx)("div",{className:"flex justify-end gap-2",children:(0,r.jsx)(w(),{href:a,className:"p-2 text-[color:var(--color-primary)] hover:bg-white rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,r.jsx)(Q,{size:20,className:"text-[color:var(--color-primary)]"})})})}}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"}]},filtersConfig:{availableFilters:[{field:"Type",label:"Тип заказа",type:"select",options:(0,O.mJ)((0,_.LP)(),[M.ZN.Unknown]).map(e=>({value:e.value,label:e.label}))},{field:"Status",label:"Статус заказа",type:"select",options:(0,_.ah)().map(e=>({value:e.value,label:e.label}))},{field:"SubStatus",label:"Подстатус заказа",type:"select",options:(0,_.Z$)().map(e=>({value:e.value,label:e.label}))},{field:"InitialPrice",label:"Начальная цена",type:"number"},{field:"FinalPrice",label:"Итоговая цена",type:"number"},{field:"CreatedAt",label:"Дата создания",type:"text"},{field:"CompletedAt",label:"Дата завершения",type:"text"}]},customHeaderComponent:e=>{let{filters:t,updateFilters:a,updateSort:l,updateFiltersWithSort:s}=e;return(0,r.jsx)("div",{children:(0,r.jsx)(X,{filters:t,updateFilters:a,updateSort:l,onFilterWithSort:e=>{let r={columnId:"scheduledTime",direction:"desc"},i=t.filter(e=>"status"!==e.columnId);e&&i.push({columnId:"status",operator:"equals",value:e}),s?s(i,r):(l&&l(r,!0),a(i))}})})}},Y=(0,i.lazy)(()=>Promise.resolve().then(a.bind(a,11523))),H={columns:[{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,O.mJ)((0,_.LP)(),[M.ZN.Unknown])},renderCell:e=>(0,_.vP)(e.type)},{id:"status",label:"Статус",accessor:"status",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,_.ah)()},renderCell:e=>(0,_.Vh)(e.status)},{id:"initialPrice",label:"Стоимость",accessor:"initialPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>"".concat(e.initialPrice.toLocaleString("ru-RU")," сом")},{id:"passengers",label:"Пассажиры",accessor:"passengers",sortable:!1,filterable:!1,renderCell:e=>{var t;let a=null==(t=e.passengers)?void 0:t.find(e=>e.isMainPassenger);return a?"".concat(a.firstName," ").concat(a.lastName||"").trim():"Не указан"}},{id:"scheduledTime",label:"Запланированное время",accessor:"scheduledTime",sortable:!0,filterable:!0,filterConfig:{fieldType:"date",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>e.scheduledTime?new Date(e.scheduledTime).toLocaleDateString("ru-RU"):"—"},{id:"completedAt",label:"Завершен",accessor:"completedAt",sortable:!0,filterable:!0,filterConfig:{fieldType:"date",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual","isEmpty","isNotEmpty"]},renderCell:e=>e.completedAt?new Date(e.completedAt).toLocaleDateString("ru-RU"):"—"},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,r.jsx)("div",{className:"flex justify-end gap-2",children:(0,r.jsx)(w(),{href:"/orders/".concat(e.id),className:"p-2 text-[color:var(--color-primary)] hover:bg-white rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",onClick:()=>{let t=e.type.toLowerCase();console.log("\uD83D\uDD17 Клик по заказу в таблице:",{orderId:e.id,originalType:e.type,normalizedType:t}),(0,n.iY)(t),console.log("✅ Тип заказа установлен в состояние:",t)},children:(0,r.jsx)(Y,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"}]},filtersConfig:{availableFilters:[{field:"Type",label:"Тип заказа",type:"select",options:(0,O.mJ)((0,_.LP)(),[M.ZN.Unknown]).map(e=>({value:e.value,label:e.label}))},{field:"Status",label:"Статус заказа",type:"select",options:(0,_.ah)().map(e=>({value:e.value,label:e.label}))},{field:"InitialPrice",label:"Стоимость",type:"number"},{field:"CreatedAt",label:"Дата создания",type:"text"},{field:"CompletedAt",label:"Дата завершения",type:"text"}]}},W=(0,i.lazy)(()=>Promise.resolve().then(a.bind(a,11523))),K={columns:[{id:"name",label:"Название",accessor:"name",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"description",label:"Описание",accessor:"description",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","isEmpty","isNotEmpty"]},renderCell:e=>(0,r.jsx)("span",{children:e.description||(0,r.jsx)("span",{className:"text-[color:var(--color-neutral-400)]",children:"Не указано"})})},{id:"price",label:"Цена (сом)",accessor:"price",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,r.jsx)("span",{className:"font-medium",children:e.price.toLocaleString("ru-RU")})},{id:"isQuantifiable",label:"Количество",accessor:"isQuantifiable",sortable:!0,filterable:!0,filterConfig:{fieldType:"boolean"},renderCell:e=>(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(e.isQuantifiable?"bg-[color:var(--color-success-50)] text-[color:var(--color-success)]":""),children:e.isQuantifiable?"Можно указать":"Нельзя указать"})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,r.jsx)("div",{className:"flex justify-end gap-2",children:(0,r.jsx)(w(),{href:"/services/".concat(e.id),className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,r.jsx)(W,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"Name",label:"Название",operator:"contains",operatorField:"NameOp"},{field:"Description",label:"Описание",operator:"contains",operatorField:"DescriptionOp"}]},filtersConfig:{availableFilters:[{field:"Price",label:"Цена",type:"number"},{field:"IsQuantifiable",label:"Можно указать количество",type:"boolean"}]}};var $=a(83549),J=a(77981),Z=a(37780);Z.X.Admin,Z.X.Operator,Z.X.Partner,Z.X.Customer,Z.X.Driver,Z.X.Terminal,Z.X.Unknown,Z.X.Admin,Z.X.Operator,Z.X.Partner,Z.X.Customer,Z.X.Driver,Z.X.Terminal,Z.X.Unknown,Z.X.Admin,J.L,Z.X.Operator,J.L,Z.X.Partner,J.L,Z.X.Customer,J.L,Z.X.Driver,J.L,Z.X.Terminal,J.L,Z.X.Unknown,J.L,Z.X.Admin,E.F,Z.X.Operator,E.F,Z.X.Partner,E.F,Z.X.Customer,E.F,Z.X.Driver,E.F,Z.X.Terminal,E.F,Z.X.Unknown,E.F,Z.X.Admin,D.c,Z.X.Operator,D.c,Z.X.Partner,D.c,Z.X.Customer,D.c,Z.X.Driver,D.c,Z.X.Terminal,D.c,Z.X.Unknown,D.c,Z.X.Admin,Z.X.Operator,Z.X.Partner,Z.X.Customer,Z.X.Driver,Z.X.Terminal,Z.X.Unknown,Z.X.Admin,$.T,Z.X.Operator,$.T,Z.X.Partner,$.T,Z.X.Customer,$.T,Z.X.Driver,$.T,Z.X.Terminal,$.T,Z.X.Unknown,$.T,J.L,E.F,D.c,$.T;var ee=a(61938);let et=(0,i.createContext)(null);var ea=a(47510);let er=()=>{var e,t;let{setValue:a,watch:r}=(0,ee.xW)(),{formData:l}=function(){let e=function(){let e=(0,i.useContext)(et);if(!e)throw Error("useManageCollectionContext должен использоваться внутри ManageCollectionProvider");return e}();return{collectionName:e.collectionName,mode:e.mode,role:e.role,id:e.id,formData:e.formData,formConfig:e.formConfig,isLoading:e.isLoading,isSaving:e.isSaving,error:e.error}}(),s=r("firstShiftDriver"),n=r("secondShiftDriver"),[o,c]=(0,i.useState)({}),[d,u]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{(async()=>{if((null==l?void 0:l.drivers)&&Array.isArray(l.drivers)&&l.drivers.length>0&&!d){console.log("\uD83D\uDE97 Загружаем водителей автомобиля:",l.drivers),u(!0);let e=l.drivers[0],t=l.drivers[1],r=[];e&&r.push(ea.Dv.getDriverById(e.driverId).then(t=>{c(e=>({...e,firstShift:t})),a("firstShiftDriver",e.driverId,{shouldValidate:!1}),a("previousFirstShiftDriver",e.driverId,{shouldValidate:!1})}).catch(e=>console.error("Ошибка загрузки водителя 1-й смены:",e))),t&&r.push(ea.Dv.getDriverById(t.driverId).then(e=>{c(t=>({...t,secondShift:e})),a("secondShiftDriver",t.driverId,{shouldValidate:!1}),a("previousSecondShiftDriver",t.driverId,{shouldValidate:!1})}).catch(e=>console.error("Ошибка загрузки водителя 2-й смены:",e))),await Promise.all(r)}})()},[null==l?void 0:l.id,null==l?void 0:l.drivers,d,a]),(0,i.useEffect)(()=>{var e,t;let a=async(e,t)=>{try{let a=await ea.Dv.getDriverById(e);c(e=>({...e,[t]:a}))}catch(t){console.error("Ошибка загрузки информации о водителе ".concat(e,":"),t)}};s&&s!==(null==(e=o.firstShift)?void 0:e.id)&&a(s,"firstShift"),n&&n!==(null==(t=o.secondShift)?void 0:t.id)&&a(n,"secondShift")},[s,n,null==(e=o.firstShift)?void 0:e.id,null==(t=o.secondShift)?void 0:t.id]),{selectedDrivers:o,assignDriver:(e,t)=>{"first"===t?(e.id===n&&(a("secondShiftDriver","",{shouldValidate:!1}),c(e=>({...e,secondShift:void 0}))),a("firstShiftDriver",e.id,{shouldValidate:!1}),c(t=>({...t,firstShift:e}))):(e.id===s&&(a("firstShiftDriver","",{shouldValidate:!1}),c(e=>({...e,firstShift:void 0}))),a("secondShiftDriver",e.id,{shouldValidate:!1}),c(t=>({...t,secondShift:e})))},removeDriver:e=>{"first"===e?(a("firstShiftDriver","",{shouldValidate:!1}),c(e=>({...e,firstShift:void 0}))):(a("secondShiftDriver","",{shouldValidate:!1}),c(e=>({...e,secondShift:void 0})))},firstShiftDriverId:s,secondShiftDriverId:n}};var el=a(78032);let es=()=>{let[e,t]=(0,i.useState)(""),[a,r]=(0,i.useState)("fullName"),[l,s]=(0,i.useState)([]),[n,o]=(0,i.useState)(!1),[c,d]=(0,i.useState)(!1),[u,m]=(0,i.useState)({}),p=(0,i.useMemo)(()=>u,[JSON.stringify(u)]),h=(0,el.d)(e,300),f=(0,i.useCallback)(async()=>{if(!h||!(h.length<2)){o(!0);try{let e=new URLSearchParams;e.append("Size","20"),h&&h.length>=2&&("fullName"===a?(e.append("FullName",h),e.append("FullNameOp","Contains")):(e.append("PhoneNumber",h),e.append("PhoneNumberOp","Contains"))),e.append("Role","Driver"),Object.entries(p).forEach(t=>{let[a,r]=t;r&&("DrivingExperience"===a?(e.append("DrivingExperience",r),e.append("DrivingExperienceOp","GreaterThanOrEqual")):e.append(a,r))});let t=await j.uE.get("/User/Driver?".concat(e.toString()));t.data&&Array.isArray(t.data.data)?s(t.data.data):Array.isArray(t.data)?s(t.data):s([])}catch(e){console.error("Ошибка поиска водителей:",e),s([])}finally{o(!1)}}},[h,a,p]);return(0,i.useEffect)(()=>{f()},[f]),(0,i.useEffect)(()=>{(async()=>{o(!0);try{let e=new URLSearchParams;e.append("Size","20"),e.append("Role","Driver");let t=await j.uE.get("/User/Driver?".concat(e.toString()));t.data&&Array.isArray(t.data.data)?s(t.data.data):Array.isArray(t.data)&&s(t.data)}catch(e){console.error("Ошибка загрузки начального списка водителей:",e)}finally{o(!1)}})()},[]),{driversList:l,isLoading:n,searchQuery:e,setSearchQuery:t,searchField:a,setSearchField:r,activeFilters:u,setActiveFilters:m,showFilters:c,setShowFilters:d}},ei=e=>{let{value:t,onChange:a,options:l,placeholder:s="Выберите...",showMarginTop:n=!0}=e,[o,c]=(0,i.useState)(!1),d=(0,i.useRef)(null),u=l.find(e=>e.value===t);return(0,i.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&c(!1)};if(o)return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[o]),(0,r.jsxs)("div",{className:"relative",ref:d,children:[(0,r.jsxs)("button",{type:"button",onClick:()=>c(!o),className:"w-full px-3 py-2 text-sm border rounded-md transition-all text-left flex items-center justify-between cursor-pointer backdrop-blur-md ".concat(o?"bg-blue-500/20 border-blue-500/50":"border-white/10 bg-black/20 hover:bg-white/10"," ").concat(n?"mt-2":""),children:[(0,r.jsx)("span",{children:(null==u?void 0:u.label)||s}),(0,r.jsx)("span",{className:"text-xs transition-transform ".concat(o?"rotate-180":""),children:"▼"})]}),o&&(0,r.jsx)("div",{className:"absolute top-full left-0 right-0 mt-2 bg-black border border-white/10 rounded-md shadow-lg z-50 p-1",children:l.map((e,l)=>(0,r.jsx)("button",{type:"button",onClick:()=>{a(e.value),c(!1)},className:"w-full px-3 py-2 text-sm text-left hover:bg-white/10 rounded transition-all cursor-pointer ".concat(t===e.value?"bg-blue-500/20 text-blue-400":""," ").concat(l>0?"mt-1":""),children:e.label},e.value))})]})},en=e=>{var t,a;let{driver:l,shift:s,title:i,onRemove:n}=e;return(0,r.jsxs)("div",{className:"h-full p-4 flex flex-col overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"text-sm font-medium",children:i}),l&&(0,r.jsx)("button",{type:"button",onClick:()=>n(s),className:"text-red-500 hover:text-red-700 text-sm cursor-pointer",title:"Удалить водителя",children:"✕"})]}),l?(0,r.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-blue-500/20 border border-blue-500/30 flex items-center justify-center text-blue-400 font-medium flex-shrink-0",children:l.fullName.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"font-medium",children:l.fullName}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:l.phoneNumber||"Нет телефона"})]})]}),(0,r.jsxs)("div",{className:"mt-3 space-y-2 text-xs",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Опыт вождения:"}),(0,r.jsx)("span",{className:"text-gray-300",children:(null==(t=l.profile)?void 0:t.drivingExperience)?"".concat(l.profile.drivingExperience," лет"):"Нет данных"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Зоны работы:"}),(0,r.jsx)("span",{className:"text-gray-300",children:(null==(a=l.profile)?void 0:a.preferredWorkZones)&&l.profile.preferredWorkZones.length>0?l.profile.preferredWorkZones.join(", "):"Нет данных"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Рейтинг:"}),(0,r.jsx)("span",{className:"text-gray-300",children:l.rating?"⭐ ".concat(l.rating.toFixed(1)):"Нет данных"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Статус:"}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"w-2 h-2 rounded-full ".concat(l.online?"bg-green-500":"bg-gray-400")}),(0,r.jsx)("span",{className:"text-gray-300",children:l.online?"Онлайн":"Оффлайн"})]})]})]})]}):(0,r.jsxs)("div",{className:"flex-1 flex flex-col items-center justify-center text-gray-400",children:[(0,r.jsx)("div",{className:"w-16 h-16 mb-3 rounded-full bg-white/5"}),(0,r.jsx)("p",{className:"text-sm",children:"Водитель не назначен"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Выберите из списка справа"})]})]})};var eo=a(64942);let ec={title:{create:"Создание нового автомобиля",edit:"Редактирование автомобиля"},groups:[{id:"main",title:"Основная информация",order:1,layout:{gridCols:2,gapX:8,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"stretch"},fields:[{name:"photoGroup",type:eo.V.GROUP,label:"Фото автомобиля",description:"Загрузите фотографию автомобиля",layout:{flexDirection:"column",gapY:4,itemsAlignment:"center",className:"flex-1 flex flex-col justify-between gap-4"},fields:[{name:"photoUrl",type:eo.V.IMAGE,helperSmallText:"JPG, PNG до 5 МБ",helperBigText:"Загрузите фотографию автомобиля в формате JPG или PNG. Максимальный размер файла - 5 МБ.",className:"w-full h-full"}]},{name:"infoGroup",type:eo.V.GROUP,label:"Информация об автомобиле",description:"Основные данные автомобиля для идентификации в системе",layout:{flexDirection:"column",gapY:4,itemsAlignment:"start",className:"flex-2 flex flex-col justify-between gap-4"},fields:[{name:"id",type:eo.V.HIDDEN,defaultValue:""},{name:"make",label:"Марка",type:eo.V.TEXT,placeholder:"Введите марку автомобиля",required:!0,helperSmallText:"Например: Toyota, BMW, Audi",helperBigText:"Марка автомобиля, указанная в техническом паспорте",className:"w-full",maxLength:63},{name:"model",label:"Модель",type:eo.V.TEXT,placeholder:"Введите модель автомобиля",required:!0,helperSmallText:"Например: Camry, X5, A6",helperBigText:"Модель автомобиля, указанная в техническом паспорте",className:"w-full",maxLength:63},{name:"year",label:"Год выпуска",type:eo.V.NUMBER,placeholder:"Введите год выпуска",required:!0,helperSmallText:"Год выпуска автомобиля",helperBigText:"Год выпуска автомобиля, указанный в техническом паспорте",className:"w-full",min:1900,max:new Date().getFullYear()+1},{name:"licensePlate",label:"Государственный номер",type:eo.V.TEXT,placeholder:"Введите гос. номер",required:!0,helperSmallText:"Регистрационный знак",helperBigText:"Государственный регистрационный знак автомобиля",className:"w-full",maxLength:15}]}]},{id:"details",title:"Характеристики",order:2,description:"Технические характеристики и параметры автомобиля",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"center"},fields:[{name:"color",label:"Цвет",type:eo.V.SELECT,placeholder:"Выберите цвет автомобиля",required:!0,options:T(),className:"w-full"},{name:"type",label:"Тип автомобиля",type:eo.V.SELECT,placeholder:"Выберите тип автомобиля",required:!0,options:R(),className:"w-full"},{name:"serviceClass",label:"Класс обслуживания",type:eo.V.SELECT,placeholder:"Выберите класс обслуживания",required:!0,options:[{value:"Economy",label:"Эконом"},{value:"Comfort",label:"Комфорт"},{value:"ComfortPlus",label:"Комфорт+"},{value:"Business",label:"Бизнес"},{value:"Premium",label:"Премиум"},{value:"Vip",label:"VIP"},{value:"Luxury",label:"Люкс"}],className:"w-full"},{name:"status",label:"Статус",type:eo.V.SELECT,placeholder:"Выберите статус автомобиля",required:!0,options:P(),className:"w-full"},{name:"passengerCapacity",label:"Пассажировместимость",type:eo.V.NUMBER,placeholder:"Введите количество мест",required:!0,helperSmallText:"Количество пассажирских мест",helperBigText:"Максимальное количество пассажиров, которое может перевозить автомобиль",className:"w-full",min:1,max:50}]},{id:"features",title:"Дополнительные опции",order:3,description:"Дополнительные опции и оборудование автомобиля",layout:{gridCols:2,gapX:4,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:3,itemsAlignment:"start"},fields:[{name:"features",label:"Дополнительные опции",type:eo.V.MULTISELECT,placeholder:"Выберите опции",required:!0,options:Object.values(c.Lz).map(e=>({value:e,label:function(e){switch(e){case c.Lz.AirConditioning:return"Кондиционер";case c.Lz.ClimateControl:return"Климат-контроль";case c.Lz.LeatherSeats:return"Кожаные сиденья";case c.Lz.HeatedSeats:return"Подогрев сидений";case c.Lz.Bluetooth:return"Bluetooth";case c.Lz.USBPort:return"USB-порт";case c.Lz.AuxInput:return"AUX-вход";case c.Lz.Navigation:return"Навигация";case c.Lz.BackupCamera:return"Камера заднего вида";case c.Lz.ParkingSensors:return"Парковочные датчики";case c.Lz.Sunroof:return"Люк";case c.Lz.PanoramicRoof:return"Панорамная крыша";case c.Lz.ThirdRowSeats:return"Третий ряд сидений";case c.Lz.ChildSeat:return"Детское кресло";case c.Lz.WheelchairAccess:return"Доступ для инвалидных колясок";case c.Lz.Wifi:return"Wi-Fi";case c.Lz.PremiumAudio:return"Премиальная аудиосистема";case c.Lz.AppleCarplay:return"Apple CarPlay";case c.Lz.AndroidAuto:return"Android Auto";case c.Lz.SmokingAllowed:return"Разрешено курение";case c.Lz.PetFriendly:return"Допускаются животные";case c.Lz.LuggageCarrier:return"Багажник на крыше";case c.Lz.BikeRack:return"Велосипедная стойка";default:return""}}(e)})),helperSmallText:"Выберите хотя бы одну опцию",helperBigText:"Дополнительные опции и оборудование, которыми оснащен автомобиль",className:"w-full"}]},{id:"drivers",title:"Водители автомобиля",order:4,description:"Система поддерживает максимум 2 водителей с фиксированными сменами",layout:{gridCols:1,gapX:4,gapY:4},customComponent:()=>{let{driversList:e,isLoading:t,searchQuery:a,setSearchQuery:l,searchField:s,setSearchField:n,activeFilters:o,setActiveFilters:c,showFilters:d,setShowFilters:u}=es(),{selectedDrivers:m,assignDriver:p,removeDriver:h,firstShiftDriverId:f,secondShiftDriverId:g}=er();return(0,i.useEffect)(()=>{let e=e=>{let t=e.target;!d||t.closest(".filter-panel")||t.closest(".filter-button")||u(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[d,u]),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 h-[700px]",children:[(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Назначенные водители"}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col divide-y rounded-lg border shadow-sm backdrop-blur-md",children:[(0,r.jsx)(en,{driver:m.firstShift,shift:"first",title:"Первая смена (08:00 - 20:00)",onRemove:h}),(0,r.jsx)(en,{driver:m.secondShift,shift:"second",title:"Вторая смена (20:00 - 08:00)",onRemove:h})]})]}),(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Доступные водители"}),(0,r.jsxs)("div",{className:"mb-4 relative",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("div",{className:"w-[150px]",children:(0,r.jsx)(ei,{value:s,onChange:e=>n(e),options:[{value:"fullName",label:"По имени"},{value:"phoneNumber",label:"По телефону"}],placeholder:"Поиск по...",showMarginTop:!1})}),(0,r.jsx)("input",{type:"text",value:a,onChange:e=>l(e.target.value),placeholder:"fullName"===s?"Введите имя...":"Введите телефон...",className:"flex-1 px-3 py-2 text-sm border border-white/10 rounded-md bg-black/20 focus:outline-none focus:border-blue-500/50 hover:bg-white/10 transition-all backdrop-blur-md"}),(0,r.jsx)("button",{type:"button",onClick:()=>u(!d),className:"filter-button px-3 py-2 text-sm border rounded-md transition-all cursor-pointer backdrop-blur-md ".concat(d?"bg-blue-500/20 border-blue-500/50 text-blue-400":"border-white/10 bg-black/20 hover:bg-white/10"),title:"Дополнительные фильтры",children:(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["\uD83D\uDD27 Фильтры",Object.keys(o).length>0&&(0,r.jsx)("span",{className:"px-1.5 py-0.5 bg-blue-500/30 rounded-full text-xs",children:Object.keys(o).length})]})})]}),d&&(0,r.jsx)("div",{className:"filter-panel absolute right-0 top-full mt-2 w-[400px] p-4 bg-black/80 rounded-lg border border-white/10 z-50 shadow-2xl",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Дополнительные фильтры"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Класс автомобиля"}),(0,r.jsx)(ei,{value:o.VehicleServiceClass||"",onChange:e=>c(t=>({...t,VehicleServiceClass:e})),options:[{value:"",label:"Все"},{value:"Economy",label:"Эконом"},{value:"Comfort",label:"Комфорт"},{value:"ComfortPlus",label:"Комфорт+"},{value:"Business",label:"Бизнес"},{value:"Premium",label:"Премиум"},{value:"Vip",label:"VIP"},{value:"Luxury",label:"Люкс"}],placeholder:"Выберите класс"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Опыт вождения (лет)"}),(0,r.jsx)("input",{type:"number",value:o.DrivingExperience||"",onChange:e=>c(t=>({...t,DrivingExperience:e.target.value})),placeholder:"От",className:"w-full px-2 py-1 text-sm border border-white/10 rounded bg-black/80 mt-2"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Статус"}),(0,r.jsx)(ei,{value:o.Online||"",onChange:e=>c(t=>({...t,Online:e})),options:[{value:"",label:"Все"},{value:"true",label:"Онлайн"},{value:"false",label:"Оффлайн"}],placeholder:"Выберите статус"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Языки"}),(0,r.jsx)(ei,{value:o.Languages||"",onChange:e=>c(t=>({...t,Languages:e})),options:[{value:"",label:"Все"},{value:"ru",label:"Русский"},{value:"kg",label:"Кыргызский"},{value:"en",label:"Английский"}],placeholder:"Выберите язык"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,r.jsx)("button",{type:"button",onClick:()=>c({}),className:"px-3 py-1 text-xs border border-white/10 rounded hover:bg-white/10 transition-all cursor-pointer",children:"Очистить"}),(0,r.jsx)("button",{type:"button",onClick:()=>u(!1),className:"px-3 py-1 text-xs bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded hover:bg-blue-500/30 transition-all cursor-pointer",children:"Применить"})]})]})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto space-y-2 border rounded-lg p-2 backdrop-blur-md",children:t?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)("div",{className:"inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"}),(0,r.jsx)("p",{className:"mt-2 text-sm",children:"Поиск водителей..."})]}):e.length>0?e.map(e=>(0,r.jsx)("div",{className:"p-3 rounded-lg border hover:bg-white/20 transition-all cursor-pointer",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-400/20 border border-gray-400/30 flex items-center justify-center text-gray-300 text-sm font-medium",children:e.fullName.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.fullName}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:e.phoneNumber}),e.activeCar&&(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.activeCar.make," ",e.activeCar.model," • ",e.activeCar.licensePlate]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,r.jsx)("button",{type:"button",onClick:()=>p(e,"first"),disabled:e.id===f,className:"px-3 py-1 text-xs rounded transition-all ".concat(e.id===f?"bg-gray-600/50 text-gray-400 cursor-not-allowed":"bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 border border-blue-500/30 cursor-pointer"),children:e.id===f?"✓ 1-я смена":"1-я смена"}),(0,r.jsx)("button",{type:"button",onClick:()=>p(e,"second"),disabled:e.id===g,className:"px-3 py-1 text-xs rounded transition-all ".concat(e.id===g?"bg-gray-600/50 text-gray-400 cursor-not-allowed":"bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30 cursor-pointer"),children:e.id===g?"✓ 2-я смена":"2-я смена"})]})]})},e.id)):(0,r.jsx)("div",{className:"text-center py-8 text-gray-400",children:(0,r.jsx)("p",{className:"text-sm",children:a?"Водители не найдены":"Начните вводить для поиска"})})})]})]})},fields:[{name:"firstShiftDriver",type:eo.V.HIDDEN},{name:"secondShiftDriver",type:eo.V.HIDDEN},{name:"previousFirstShiftDriver",type:eo.V.HIDDEN},{name:"previousSecondShiftDriver",type:eo.V.HIDDEN}]}],writeOperations:[{api:"delete",endpoint:"/Car/{formData.id}/drivers/{formData.previousFirstShiftDriver}",method:"remove",condition:"onTrue",description:"Удаление предыдущего водителя первой смены",order:1},{api:"delete",endpoint:"/Car/{formData.id}/drivers/{formData.previousSecondShiftDriver}",method:"remove",condition:"onTrue",description:"Удаление предыдущего водителя второй смены",order:2},{api:"post",endpoint:"/Car/{formData.id}/drivers/{formData.firstShiftDriver}",method:"assign",condition:"onTrue",bodyData:{active:!0},description:"Назначение нового водителя первой смены",order:3},{api:"post",endpoint:"/Car/{formData.id}/drivers/{formData.secondShiftDriver}",method:"assign",condition:"onTrue",bodyData:{active:!0},description:"Назначение нового водителя второй смены",order:4}]},ed={create:ec,update:ec};var eu=a(81257),em=a(92177);eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.MULTISELECT,eo.R.ARRAY,eo.V.MULTISELECT,eo.R.ARRAY,eo.V.NUMBER,eo.R.NUMBER,eo.V.HIDDEN,eo.R.ARRAY;let ep={title:{create:"Создание запланированного заказа",edit:"Редактирование запланированного заказа"},description:{create:"Заполните информацию для создания нового запланированного заказа",edit:"Редактирование информации о запланированном заказе"},groups:[{id:"client_selection",title:"Выбор клиента",description:"Выберите клиента для заказа",order:1,collapsed:!1,fields:[{name:"clientType",label:"Тип клиента",type:eo.V.BUTTON_GROUP,dataType:eo.R.STRING,required:!0,options:[{value:"existing",label:"Данные клиента"},{value:"new",label:"Новый клиент"}],defaultValue:"existing",helperText:"Выберите тип клиента"},{name:"customerId",label:"Клиент из базы",type:eo.V.SELECT,dataType:eo.R.STRING,required:!1,placeholder:"Выберите клиента",helperText:"Выберите существующего клиента из базы данных",dynamicReference:{api:"get",endpoint:j.QQ.USER.LIST},dependsOn:{field:"clientType",value:"existing"}},{name:"firstName",label:"Имя пассажира",type:eo.V.TEXT,dataType:eo.R.STRING,required:!0,maxLength:63,placeholder:"Введите имя",helperText:"Имя пассажира",dependsOn:{field:"clientType",value:"new"}},{name:"lastName",label:"Фамилия пассажира",type:eo.V.TEXT,dataType:eo.R.STRING,required:!1,maxLength:63,placeholder:"Введите фамилию",helperText:"Фамилия пассажира (необязательно)",dependsOn:{field:"clientType",value:"new"}},{name:"scheduledTime",label:"Дата и время отправки",type:eo.V.DATETIME,dataType:eo.R.STRING,required:!0,placeholder:"Выберите дату и время",helperText:"Запланированное время начала поездки (прошлое время недоступно)"}],layout:{gridCols:1,gapX:4,gapY:4}},{id:"pricing",title:"Тариф и стоимость",description:"Выберите тариф и укажите стоимость",order:2,fields:[{name:"tariffId",label:"Тариф",type:eo.V.SELECT,dataType:eo.R.STRING,required:!0,placeholder:"Выберите тариф",helperText:"Выберите тариф для расчета стоимости",dynamicReference:{api:"get",endpoint:j.QQ.TARIFF.LIST}},{name:"tariffInfo",label:"Информация о тарифе",type:eo.V.TEXT,dataType:eo.R.STRING,required:!1,readOnly:!0,placeholder:"Выберите тариф для просмотра информации",helperText:"Детали выбранного тарифа",dependsOn:{field:"tariffId",value:"",condition:"notEquals"}},{name:"initialPrice",label:"Предварительная стоимость",type:eo.V.NUMBER,dataType:eo.R.NUMBER,required:!0,placeholder:"Введите стоимость",helperText:"Предварительная расчетная стоимость в сомах",min:0}],layout:{gridCols:1,gapX:4,gapY:4}},{id:"status",title:"Статус заказа",description:"Установите статус заказа",order:3,fields:[{name:"status",label:"Статус заказа",type:eo.V.SELECT,dataType:eo.R.OBJECT,required:!0,placeholder:"Выберите статус",helperText:"Текущий статус заказа",options:(0,_.ah)(),defaultValue:"Pending"}],layout:{gridCols:1,gapX:4,gapY:4}},{id:"route_selection",title:"Выбор маршрута",description:"Выберите готовый маршрут или укажите локации вручную",order:4,fields:[{name:"routeType",label:"Тип маршрута",type:eo.V.BUTTON_GROUP,dataType:eo.R.STRING,required:!0,options:[{value:"template",label:"Готовый маршрут"},{value:"manual",label:"Ручной выбор"}],defaultValue:"template",helperText:"Выберите способ задания маршрута"},{name:"routeId",label:"Готовый маршрут",type:eo.V.SELECT,dataType:eo.R.STRING,required:!1,placeholder:"Выберите готовый маршрут",helperText:"Выберите готовый шаблон маршрута (только маршруты вашего партнера)",dynamicReference:{api:"get",endpoint:j.QQ.ROUTE.LIST,filters:{partnerId:"current_user_partner_id"}},dependsOn:{field:"routeType",value:"template"}},{name:"startLocationId",label:"Начальная точка",type:eo.V.SELECT,dataType:eo.R.STRING,required:!1,placeholder:"Выберите начальную точку",helperText:"Точка отправления",dynamicReference:{api:"get",endpoint:j.QQ.LOCATION.LIST},dependsOn:{field:"routeType",value:"manual"}},{name:"endLocationId",label:"Конечная точка",type:eo.V.SELECT,dataType:eo.R.STRING,required:!1,placeholder:"Выберите конечную точку",helperText:"Точка назначения",dynamicReference:{api:"get",endpoint:j.QQ.LOCATION.LIST},dependsOn:{field:"routeType",value:"manual"}},{name:"additionalStops",label:"Промежуточные остановки",type:eo.V.MULTISELECT,dataType:eo.R.ARRAY,required:!1,placeholder:"Выберите промежуточные остановки",helperText:"Дополнительные остановки по маршруту (работают с обоими типами маршрутов)",dynamicReference:{api:"get",endpoint:j.QQ.LOCATION.LIST}}],layout:{gridCols:1,gapX:4,gapY:4}},{id:"services",title:"Дополнительные услуги",description:"Выберите дополнительные услуги",order:5,fields:[{name:"services",label:"Услуги",type:eo.V.MULTISELECT,dataType:eo.R.ARRAY,required:!1,placeholder:"Выберите дополнительные услуги",helperText:"Дополнительные услуги, включенные в заказ",dynamicReference:{api:"get",endpoint:j.QQ.SERVICE.LIST}}],layout:{gridCols:1,gapX:4,gapY:4}}]},eh=(eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.SELECT,eo.R.STRING,eo.V.MULTISELECT,eo.R.ARRAY,eo.V.MULTISELECT,eo.R.ARRAY,eo.V.NUMBER,eo.R.NUMBER,eo.V.SELECT,eo.R.OBJECT,(0,_.ah)(),eo.V.TEXT,eo.R.STRING,eo.V.NUMBER,eo.R.NUMBER,eo.V.TEXT,eo.R.STRING,eo.V.TEXT,eo.R.STRING,eo.V.TEXT,eo.R.STRING,eo.V.TEXT,eo.R.STRING,eo.V.CHECKBOX,eo.R.BOOLEAN,{create:ep,update:ep}),ef={title:{create:"Создание новой услуги",edit:"Редактирование услуги"},groups:[{id:"main",title:"Информация об услуге",order:1,layout:{gridCols:1,gapY:4,className:"h-full",flexDirection:"column",itemsAlignment:"stretch"},fields:[{name:"name",label:"Название",type:eo.V.TEXT,placeholder:"Введите название услуги",required:!0,helperSmallText:"Название услуги, которое будет отображаться клиентам",helperBigText:"Укажите понятное и краткое название услуги, которое будет отображаться в списке услуг и заказах",className:"w-full",maxLength:127},{name:"description",label:"Описание",type:eo.V.TEXTAREA,placeholder:"Введите описание услуги",helperSmallText:"Подробное описание услуги",helperBigText:"Укажите подробное описание услуги, включая все важные детали и условия предоставления",className:"w-full",maxLength:255}]},{id:"pricing",title:"Стоимость и параметры",order:2,description:"Настройки стоимости и параметров услуги",layout:{gridCols:2,gapX:6,gapY:4,className:"h-full",flexDirection:"row",maxItemsPerRow:2,itemsAlignment:"center"},fields:[{name:"price",label:"Цена (сом)",type:eo.V.NUMBER,placeholder:"Введите цену услуги",required:!0,helperSmallText:"Стоимость услуги в сомах",helperBigText:"Укажите стоимость услуги в сомах. Для услуг с переменной стоимостью укажите базовую цену.",className:"w-full",min:0,step:.01},{name:"isQuantifiable",label:"Количественная услуга",type:eo.V.CHECKBOX,helperSmallText:"Можно ли заказать несколько единиц услуги",helperBigText:"Отметьте, если услуга может быть заказана в нескольких экземплярах (например, дополнительный багаж, детское кресло и т.д.)",className:"w-full"}]}]},eg={create:ef,update:ef};var eb=a(14701),ev=a(66517),ex=a(24723);ev.m,eu._,em.a,eb.b,ev.D[ex.Xh.Admin],ed.create,eu.D.create,em.t.create,eh.create,eg.create,eb.I.create,ev.D[ex.Xh.Admin],ed.update,eu.D.update,em.t.get,eh.update,eg.update,eb.I.update;let ey={},eN={profile:{label:"Мой профиль",description:"Просмотр и управление личным профилем пользователя",icon:"ProfileIcon"},users:{label:"Пользователи",description:"Управление пользователями системы (водители, клиенты, администраторы)",icon:"UsersIcon"},cars:{label:"Автомобили",description:"Управление автомобилями компании с их характеристиками",icon:"VehiclesIcon"},locations:{label:"Локации",description:"Управление локациями (адреса, координаты, типы мест)",icon:"ReferencesIcon"},services:{label:"Услуги",description:"Управление услугами и их ценами",icon:"TariffsIcon"},tariffs:{label:"Тарифы",description:"Управление тарифами на перевозки и услуги",icon:"TariffsIcon"},orders:{label:"Заказы",description:"Управление заказами клиентов на перевозки",icon:"OrdersIcon"},notifications:{label:"Уведомления",description:"Управление уведомлениями для пользователей",icon:"OrdersIcon"},settings:{label:"Настройки",description:"Настройки системы и пользовательского интерфейса",icon:"SettingsIcon"}},ej=new Proxy({},{get:(e,t)=>ey[t]||eN[t],ownKeys:()=>Object.keys(eN),has:(e,t)=>t in eN,getOwnPropertyDescriptor(e,t){if(t in eN)return{enumerable:!0,configurable:!0}}});class eS{getButtonText(e){return"edit"===e?"Просмотр":"Редактировать"}handleButtonClick(e){let t="edit"===e.mode?"/profile":"/profile/edit";e.router.push(t)}shouldShowButton(e){return"view"===e||"edit"===e}getAdditionalInfo(e){return null}}class ew{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.openModal(n.bY.SelectUserRoleModal):e.router.push("/users")}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){return null}}class eC{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.openModal(n.bY.SelectOrderTypeModal):e.router.push("/orders")}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){let{orderType:t,mode:a}=e;return"create"!==a&&"edit"!==a?null:"instant"===t?(0,r.jsx)("div",{className:"max-w-4xl h-full p-2 mx-auto hourglass-shape",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"⚡"}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Мгновенный заказ"}),(0,r.jsx)("p",{className:"text-sm text-white",children:"Выполнение как можно скорее"})]})]})}):"scheduled"===t?(0,r.jsx)("div",{className:"max-w-4xl h-full p-2 mx-auto hourglass-shape",children:(0,r.jsx)("div",{className:"flex items-center justify-center gap-2",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Запланированный заказ"}),(0,r.jsx)("p",{className:"text-sm text-white",children:"Выполнение в указанное время"})]})})}):null}}class eT{getButtonText(e){return"list"===e?"Создать":"Вернуться к списку"}handleButtonClick(e){"list"===e.mode?e.router.push("/".concat(e.collectionName,"/create")):e.router.push("/".concat(e.collectionName))}shouldShowButton(e){return"list"===e||"create"===e||"edit"===e}getAdditionalInfo(e){return null}}class eA{static getStrategy(e){return this.strategies.get(e)||new eT}}eA.strategies=new Map([["profile",new eS],["users",new ew],["orders",new eC]]);let eR=e=>{var t;let{collectionName:a,mode:i="view",orderType:o}=e,c=(0,s.useRouter)(),d=(0,l.e3)(n.qf),u=ej[a];if(!u)return(0,r.jsxs)("div",{className:"px-8 py-2",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-red-600",children:"Коллекция не найдена"}),(0,r.jsxs)("p",{className:"text-sm text-red-600",children:['Коллекция "',a,'" не существует.']})]});let m=eA.getStrategy(a),p=m.shouldShowButton(i),h=p?m.getButtonText(i):"",f={mode:i,router:c,openModal:d,collectionName:a,orderType:o},g=null==(t=m.getAdditionalInfo)?void 0:t.call(m,f);return(0,r.jsxs)("div",{className:"flex justify-between items-start sticky top-0 z-10 border-b border-[color:var(--color-neutral-300)]",children:[(0,r.jsx)("div",{className:"h-full flex flex-row py-2 px-8 skew-right bg-[color:var(--color-brand)]/80",children:(0,r.jsxs)("div",{className:"h-full flex flex-col gap-1",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:u.label}),u.description&&(0,r.jsx)("p",{className:"text-sm text-white",children:u.description})]})}),(0,r.jsx)("div",{className:"h-full flex-1 flex-row",children:g}),p&&(0,r.jsx)("button",{onClick:()=>{m.handleButtonClick(f)},className:"btn-primary flex-shrink-0 flex items-center justify-center h-full skew-left transition-transform duration-200 min-w-sm !rounded-none !px-8",children:(0,r.jsx)("span",{className:"text-xl block font-medium",children:h})})]})}},27512:(e,t,a)=>{"use strict";a.d(t,{h:()=>u,Q:()=>m});var r=a(54568),l=a(7620);let s=(0,l.lazy)(()=>Promise.resolve().then(a.bind(a,98671))),i=(0,l.lazy)(()=>Promise.resolve().then(a.bind(a,33471))),n=(0,l.lazy)(()=>Promise.resolve().then(a.bind(a,36760))),o=(0,l.lazy)(()=>Promise.resolve().then(a.bind(a,8382))),c=(0,l.lazy)(()=>Promise.resolve().then(a.bind(a,91812))),d=(0,l.lazy)(()=>Promise.resolve().then(a.bind(a,92601))),u=[{id:"dashboard",title:"Панель управления",path:"/",icon:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(s,{className:"w-6 h-6"})})},{id:"users",title:"Пользователи",path:"/users",icon:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(i,{className:"w-6 h-6"})})},{id:"cars",title:"Автомобили",path:"/cars",icon:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(n,{className:"w-6 h-6"})})},{id:"locations",title:"Локации",path:"/locations",icon:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(d,{className:"w-6 h-6"})})},{id:"services",title:"Услуги",path:"/services",icon:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(o,{className:"w-6 h-6"})})},{id:"tariffs",title:"Тарифы",path:"/tariffs",icon:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(o,{className:"w-6 h-6"})})},{id:"orders",title:"Заказы",path:"/orders",icon:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(c,{className:"w-6 h-6"})})},{id:"notifications",title:"Уведомления",path:"/notifications",icon:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(c,{className:"w-6 h-6"})})}],m=[{id:"orders",title:"Заказы",path:"/",icon:"\uD83D\uDCC5"},{id:"scheduled-orders",title:"Запланированные заказы",path:"/scheduled-orders",icon:"\uD83D\uDCC5"},{id:"notifications",title:"Уведомления",path:"/notifications",icon:"\uD83D\uDD14"},{id:"my-car",title:"Мои автомобили",path:"/my-car",icon:"\uD83D\uDE97"},{id:"settings",title:"Настройки",path:"/settings",icon:"⚙️"},{id:"support",title:"Служба поддержки",path:"/support",icon:"\uD83C\uDFA7"},{id:"statistics",title:"Моя статистика",path:"/statistics",icon:"\uD83D\uDCCA"}]},28128:(e,t,a)=>{"use strict";a.d(t,{DriverLocationProvider:()=>n});var r=a(54568),l=a(7620),s=a(27414),i=a(8426);let n=e=>{let{children:t}=e,[a,n]=(0,l.useState)(null),[o,c]=(0,l.useState)(!1),[d,u]=(0,l.useState)(null),m=(0,l.useRef)(null),p=(0,l.useRef)(null),h=(0,l.useRef)(null),f=(0,l.useCallback)(async()=>{if(null===m.current){if(console.log("\uD83D\uDE80 DriverLocationProvider: запуск отслеживания геолокации"),c(!0),u(null),"permissions"in navigator&&navigator.permissions.query({name:"geolocation"}).then(e=>{"denied"===e.state&&(console.error("\uD83D\uDEAB\uD83D\uDEAB\uD83D\uDEAB ГЕОЛОКАЦИЯ ЗАБЛОКИРОВАНА! \uD83D\uDEAB\uD83D\uDEAB\uD83D\uDEAB"),console.error("\uD83D\uDC46 РАЗРЕШИТЕ ГЕОЛОКАЦИЮ В БРАУЗЕРЕ! \uD83D\uDC46"),console.error("\uD83D\uDD12 Нажмите на иконку замка в адресной строке"))}).catch(e=>{console.error("❌ Ошибка проверки разрешений:",e)}),navigator.geolocation)try{m.current=navigator.geolocation.watchPosition(e=>{let t=p.current,a=e.coords.heading||0;t&&(Math.abs(e.coords.latitude-t.latitude)>1e-5||Math.abs(e.coords.longitude-t.longitude)>1e-5)&&(a=((e,t,a,r)=>{let l=(r-t)*Math.PI/180,s=e*Math.PI/180,i=a*Math.PI/180,n=Math.sin(l)*Math.cos(i),o=Math.cos(s)*Math.sin(i)-Math.sin(s)*Math.cos(i)*Math.cos(l);return(180*Math.atan2(n,o)/Math.PI+360)%360})(t.latitude,t.longitude,e.coords.latitude,e.coords.longitude),console.log("\uD83E\uDDED Вычислено направление движения:",a,"градусов"));let r={latitude:e.coords.latitude,longitude:e.coords.longitude,speed:e.coords.speed||0,heading:a,accuracy:e.coords.accuracy,timestamp:e.timestamp};h.current=p.current,n(r),p.current=r},async e=>{console.error("HTML5 Geolocation ошибка:",e),console.error("\uD83D\uDEAB ГЕОЛОКАЦИЯ ЗАБЛОКИРОВАНА! Разрешите геолокацию в браузере для точных координат!"),u("Геолокация заблокирована. Разрешите в настройках браузера."),c(!1)},{enableHighAccuracy:!0,timeout:6e4,maximumAge:0});return}catch(e){console.error("HTML5 Geolocation недоступен:",e)}console.error("❌ HTML5 Geolocation недоступен в этом браузере"),u("Геолокация недоступна в этом браузере"),c(!1)}},[]),g=(0,l.useCallback)(()=>{null!==m.current&&(navigator.geolocation.clearWatch(m.current),m.current=null),c(!1),console.log("\uD83D\uDED1 Отслеживание геолокации остановлено")},[]),b=(0,l.useCallback)(()=>new Promise(e=>{if(!("geolocation"in navigator)){console.error("❌ Geolocation API не поддерживается"),e(null);return}console.log("\uD83C\uDFAF Запрос текущей позиции с максимальной точностью..."),navigator.geolocation.getCurrentPosition(t=>{let a={latitude:t.coords.latitude,longitude:t.coords.longitude,speed:t.coords.speed||0,heading:t.coords.heading||0,accuracy:t.coords.accuracy,timestamp:t.timestamp};console.log("\uD83C\uDFAF Текущая позиция получена:",a),e(a)},t=>{console.error("❌ Ошибка получения текущей позиции:",t),e(p.current)},{enableHighAccuracy:!0,timeout:6e4,maximumAge:0})}),[]);return(0,l.useEffect)(()=>(console.log("\uD83D\uDE80 DriverLocationProvider: автозапуск отслеживания геолокации"),f(),()=>{console.log("\uD83D\uDED1 DriverLocationProvider: остановка отслеживания при размонтировании"),g()}),[f,g]),(0,l.useEffect)(()=>{if(console.log("\uD83D\uDCE1 DriverLocationProvider: проверка условий отправки координат",{hasLocation:!!a,isTracking:o,location:a}),!a||!o)return void console.log("❌ DriverLocationProvider: условия не выполнены, пропускаем отправку");console.log("✅ DriverLocationProvider: начинаем отправку координат на сервер");let e=async()=>{try{console.log("\uD83D\uDCE1 DriverLocationProvider: отправляем координаты",{latitude:a.latitude,longitude:a.longitude}),await s.e.updateCurrentLocation({latitude:a.latitude,longitude:a.longitude}),console.log("✅ DriverLocationProvider: координаты успешно отправлены")}catch(e){console.error("❌ DriverLocationProvider: ошибка отправки координат на сервер:",e)}};e();let t=setInterval(()=>{console.log("⏰ DriverLocationProvider: периодическая отправка координат"),e()},3e4);return()=>{console.log("\uD83D\uDD0C DriverLocationProvider: очистка интервала отправки координат"),clearInterval(t)}},[a,o]),(0,r.jsx)(i.pl.Provider,{value:{location:a,isTracking:o,error:d,startTracking:f,stopTracking:g,getCurrentPosition:b},children:t})}},29988:(e,t,a)=>{"use strict";a.d(t,{s:()=>l,I:()=>s});var r=a(7620);let l=(0,r.createContext)({selectedLocations:[],addLocation:()=>{},removeLocation:()=>{},clearLocations:()=>{},allLocations:[],isLoadingLocations:!1,locationsError:null,currentRegionSlug:null,searchLocations:async()=>[],getLocationById:()=>void 0,fetchLocationById:async()=>null,loadLocations:async e=>{},reloadLastLocations:async()=>{},reloadRegionLocations:async()=>{}}),s=()=>(0,r.useContext)(l)},32037:(e,t,a)=>{"use strict";a.d(t,{DriverSidebar:()=>h});var r=a(54568),l=a(62942),s=a(7620),i=a(27512),n=a(45452),o=a(32959),c=a(8426),d=a(48484),u=a(59959),m=a(8037),p=a(80980);let h=e=>{let{className:t=""}=e,a=(0,l.useRouter)(),{isInitialized:h,isLoading:f}=(0,c.g0)(),{isActive:g}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,a]=(0,s.useState)(!1),[r,l]=(0,s.useState)(!1),[i,n]=(0,s.useState)(null),[o,c]=(0,s.useState)(null),d=(0,u.k)(),m=(0,s.useCallback)(async()=>{l(!1),n(null)},[]),p=(0,s.useCallback)(async e=>{a(e),c(new Date().toISOString()),n(null)},[]),h=(0,s.useCallback)(async()=>{await p(!t)},[t,p]),f=(0,s.useCallback)(async()=>{await m()},[m]);return(0,s.useEffect)(()=>{!1!==e.autoFetch&&m()},[e.autoFetch,m]),(0,s.useEffect)(()=>{e.syncWithSignalR&&(d.isConnected?(a(!0),c(new Date().toISOString())):a(!1))},[e.syncWithSignalR,d.isConnected]),(0,s.useEffect)(()=>{if(e.syncWithSignalR&&d.connection){let e=e=>{void 0!==e.isActive&&a(e.isActive),e.lastActiveAt&&c(e.lastActiveAt)};return d.on("DriverActivityUpdated",e),()=>{d.off("DriverActivityUpdated",e)}}},[e.syncWithSignalR,d]),{isActive:t,isLoading:r,error:i,lastActiveAt:o,toggleActivity:h,setActive:p,refresh:f}}({autoFetch:!0,syncWithSignalR:!0}),{hasUnreadNotifications:b}=(0,n.M)(),{isInQueue:v,position:x}=(0,o.o)(),{hasActiveRide:y,isLoading:N,forceUpdateActiveRide:j}=(0,d.c7)(),{newScheduledRidesCount:S,totalScheduledRidesCount:w,resetNewRidesCount:C}=(0,p.UF)(),T=(0,s.useRef)(!1),[A,R]=(0,s.useState)(!1),I=(0,s.useRef)(null),P=(0,s.useRef)(0);(0,s.useLayoutEffect)(()=>{h&&!f&&(T.current||(T.current=!0,(async()=>{await j(),R(!0)})()))},[j,h,f]),(0,s.useLayoutEffect)(()=>(y&&v?!I.current&&P.current<3?(P.current+=1,console.log("⚠️ Конфликт: есть активная поездка, но всё ещё в очереди. Попытка ".concat(P.current,"/").concat(3,"...")),I.current=setTimeout(async()=>{try{await j(),setTimeout(async()=>{y&&v&&console.log("\uD83D\uDEAA Конфликт не разрешился - принудительно выходим из очереди")},500)}catch(e){console.error("❌ Ошибка при разрешении конфликта:",e)}finally{I.current=null}},1e3)):P.current>=3&&console.log("\uD83D\uDED1 Превышен лимит попыток разрешения конфликта. Останавливаем попытки."):(P.current=0,I.current&&(clearTimeout(I.current),I.current=null)),()=>{I.current&&(clearTimeout(I.current),I.current=null)}),[y,v,x,j,3]);let k=(0,s.useMemo)(()=>{let e=(0,m.az)(i.Q);return(0,m.Y6)(e,b,S>0)},[b,S]),z=(0,p.Wj)(i.Q),E=e=>{"scheduled-orders"===e.id&&C(),e.path&&a.push(e.path)};return(0,r.jsxs)("aside",{className:"p-2 rounded-2xl w-90 flex flex-col gap-2 overflow-auto ".concat(t),children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 flex flex-col gap-2 justify-center",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Статус активности"}),g&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"\n              w-3 h-3 rounded-full\n              ".concat(!A||N?"bg-gray-300 animate-pulse":y?"bg-orange-500":v?"bg-blue-500":"bg-gray-400","\n            ")}),(0,r.jsx)("span",{className:"text-xs",children:!A||N?"Загрузка...":y?"В активной поездке":v?x?"В очереди #".concat(x):"В очереди":"Не в очереди"})]})]}),(0,r.jsx)("nav",{className:"flex-1 overflow-y-auto",children:(0,r.jsx)("ul",{className:"flex flex-col gap-2",children:k.map(e=>{let t=z===e.id,a="scheduled-orders"===e.id;return(0,r.jsx)("li",{children:(0,r.jsxs)("button",{onClick:()=>E(e),className:"\n                    flex items-center justify-between w-full p-3 rounded-lg text-left transition-colors bg-[#D9D9D926] h-[120px] cursor-pointer\n                    ".concat(t?"bg-blue-50 text-blue-700 border border-blue-200":" hover:bg-gray-50","\n                  "),children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.title}),a&&w>0&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-xs text-gray-600",children:["Всего: ",w]}),S>0&&(0,r.jsxs)("span",{className:"text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full",children:["+",S," новых"]})]})]}),e.hasNotifications&&(0,r.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full"})]})},e.id)})})})]})}},34915:(e,t,a)=>{"use strict";a(27736)},44146:(e,t,a)=>{"use strict";a.d(t,{TerminalLocationsProvider:()=>p});var r=a(54568),l=a(62942),s=a(7620),i=a(7401);a(80408),a(28285),a(15493),a(81257),a(50385),a(55915),a(37556),a(27736),a(89751),a(76806);var n=a(29988);let o={bishkek:"Бишкек",chui:"Чуй",naryn:"Нарын",osh:"Ош",batken:"Баткен",talas:"Талас","jalal-abad":"Жалал-Абад","issyk-kul":"Иссык-Куль"};var c=a(94455),d=a(8426);let u={"bishkek-city":"Бишкек",tokmok:"Токмок",kant:"Кант","kara-balta":"Кара-Балта",shopokov:"Шопоков","osh-city":"Ош",uzgen:"Узген","kara-suu":"Кара-Суу","jalal-abad-city":"Жалал-Абад","tash-kumyr":"Таш-Кумыр",kerben:"Кербен",karakol:"Каракол",balykchy:"Балыкчы","cholpon-ata":"Чолпон-Ата","naryn-city":"Нарын","talas-city":"Талас","batken-city":"Баткен",sulyukta:"Сулюкта"},m={"birinchi-may":"Биринчи Май",leninsky:"Ленинский",oktyabrsky:"Октябрьский",sverdlovsky:"Свердловский","tokmok-central":"Центральный","tokmok-north":"Северный","tokmok-south":"Южный","kant-central":"Центральный","kant-industrial":"Промышленный","kara-balta-central":"Центральный","kara-balta-east":"Восточный","shopokov-central":"Центральный","osh-central":"Центральный","osh-north":"Северный","osh-south":"Южный","osh-east":"Восточный","uzgen-central":"Центральный","uzgen-old":"Старый город","kara-suu-central":"Центральный","kara-suu-bazar":"Базарный","jalal-abad-central":"Центральный","jalal-abad-north":"Северный","jalal-abad-south":"Южный","tash-kumyr-central":"Центральный","tash-kumyr-mining":"Горняцкий","kerben-central":"Центральный","karakol-central":"Центральный","karakol-north":"Северный","karakol-south":"Южный","balykchy-central":"Центральный","balykchy-port":"Портовый","cholpon-ata-central":"Центральный","cholpon-ata-resort":"Курортный","naryn-central":"Центральный","naryn-east":"Восточный","talas-central":"Центральный","talas-north":"Северный","batken-central":"Центральный","sulyukta-central":"Центральный","sulyukta-mining":"Горняцкий"},p=e=>{let{children:t}=e,a=(0,l.useRouter)(),p=(0,l.usePathname)(),{terminalLocation:h,isLoading:f}=(0,d.UH)(),{economyTariff:g,isLoading:b}=(0,c.Y)(),[v,x]=(0,s.useState)([]),[y,N]=(0,s.useState)([]),[j,S]=(0,s.useState)(!1),[w,C]=(0,s.useState)(null),[T,A]=(0,s.useState)(null),[R,I]=(0,s.useState)(null),[P,k]=(0,s.useState)(null);(0,s.useEffect)(()=>{if("/"===p||""===p)return void k(null);if(!f&&!b){if(!h)return void a.push("/");g||a.push("/")}},[h,g,f,b,p,a]);let z=(0,s.useCallback)(async e=>{if(j)return void console.log("⚠️ Пропускаем запрос - уже идет загрузка локаций");S(!0),C(null),e&&(A(e),(e.regionSlug||e.city)&&!e.address&&!e.searchQuery&&(I(e),k(e.regionSlug||null)));try{var t,a,r,l,s;let n={size:10,isActive:null==(s=null==e?void 0:e.isActive)||s,...null==e?void 0:e.customFilters};if((null==e?void 0:e.popularOnly)&&(n.popular1=!0),(null==e?void 0:e.type)&&(n.type=e.type),(null==e||null==(t=e.customFilters)?void 0:t.locationTypes)&&Array.isArray(e.customFilters.locationTypes)&&e.customFilters.locationTypes.length>0&&(n.type=e.customFilters.locationTypes,console.log("\uD83C\uDFF7️ Фильтр по типам локации:",e.customFilters.locationTypes)),(null==e?void 0:e.name)&&(n.name=e.name),(null==e?void 0:e.address)&&(n.address=e.address),(null==e?void 0:e.district)&&(n.district=e.district),(null==e?void 0:e.city)&&(n.city=e.city),(null==e?void 0:e.country)&&(n.country=e.country),(null==e?void 0:e.region)&&(n.region=e.region),(null==e?void 0:e.latitude)!==void 0&&(n.latitude=e.latitude),(null==e?void 0:e.longitude)!==void 0&&(n.longitude=e.longitude),(null==e?void 0:e.popular1)!==void 0&&(n.popular1=e.popular1),(null==e?void 0:e.popular2)!==void 0&&(n.popular2=e.popular2),(null==e?void 0:e.regionSlug)&&!(null==e?void 0:e.city)){let t=o[e.regionSlug]||e.regionSlug;n.city=t,console.log("\uD83C\uDFE2 Добавляем фильтр по городу:",e.regionSlug,"→",t)}if((null==e||null==(a=e.customFilters)?void 0:a.regionSlugs)&&Array.isArray(e.customFilters.regionSlugs)){let t=e.customFilters.regionSlugs.map(e=>o[e]||e);t.length>0&&(n.city=t,console.log("\uD83C\uDFE2 Фильтр по регионам:",e.customFilters.regionSlugs,"→",t))}if((null==e||null==(r=e.customFilters)?void 0:r.citySlugs)&&Array.isArray(e.customFilters.citySlugs)&&e.customFilters.citySlugs.length>0){let t=e.customFilters.citySlugs.map(e=>u[e]||e);n.city=t,console.log("\uD83C\uDFD9️ Фильтр по городам:",e.customFilters.citySlugs,"→",t)}if((null==e||null==(l=e.customFilters)?void 0:l.districtSlugs)&&Array.isArray(e.customFilters.districtSlugs)&&e.customFilters.districtSlugs.length>0){let t=e.customFilters.districtSlugs.map(e=>m[e]||e);n.district=t,console.log("\uD83C\uDFD8️ Фильтр по районам:",e.customFilters.districtSlugs,"→",t)}(null==e?void 0:e.searchQuery)&&e.searchQuery.trim()&&!(null==e?void 0:e.address)&&(n.address=e.searchQuery,console.log("\uD83D\uDD0D Добавляем поиск по адресу:",e.searchQuery)),console.log("\uD83C\uDF10 API запрос с параметрами:",n);let c=(await i.Ny.getAll(n)).data||[];N(c);let d=[];((null==e?void 0:e.popularOnly)||(null==e?void 0:e.popular1))&&d.push("популярных"),(null==e?void 0:e.type)&&d.push("типа ".concat(e.type)),((null==e?void 0:e.regionSlug)||(null==e?void 0:e.city))&&d.push("в городе ".concat((null==e?void 0:e.city)||(null==e?void 0:e.regionSlug))),((null==e?void 0:e.searchQuery)||(null==e?void 0:e.address))&&d.push('по запросу "'.concat((null==e?void 0:e.address)||(null==e?void 0:e.searchQuery),'"')),(null==e?void 0:e.country)&&d.push("в стране ".concat(e.country)),(null==e?void 0:e.region)&&d.push("в регионе ".concat(e.region));let p=d.length>0?d.join(", "):"всех";console.log("\uD83C\uDFE2 Терминал: загружено ".concat(p," локаций:"),c.length)}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки локаций";console.error("❌ Ошибка загрузки локаций:",t),C(e)}finally{S(!1)}},[]),E=(0,s.useCallback)(async e=>{if(!e.trim())return y;let t=e.toLowerCase();return y.filter(e=>{var a,r;return(null==(a=e.name)?void 0:a.toLowerCase().includes(t))||(null==(r=e.address)?void 0:r.toLowerCase().includes(t))})},[y]),D=(0,s.useCallback)(e=>y.find(t=>t.id===e),[y]),O=(0,s.useCallback)(async e=>{try{let t=D(e);if(t)return t;return await i.Ny.getById(e)}catch(e){return console.error("❌ Ошибка загрузки локации по ID:",e),null}},[D]),L=(0,s.useCallback)(e=>{x(t=>t.some(t=>t.id===e.id)?t:[...t,e])},[]),U=(0,s.useCallback)(e=>{x(t=>t.filter(t=>t.id!==e))},[]),q=(0,s.useCallback)(()=>{x([])},[]),B=(0,s.useCallback)(async()=>{T&&await z(T)},[T]),M=(0,s.useCallback)(async()=>{R&&await z(R)},[R]);return(0,r.jsx)(n.s.Provider,{value:{selectedLocations:v,addLocation:L,removeLocation:U,clearLocations:q,allLocations:y,isLoadingLocations:j,locationsError:w,currentRegionSlug:P,searchLocations:E,getLocationById:D,fetchLocationById:O,loadLocations:z,reloadLastLocations:B,reloadRegionLocations:M},children:t})}},45901:(e,t,a)=>{"use strict";a.d(t,{OU:()=>r,Lz:()=>n,mm:()=>s,zl:()=>i,$O:()=>l});var r=function(e){return e.Black="Black",e.White="White",e.Silver="Silver",e.Gray="Gray",e.Red="Red",e.Blue="Blue",e.Green="Green",e.Yellow="Yellow",e.Brown="Brown",e.Orange="Orange",e.Purple="Purple",e.Gold="Gold",e.Other="Other",e}({}),l=function(e){return e.Sedan="Sedan",e.Hatchback="Hatchback",e.SUV="SUV",e.Minivan="Minivan",e.Coupe="Coupe",e.Cargo="Cargo",e.Pickup="Pickup",e}({}),s=function(e){return e.Economy="Economy",e.Comfort="Comfort",e.ComfortPlus="ComfortPlus",e.Business="Business",e.Premium="Premium",e.Vip="Vip",e.Luxury="Luxury",e}({}),i=function(e){return e.Available="Available",e.Maintenance="Maintenance",e.Repair="Repair",e.Other="Other",e}({}),n=function(e){return e.AirConditioning="AirConditioning",e.ClimateControl="ClimateControl",e.LeatherSeats="LeatherSeats",e.HeatedSeats="HeatedSeats",e.Bluetooth="Bluetooth",e.USBPort="USBPort",e.AuxInput="AuxInput",e.Navigation="Navigation",e.BackupCamera="BackupCamera",e.ParkingSensors="ParkingSensors",e.Sunroof="Sunroof",e.PanoramicRoof="PanoramicRoof",e.ThirdRowSeats="ThirdRowSeats",e.ChildSeat="ChildSeat",e.WheelchairAccess="WheelchairAccess",e.Wifi="Wifi",e.PremiumAudio="PremiumAudio",e.AppleCarplay="AppleCarplay",e.AndroidAuto="AndroidAuto",e.SmokingAllowed="SmokingAllowed",e.PetFriendly="PetFriendly",e.LuggageCarrier="LuggageCarrier",e.BikeRack="BikeRack",e}({})},45935:(e,t,a)=>{"use strict";a.d(t,{ModalManagerComponent:()=>N});var r=a(54568),l=a(41186),s=a(91039),i=a(11447),n=a(7620),o=a(59959),c=a(68327);let d=()=>{let e=(0,o.k)(),[t,a]=(0,n.useState)(null),[r,l]=(0,n.useState)(!1),s=(0,n.useCallback)(t=>{var r,s,i;console.log("\uD83D\uDE97 ПОЛУЧЕН ЗАПРОС НА ПОЕЗДКУ В ХУКЕ:",t),console.log("\uD83D\uDE97 ТЕКУЩЕЕ СОСТОЯНИЕ signalR.isConnected:",e.isConnected);let n=(null==(r=t.data)?void 0:r.waypoints)||[],o=null==(s=n[0])?void 0:s.location,d=null==(i=n[n.length-1])?void 0:i.location,u={rideId:t.rideId,orderId:t.orderId,orderType:t.orderType||"Instant",customerName:t.customerName||"Неизвестный клиент",fromAddress:(null==o?void 0:o.address)||(null==o?void 0:o.name)||"Адрес не указан",toAddress:(null==d?void 0:d.address)||(null==d?void 0:d.name)||"Адрес не указан",price:t.price,distance:t.distance};console.log("\uD83C\uDFAF ОБРАБОТАННЫЕ ДАННЫЕ ДЛЯ МОДАЛКИ:",u),console.log("\uD83C\uDFAF УСТАНАВЛИВАЮ showModal = true"),a(u),l(!0),console.log("\uD83C\uDFAF ПОСЛЕ УСТАНОВКИ - currentRequest:",u),console.log("\uD83C\uDFAF ПОСЛЕ УСТАНОВКИ - showModal: true"),c.P.info("Новый заказ: ".concat(u.fromAddress," → ").concat(u.toAddress))},[e.isConnected]);(0,n.useEffect)(()=>{if(e.isConnected)return e.on("RideRequestNotification",s),()=>{e.off("RideRequestNotification",s)}},[e,e.isConnected,s]);let i=(0,n.useCallback)(()=>{l(!1),a(null)},[]),d=(0,n.useCallback)(()=>{i()},[i]),u=(0,n.useCallback)(()=>{i()},[i]);return{currentRequest:t,showModal:r,closeModal:i,handleAccept:d,handleReject:u}},u=(0,s.default)(()=>Promise.all([a.e(5194),a.e(298)]).then(a.bind(a,10298)).then(e=>({default:e.CardPaymentModal})),{loadableGenerated:{webpack:()=>[10298]}}),m=(0,s.default)(()=>a.e(5395).then(a.bind(a,75395)).then(e=>({default:e.CompanyDetailsModal})),{loadableGenerated:{webpack:()=>[75395]}}),p=(0,s.default)(()=>Promise.all([a.e(5194),a.e(9718)]).then(a.bind(a,99718)).then(e=>({default:e.FAQModal})),{loadableGenerated:{webpack:()=>[99718]}}),h=(0,s.default)(()=>a.e(6412).then(a.bind(a,6412)).then(e=>({default:e.PotentialDriversModal})),{loadableGenerated:{webpack:()=>[6412]}}),f=(0,s.default)(()=>a.e(9484).then(a.bind(a,49484)).then(e=>({default:e.PrivacyPolicyModal})),{loadableGenerated:{webpack:()=>[49484]}}),g=(0,s.default)(()=>a.e(6271).then(a.bind(a,56271)).then(e=>({default:e.PublicOfferModal})),{loadableGenerated:{webpack:()=>[56271]}}),b=(0,s.default)(()=>a.e(8371).then(a.bind(a,18371)).then(e=>({default:e.QRPaymentModal})),{loadableGenerated:{webpack:()=>[18371]}}),v=(0,s.default)(()=>a.e(3605).then(a.bind(a,23605)).then(e=>({default:e.RideRequestModal})),{loadableGenerated:{webpack:()=>[23605]}}),x=(0,s.default)(()=>a.e(3174).then(a.bind(a,83174)).then(e=>({default:e.SelectOrderTypeModal})),{loadableGenerated:{webpack:()=>[83174]}}),y=(0,s.default)(()=>a.e(9795).then(a.bind(a,19795)).then(e=>({default:e.SelectUserRoleModal})),{loadableGenerated:{webpack:()=>[19795]}}),N=()=>{let e=(0,l.e3)(i.Gw),t=(0,l.e3)(i.Qx),a=(0,l.e3)(i.Oo),{currentRequest:s,showModal:n,closeModal:o,handleAccept:c,handleReject:N}=d(),j=e.length>0;if(n&&s)return(0,r.jsx)(v,{requestData:s,isOpen:n,onClose:o,onAccept:c,onReject:N});if(!j)return null;switch(t){case i.bY.SelectUserRoleModal:return(0,r.jsx)(y,{});case i.bY.SelectOrderTypeModal:return(0,r.jsx)(x,{onClose:()=>a(void 0)});case i.bY.QRPaymentModal:return(0,r.jsx)(b,{});case i.bY.CardPaymentModal:return(0,r.jsx)(u,{});case i.bY.PotentialDriversModal:return(0,r.jsx)(h,{});case i.bY.CompanyDetailsModal:return(0,r.jsx)(m,{});case i.bY.PublicOfferModal:return(0,r.jsx)(g,{});case i.bY.PrivacyPolicyModal:return(0,r.jsx)(f,{});case i.bY.FAQModal:return(0,r.jsx)(p,{});default:return null}}},48484:(e,t,a)=>{"use strict";a.d(t,{Hq:()=>f,c7:()=>h});var r=a(62942),l=a(7620),s=a(66478);let i=null,n=[],o=!1,c=[],d=e=>{i=e,n.forEach(t=>t(e))},u=e=>{o=e,c.forEach(t=>t(e))},m=e=>(c.push(e),()=>{c=c.filter(t=>t!==e)}),p=e=>(n.push(e),()=>{n=n.filter(t=>t!==e)}),h=()=>{let e=(0,r.useRouter)(),[t,a]=(0,l.useState)(i),[n,c]=(0,l.useState)(o),{driverId:h,activeRides:f,isInitialized:g,isLoadingRides:b,refreshRides:v}=(0,s.g0)();(0,l.useEffect)(()=>{let e=p(e=>{a(e)}),t=m(e=>{c(e)});return o&&!n&&c(!0),()=>{e(),t()}},[n]),(0,l.useEffect)(()=>{if(g&&f.length>0){let e=f[0],a={orderId:e.orderId||e.id,rideId:e.id,status:e.status,ride:e};t&&t.rideId===a.rideId||d(a)}else g&&0===f.length&&t&&(console.log("\uD83D\uDDD1️ Нет активных поездок в централизованных данных, очищаем локальное состояние"),d(null))},[f,g,t]);let x=(0,l.useCallback)(async()=>(g&&h?(c(!0),u(!0)):console.log("⏳ Ждем инициализации водителя для проверки активных поездок..."),t),[t,h,g]),y=(0,l.useCallback)((e,t)=>{console.log("\uD83D\uDE80 setActiveRideData вызван - устанавливаем активную поездку глобально");let a={orderId:e,rideId:t,status:"Accepted",ride:null};d(a),console.log("✅ Активная поездка установлена глобально:",a)},[]),N=(0,l.useCallback)(async()=>(await v(),await x()),[x,v]),j=(0,l.useCallback)(()=>{console.log("\uD83D\uDDD1️ clearActiveRide вызван - очищаем активную поездку глобально"),localStorage.removeItem("activeRide"),d(null),console.log("✅ Активная поездка очищена глобально")},[]),S=(0,l.useCallback)(()=>{if(t){let a=t.orderType?"&orderType=".concat(t.orderType):"",r="/active-ride/?orderId=".concat(t.orderId,"&rideId=").concat(t.rideId).concat(a);e.push(r)}},[t,e]);return{activeRide:t,hasActiveRide:!!t,isLoading:b,isInitialized:n,isRideActive:e=>["Accepted","DriverHeading","Arrived","InProgress"].includes(e),checkActiveRides:x,setActiveRideData:y,clearActiveRide:j,navigateToActiveRide:S,forceUpdateActiveRide:N}},f=(e,t)=>{console.log("\uD83C\uDF0D setActiveRideGlobally вызван:",{orderId:e,rideId:t}),d({orderId:e,rideId:t,status:"Accepted",ride:null}),console.log("✅ Активная поездка установлена глобально через прямой вызов")}},50385:(e,t,a)=>{"use strict";a.d(t,{F:()=>l});var r=a(76806);r.V;let l=r.V},54994:(e,t,a)=>{"use strict";a.d(t,{DriverQueueProvider:()=>d});var r=a(54568),l=a(7620),s=a(68327),i=a(27736);class n extends i.vA{async getMyPosition(){try{let e=await this.get("self");return this.handleApiResultSafe(e)}catch(e){return e instanceof Error&&e.message.includes("404")?console.warn("⚠️ DriverQueue API endpoint не найден (404). Возможно, функция очереди отключена на сервере."):console.error("❌ Ошибка получения позиции в очереди:",e),null}}async join(){try{let e=await this.post("self");return this.handleApiResult(e)}catch(e){if(e instanceof Error&&e.message.includes("404"))throw Error("Функция очереди временно недоступна. Обратитесь к администратору.");throw e}}async leave(){try{let e=await this.delete("self");this.handleApiResult(e)}catch(e){if(e instanceof Error&&e.message.includes("404"))return void console.warn("⚠️ DriverQueue API endpoint не найден (404). Считаем что водитель уже не в очереди.");throw e}}constructor(...e){super(...e),this.baseUrl="/DriverQueue"}}let o=new n;var c=a(32959);let d=e=>{let{children:t,refreshInterval:a=3e4}=e,[i,n]=(0,l.useState)(!1),[d,u]=(0,l.useState)(null),[m,p]=(0,l.useState)(null),[h,f]=(0,l.useState)(!1),[g,b]=(0,l.useState)(!1),[v,x]=(0,l.useState)(null),y=(0,l.useCallback)(async()=>{try{f(!0),x(null),console.log("\uD83D\uDD0D Проверяем статус очереди через rideService...");let e=await o.getMyPosition();e?(n(!0),u(e.position),p(e.joinedAt),console.log("✅ Водитель в очереди, позиция:",e.position)):(n(!1),u(null),p(null),console.log("ℹ️ Водитель не в очереди"))}catch(t){let e=t instanceof Error?t.message:"Ошибка проверки статуса очереди";console.error("❌ Ошибка проверки статуса очереди через rideService:",t),x(e)}finally{f(!1)}},[]),N=(0,l.useCallback)(async()=>{try{b(!0),x(null),console.log("\uD83D\uDEB6‍♂️ Встаем в очередь через rideService...");let e=await o.join();n(!0),p(e.joinedAt),await y(),s.P.success("Вы встали в очередь"),console.log("✅ Успешно встали в очередь")}catch(t){let e=t instanceof Error?t.message:"Ошибка входа в очередь";console.error("❌ Ошибка входа в очередь через rideService:",t),x(e),s.P.error(e)}finally{b(!1)}},[y]),j=(0,l.useCallback)(async()=>{try{b(!0),x(null),console.log("\uD83D\uDEAA Выходим из очереди через rideService..."),await o.leave(),n(!1),u(null),p(null),s.P.success("Вы вышли из очереди"),console.log("✅ Успешно вышли из очереди")}catch(t){let e=t instanceof Error?t.message:"Ошибка выхода из очереди";console.error("❌ Ошибка выхода из очереди через rideService:",t),x(e),s.P.error(e)}finally{b(!1)}},[]),S=(0,l.useCallback)(async()=>{await y()},[y]);return(0,l.useEffect)(()=>{y()},[y]),(0,l.useEffect)(()=>{if(a>0&&i){let e=setInterval(y,a);return()=>clearInterval(e)}},[a,i,y]),(0,r.jsx)(c.N.Provider,{value:{isInQueue:i,position:d,joinedAt:m,isLoading:h,isActionLoading:g,error:v,actions:{joinQueue:N,leaveQueue:j,refresh:S}},children:t})}},57870:(e,t,a)=>{"use strict";a.d(t,{Header:()=>j});var r=a(54568),l=a(7620),s=a(88154),i=a(27261),n=a.n(i);let o=()=>{let e=(0,s.if)();return 0===e.length?null:(0,r.jsx)("nav",{"aria-label":"Breadcrumbs",children:(0,r.jsx)("ol",{className:"flex items-center space-x-1 text-sm",children:e.map((t,a)=>{let l=a===e.length-1;return(0,r.jsxs)("li",{className:"flex items-center",children:[a>0&&(0,r.jsx)("span",{className:"mx-1",children:"/"}),l?(0,r.jsx)("span",{className:"font-medium text-[color:var(--color-brand)]",children:t.label}):(0,r.jsx)(n(),{href:t.path,className:"hover:text-[color:var(--color-primary)] transition-colors duration-150",children:t.label})]},t.path)})})})};var c=a(62942),d=a(4585),u=a(45452),m=a(32326),p=a(51669);let h=(0,l.forwardRef)((e,t)=>{let{isOpen:a}=e,s=(0,c.useRouter)(),i=(0,l.useRef)(null),n=(0,l.useRef)(null),{notifications:o,isLoading:h,isLoadingMore:f,hasMore:g,unreadCount:b,actions:v}=(0,u.M)(),x=(0,l.useCallback)(()=>{i.current&&i.current.disconnect(),i.current=new IntersectionObserver(e=>{e[0].isIntersecting&&g&&!f&&!h&&o.length>0&&(console.log("\uD83D\uDD04 Intersection Observer: загружаем больше уведомлений"),v.loadMore())},{threshold:.1,rootMargin:"20px"}),n.current&&g&&i.current.observe(n.current)},[g,f,h,v,o.length]);(0,l.useEffect)(()=>(a&&x(),()=>{i.current&&i.current.disconnect()}),[x,a]);let y=async e=>{try{if(e.isRead||await v.markAsRead(e.id),e.orderId){var t;let a=(null==(t=e.orderType)?void 0:t.toLowerCase())||"instant";s.push("/orders/".concat(e.orderId,"?type=").concat(a))}else e.rideId&&s.push("/rides/".concat(e.rideId))}catch(e){}};return a?(0,r.jsxs)("div",{ref:t,role:"menu","aria-hidden":!1,className:"absolute right-0 top-[calc(100%+1.5rem)] w-80 rounded-md overflow-hidden shadow-lg border border-[color:var(--color-neutral-300)]  z-40 transition-all duration-200 transform origin-top-right opacity-100 scale-100 animate-fadeIn backdrop-blur-md bg-black/90",children:[(0,r.jsxs)("div",{className:"p-3 border-b border-[color:var(--color-neutral-300)] flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-medium",children:"Уведомления"}),b>0&&(0,r.jsx)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300 rounded-full",children:b})]}),(0,r.jsx)("div",{className:"max-h-96 overflow-y-auto",children:h&&0===o.length?(0,r.jsx)("div",{className:"p-4 text-center",children:"Загрузка уведомлений..."}):0===o.length?(0,r.jsx)("div",{className:"p-4 text-center",children:"Нет уведомлений"}):(0,r.jsxs)(r.Fragment,{children:[o.map(e=>(0,r.jsx)("div",{onClick:()=>y(e),className:"\n                    p-3 border-b border-[color:var(--color-neutral-300)] \n                    hover:bg-[color:var(--color-surface-variant)] \n                    dark:hover:bg-[color:var(--color-neutral-800)]/50 \n                    transition-colors duration-200 cursor-pointer\n                    ".concat(e.isRead?"opacity-70":"","\n                  "),children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"\n                      flex-shrink-0 rounded-full p-2 transition-colors duration-200 text-lg\n                      ".concat(function(e,t){let a=t?"50":"100";switch(e){case p._n.RideRequest:case p._n.RideAccepted:return"bg-blue-".concat(a," text-blue-700 dark:bg-blue-900/30 dark:text-blue-300");case p._n.RideCompleted:case p._n.OrderCompleted:return"bg-green-".concat(a," text-green-700 dark:bg-green-900/30 dark:text-green-300");case p._n.PaymentReceived:return"bg-emerald-".concat(a," text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300");case p._n.PaymentFailed:case p._n.OrderCancelled:case p._n.RideCancelled:return"bg-red-".concat(a," text-red-700 dark:bg-red-900/30 dark:text-red-300");case p._n.System:case p._n.SystemMessage:return"bg-gray-".concat(a,"  dark:bg-gray-900/30 dark:text-gray-300");default:return"bg-blue-".concat(a," text-blue-700 dark:bg-blue-900/30 dark:text-blue-300")}}(e.type,e.isRead||!1),"\n                    "),children:function(e){switch(e){case p._n.RideRequest:return"\uD83D\uDE97";case p._n.RideAccepted:return"✅";case p._n.RideCompleted:return"\uD83C\uDFC1";case p._n.OrderCompleted:return"\uD83C\uDF89";case p._n.DriverArrived:return"\uD83D\uDCCD";case p._n.DriverHeading:return"\uD83D\uDE99";case p._n.DriverAssigned:return"\uD83D\uDC64";case p._n.PaymentReceived:return"\uD83D\uDCB0";case p._n.PaymentFailed:return"\uD83D\uDCB8";case p._n.OrderCancelled:case p._n.RideCancelled:return"❌";case p._n.System:case p._n.SystemMessage:return"⚙️";default:return"\uD83D\uDCE2"}}(e.type)}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,r.jsx)("p",{className:"\n                          text-sm font-medium truncate\n                          ".concat(e.isRead?"":"text-[color:var(--color-text-primary)]","\n                        "),children:e.title}),!e.isRead&&(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-1"})]}),e.content&&(0,r.jsx)("p",{className:"text-xs  transition-colors duration-200 line-clamp-2",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("p",{className:"text-xs text-[color:var(--color-text-tertiary)] transition-colors duration-200",children:(0,d.f)(e.createdAt)}),(0,r.jsx)("span",{className:"text-xs text-[color:var(--color-text-tertiary)] bg-[color:var(--color-neutral-100)] dark:bg-[color:var(--color-neutral-800)] px-2 py-1 rounded",children:(0,m.gw)(e.type)})]})]})]})},e.id)),g&&(0,r.jsx)("div",{ref:n,className:"flex items-center justify-center py-3",children:f?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,r.jsx)("span",{className:"text-xs",children:"Загрузка..."})]}):(0,r.jsx)("button",{onClick:()=>v.loadMore(),className:"text-xs text-[color:var(--color-primary)] hover:text-[color:var(--color-primary-hover)] transition-colors",children:"Загрузить еще"})}),!g&&o.length>0&&(0,r.jsx)("div",{className:"text-center py-2 text-[color:var(--color-text-tertiary)] text-xs",children:"Все уведомления загружены"})]})})]}):null});h.displayName="NotificationsDropdown";var f=a(27736),g=a(79702),b=a(8703),v=a(79394);let x=(0,l.forwardRef)((e,t)=>{let{isOpen:a}=e,[s,i]=(0,l.useState)(!1),o=async()=>{try{i(!0);let e=fetch("/api".concat(f.QQ.AUTH.LOGOUT),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include"}),t=await (0,g.n)(e);if(t.ok)window.location.href=f.vc.AUTH.LOGIN;else{let e="Ошибка при выходе из системы";try{e=(await t.json()).message||e}catch(a){e="Ошибка при выходе из системы: ".concat(t.status," ").concat(t.statusText)}b.P.error(e)}}catch(t){let e=t instanceof Error?t.message:"Произошла непредвиденная ошибка при выходе из системы";b.P.error(e)}finally{i(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.wh,{isVisible:s,message:"Выход из системы..."}),(0,r.jsxs)("div",{ref:t,role:"menu","aria-hidden":!a,className:"absolute right-0 top-[calc(100%+1.5rem)] w-56 rounded-md overflow-hidden shadow-lg \n            border border-[color:var(--color-neutral-300)]  z-40 transition-all duration-200 transform origin-top-right \n            bg-black/50 backdrop-blur-md\n            ".concat(a?"opacity-100 scale-100 pointer-events-auto":"opacity-0 scale-95 pointer-events-none"),children:[(0,r.jsxs)("div",{className:"p-3 border-b border-[color:var(--color-neutral-300)]",children:[(0,r.jsx)("p",{className:"font-medium",children:"Администратор"}),(0,r.jsx)("p",{className:"text-sm  transition-colors duration-200",children:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(n(),{href:f.vc.PROFILE.SELF,role:"menuitem",className:"block w-full px-4 py-2 text-sm hover:bg-gray-50/20 transition-colors duration-200",children:"Мой профиль"}),(0,r.jsx)(n(),{href:"/settings",role:"menuitem",className:"block w-full px-4 py-2 text-sm hover:bg-gray-50/20 transition-colors duration-200",children:"Настройки"}),(0,r.jsx)("button",{type:"button",role:"menuitem",onClick:o,disabled:s,className:"block w-full text-left px-4 py-2 text-sm text-[color:var(--color-error)] hover:bg-red-50/20 transition-colors duration-200 cursor-pointer",children:"Выйти"})]})]})]})});x.displayName="ProfileDropdown";let y=l.lazy(()=>Promise.resolve().then(a.bind(a,73282))),N=l.lazy(()=>Promise.resolve().then(a.bind(a,76704))),j=()=>{let{notificationsOpen:e,profileOpen:t,notificationsRef:a,profileRef:i,notificationsButtonRef:n,profileButtonRef:c,toggleNotifications:d,toggleProfile:u}=(0,s.Nu)();return(0,r.jsx)("header",{className:"h-16 px-8 py-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-full",children:[(0,r.jsxs)("div",{className:"flex flex-col items-start justify-start",children:[(0,r.jsx)("h1",{className:"text-xl font-medium",children:"Админ-панель"}),(0,r.jsx)(o,{})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{ref:n,onClick:d,className:"p-2 rounded-full hover:bg-[color:var(--color-surface-variant)] active:bg-[color:var(--color-surface-variant)]/80 transition-colors duration-200 relative cursor-pointer","aria-label":"Уведомления",children:[(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(y,{className:"w-6 h-6"})}),(0,r.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-error rounded-full"})]}),(0,r.jsx)(h,{ref:a,isOpen:e})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{ref:c,onClick:u,className:"flex items-center justify-center p-2 rounded-full hover:bg-[color:var(--color-surface-variant)] active:bg-[color:var(--color-surface-variant)]/80 transition-colors duration-200 cursor-pointer","aria-label":"Профиль",style:{width:"40px",height:"40px"},children:(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)("div",{className:"w-6 h-6"}),children:(0,r.jsx)(N,{className:"w-6 h-6 text-primary"})})}),(0,r.jsx)(x,{ref:i,isOpen:t})]})]})]})})}},60916:(e,t,a)=>{"use strict";a.d(t,{NotificationProvider:()=>d});var r=a(54568),l=a(7620),s=a(59959),i=a(42997),n=a(45452);let o={RideRequest:1,OrderCreated:1,RideAccepted:2,DriverAssigned:3,DriverArrived:4,DriverHeading:4,RideStarted:5,OrderConfirmed:5,OrderUpdated:5,RideCompleted:10,OrderCompleted:10,RideCancelled:9,OrderCancelled:9,DriverCancelled:8,RideRejected:7,PaymentReceived:100,PaymentFailed:100,System:100,SystemMessage:100,Maintenance:100,Verification:100,Payment:100};var c=a(91599);let d=e=>{let{children:t}=e,a=(0,s.k)(),{notifications:d,isLoading:u,isLoadingMore:m,error:p,hasMore:h,totalCount:f,unreadCount:g,actions:{loadNotifications:b,loadMore:v,refresh:x,markAsRead:y,deleteNotification:N}}=(0,c.E)(20),[j,S]=(0,l.useState)(0);console.log("\uD83D\uDD04 NotificationProvider состояние:",{notificationsCount:d.length,isLoading:u,isLoadingMore:m,hasMore:h,totalCount:f,unreadCount:g,realTimeUnreadCount:j});let w=(0,l.useCallback)(()=>(function(e){let t=new Map,a=[];e.forEach(e=>{if(!e.orderId||o[e.type]>=100)return void a.push(e);t.has(e.orderId)||t.set(e.orderId,[]),t.get(e.orderId).push(e)});let r=[];return t.forEach(e=>{let t=e.sort((e,t)=>{let a=o[e.type]||0,r=o[t.type]||0;return a!==r?r-a:new Date(t.createdAt||0).getTime()-new Date(e.createdAt||0).getTime()});r.push(t[0])}),r.push(...a),r.sort((e,t)=>new Date(t.createdAt||0).getTime()-new Date(e.createdAt||0).getTime())})(d),[d]),C=(0,l.useCallback)(()=>{let e=w(),t={order:0,completed:0,important:0,warning:0},a=0;return e.forEach(e=>{if(!e.isRead){let r=(0,i.CU)(e.type);t[r]++,a++}}),{priorityCounts:t,totalUnread:a,totalCount:e.length}},[w]),T=(0,l.useCallback)(async e=>{try{await y(e),S(e=>Math.max(0,e-1)),console.log("✅ NotificationProvider.markAsRead успешно:",e)}catch(e){console.error("❌ NotificationProvider.markAsRead ошибка:",e)}},[y]),A=(0,l.useCallback)(async e=>{try{let t=d.find(t=>t.id===e),a=t&&!t.isRead;await N(e),a&&S(e=>Math.max(0,e-1)),console.log("\uD83D\uDDD1️ NotificationProvider.deleteNotification успешно:",e)}catch(e){console.error("❌ NotificationProvider.deleteNotification ошибка:",e)}},[N,d]);(0,l.useEffect)(()=>{if(a.connection&&a.isConnected){let e=e=>{console.log("\uD83D\uDCE8 NotificationProvider: получено новое уведомление через SignalR"),S(e=>e+1),x()},t=["RideRequestNotification","RideAcceptedNotification","RideAssignedNotification","RideCancelledNotification","RideCompletedNotification","DriverActivityUpdated","DriverArrivedNotification","DriverHeadingNotification","DriverCancelledNotification","OrderConfirmedNotification","OrderCancelledNotification","OrderCompletedNotification","RideRejectedNotification","RideStartedNotification","PaymentNotification","PaymentReceivedNotification","PaymentFailedNotification"];return t.forEach(t=>{a.on(t,e)}),()=>{t.forEach(t=>{a.off(t,e)})}}},[a,x]),(0,l.useEffect)(()=>{console.log("\uD83D\uDE80 NotificationProvider: автоматическая загрузка уведомлений"),b(!1)},[b]);let R=C(),I={notifications:w(),hasUnreadNotifications:g+j>0,unreadCount:g+j,unreadCountsByPriority:R.priorityCounts,isLoading:u,isLoadingMore:m,error:p,hasMore:h,totalCount:R.totalCount,originalTotalCount:f,actions:{loadMore:v,refresh:x,markAsRead:T,deleteNotification:A,markAllAsRead:async()=>{console.log("TODO: Implement markAllAsRead")},markAllAsReadByPriority:async e=>{console.log("TODO: Implement markAllAsReadByPriority")},loadMoreByPriority:e=>{console.log("TODO: Implement loadMoreByPriority")},loadMoreIfNeeded:()=>{console.log("TODO: Implement loadMoreIfNeeded")},markAsReadById:T,markAsReadByType:async e=>{console.log("TODO: Implement markAsReadByType")}}};return(0,r.jsx)(n.V.Provider,{value:I,children:t})}},61889:(e,t,a)=>{"use strict";a.r(t),a.d(t,{useDriverHeader:()=>n});var r=a(40529),l=a(45901);let s={[l.$O.Sedan]:"/car/sedan.png",[l.$O.Hatchback]:"/car/hatchback.png",[l.$O.SUV]:"/car/suv.png",[l.$O.Minivan]:"/car/minivan.png",[l.$O.Coupe]:"/car/coupe.png",[l.$O.Cargo]:"/car/cargo.png",[l.$O.Pickup]:"/car/pickup.png"},i="/car/HongQi E-QM5.png";function n(){var e,t,a;let{profile:l,isLoading:n,error:o}=(0,r.Z)(),c=null==l?void 0:l.activeCar,d=l?{id:l.id,firstName:l.fullName.split(" ")[0]||"Водитель",lastName:l.fullName.split(" ").slice(1).join(" ")||"",fullName:l.fullName,phone:l.phoneNumber||"Телефон не указан",email:l.email,avatar:l.avatarUrl||void 0,vehicle:c?{licensePlate:c.licensePlate,model:c.model,make:c.make,year:c.year,color:c.color,category:c.serviceClass||"Economy",bodyType:c.type||"Economy"}:void 0}:null,u=(a=null==d||null==(e=d.vehicle)?void 0:e.bodyType)&&s[a]||i,m=(null==d?void 0:d.fullName)||"Водитель",p=(null==d?void 0:d.phone)||"Телефон не указан",h=(null==d||null==(t=d.firstName)?void 0:t[0])||"В",f=!!(null==d?void 0:d.vehicle);return{profile:d,isLoading:n,error:o,isActive:!!(null==l?void 0:l.online),carImagePath:u,displayName:m,displayPhone:p,avatarInitials:h,hasVehicleInfo:f}}},62571:(e,t,a)=>{"use strict";a.d(t,{C:()=>i});var r=a(27736),l=a(80745);class s extends r.vA{async getRide(e){let t=await this.get("/".concat(e));return this.handleApiResult(t)}async getRideSafe(e){let t=await this.get("/".concat(e));return this.handleApiResultSafe(t)}async getRides(e){let t=new URLSearchParams;(null==e?void 0:e.first)!==void 0&&t.append("first",e.first.toString()),(null==e?void 0:e.before)&&t.append("before",e.before),(null==e?void 0:e.after)&&t.append("after",e.after),(null==e?void 0:e.last)!==void 0&&t.append("last",e.last.toString()),(null==e?void 0:e.size)!==void 0&&t.append("size",e.size.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.status)&&t.append("status",e.status),(null==e?void 0:e.driverId)&&t.append("driverId",e.driverId),(null==e?void 0:e.passengerId)&&t.append("passengerId",e.passengerId);let a=t.toString()?"?".concat(t.toString()):"",r=await this.get(a);return this.handleApiResult(r)}async getDriverActiveRides(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,a=new URLSearchParams;a.append("DriverId",e),a.append("Size",t.toString()),a.append("Status","Accepted"),a.append("Status","Arrived"),a.append("Status","InProgress");let r=await this.get("my?".concat(a.toString()));return this.handleApiResult(r)}async getDriverScheduledRides(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,a=new URLSearchParams;a.append("driverId",e),a.append("pageSize",t.toString()),a.append("Status","Requested"),a.append("Status","Accepted");let r=await this.get("my/assigned?".concat(a.toString()));return this.handleApiResult(r)}async createRide(e){let t=await this.post("",e);return this.handleApiResult(t)}async updateRide(e,t){let a=await this.put("/".concat(e),t);return this.handleApiResult(a)}async deleteRide(e){let t=await this.delete("/".concat(e));this.handleApiResult(t)}async updateRideStatus(e,t){let a=await this.patch("/".concat(e,"/status"),{status:t});return this.handleApiResult(a)}async acceptRide(e,t){let a=await this.patch("/".concat(e,"/accept"),{driverId:t});return this.handleApiResult(a)}async completeRide(e){let t=await this.patch("/".concat(e,"/complete"));return this.handleApiResult(t)}async cancelRide(e,t){let a=await this.patch("/".concat(e,"/cancel"),{reason:t});return this.handleApiResult(a)}async getDriverQueuePosition(){try{let e=await this.get("/DriverQueue/self");return this.handleApiResultSafe(e)}catch(e){return null}}async joinDriverQueue(){let e=await this.post("/DriverQueue/self");return this.handleApiResult(e)}async leaveDriverQueue(){let e=await this.delete("/DriverQueue/self");this.handleApiResult(e)}async isDriverInQueue(){return null!==await this.getDriverQueuePosition()}async acceptInstantOrder(e){let t=await this.post("/Order/instant/".concat(e,"/accept-by-driver"));return this.handleApiResult(t)}async acceptScheduledRide(e){let t=await this.post("/Ride/".concat(e,"/accept-by-driver"));return this.handleApiResult(t)}async notifyScheduledRideAcceptance(e){let t=await this.post("/Ride/".concat(e,"/notification-by-driver"));this.handleApiResult(t)}async rejectScheduledRide(e){let t=await this.post("/Ride/".concat(e,"/reject-by-driver"));return this.handleApiResult(t)}async driverHeadingToClient(e){let t=await this.post("/Ride/".concat(e,"/status/driver-heading-to-client"));return this.handleApiResult(t)}async driverArrived(e){let t=await this.post("/Ride/".concat(e,"/status/driver-arrived"));return this.handleApiResult(t)}async rideStarted(e){let t=await this.post("/Ride/".concat(e,"/status/ride-started"));return this.handleApiResult(t)}async rideFinished(e){let t=await this.post("/Ride/".concat(e,"/status/ride-finished"));return this.handleApiResult(t)}async rideCancelled(e){let t=await this.post("/Ride/".concat(e,"/status/ride-cancelled"));return this.handleApiResult(t)}async getActiveRides(e){return e?(await this.getDriverActiveRides(e)).data||[]:(await this.getDriverActiveRides("current")).data||[]}async getRideById(e){let t=await this.get("/Ride/".concat(e));return this.handleApiResult(t)}async getScheduledOrderDetails(e){let t=await this.get("/Order/scheduled/".concat(e));return this.handleApiResult(t)}async getOrderDetails(e){let t=await this.get("/Order/".concat(e));return this.handleApiResult(t)}async getOrderDetailsUniversal(e){try{return await this.getScheduledOrderDetails(e)}catch(a){if(a&&"object"==typeof a&&"response"in a){var t;if((null==(t=a.response)?void 0:t.status)===404)return await this.getOrderDetails(e)}throw a}}async getCurrentDriverId(){try{let e=await this.get("/Driver/current"),t=this.handleApiResultSafe(e);return(null==t?void 0:t.driverId)||null}catch(e){return console.error("❌ Ошибка получения ID текущего водителя:",e),null}}async assignDriver(e,t,a){let r={driverId:t};a&&(r.carId=a);let l=await this.patch("/".concat(e,"/assign-driver"),r);return this.handleApiResult(l)}constructor(...e){super(...e),this.baseUrl=l.QQ.RIDE.LIST}}let i=new s},72617:(e,t,a)=>{"use strict";a.d(t,{DriverDataProvider:()=>o});var r=a(54568),l=a(7620),s=a(98893),i=a(9205),n=a(8426);let o=e=>{let{children:t}=e,[a,o]=(0,l.useState)(null),[c,d]=(0,l.useState)(null),[u,m]=(0,l.useState)([]),[p,h]=(0,l.useState)([]),[f,g]=(0,l.useState)(null),[b,v]=(0,l.useState)(!1),[x,y]=(0,l.useState)(!0),[N,j]=(0,l.useState)(!1),[S,w]=(0,l.useState)(null),C=(0,l.useRef)(!1),T=(0,l.useRef)(!1),A=(0,l.useCallback)(async()=>{try{y(!0),w(null);let e=await i.Dv.getCurrentDriver();if(e&&e.id)o(e.id),d(e);else throw Error("Не удалось получить данные водителя")}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки данных водителя";console.error("❌ Ошибка централизованной загрузки данных водителя:",t),w(e)}finally{y(!1),v(!0)}},[]),R=(0,l.useCallback)(async()=>{if(a&&!T.current)try{T.current=!0,j(!0),w(null);let[e,t]=await Promise.all([s.C.getDriverActiveRides(a,100),s.C.getDriverScheduledRides(a,50)]),r=(null==e?void 0:e.data)||[],l=(null==t?void 0:t.data)||[],i=t?{totalCount:t.totalCount,pageSize:t.pageSize,hasPrevious:t.hasPrevious,hasNext:t.hasNext}:null;m(r),h(l),g(i)}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки данных поездок";console.error("❌ Ошибка централизованной загрузки поездок:",t),w(e)}finally{j(!1),T.current=!1}},[a]),I=(0,l.useCallback)(async()=>{console.log("\uD83D\uDD04 Обновление данных водителя..."),await A()},[A]),P=(0,l.useCallback)(async()=>{T.current=!1,await R()},[R]);return(0,l.useEffect)(()=>{C.current||(C.current=!0,A())},[A]),(0,l.useEffect)(()=>{a&&b&&!x&&R()},[a,b,x,R]),(0,r.jsx)(n.aN.Provider,{value:{driverId:a,driverProfile:c,activeRides:u,scheduledRides:p,scheduledRidesPagination:f,isInitialized:b,isLoading:x,isLoadingRides:N,error:S,refreshDriverData:I,refreshRides:P},children:t})}},75170:(e,t,a)=>{"use strict";a.d(t,{G:()=>c});var r=a(54568),l=a(27261),s=a.n(l),i=a(7620),n=a(95559);let o=(0,i.lazy)(()=>Promise.resolve().then(a.bind(a,11523))),c={columns:[{id:"name",label:"Название",accessor:"name",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"serviceClass",label:"Класс обслуживания",accessor:"serviceClass",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,n.cx)()},renderCell:e=>(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-[color:var(--color-primary-50)] text-[color:var(--color-primary)]",children:(0,n.mE)(e.serviceClass)})},{id:"basePrice",label:"Базовая стоимость",accessor:"basePrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,r.jsxs)("span",{className:"font-medium",children:[e.basePrice.toLocaleString("ru-RU")," сом"]})},{id:"minutePrice",label:"Стоимость минуты",accessor:"minutePrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,r.jsxs)("span",{className:"font-medium",children:[e.minutePrice.toLocaleString("ru-RU")," сом"]})},{id:"minimumPrice",label:"Минимальная стоимость",accessor:"minimumPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,r.jsxs)("span",{className:"font-medium",children:[e.minimumPrice.toLocaleString("ru-RU")," сом"]})},{id:"perKmPrice",label:"Стоимость километра",accessor:"perKmPrice",sortable:!0,filterable:!0,filterConfig:{fieldType:"number",availableOperators:["equals","greaterThan","lessThan","greaterThanOrEqual","lessThanOrEqual"]},renderCell:e=>(0,r.jsxs)("span",{className:"font-medium",children:[e.perKmPrice.toLocaleString("ru-RU")," сом/км"]})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,r.jsx)("div",{className:"flex justify-end gap-2",children:(0,r.jsx)(s(),{href:"/tariffs/".concat(e.id),className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,r.jsx)(o,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"Name",label:"Название",operator:"contains",operatorField:"NameOp"}]},filtersConfig:{availableFilters:[{field:"ServiceClass",label:"Класс обслуживания",type:"select",options:(0,n.cx)().map(e=>({value:e.value,label:e.label}))},{field:"BasePrice",label:"Базовая стоимость",type:"number"},{field:"MinutePrice",label:"Стоимость минуты",type:"number"},{field:"MinimumPrice",label:"Минимальная стоимость",type:"number"},{field:"PerKmPrice",label:"Стоимость километра",type:"number"}]}}},76806:(e,t,a)=>{"use strict";a.d(t,{V:()=>u});var r=a(54568),l=a(27261),s=a.n(l),i=a(7620),n=a(66251),o=a(55915),c=a(37556);let d=(0,i.lazy)(()=>Promise.resolve().then(a.bind(a,11523))),u={columns:[{id:"name",label:"Название",accessor:"name",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","endsWith","equals","notEquals"]}},{id:"type",label:"Тип",accessor:"type",sortable:!0,filterable:!0,filterConfig:{fieldType:"enum",enumOptions:(0,n.wI)(o.i,c.al)},renderCell:e=>(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium",children:(0,c.al)(e.type)})},{id:"address",label:"Адрес",accessor:"address",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","startsWith","equals","notEquals"]}},{id:"city",label:"Город",accessor:"city",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]}},{id:"district",label:"Округ/район",accessor:"district",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]},renderCell:e=>(0,r.jsx)("span",{children:e.district||"-"})},{id:"region",label:"Область/регион",accessor:"region",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]}},{id:"country",label:"Страна",accessor:"country",sortable:!0,filterable:!0,filterConfig:{fieldType:"string",availableOperators:["contains","equals","notEquals"]}},{id:"isActive",label:"Активна",accessor:"isActive",sortable:!0,filterable:!0,filterConfig:{fieldType:"boolean"},renderCell:e=>(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Активна":"Неактивна"})},{id:"popular",label:"Популярность",accessor:"id",sortable:!1,filterable:!0,filterConfig:{fieldType:"boolean"},renderCell:e=>(0,r.jsxs)("div",{className:"flex gap-1",children:[e.popular1&&(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"П1"}),e.popular2&&(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:"П2"}),!e.popular1&&!e.popular2&&(0,r.jsx)("span",{className:"text-gray-400",children:"-"})]})},{id:"coordinates",label:"Координаты",accessor:"id",sortable:!1,filterable:!1,renderCell:e=>(0,r.jsxs)("span",{children:[e.latitude.toFixed(6),", ",e.longitude.toFixed(6)]})},{id:"actions",label:"Действия",accessor:"id",width:"120px",renderCell:e=>(0,r.jsx)("div",{className:"flex justify-end gap-2",children:(0,r.jsx)(s(),{href:"/locations/".concat(e.id),className:"p-2 text-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-50)] rounded-full transition-colors bg-[color:var(--color-neutral-200)] hover:scale-105",children:(0,r.jsx)(d,{size:20,className:"text-[color:var(--color-primary)]"})})})}],searchConfig:{searchableFields:[{field:"search",label:"Везде",operator:"contains"},{field:"Name",label:"Название",operator:"contains",operatorField:"NameOp"},{field:"Address",label:"Адрес",operator:"contains",operatorField:"AddressOp"},{field:"City",label:"Город",operator:"contains",operatorField:"CityOp"},{field:"District",label:"Округ/район",operator:"contains",operatorField:"DistrictOp"},{field:"Region",label:"Область/регион",operator:"contains",operatorField:"RegionOp"}]},filtersConfig:{availableFilters:[{field:"Type",label:"Тип локации",type:"select",options:(0,c._n)().map(e=>({value:e.value,label:e.label}))},{field:"IsActive",label:"Активна",type:"boolean"},{field:"Country",label:"Страна",type:"text"},{field:"Region",label:"Область/регион",type:"text"},{field:"Popular1",label:"Популярная локация 1",type:"boolean"},{field:"Popular2",label:"Популярная локация 2",type:"boolean"}]}}},77566:(e,t,a)=>{"use strict";a.d(t,{B8:()=>i,El:()=>s});var r=a(75006);let l=r.z.enum(["Economy","Comfort","ComfortPlus","Business","Premium","Vip","Luxury"]);r.z.object({fromId:r.z.string().uuid(),toId:r.z.string().uuid(),tariffId:r.z.string().uuid(),basePrice:r.z.number()});let s=r.z.object({name:r.z.string().min(1,{message:"Название тарифа обязательно"}).max(127,{message:"Название тарифа не должно превышат�� 127 символов"}).describe("Название тарифа (Эконом, Комфорт)"),serviceClass:l.describe("Класс обслуживания"),basePrice:r.z.number().min(0,{message:"Базовая стоимость не может быть отрицательной"}).describe("Базовая стоимость подачи / минимальная стоимость"),minutePrice:r.z.number().min(0,{message:"Стоимость минуты не может быть отрицательной"}).describe("Стоимость минуты поездки"),minimumPrice:r.z.number().min(0,{message:"Минимальная стоимость не может быть отрицательной"}).describe("Минимальная стоимость поездки по тарифу"),perKmPrice:r.z.number().min(0,{message:"Стоимость километра не может быть отрицательной"}).describe("Стоимость километра поездки (сом)")}),i=s.extend({id:r.z.string().uuid({message:"Некорректный формат UUID"}).describe("Идентификатор тарифа")});r.z.object({data:r.z.lazy(()=>i).optional(),totalCount:r.z.number().int().optional(),pageSize:r.z.number().int().optional(),hasPrevious:r.z.boolean().optional(),hasNext:r.z.boolean().optional()})},77963:(e,t,a)=>{"use strict";a.d(t,{x:()=>i});var r=a(27736),l=a(80745);class s extends r.vA{async getOrder(e){let t=await this.get("/".concat(e));return this.handleApiResult(t)}async getInstantOrder(e){let t=await this.get("instant/".concat(e));return this.handleApiResult(t)}async getScheduledOrder(e){let t=await this.get("scheduled/".concat(e));return this.handleApiResult(t)}async getOrderSafe(e){let t=await this.get("/".concat(e));return this.handleApiResultSafe(t)}async getMyParticipantOrder(e){let t=await this.get("/Order/my/participant/".concat(e));return this.handleApiResult(t)}async getOrders(e){var t,a,r,l;let s=new URLSearchParams;(null==e?void 0:e.First)!==void 0&&s.append("First",e.First.toString()),(null==e?void 0:e.Before)&&s.append("Before",e.Before),(null==e?void 0:e.After)&&s.append("After",e.After),(null==e?void 0:e.Last)!==void 0&&s.append("Last",e.Last.toString()),(null==e?void 0:e.Size)!==void 0&&s.append("Size",e.Size.toString()),(null==e||null==(t=e.Type)?void 0:t.length)&&e.Type.forEach(e=>s.append("Type",e)),(null==e||null==(a=e.status)?void 0:a.length)&&e.status.forEach(e=>s.append("status",e)),(null==e?void 0:e.statusOp)&&s.append("statusOp",e.statusOp),(null==e||null==(r=e.SubStatus)?void 0:r.length)&&e.SubStatus.forEach(e=>s.append("SubStatus",e)),(null==e?void 0:e.CreatorId)&&s.append("CreatorId",e.CreatorId),(null==e?void 0:e.CustomerId)&&s.append("CustomerId",e.CustomerId),(null==e?void 0:e.CreatedAt)&&s.append("CreatedAt",e.CreatedAt),(null==e?void 0:e.CreatedAtOp)&&s.append("CreatedAtOp",e.CreatedAtOp),(null==e?void 0:e.CompletedAt)&&s.append("CompletedAt",e.CompletedAt),(null==e?void 0:e.CompletedAtOp)&&s.append("CompletedAtOp",e.CompletedAtOp),(null==e?void 0:e.ScheduledTime)&&s.append("ScheduledTime",e.ScheduledTime),(null==e?void 0:e.ScheduledTimeOp)&&s.append("ScheduledTimeOp",e.ScheduledTimeOp),(null==e||null==(l=e.Services)?void 0:l.length)&&e.Services.forEach(e=>s.append("Services",e)),(null==e?void 0:e.AirFlight)&&s.append("AirFlight",e.AirFlight),(null==e?void 0:e.AirFlightOp)&&s.append("AirFlightOp",e.AirFlightOp),(null==e?void 0:e["FTS.Plain"])&&s.append("FTS.Plain",e["FTS.Plain"]),(null==e?void 0:e["FTS.Query"])&&s.append("FTS.Query",e["FTS.Query"]),(null==e?void 0:e.SortBy)&&s.append("SortBy",e.SortBy),(null==e?void 0:e.SortOrder)&&s.append("SortOrder",e.SortOrder);let i=s.toString()?"?".concat(s.toString()):"",n=await this.get(i);return this.handleApiResult(n)}async createInstantOrder(e){let t=await this.post("instant/by-operator",e);return this.handleApiResult(t)}async createInstantOrderByTerminal(e){let t=await this.post("instant/by-terminal",e);return this.handleApiResult(t)}async createScheduledOrder(e){let t=await this.post("scheduled",e);return this.handleApiResult(t)}async updateInstantOrder(e,t){let a=await this.put("instant/".concat(e),t);return this.handleApiResult(a)}async updateScheduledOrder(e,t){let a=await this.put("scheduled/".concat(e),t);return this.handleApiResult(a)}async deleteOrder(e){let t=await this.delete("/".concat(e));this.handleApiResult(t)}async cancelOrder(e,t){let a=await this.patch("/".concat(e,"/cancel"),t?{reason:t}:{});return this.handleApiResult(a)}async acceptOrder(e,t){let a=await this.patch("/".concat(e,"/accept"),{driverId:t});return this.handleApiResult(a)}async completeOrder(e){let t=await this.patch("/".concat(e,"/complete"));return this.handleApiResult(t)}async updatePassengers(e,t){let a=await this.put("".concat(e,"/passengers"),t);return this.handleApiResult(a)}async createScheduledRide(e,t){let a=await this.post("scheduled/".concat(e,"/ride"),t);return this.handleApiResult(a)}async getPotentialDrivers(e){let t=await this.get("".concat(e,"/PotentialDrivers"));return this.handleApiResult(t)}async getOrderStats(){let e=await this.get("stats");return this.handleApiResult(e)}constructor(...e){super(...e),this.baseUrl=l.QQ.ORDER.LIST}}let i=new s},78171:(e,t,a)=>{"use strict";a.d(t,{TerminalDataProvider:()=>o});var r=a(54568),l=a(7620),s=a(7401),i=a(9205),n=a(8426);let o=e=>{let{children:t}=e,[a,o]=(0,l.useState)(null),[c,d]=(0,l.useState)(!1),[u,m]=(0,l.useState)(null),[p,h]=(0,l.useState)(null),[f,g]=(0,l.useState)(!1),[b,v]=(0,l.useState)(null),x=(0,l.useCallback)(async()=>{d(!0),m(null);try{let e=await i.Dv.getTerminalSelfProfile();o(e)}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки профиля пользователя";console.error("❌ Ошибка загрузки профиля пользователя:",t),m(e)}finally{d(!1)}},[]),y=(0,l.useCallback)(async e=>{g(!0),v(null);try{let t=await s.Ny.getById(e);if(!t)throw Error("Локация терминала не найдена");h(t)}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки локации терминала";console.error("❌ Ошибка загрузки локации терминала:",t),v(e)}finally{g(!1)}},[]),N=(0,l.useCallback)(async()=>{await x()},[x]),j=(0,l.useCallback)(async()=>{(null==a?void 0:a.locationId)&&await y(a.locationId)},[null==a?void 0:a.locationId,y]),S=(0,l.useCallback)(async()=>{await x(),(null==a?void 0:a.locationId)&&await y(a.locationId)},[x,null==a?void 0:a.locationId,y]);(0,l.useEffect)(()=>{x()},[x]),(0,l.useEffect)(()=>{(null==a?void 0:a.locationId)&&!c&&y(a.locationId)},[null==a?void 0:a.locationId,c,y]);let w=c||f;return(0,r.jsx)(n.OW.Provider,{value:{profile:a,isProfileLoading:c,profileError:u,terminalLocation:p,isLocationLoading:f,locationError:b,isLoading:w,error:u||b,refetchProfile:N,refetchLocation:j,refetchAll:S},children:t})}},80980:(e,t,a)=>{"use strict";a.d(t,{Wj:()=>s,UF:()=>i.U});var r=a(62942),l=a(7620);let s=e=>{let[t,a]=(0,l.useState)(""),s=(0,r.usePathname)();return(0,l.useEffect)(()=>{let t=e.find(e=>s===e.path||s.startsWith("".concat(e.path,"/")));t&&a(t.id)},[s,e]),t};a(59959);var i=a(97414)},81257:(e,t,a)=>{"use strict";a.d(t,{D:()=>l,_:()=>s});var r=a(15493);let l={base:r.F,create:r.F,update:r.F},s=r.F},81745:(e,t,a)=>{"use strict";a.r(t),a.d(t,{useBreadcrumbs:()=>i});var r=a(62942),l=a(7620),s=a(27512);let i=()=>{let e=(0,r.usePathname)();return(0,l.useMemo)(()=>{if(!e||"/"===e)return[];let t=e.split("/").filter(Boolean),a=[],r="";return t.forEach((e,l)=>{r+="/".concat(e);let i=s.h.find(e=>e.path===r),n=l===t.length-1;i?a.push({label:i.title,path:i.path,isActive:n}):a.push({label:e,path:r,isActive:n})}),a},[e])}},83549:(e,t,a)=>{"use strict";a.d(t,{T:()=>l});var r=a(75170);r.G;let l=r.G},88154:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>l.useHeaderDropdowns,V$:()=>s.useDriverHeader,if:()=>r.useBreadcrumbs});var r=a(81745),l=a(92801),s=a(61889)},89751:(e,t,a)=>{"use strict";a.d(t,{e1:()=>i,bo:()=>s});var r=a(75006);let l=r.z.enum(["Home","Work","Airport","Station","Hotel","Restaurant","Shop","Entertainment","Medical","Educational","BusinessCenter","Other"]),s=r.z.object({type:l.describe("Тип локации"),name:r.z.string().min(1,{message:"Название локации обязательно"}).max(127,{message:"Название локации не должно превышать 127 символов"}).describe("Название места (например, Аэропорт Манас)"),address:r.z.string().min(1,{message:"Адрес обязателен"}).max(255,{message:"Адрес не должен превышать 255 символов"}).describe("Адрес"),district:r.z.string().max(63,{message:"Округ/район не должен превышать 63 символа"}).nullable().optional().describe("Округ/район"),city:r.z.string().min(1,{message:"Город обязателен"}).max(63,{message:"Город не должен превышать 63 символа"}).describe("Город/Населенный пункт"),country:r.z.string().min(1,{message:"Страна обязательна"}).max(63,{message:"Страна не должна превышать 63 символа"}).describe("Страна"),region:r.z.string().min(1,{message:"Область/регион о��язательна"}).max(63,{message:"Область/регион не должна превышать 63 символа"}).describe("Область/регион"),latitude:r.z.number().min(-90,{message:"Широта должна быть не менее -90"}).max(90,{message:"Широта должна быть не более 90"}).describe("Географическая широта"),longitude:r.z.number().min(-180,{message:"Долгота должна быть не менее -180"}).max(180,{message:"Долгота должна быть не более 180"}).describe("Географическая долгота"),isActive:r.z.boolean().describe("Активна ли локация"),popular1:r.z.boolean().optional().describe("Популярная локация 1"),popular2:r.z.boolean().optional().describe("Популярная локация 2")}),i=s.extend({id:r.z.string().uuid({message:"Некорректный формат UUID"}).describe("Идентификатор локации")});r.z.object({data:r.z.array(i).describe("Данные локаций"),totalCount:r.z.number().int().describe("Общее количество записей"),pageSize:r.z.number().int().describe("Размер страницы"),hasPrevious:r.z.boolean().describe("Есть ли предыдущая страница"),hasNext:r.z.boolean().describe("Есть ли следующая страница")}),r.z.object({fromId:r.z.string().uuid({message:"Некорректный формат UUID для fromId"}).describe("Идентификатор начальной локации"),toId:r.z.string().uuid({message:"Некорректный формат UUID для toId"}).describe("Идентификатор конечной локации"),tariffId:r.z.string().uuid({message:"Некорректный формат UUID для tariffId"}).describe("Идентификатор тарифа"),basePrice:r.z.number().min(0,{message:"Базовая цена должна быть не менее 0"}).describe("Базовая цена")})},90683:(e,t,a)=>{"use strict";a.d(t,{m:()=>r});var r=function(e){return e.Economy="Economy",e.Comfort="Comfort",e.ComfortPlus="ComfortPlus",e.Business="Business",e.Premium="Premium",e.Vip="Vip",e.Luxury="Luxury",e}({})},92801:(e,t,a)=>{"use strict";a.r(t),a.d(t,{useHeaderDropdowns:()=>l});var r=a(7620);let l=()=>{let[e,t]=(0,r.useState)(!1),[a,l]=(0,r.useState)(!1),s=(0,r.useRef)(null),i=(0,r.useRef)(null),n=(0,r.useRef)(null),o=(0,r.useRef)(null),c=(0,r.useRef)(null);return(0,r.useEffect)(()=>{let r=r=>{e&&i.current&&!i.current.contains(r.target)&&o.current&&!o.current.contains(r.target)&&t(!1),a&&n.current&&!n.current.contains(r.target)&&c.current&&!c.current.contains(r.target)&&l(!1)};return document.addEventListener("mousedown",r),()=>{document.removeEventListener("mousedown",r)}},[e,a]),{notificationsOpen:e,profileOpen:a,searchRef:s,notificationsRef:i,profileRef:n,notificationsButtonRef:o,profileButtonRef:c,toggleNotifications:()=>{t(!e),e||l(!1)},toggleProfile:()=>{l(!a),a||t(!1)}}}},94455:(e,t,a)=>{"use strict";a.d(t,{C:()=>l,Y:()=>s});var r=a(7620);let l=(0,r.createContext)(void 0),s=()=>{let e=(0,r.useContext)(l);if(void 0===e)throw Error("useTerminalTariff must be used within a TerminalTariffProvider");return e}},95559:(e,t,a)=>{"use strict";a.d(t,{mE:()=>l,cx:()=>s});var r=a(9039);function l(e){switch(e){case r.m.Economy:return"Эконом";case r.m.Comfort:return"Комфорт";case r.m.ComfortPlus:return"Комфорт+";case r.m.Business:return"Бизнес";case r.m.Premium:return"Премиум";case r.m.Vip:return"VIP";case r.m.Luxury:return"Люкс";default:return""}}function s(){return Object.values(r.m).map(e=>({value:e,label:l(e)}))}},97414:(e,t,a)=>{"use strict";a.d(t,{U:()=>i});var r=a(7620),l=a(59959),s=a(34744);function i(){let e=(0,l.k)(),[t,a]=(0,r.useState)(0),{data:i,isLoading:n,error:o,refetch:c}=(0,s.C)({size:50,status:["Requested","Searching","Accepted","Arrived","InProgress"]});(0,r.useEffect)(()=>{if(e.isConnected){let t=e=>{console.log("\uD83D\uDCC5 Новая запланированная поездка:",e),(null==e?void 0:e.orderType)==="Scheduled"&&(a(e=>e+1),c())};return e.on("RideRequestNotification",t),e.on("OrderStatusChanged",t),e.on("DriverNotification",t),()=>{e.off("RideRequestNotification",t),e.off("OrderStatusChanged",t),e.off("DriverNotification",t)}}},[e,c]);let d=(0,r.useCallback)(()=>{a(0)},[]),{hasUpcomingRideWithin1_5Hours:u,hoursUntilNextRide:m,hasOverdueRequestedRides:p,overdueRidesCount:h}=(0,r.useMemo)(()=>{if(!i||0===i.length)return{hasUpcomingRideWithin1_5Hours:!1,hoursUntilNextRide:null,hasOverdueRequestedRides:!1,overdueRidesCount:0};let e=new Date().getTime()+216e5,t=i.map(t=>({...t,timeUntilRide:new Date(t.scheduledTime).getTime()-e})),a=t.filter(e=>e.timeUntilRide>0&&("Requested"===e.status||"Accepted"===e.status)).sort((e,t)=>e.timeUntilRide-t.timeUntilRide),r=t.filter(e=>e.timeUntilRide<=0&&"Requested"===e.status),l=!1,s=null;return a.length>0&&(l=(s=a[0].timeUntilRide/36e5)<=1.5),{hasUpcomingRideWithin1_5Hours:l,hoursUntilNextRide:s,hasOverdueRequestedRides:r.length>0,overdueRidesCount:r.length}},[i]);return{newScheduledRidesCount:t,totalScheduledRidesCount:i.length,hasUpcomingRideWithin1_5Hours:u,hoursUntilNextRide:m,hasOverdueRequestedRides:p,overdueRidesCount:h,isLoading:n,error:o,resetNewRidesCount:d,refetch:c}}},98893:(e,t,a)=>{"use strict";a.d(t,{C:()=>r.C});var r=a(62571)}},e=>{var t=t=>e(e.s=t);e.O(0,[3257,5233,9031,7261,3766,1938,7914,2906,5490,3970,5907,6467,3720,2634,3822,7141,8128,1669,587,8315,7358],()=>t(12719)),_N_E=e.O()}]);