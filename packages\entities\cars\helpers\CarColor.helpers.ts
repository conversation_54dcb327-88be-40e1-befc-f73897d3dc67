import { CarColor } from '@entities/cars/enums';

/**
 * Проверяет, является ли значение допустимым значением CarColor
 * @param {unknown} value Значение для проверки
 * @returns {boolean} true, если значение допустимо
 */
export function isCarColor(value: unknown): value is CarColor {
  return Object.values(CarColor).includes(value as CarColor);
}
/**
 * Получает метку для значения CarColor
 * @param {CarColor} value Значение CarColor
 * @returns {string} Метка
 */
export function getCarColorLabel(value: CarColor): string {
  switch (value) {
    case CarColor.Black:
      return 'Черный';
    case CarColor.White:
      return 'Белый';
    case CarColor.Silver:
      return 'Серебристый';
    case CarColor.Gray:
      return 'Серый';
    case CarColor.Red:
      return 'Красный';
    case CarColor.Blue:
      return 'Синий';
    case CarColor.Green:
      return 'Зеленый';
    case CarColor.Yellow:
      return 'Желтый';
    case CarColor.Brown:
      return 'Коричневый';
    case CarColor.Orange:
      return 'Оранжевый';
    case CarColor.Purple:
      return 'Фиолетовый';
    case CarColor.Gold:
      return 'Золотой';
    case CarColor.Other:
      return 'Другой';
    default:
      return '';
  }
}
/**
 * Получает массив всех значений CarColor
 * @returns {CarColor[]} Массив всех значений
 */
export function getCarColorValues(): CarColor[] {
  return Object.values(CarColor);
}
/**
 * Получает массив всех меток CarColor
 * @returns {string[]} Массив всех меток
 */
export function getCarColorLabels(): string[] {
  return getCarColorValues().map(value => getCarColorLabel(value));
}
/**
 * Получает массив объектов { value, label } для CarColor
 * @returns {{ value: CarColor, label: string }[]} Массив объектов
 */
export function getCarColorOptions(): { value: CarColor, label: string }[] {
  return getCarColorValues().map(value => ({
    value,
    label: getCarColorLabel(value)
  }));
}