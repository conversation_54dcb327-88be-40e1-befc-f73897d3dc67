exports.id=4309,exports.ids=[4309],exports.modules={4004:(e,t,r)=>{"use strict";r.d(t,{$z:()=>c,Or:()=>o,Z6:()=>d,aZ:()=>n,eX:()=>i,pL:()=>a,uW:()=>u,wp:()=>l});var s=r(96274);r(38013);let n=async e=>(await s.uE.post(`/Order/instant/${e}/accept-by-driver`)).data,i=async e=>{try{console.log("\uD83D\uDCDD Подтверждаем запланированную поездку водителем:",e);let t=await s.uE.post(`/Ride/${e}/accept-by-driver`);return console.log("✅ Поездка подтверждена водителем:",t.data),t.data}catch(e){throw console.error("❌ Ошибка подтверждения поездки водителем:",e),e}},o=async e=>{try{console.log("\uD83D\uDCE2 Уведомляем о принятии запланированной поездки в сведении:",e);let t=await s.uE.post(`/Ride/${e}/notification-by-driver`);console.log("✅ Уведомление отправлено:",t.data)}catch(e){throw console.error("❌ Ошибка отправки уведомления:",e),e}},a=async e=>(await s.uE.post(`/Ride/${e}/reject-by-driver`)).data,d=async e=>(await s.uE.post(`/Ride/${e}/status/driver-heading-to-client`)).data,l=async e=>(await s.uE.post(`/Ride/${e}/status/driver-arrived`)).data,u=async e=>(await s.uE.post(`/Ride/${e}/status/ride-started`)).data,c=async e=>(await s.uE.post(`/Ride/${e}/status/ride-finished`)).data},4309:(e,t,r)=>{"use strict";r.r(t),r.d(t,{RideRequestModal:()=>f});var s=r(13486),n=r(15917),i=r.n(n),o=r(2984),a=r(60159),d=r(63497),l=r(26134),u=r(24903),c=r(4004),h=r(95816);let f=({requestData:e,isOpen:t,onClose:r,onAccept:n,onReject:f})=>{let p=(0,o.useRouter)(),[m,b]=(0,a.useState)(30),[x,y]=(0,a.useState)(!1),{actions:v}=(0,l.o)(),{refreshRides:_}=(0,u.g0)(),g=(0,a.useRef)(null);(0,a.useEffect)(()=>(t&&e?(g.current||(g.current=new Audio("/sounds/notification.wav"),g.current.loop=!0,g.current.volume=.7,g.current.onerror=()=>{console.warn("Не удалось загрузить аудиофайл уведомления"),w()}),(async()=>{try{g.current&&(g.current.currentTime=0,await g.current.play())}catch(e){console.warn("Не удалось воспроизвести аудио:",e),w()}})()):g.current&&(g.current.pause(),g.current.currentTime=0),()=>{g.current&&(g.current.pause(),g.current.currentTime=0)}),[t,e]);let w=(0,a.useCallback)(()=>{try{let e=new(window.AudioContext||window.webkitAudioContext),t=(t,r,s)=>{setTimeout(()=>{let s=e.createOscillator(),n=e.createGain();s.connect(n),n.connect(e.destination),s.frequency.setValueAtTime(t,e.currentTime),s.type="sine",n.gain.setValueAtTime(.3,e.currentTime),n.gain.exponentialRampToValueAtTime(.01,e.currentTime+r/1e3),s.start(e.currentTime),s.stop(e.currentTime+r/1e3)},s)};t(800,200,0),t(1e3,200,300),t(1200,300,600)}catch(e){console.warn("Не удалось создать fallback звук:",e)}},[]),S=(0,a.useCallback)(async()=>{if(!x){g.current&&(g.current.pause(),g.current.currentTime=0);try{await v.leaveQueue(),d.P.info("Заказ отклонен. Вы исключены из очереди")}catch(e){console.error("Ошибка при исключении из очереди:",e)}f(),r()}},[x,v,f,r]);(0,a.useEffect)(()=>{if(!t||!e)return void b(30);let r=setInterval(()=>{b(e=>e<=1?(S(),0):e-1)},1e3);return()=>clearInterval(r)},[t,e,S]);let j=(0,a.useCallback)(async()=>{if(e&&!x){g.current&&(g.current.pause(),g.current.currentTime=0),y(!0);try{if("Instant"===e.orderType){let t=await (0,c.aZ)(e.orderId),s=e.rideId||t.id||e.orderId;(0,h.Hq)(e.orderId,s);try{await (0,c.Z6)(s)}catch(e){console.error('❌ Ошибка установки статуса "Водитель направляется к клиенту":',e)}d.P.success("Заказ принят, направляюсь к клиенту"),await v.leaveQueue(),await _();let i=`&orderType=${e.orderType}`,o=e.rideId?`/active-ride?orderId=${e.orderId}&rideId=${e.rideId}${i}`:`/active-ride?orderId=${e.orderId}${i}`;console.log("\uD83D\uDE97 Переходим на активную поездку:",o),n(),r(),p.push(o)}else d.P.success("Вы уведомлены о назначенной поездке"),n(),r()}catch(e){console.error("Ошибка принятия заказа:",e),d.P.error("Ошибка принятия заказа")}finally{y(!1)}}},[e,x,v,_,n,r,p]);return t&&e?(0,s.jsxs)("div",{className:"jsx-908492bef7274fd9 absolute inset-0 z-50 w-full h-full flex justify-between items-center backdrop-filter backdrop-blur-md bg-black/80",children:[(0,s.jsxs)("div",{className:"jsx-908492bef7274fd9 rounded-xl shadow-lg p-6 w-full max-w-lg mx-auto backdrop-blur-md text-black bg-white/95 animate-pulse-border",children:[(0,s.jsxs)("div",{className:"jsx-908492bef7274fd9 flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"jsx-908492bef7274fd9 text-xl font-semibold flex items-center",children:"\uD83D\uDD14 Новый заказ"}),(0,s.jsx)("button",{onClick:S,className:"jsx-908492bef7274fd9 text-gray-500 hover:text-gray-700 text-2xl leading-none",children:"\xd7"})]}),(0,s.jsxs)("div",{className:"jsx-908492bef7274fd9 space-y-6",children:[(0,s.jsxs)("div",{className:"jsx-908492bef7274fd9 text-center",children:[(0,s.jsx)("div",{className:`jsx-908492bef7274fd9 text-6xl font-bold mb-2 transition-colors duration-300 ${m<=10?"text-red-500 animate-pulse":"text-blue-500"}`,children:m}),(0,s.jsx)("p",{className:"jsx-908492bef7274fd9 text-gray-600",children:"секунд до автоматического отклонения"})]}),(0,s.jsx)("div",{className:"jsx-908492bef7274fd9 bg-gray-50 rounded-lg p-4 space-y-3",children:(0,s.jsxs)("div",{className:"jsx-908492bef7274fd9 flex items-center justify-between",children:[(0,s.jsx)("span",{className:"jsx-908492bef7274fd9 font-medium",children:"Тип заказа:"}),(0,s.jsx)("span",{className:`jsx-908492bef7274fd9 px-2 py-1 rounded text-sm font-medium ${"Instant"===e.orderType?"bg-orange-100 text-orange-800":"bg-blue-100 text-blue-800"}`,children:"Instant"===e.orderType?"Мгновенный":"Запланированный"})]})}),(0,s.jsx)("div",{className:"jsx-908492bef7274fd9 flex space-x-4",children:"Scheduled"===e.orderType?(0,s.jsx)("button",{onClick:j,disabled:x,className:"jsx-908492bef7274fd9 w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?"Подтверждаю...":"Уведомлен"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:S,disabled:x,className:"jsx-908492bef7274fd9 flex-1 bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Отклонить"}),(0,s.jsx)("button",{onClick:j,disabled:x,className:"jsx-908492bef7274fd9 flex-1 bg-green-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?"Принимаю...":"Принять заказ"})]})})]})]}),(0,s.jsx)(i(),{id:"908492bef7274fd9",children:"@-webkit-keyframes pulse-border{0%,100%{-webkit-box-shadow:0 0 20px rgba(59,130,246,.5);box-shadow:0 0 20px rgba(59,130,246,.5)}50%{-webkit-box-shadow:0 0 40px rgba(59,130,246,.8);box-shadow:0 0 40px rgba(59,130,246,.8)}}@-moz-keyframes pulse-border{0%,100%{-moz-box-shadow:0 0 20px rgba(59,130,246,.5);box-shadow:0 0 20px rgba(59,130,246,.5)}50%{-moz-box-shadow:0 0 40px rgba(59,130,246,.8);box-shadow:0 0 40px rgba(59,130,246,.8)}}@-o-keyframes pulse-border{0%,100%{box-shadow:0 0 20px rgba(59,130,246,.5)}50%{box-shadow:0 0 40px rgba(59,130,246,.8)}}@keyframes pulse-border{0%,100%{-webkit-box-shadow:0 0 20px rgba(59,130,246,.5);-moz-box-shadow:0 0 20px rgba(59,130,246,.5);box-shadow:0 0 20px rgba(59,130,246,.5)}50%{-webkit-box-shadow:0 0 40px rgba(59,130,246,.8);-moz-box-shadow:0 0 40px rgba(59,130,246,.8);box-shadow:0 0 40px rgba(59,130,246,.8)}}.animate-pulse-border.jsx-908492bef7274fd9{-webkit-animation:pulse-border 2s ease-in-out infinite;-moz-animation:pulse-border 2s ease-in-out infinite;-o-animation:pulse-border 2s ease-in-out infinite;animation:pulse-border 2s ease-in-out infinite}"})]}):null}},15917:(e,t,r)=>{"use strict";e.exports=r(90058).style},90058:(e,t,r)=>{"use strict";r(98740);var s=r(60159),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),i="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,n=t.optimizeForSpeed,a=void 0===n?i:n;d(o(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",d("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){d("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),d(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;d(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return d(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&d(o(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return r?n.insertBefore(s,r):n.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function d(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var l=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},u={};function c(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return u[s]||(u[s]="jsx-"+l(e+"-"+r)),u[s]}function h(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return u[r]||(u[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[r]}var f=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,n=t.optimizeForSpeed,i=void 0!==n&&n;this._sheet=s||new a({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),s&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,n=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var i=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=i,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var n=c(s,r);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return h(n,e)}):[h(n,t)]}}return{styleId:c(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=s.createContext(null);p.displayName="StyleSheetContext";n.default.useInsertionEffect||n.default.useLayoutEffect;var m=void 0;function b(e){var t=m||s.useContext(p);return t&&t.add(e),null}b.dynamic=function(e){return e.map(function(e){return c(e[0],e[1])}).join(" ")},t.style=b},98740:()=>{}};