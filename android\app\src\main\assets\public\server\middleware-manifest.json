{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "J6IJl5_dZAx_NjCg8xS2U", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6LvBbNpCrIRhR4SzIctdnbJLzaG7sqbzy66mIAeAkW4=", "__NEXT_PREVIEW_MODE_ID": "7bc17a62f5830661b3ceaf262880027a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "16d1ea87d9c696e9126d878a59d3877f869f32ea802a1a7ee7a30e510ab0fa3f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e17bda3d41b3ab9e7d2c8e762b2b2ff477c964e0e460b0fc8d9c937378277d7c"}}}, "functions": {}, "sortedMiddleware": ["/"]}