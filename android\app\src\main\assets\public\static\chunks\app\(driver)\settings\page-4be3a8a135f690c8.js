(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9474],{26899:(e,l,r)=>{Promise.resolve().then(r.bind(r,75336))},32959:(e,l,r)=>{"use strict";r.d(l,{N:()=>a,o:()=>o});var t=r(7620);let a=(0,t.createContext)(null),o=()=>{let e=(0,t.useContext)(a);if(!e)throw Error("useDriverQueueContext must be used within a DriverQueueProvider");return e}},75336:(e,l,r)=>{"use strict";r.r(l),r.d(l,{SettingsPage:()=>b});var t=r(54568),a=r(7620),o=r(79394),s=r(32959),i=r(43356);r(61773),r(27261),r(55115),r(90739),r(62942);var n=r(75006);r(27736),r(8703),r(24723);let d=n.z.object({fullName:n.z.string().min(1,"Полное имя обязательно"),phoneNumber:n.z.string().optional(),email:n.z.string().email("Некорректный email")});d.extend({profile:n.z.object({companyName:n.z.string().min(1,"Название компании обязательно"),companyType:n.z.string().min(1,"Тип компании обязателен"),registrationNumber:n.z.string().optional(),taxIdentifier:n.z.string().optional(),legalAddress:n.z.string().min(1,"Юридический адрес обязателен"),contactEmail:n.z.string().email("Некорректный email").optional(),contactPhone:n.z.string().optional(),website:n.z.string().url("Некорректный URL").optional().or(n.z.literal(""))}).optional()}),d.extend({profile:n.z.object({licenseNumber:n.z.string().min(1,"Номер лицензии обязателен"),licenseCategories:n.z.array(n.z.string()).optional(),licenseIssueDate:n.z.string().optional(),licenseExpiryDate:n.z.string().optional(),dateOfBirth:n.z.string().optional(),birthPlace:n.z.string().optional(),citizenship:n.z.string().optional(),citizenshipCountry:n.z.string().optional(),drivingExperience:n.z.number().optional(),taxIdentifier:n.z.string().optional(),languages:n.z.array(n.z.string()).optional()}).optional()}),r(53635);let c=e=>{let{currentScale:l=.8,onPreviewChange:r}=e,[o,s]=(0,a.useState)(l);(0,a.useEffect)(()=>{var e;let l=null==(e=document.cookie.split("; ").find(e=>e.startsWith("driver-ui-scale=")))?void 0:e.split("=")[1];l&&s(parseFloat(l))},[]);let i=e=>{s(e),null==r||r(e)};return(0,t.jsxs)("div",{className:"bg-white border rounded-lg p-6 shadow-sm",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Масштаб интерфейса"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Выберите масштаб:"}),(0,t.jsx)("div",{className:"grid grid-cols-4 gap-3",children:[{value:.7,label:"70%"},{value:.8,label:"80%"},{value:.9,label:"90%"},{value:1,label:"100%"}].map(e=>(0,t.jsx)("button",{onClick:()=>i(e.value),className:"\n                  px-4 py-3 text-sm font-medium rounded-lg border-2 transition-all cursor-pointer\n                  ".concat(o===e.value?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400","\n                "),children:e.label},e.value))})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded-lg",children:[(0,t.jsx)("p",{className:"mb-1",children:"\uD83D\uDCA1 Масштаб влияет на размер всех элементов интерфейса внутри планшета."}),(0,t.jsx)("p",{children:'Изменения будут применены после нажатия кнопки "Применить все" в превью.'})]})]})]})},u=e=>{let{currentType:l="color",currentValue:r="#f9fafb",onPreviewChange:o}=e,[s,i]=(0,a.useState)(l),[n,d]=(0,a.useState)(r);(0,a.useEffect)(()=>{var e,l;let r=null==(e=document.cookie.split("; ").find(e=>e.startsWith("driver-background-type=")))?void 0:e.split("=")[1],t=null==(l=document.cookie.split("; ").find(e=>e.startsWith("driver-background-value=")))?void 0:l.split("=")[1];r&&["color","image"].includes(r)&&i(r),t&&d(decodeURIComponent(t))},[]);let c=(e,l)=>{i(e),d(l),null==o||o({backgroundType:e,backgroundValue:l})};return(0,t.jsxs)("div",{className:"bg-white border rounded-lg p-6 shadow-sm space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Настройки фона"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Настройте фон за планшетом"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Тип фона:"}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)("button",{onClick:()=>{i("color"),null==o||o({backgroundType:"color"})},className:"px-6 py-3 rounded-lg border-2 transition-all font-medium ".concat("color"===s?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"),children:"Цвет"}),(0,t.jsx)("button",{onClick:()=>{i("image"),null==o||o({backgroundType:"image"})},className:"px-6 py-3 rounded-lg border-2 transition-all font-medium ".concat("image"===s?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"),children:"Изображение"})]})]}),"color"===s&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Выберите цвет фона:"}),(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg border",children:(0,t.jsx)("div",{className:"grid grid-cols-4 gap-3",children:[{value:"#ffffff",label:"Белый",color:"#ffffff"},{value:"#f9fafb",label:"Очень светлый серый",color:"#f9fafb"},{value:"#f3f4f6",label:"Светлый серый",color:"#f3f4f6"},{value:"#e5e7eb",label:"Серый",color:"#e5e7eb"},{value:"#eff6ff",label:"Светло-синий",color:"#eff6ff"},{value:"#dbeafe",label:"Синий 100",color:"#dbeafe"},{value:"#bfdbfe",label:"Синий 200",color:"#bfdbfe"},{value:"#93c5fd",label:"Синий 300",color:"#93c5fd"},{value:"#f0fdf4",label:"Светло-зеленый",color:"#f0fdf4"},{value:"#dcfce7",label:"Зеленый 100",color:"#dcfce7"},{value:"#bbf7d0",label:"Зеленый 200",color:"#bbf7d0"},{value:"#86efac",label:"Зеленый 300",color:"#86efac"},{value:"#fefce8",label:"Светло-желтый",color:"#fefce8"},{value:"#fef08a",label:"Желтый 100",color:"#fef08a"},{value:"#fde047",label:"Желтый 200",color:"#fde047"},{value:"#facc15",label:"Желтый 300",color:"#facc15"},{value:"#f5f3ff",label:"Светло-фиолетовый",color:"#f5f3ff"},{value:"#ede9fe",label:"Фиолетовый 100",color:"#ede9fe"},{value:"#ddd6fe",label:"Фиолетовый 200",color:"#ddd6fe"},{value:"#c4b5fd",label:"Фиолетовый 300",color:"#c4b5fd"}].map((e,l)=>(0,t.jsx)("button",{onClick:()=>c("color",e.value),className:"\n                    w-12 h-12 rounded-lg border-2 transition-all hover:scale-105 cursor-pointer\n                    ".concat(n===e.value&&"color"===s?"border-blue-500 ring-2 ring-blue-500/50 scale-105":"border-gray-300 hover:border-gray-400","\n                  "),style:{backgroundColor:e.color},title:e.label},"driver-bg-color-".concat(l,"-").concat(e.value)))})})]}),"image"===s&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Выберите изображение фона:"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-4",children:[{value:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop",label:"Горы"},{value:"https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1920&h=1080&fit=crop",label:"Лес"},{value:"https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=1920&h=1080&fit=crop",label:"Озеро"},{value:"https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=1920&h=1080&fit=crop",label:"Небо"}].map((e,l)=>(0,t.jsxs)("button",{onClick:()=>c("image",e.value),className:"\n                  relative p-3 rounded-lg border-2 transition-all text-left hover:scale-105 cursor-pointer\n                  ".concat(n===e.value&&"image"===s?"border-blue-500 ring-2 ring-blue-500/50 scale-105":"border-gray-300 hover:border-gray-400","\n                "),children:[(0,t.jsx)("div",{className:"w-full h-20 rounded-md mb-2 bg-cover bg-center bg-gray-200",style:{backgroundImage:"url(".concat(e.value,")")}}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label})]},"driver-bg-image-".concat(l,"-").concat(e.label)))})]})]})},m=e=>{let{currentOpacity:l=.95,currentBlur:r=4,currentOverlayColor:o="#ffffff",currentComponentOpacity:s=.9,currentComponentBlur:i=8,onPreviewChange:n}=e,[d,c]=(0,a.useState)(l),[u,m]=(0,a.useState)(r),[b,p]=(0,a.useState)(o),[g,h]=(0,a.useState)(s),[f,x]=(0,a.useState)(i);(0,a.useEffect)(()=>{var e,l,r,t,a;let o=null==(e=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-opacity=")))?void 0:e.split("=")[1],s=null==(l=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-blur=")))?void 0:l.split("=")[1],i=null==(r=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-color=")))?void 0:r.split("=")[1];if(o){let e=parseFloat(o);!isNaN(e)&&e>=0&&e<=1&&c(e)}if(s){let e=parseFloat(s);!isNaN(e)&&e>=0&&e<=20&&m(e)}i&&p(decodeURIComponent(i));let n=null==(t=document.cookie.split("; ").find(e=>e.startsWith("driver-component-opacity=")))?void 0:t.split("=")[1],d=null==(a=document.cookie.split("; ").find(e=>e.startsWith("driver-component-blur=")))?void 0:a.split("=")[1];if(n){let e=parseFloat(n);!isNaN(e)&&e>=0&&e<=1&&h(e)}if(d){let e=parseFloat(d);!isNaN(e)&&e>=0&&e<=20&&x(e)}},[]);let v=e=>{c(e),null==n||n({overlayOpacity:e})},y=e=>{m(e),null==n||n({overlayBlur:e})},j=e=>{p(e),null==n||n({overlayColor:e})},N=e=>{h(e),null==n||n({componentOpacity:e})},w=e=>{x(e),null==n||n({componentBlur:e})};return(0,t.jsxs)("div",{className:"bg-white border rounded-lg p-6 shadow-sm space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Настройки прозрачности и размытия"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Настройте эффекты для планшета и отдельных компонентов"})]}),(0,t.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"\uD83D\uDDA5️ Планшетный контейнер"}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Цвет планшета"}),(0,t.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,t.jsx)("input",{type:"color",value:b,onChange:e=>j(e.target.value),className:"w-16 h-10 border border-gray-300 rounded-lg cursor-pointer"}),(0,t.jsx)("input",{type:"text",value:b,onChange:e=>j(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg font-mono text-sm",placeholder:"#ffffff"})]}),(0,t.jsx)("div",{className:"grid grid-cols-10 gap-2",children:["#ffffff","#f8f9fa","#e9ecef","#dee2e6","#000000","#1e3a8a","#7c3aed","#dc2626","#059669","#d97706"].map(e=>(0,t.jsx)("button",{onClick:()=>j(e),className:"w-8 h-8 rounded-lg border-2 transition-all cursor-pointer ".concat(b.toLowerCase()===e.toLowerCase()?"border-blue-500 scale-110 shadow-md":"border-gray-300 hover:border-gray-400"),style:{backgroundColor:e},title:e},e))})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["Прозрачность планшета: ",Math.round(100*d),"%"]}),(0,t.jsx)("div",{className:"flex gap-2 flex-wrap",children:[.1,.3,.5,.7,.85,.95,1].map(e=>(0,t.jsxs)("button",{onClick:()=>v(e),className:"px-4 py-3 text-sm rounded-lg border transition-all font-medium cursor-pointer ".concat(.01>Math.abs(d-e)?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"),children:[Math.round(100*e),"%"]},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["Размытие фона планшета: ",u,"px"]}),(0,t.jsx)("div",{className:"flex gap-2 flex-wrap",children:[0,2,4,8,12,16,20].map(e=>(0,t.jsxs)("button",{onClick:()=>y(e),className:"px-4 py-3 text-sm rounded-lg border transition-all font-medium cursor-pointer ".concat(u===e?"bg-blue-600 border-blue-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"),children:[e,"px"]},e))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"\uD83E\uDDE9 Компоненты (Header, Content, Sidebar)"}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["Прозрачность компонентов: ",Math.round(100*g),"%"]}),(0,t.jsx)("div",{className:"flex gap-2 flex-wrap",children:[.1,.3,.5,.7,.85,.95,1].map(e=>(0,t.jsxs)("button",{onClick:()=>N(e),className:"px-4 py-3 text-sm rounded-lg border transition-all font-medium cursor-pointer ".concat(.01>Math.abs(g-e)?"bg-green-600 border-green-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"),children:[Math.round(100*e),"%"]},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["Размытие фона компонентов: ",f,"px"]}),(0,t.jsx)("div",{className:"flex gap-2 flex-wrap",children:[0,4,8,12,16,20].map(e=>(0,t.jsxs)("button",{onClick:()=>w(e),className:"px-4 py-3 text-sm rounded-lg border transition-all font-medium cursor-pointer ".concat(f===e?"bg-green-600 border-green-600 text-white shadow-md":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"),children:[e,"px"]},e))})]})]}),(0,t.jsx)("div",{className:"text-sm text-gray-600 bg-gray-50 p-4 rounded-lg",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83C\uDFA8 Цвет планшета:"})," Основной цвет планшетного контейнера"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDDA5️ Планшет:"})," Контролирует прозрачность и размытие всего планшетного контейнера"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83E\uDDE9 Компоненты:"})," Отдельные настройки для Header, Content и Sidebar"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCA1 Совет:"})," Комбинируйте настройки для создания уникального стиля интерфейса"]})]})})]})},b=()=>{let{logout:e,isLoggingOut:l}=(0,i.Wn)(),{isInQueue:r,actions:n}=(0,s.o)(),[d,b]=(0,a.useState)(.8),[p,g]=(0,a.useState)("color"),[h,f]=(0,a.useState)("#f9fafb"),[x,v]=(0,a.useState)(.95),[y,j]=(0,a.useState)(4),[N,w]=(0,a.useState)("#ffffff"),[k,C]=(0,a.useState)(.9),[z,S]=(0,a.useState)(8),[D,O]=(0,a.useState)(!1),[W,E]=(0,a.useState)(!1);(0,a.useEffect)(()=>{var e,l,r,t,a,o,s,i;let n=null==(e=document.cookie.split("; ").find(e=>e.startsWith("driver-ui-scale=")))?void 0:e.split("=")[1];n&&b(parseFloat(n));let d=null==(l=document.cookie.split("; ").find(e=>e.startsWith("driver-background-type=")))?void 0:l.split("=")[1];d&&["color","image"].includes(d)&&g(d);let c=null==(r=document.cookie.split("; ").find(e=>e.startsWith("driver-background-value=")))?void 0:r.split("=")[1];c&&f(decodeURIComponent(c));let u=null==(t=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-opacity=")))?void 0:t.split("=")[1];if(u){let e=parseFloat(u);!isNaN(e)&&e>=0&&e<=1&&v(e)}let m=null==(a=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-blur=")))?void 0:a.split("=")[1];if(m){let e=parseFloat(m);!isNaN(e)&&e>=0&&e<=20&&j(e)}let p=null==(o=document.cookie.split("; ").find(e=>e.startsWith("driver-overlay-color=")))?void 0:o.split("=")[1];p&&w(decodeURIComponent(p));let h=null==(s=document.cookie.split("; ").find(e=>e.startsWith("driver-component-opacity=")))?void 0:s.split("=")[1];if(h){let e=parseFloat(h);!isNaN(e)&&e>=0&&e<=1&&C(e)}let x=null==(i=document.cookie.split("; ").find(e=>e.startsWith("driver-component-blur=")))?void 0:i.split("=")[1];if(x){let e=parseFloat(x);!isNaN(e)&&e>=0&&e<=20&&S(e)}},[]);let F=e=>{void 0!==e.scale&&b(e.scale),void 0!==e.backgroundType&&g(e.backgroundType),void 0!==e.backgroundValue&&f(e.backgroundValue),void 0!==e.overlayOpacity&&v(e.overlayOpacity),void 0!==e.overlayBlur&&j(e.overlayBlur),void 0!==e.overlayColor&&w(e.overlayColor),void 0!==e.componentOpacity&&C(e.componentOpacity),void 0!==e.componentBlur&&S(e.componentBlur),O(!0)},T=async()=>{E(!0);try{let e=await fetch("/api/scale",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({scale:d})}),l=await fetch("/api/background",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:p,value:h})}),r=await fetch("/api/overlay",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({opacity:x,blur:y,overlayColor:N,componentOpacity:k,componentBlur:z})});e.ok&&l.ok&&r.ok?(O(!1),setTimeout(()=>{window.location.reload()},1e3)):console.error("Ошибка при сохранении настроек")}catch(e){console.error("Ошибка при сохранении настроек:",e)}finally{E(!1)}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.wh,{isVisible:l,message:"Выход из системы..."}),(0,t.jsxs)("div",{className:"h-full flex flex-col p-4 overflow-y-auto",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Настройки"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Настройки приложения и профиля"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,t.jsx)(c,{currentScale:d,onPreviewChange:e=>F({scale:e})}),(0,t.jsx)(u,{currentType:p,currentValue:h,onPreviewChange:e=>F(e)}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsx)(m,{currentOpacity:x,currentBlur:y,currentOverlayColor:N,currentComponentOpacity:k,currentComponentBlur:z,onPreviewChange:e=>F(e)})})]}),D&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-900",children:"Есть несохраненные изменения"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Нажмите “Применить все”, чтобы сохранить настройки"})]}),(0,t.jsx)("button",{onClick:T,disabled:W,className:"px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium rounded-lg transition-colors duration-200",children:W?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block"}),"Применяем..."]}):"Применить все"})]})}),(0,t.jsxs)("div",{className:"bg-white border rounded-lg p-6 shadow-sm",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Безопасность"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("button",{onClick:()=>{e({leaveQueue:n.leaveQueue,isInQueue:r})},disabled:l,className:"w-full flex items-center justify-center px-4 py-3 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-medium rounded-lg transition-colors duration-200",children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),r?"Выходим из очереди и системы...":"Выходим из системы..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"Выйти из системы"]})}),r&&(0,t.jsx)("p",{className:"text-sm text-gray-600 text-center",children:"При выходе вы автоматически покинете очередь"})]})]})]})]})}}},e=>{var l=l=>e(e.s=l);e.O(0,[3257,5233,9031,7261,3766,1938,7914,2906,5490,3970,6467,3720,2634,7141,2267,587,8315,7358],()=>l(26899)),_N_E=e.O()}]);