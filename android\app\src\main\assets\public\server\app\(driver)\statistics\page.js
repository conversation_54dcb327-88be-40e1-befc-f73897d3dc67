(()=>{var e={};e.id=3532,e.ids=[3532],e.modules={1917:(e,s,r)=>{Promise.resolve().then(r.bind(r,30613))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8864:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a,metadata:()=>o});var t=r(38828),i=r(76383);async function a(){return(0,t.jsx)(i.StatisticsPage,{})}let o={title:"",description:""}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15421:(e,s,r)=>{Promise.resolve().then(r.bind(r,76383))},17704:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>u,tree:()=>c});var t=r(24332),i=r(48819),a=r(67851),o=r.n(a),n=r(97540),p={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);r.d(s,p);let c={children:["",{children:["(driver)",{children:["statistics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8864)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\statistics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94058)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\statistics\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(driver)/statistics/page",pathname:"/statistics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30613:(e,s,r)=>{"use strict";r.r(s),r.d(s,{StatisticsPage:()=>i});var t=r(13486);r(60159);let i=()=>(0,t.jsxs)("div",{className:"h-full flex flex-col p-4",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Статистика"}),(0,t.jsx)("p",{children:"Ваша статистика и аналитика"})]}),(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,t.jsx)("h3",{className:"text-lg font-medium  mb-2",children:"Статистика"}),(0,t.jsx)("p",{children:"Раздел в разработке"})]})]})},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76383:(e,s,r)=>{"use strict";r.r(s),r.d(s,{StatisticsPage:()=>t});let t=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call StatisticsPage() from the server but StatisticsPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\pages\\driver\\statistics\\StatisticsPage.tsx","StatisticsPage")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[191,5208,9455,6596,4680,99,3639,2960,9100,1738],()=>r(17704));module.exports=t})();