(()=>{var e={};e.id=1824,e.ids=[1824],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53603:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>u});var a=t(48106),o=t(48819),n=t(12050),i=t(65208),p=t(4235);async function u(e){try{let{scale:r}=await e.json();if("number"!=typeof r||r<.5||r>1.2)return p.NextResponse.json({error:"Неверное значение масштаба"},{status:400});return(await (0,i.UL)()).set("driver-ui-scale",r.toString(),{maxAge:31536e3,httpOnly:!1,secure:!0,sameSite:"lax"}),p.NextResponse.json({success:!0})}catch(e){return console.error("Ошибка при сохранении масштаба:",e),p.NextResponse.json({error:"Внутренняя ошибка сервера"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/scale/route",pathname:"/api/scale",filename:"route",bundlePath:"app/api/scale/route"},resolvedPagePath:"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\api\\scale\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80408:()=>{},87032:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,5208,3744],()=>t(53603));module.exports=s})();