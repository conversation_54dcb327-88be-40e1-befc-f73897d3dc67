(()=>{var e={};e.id=8030,e.ids=[8030],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10227:(e,t,r)=>{Promise.resolve().then(r.bind(r,15774))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15774:(e,t,r)=>{"use strict";r.d(t,{ActiveRidePage:()=>n});let n=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call ActiveRidePage() from the server but ActiveRidePage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\pages\\driver\\active-ride\\ActiveRidePage.tsx","ActiveRidePage")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47179:(e,t,r)=>{Promise.resolve().then(r.bind(r,93100))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58324:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>h,tree:()=>d});var n=r(24332),i=r(48819),s=r(67851),a=r.n(s),l=r(97540),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["(driver)",{children:["active-ride",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,93809)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\active-ride\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94058)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\active-ride\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(driver)/active-ride/page",pathname:"/active-ride",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93100:(e,t,r)=>{"use strict";r.d(t,{ActiveRidePage:()=>U});var n=r(13486),i=r(60159),s=r.n(i),a=r(2984),l=r(96274),o=r(63497),d=r(29457);r(32005),r(26134);let u=(e,t,r)=>{let[n,s]=(0,i.useState)(null),[a,o]=(0,i.useState)(null),[d,u]=(0,i.useState)([]),[c,h]=(0,i.useState)(!1),[p,x]=(0,i.useState)(null);return(0,i.useEffect)(()=>{let n=async()=>{if(e||t){h(!0),x(null);try{let n=[],i=[];if(e&&(n.push(l.uE.get(l.QQ.LOCATION.GET_BY_ID.replace("{uuid}",e)).then(e=>e.data)),i.push("start")),t&&(n.push(l.uE.get(l.QQ.LOCATION.GET_BY_ID.replace("{uuid}",t)).then(e=>e.data)),i.push("end")),r&&r.length>0){r.forEach(()=>{i.push("additional")});let e=r.map(e=>l.uE.get(l.QQ.LOCATION.GET_BY_ID.replace("{uuid}",e)).then(e=>e.data));n.push(...e)}console.log("\uD83D\uDDFA️ Загружаем локации:",{startLocationId:e,endLocationId:t,additionalStopIds:r});let a=await Promise.all(n),d=0,c=[];i.forEach(e=>{"start"===e?(s(a[d]),console.log("\uD83D\uDCCD Стартовая локация:",a[d])):"end"===e?(o(a[d]),console.log("\uD83C\uDFC1 Конечная локация:",a[d])):"additional"===e&&(c.push(a[d]),console.log("\uD83D\uDED1 Дополнительная остановка:",a[d])),d++}),c.length>0&&(u(c),console.log("\uD83D\uDED1 Все дополнительные остановки установлены:",c))}catch(e){console.error("❌ Ошибка загрузки локаций:",e),x("Не удалось загрузить данные локаций")}finally{h(!1)}}};s(null),o(null),u([]),n()},[e,t,r?.join(",")]),{startLocation:n,endLocation:a,additionalStops:d,isLoading:c,error:p}};r(28412);var c=r(66971);let h=({order:e,availableActions:t,onActionExecute:r})=>{let s=(0,a.useRouter)(),[l,o]=(0,i.useState)(null),u=e.status===d.Re.Completed,h=e.status===d.Re.Cancelled,p=async e=>{o(e.id);try{await r(e)}catch(e){}finally{o(null)}},x=e=>{switch(e){case"primary":default:return"bg-blue-500 hover:bg-blue-600 text-white";case"secondary":return"bg-gray-500 hover:bg-gray-600 text-white";case"danger":return"bg-red-500 hover:bg-red-600 text-white"}};return(0,n.jsx)("div",{className:"absolute top-0 left-0 right-0 mx-auto p-2 z-30",children:(0,n.jsx)("div",{className:"bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-lg",children:(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold",children:"Информация о заказе"}),(0,n.jsxs)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${u?"bg-green-100 text-green-800":e.status===d.Re.InProgress?"bg-blue-100 text-blue-800":h?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:[(0,c.Vh)(e.status),e.subStatus&&` (${(0,c.l1)(e.subStatus)})`]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[t.map((e,t)=>(0,n.jsx)("button",{type:"button",onClick:()=>p(e),disabled:null!==l,className:`py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${x(e.variant)}`,children:l===e.id?"Выполняется...":e.label},e.id)),(u||h)&&(0,n.jsx)("button",{onClick:()=>s.push("/"),className:"bg-gray-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-600 transition-colors",children:"К заказам"})]})]})})})})};r(36706);var p=r(72235),x=r(24903),g=r(4004);let m=e=>{let[t,r]=(0,i.useState)(null),[n,s]=(0,i.useState)(!0),[a,d]=(0,i.useState)(null),u=async e=>{try{console.log("\uD83D\uDD04 Выполняем действие:",e);let t=await l.uE.post(e.endpoint);if(200===t.status)o.P.success(`${e.label} - выполнено`),await h();else throw Error("Неожиданный ответ от сервера")}catch(t){throw console.error("❌ Ошибка при выполнении действия:",t),o.P.error(`Ошибка: ${e.label}`),t}},c=async()=>{if(!e){d("Нет orderId"),s(!1);return}try{s(!0),d(null),console.log("\uD83D\uDCE6 Загружаем данные заказа:",e);let t=l.QQ.ORDER.MY_PARTICIPANT.replace("{order_id}",e),n=(await l.uE.get(t)).data;console.log("✅ Данные заказа получены:",n),r({order:n})}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки данных заказа";console.error("❌ Ошибка загрузки данных заказа:",t),d(e),o.P.error(e)}finally{s(!1)}},h=async()=>{await c()};(0,i.useEffect)(()=>{c()},[e]);let p=t?(e=>{let t=[];switch(e.status){case"Scheduled":"DriverAssigned"===e.subStatus&&t.push({id:"driver-heading",label:"Принял еду",endpoint:`/Ride/for-order/${e.id}/status/driver-heading-to-client`,method:"POST",variant:"primary"});break;case"InProgress":switch(e.subStatus){case"DriverHeading":t.push({id:"driver-arrived",label:"Я прибыл",endpoint:`/Ride/for-order/${e.id}/status/driver-arrived`,method:"POST",variant:"primary"});break;case"DriverArrived":t.push({id:"ride-started",label:"Взял пассажира",endpoint:`/Ride/for-order/${e.id}/status/ride-started`,method:"POST",variant:"primary"});break;case"RideStarted":t.push({id:"ride-finished",label:"Завершить поездку",endpoint:`/Ride/for-order/${e.id}/status/ride-finished`,method:"POST",variant:"primary"})}}return"Completed"!==e.status&&"Cancelled"!==e.status&&t.push({id:"cancel",label:"Отменить",endpoint:`/Ride/for-order/${e.id}/status/ride-cancelled`,method:"POST",variant:"danger"}),t})(t.order):[];return{orderData:t,isLoading:n,error:a,availableActions:p,executeAction:u,refetch:h}},f=(e,t,r,n)=>{let i=(r-e)*Math.PI/180,s=(n-t)*Math.PI/180,a=Math.sin(i/2)*Math.sin(i/2)+Math.cos(e*Math.PI/180)*Math.cos(r*Math.PI/180)*Math.sin(s/2)*Math.sin(s/2);return 2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))*6371},v=({startLocation:e,endLocation:t,additionalStops:r=[],driverLocation:a,orderStatus:l,orderSubStatus:o,className:u=""})=>{let{location:c}=(0,x.t0)(),h=c||a,p=(0,i.useMemo)(()=>{let n=[];return e&&e.id&&n.push({id:e.id,name:e.name,address:e.address,latitude:e.latitude,longitude:e.longitude,type:"start"}),r.forEach(e=>{e&&e.id&&n.push({id:e.id,name:e.name,address:e.address,latitude:e.latitude,longitude:e.longitude,type:"stop"})}),t&&t.id&&n.push({id:t.id,name:t.name,address:t.address,latitude:t.latitude,longitude:t.longitude,type:"end"}),n},[e,t,r]),g=(0,i.useMemo)(()=>{if(p.length<2)return{totalDistance:0,progress:0,remainingDistance:0,isRideStarted:!1};let e=0;for(let t=0;t<p.length-1;t++){let r=p[t],n=p[t+1];e+=f(r.latitude,r.longitude,n.latitude,n.longitude)}let t=l===d.Re.InProgress&&o===d.lk.RideStarted,r=0,n=e;if(t&&h&&p.length>0){let t=p[0],i=p[p.length-1],s=f(t.latitude,t.longitude,h.latitude,h.longitude),a=f(h.latitude,h.longitude,i.latitude,i.longitude),l=f(t.latitude,t.longitude,i.latitude,i.longitude);Math.abs(s+a-l)<.3*l&&e>0?(r=Math.min((Math.min(s/e*100,100)+Math.max(0,(1-a/l)*100))/2,100),s<.1&&(r=Math.min(r,5)),a<.1&&(r=Math.max(r,95)),n=a):(r=0,n=e),r>20&&s<.5&&(r=Math.min(r,15))}return{totalDistance:e,progress:Math.max(0,Math.min(r,100)),remainingDistance:n,isRideStarted:t}},[p,h,l,o]);if(0===p.length)return(0,n.jsx)("div",{className:`absolute bottom-4 left-1/2 transform -translate-x-1/2 w-[85%] bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-4 z-20 ${u}`,children:(0,n.jsx)("div",{className:"text-center text-gray-500",children:"Загрузка данных маршрута..."})});let m=e=>e<1?`${Math.round(1e3*e)} м`:`${e.toFixed(1)} км`;return(0,n.jsx)("div",{className:"absolute bottom-0 left-0 right-0 mx-auto p-2 z-20",children:(0,n.jsxs)("div",{className:"relative bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4 rounded-2xl w-full flex flex-col gap-3",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center text-xs text-gray-600",children:[(0,n.jsxs)("div",{className:"flex gap-4",children:[(0,n.jsxs)("span",{children:["\uD83D\uDCCD Общее расстояние: ",(0,n.jsx)("strong",{children:m(g.totalDistance)})]}),g.isRideStarted&&(0,n.jsx)("span",{className:"text-green-600",children:"\uD83D\uDE97 Поездка начата"}),!g.isRideStarted&&(0,n.jsx)("span",{className:"text-orange-600",children:"\uD83D\uDEB6 Еду к пассажиру"})]}),(0,n.jsxs)("div",{className:"flex gap-4",children:[g.isRideStarted&&(0,n.jsxs)("span",{children:["\uD83D\uDCCD Осталось: ",(0,n.jsx)("strong",{children:m(g.remainingDistance)})]}),(0,n.jsx)("span",{className:"text-gray-500",children:g.isRideStarted?`Прогресс поездки: ${g.progress.toFixed(1)}%`:"Ожидание начала поездки (0%)"})]})]}),(0,n.jsx)("div",{className:"flex justify-between items-center gap-4",children:p.map((e,t)=>{let r=t<p.length-1?f(e.latitude,e.longitude,p[t+1].latitude,p[t+1].longitude):0;return(0,n.jsxs)(s().Fragment,{children:[(0,n.jsxs)("div",{className:"flex flex-row gap-2 items-center text-start flex-1",children:[(0,n.jsx)("div",{className:`w-4 h-4 rounded-full ${"start"===e.type?"bg-green-500":"end"===e.type?"bg-red-500":"bg-blue-500"}`}),(0,n.jsxs)("div",{className:"flex flex-col gap-1 text-xs",children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsx)("div",{className:"truncate max-w-[120px] text-gray-500",children:e.address})]})]}),r>0&&(0,n.jsx)("div",{className:"flex items-center justify-center px-2",children:(0,n.jsxs)("div",{className:"text-gray-400 text-xs whitespace-nowrap",children:["→ ",m(r)," →"]})})]},e.id)})}),(0,n.jsx)("div",{className:"relative",children:(0,n.jsx)("div",{className:"w-full h-3 bg-gray-200 rounded-full",children:(0,n.jsx)("div",{className:`h-3 rounded-full transition-all duration-1000 ease-out ${g.isRideStarted?"bg-gradient-to-r from-green-500 to-blue-500":"bg-gray-400"}`,style:{width:`${g.progress}%`}})})})]})})},b=({passengers:e,services:t,initialPrice:r})=>{let[s,a]=(0,i.useState)(!1);return e.find(e=>e.isMainPassenger)||e[0]?s?(0,n.jsxs)("div",{className:"absolute bottom-34 right-2 bg-white rounded-lg shadow-lg p-4 w-[98%] z-20 h-[240px] flex flex-col",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4 flex-shrink-0",children:[(0,n.jsx)("div",{className:"text-lg font-semibold",children:"Детали заказа"}),(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[r," Сом"]}),(0,n.jsx)("button",{onClick:()=>a(!1),className:"w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center hover:bg-blue-200 transition-colors",children:(0,n.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),(0,n.jsxs)("div",{className:"flex flex-row justify-between gap-4 flex-1 overflow-hidden h-full",children:[(0,n.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden h-full",children:[(0,n.jsxs)("div",{className:"text-sm font-medium mb-2 flex-shrink-0",children:["Пассажиры (",e.length,"):"]}),(0,n.jsx)("div",{className:"space-y-2 overflow-y-auto flex-1 min-h-0 max-h-[120px]",children:e.map((e,t)=>(0,n.jsxs)("div",{className:`flex items-center space-x-2 p-2 rounded ${e.isMainPassenger?"bg-blue-50 border border-blue-200":"bg-gray-50"}`,children:[(0,n.jsx)("div",{className:`w-6 h-6 rounded-full flex items-center justify-center ${e.isMainPassenger?"bg-blue-500 text-white":"bg-gray-300"}`,children:(0,n.jsx)("span",{className:"text-xs",children:t+1})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-sm font-medium",children:e.firstName}),e.isMainPassenger&&(0,n.jsx)("div",{className:"text-xs text-blue-600",children:"Основной пассажир"})]})]},e.id))})]}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden h-full",children:[(0,n.jsxs)("div",{className:"text-sm font-medium mb-2 flex-shrink-0",children:["Дополнительные услуги (",t.length,"):"]}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto min-h-0 max-h-[120px]",children:t.length>0?(0,n.jsx)("div",{className:"space-y-2",children:t.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-50 rounded border border-green-200",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-sm font-medium",children:e.name}),(0,n.jsxs)("div",{className:"text-xs text-gray-500",children:["Количество: ",e.quantity]})]}),(0,n.jsx)("div",{className:"w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-xs",children:e.quantity})})]},e.serviceId))}):(0,n.jsx)("div",{className:"text-sm text-gray-500 italic",children:"Нет дополнительных услуг"})})]})]})]}):(0,n.jsx)("div",{className:"absolute bottom-34 right-4 z-20",children:(0,n.jsx)("button",{onClick:()=>a(!0),className:"w-12 h-12 bg-blue-100 rounded-md flex items-center justify-center hover:bg-blue-200 transition-colors shadow-lg cursor-pointer",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})}):null};var j=r(58455),y=r.n(j);function C(e,t){return Object.freeze({...e,...t})}r(95091);let w=(0,i.createContext)(null);function N(){let e=(0,i.use)(w);if(null==e)throw Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return e}function M(){return N().map}var P=r(22358);function S(e){return(0,i.forwardRef)(function(t,r){let{instance:n,context:s}=e(t).current;(0,i.useImperativeHandle)(r,()=>n);let{children:a}=t;return null==a?null:i.createElement(w,{value:s},a)})}function k(e,t){let r=(0,i.useRef)(t);(0,i.useEffect)(function(){t!==r.current&&null!=e.attributionControl&&(null!=r.current&&e.attributionControl.removeAttribution(r.current),null!=t&&e.attributionControl.addAttribution(t)),r.current=t},[e,t])}function D(e,t){let r=(0,i.useRef)(void 0);(0,i.useEffect)(function(){return null!=t&&e.instance.on(t),r.current=t,function(){null!=r.current&&e.instance.off(r.current),r.current=null}},[e,t])}function R(e,t){let r=e.pane??t.pane;return r?{...e,pane:r}:e}function L(e,t,r){return Object.freeze({instance:e,context:t,container:r})}function E(e,t){return null==t?function(t,r){let n=(0,i.useRef)(void 0);return n.current||(n.current=e(t,r)),n}:function(r,n){let s=(0,i.useRef)(void 0);s.current||(s.current=e(r,n));let a=(0,i.useRef)(r),{instance:l}=s.current;return(0,i.useEffect)(function(){a.current!==r&&(t(l,r,a.current),a.current=r)},[l,r,t]),s}}function I(e,t){(0,i.useEffect)(function(){return(t.layerContainer??t.map).addLayer(e.instance),function(){t.layerContainer?.removeLayer(e.instance),t.map.removeLayer(e.instance)}},[t,e])}function $(e){return function(t){let r=N(),n=e(R(t,r),r);return k(r.map,t.attribution),D(n.current,t.eventHandlers),I(n.current,r),n}}function O(e,t){var r;return S((r=E(e,t),function(e){let t=N(),n=r(R(e,t),t);D(n.current,e.eventHandlers),I(n.current,t);var s=n.current;let a=(0,i.useRef)(void 0);return(0,i.useEffect)(function(){if(e.pathOptions!==a.current){let t=e.pathOptions??{};s.instance.setStyle(t),a.current=t}},[s,e]),n}))}let z=S($(E(function({position:e,...t},r){let n=new j.Marker(e,t);return L(n,C(r,{overlayContainer:n}))},function(e,t,r){t.position!==r.position&&e.setLatLng(t.position),null!=t.icon&&t.icon!==r.icon&&e.setIcon(t.icon),null!=t.zIndexOffset&&t.zIndexOffset!==r.zIndexOffset&&e.setZIndexOffset(t.zIndexOffset),null!=t.opacity&&t.opacity!==r.opacity&&e.setOpacity(t.opacity),null!=e.dragging&&t.draggable!==r.draggable&&(!0===t.draggable?e.dragging.enable():e.dragging.disable())}))),A=function(e,t){var r,n;return r=E(e),n=function(e,n){let i=N(),s=r(R(e,i),i);return k(i.map,e.attribution),D(s.current,e.eventHandlers),t(s.current,i,e,n),s},(0,i.forwardRef)(function(e,t){let[r,s]=(0,i.useState)(!1),{instance:a}=n(e,s).current;(0,i.useImperativeHandle)(t,()=>a),(0,i.useEffect)(function(){r&&a.update()},[a,r,e.children]);let l=a._contentNode;return l?(0,P.createPortal)(e.children,l):null})}(function(e,t){return L(new j.Popup(e,t.overlayContainer),t)},function(e,t,{position:r},n){(0,i.useEffect)(function(){let{instance:i}=e;function s(e){e.popup===i&&(i.update(),n(!0))}function a(e){e.popup===i&&n(!1)}return t.map.on({popupopen:s,popupclose:a}),null==t.overlayContainer?(null!=r&&i.setLatLng(r),i.openOn(t.map)):t.overlayContainer.bindPopup(i),function(){t.map.off({popupopen:s,popupclose:a}),t.overlayContainer?.unbindPopup(),t.map.removeLayer(i)}},[e,t,n,r])}),F=(0,i.forwardRef)(function({bounds:e,boundsOptions:t,center:r,children:n,className:s,id:a,placeholder:l,style:o,whenReady:d,zoom:u,...c},h){let[p]=(0,i.useState)({className:s,id:a,style:o}),[x,g]=(0,i.useState)(null),m=(0,i.useRef)(void 0);(0,i.useImperativeHandle)(h,()=>x?.map??null,[x]);let f=(0,i.useCallback)(n=>{if(null!==n&&!m.current){let i=new j.Map(n,c);m.current=i,null!=r&&null!=u?i.setView(r,u):null!=e&&i.fitBounds(e,t),null!=d&&i.whenReady(d),g(Object.freeze({__version:1,map:i}))}},[]);(0,i.useEffect)(()=>()=>{x?.map.remove()},[x]);let v=x?i.createElement(w,{value:x},n):l??null;return i.createElement("div",{...p,ref:f},v)}),T=function(e,t){var r;return r=$(E(e,t)),(0,i.forwardRef)(function(e,t){let{instance:n}=r(e).current;return(0,i.useImperativeHandle)(t,()=>n),null})}(function({url:e,...t},r){return L(new j.TileLayer(e,R(t,r)),r)},function(e,t,r){let{opacity:n,zIndex:i}=t;null!=n&&n!==r.opacity&&e.setOpacity(n),null!=i&&i!==r.zIndex&&e.setZIndex(i);let{url:s}=t;null!=s&&s!==r.url&&e.setUrl(s)}),B=O(function({positions:e,...t},r){let n=new j.Polyline(e,t);return L(n,C(r,{overlayContainer:n}))},function(e,t,r){t.positions!==r.positions&&e.setLatLngs(t.positions)}),_=O(function({center:e,children:t,...r},n){let i=new j.Circle(e,r);return L(i,C(n,{overlayContainer:i}))},function(e,t,r){t.center!==r.center&&e.setLatLng(t.center),null!=t.radius&&t.radius!==r.radius&&e.setRadius(t.radius)});var q=r(89337);delete y().Icon.Default.prototype._getIconUrl,y().Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});let W=({onMapClick:e})=>(!function(e){let t=N().map;(0,i.useEffect)(function(){return t.on(e),function(){t.off(e)}},[t,e])}({click:t=>{e&&e(t.latlng.lat,t.latlng.lng)}}),null),Z=({onBoundsChange:e})=>{let t=N().map,r=(0,i.useCallback)((r=!1)=>{if(!e)return;let n=t.getBounds();e({latFrom:n.getSouth(),latTo:n.getNorth(),longFrom:n.getWest(),longTo:n.getEast()})},[t,e]);return(0,i.useEffect)(()=>{if(!e)return;let n=null,i=null,s=()=>{let e=t.getBounds(),s=`${e.getSouth().toFixed(6)},${e.getNorth().toFixed(6)},${e.getWest().toFixed(6)},${e.getEast().toFixed(6)}`;i!==s&&(i=s,n&&clearTimeout(n),n=setTimeout(()=>{r(!1)},500))};return r(!0),t.on("moveend",s),t.on("zoomend",s),()=>{n&&clearTimeout(n),t.off("moveend",s),t.off("zoomend",s)}},[t,e,r]),null},H=({latitude:e,longitude:t,zoom:r=13,height:s="400px",width:a="100%",showMarker:l=!0,markerText:o,routePoints:d=[],showRoute:u=!1,activeDrivers:c=[],showActiveDrivers:h=!1,className:p="",onMapClick:x,onBoundsChange:g,onDriverSelect:m,selectedDriverId:f,mapLocations:v=[],selectedLocationIds:b=[],onLocationToggle:j,getDriverById:C,showDriverSearchZone:w=!1,driverSearchRadius:N=2e3,currentDriverLocation:M,routeDeviationThreshold:P=100,onRouteDeviation:S})=>{let k=[e,t],[D,R]=(0,i.useState)([]),L=(e,t,r,n)=>{let i=(r-e)*Math.PI/180,s=(n-t)*Math.PI/180,a=Math.sin(i/2)*Math.sin(i/2)+Math.cos(e*Math.PI/180)*Math.cos(r*Math.PI/180)*Math.sin(s/2)*Math.sin(s/2);return 2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))*6371e3},E=(e,t,r,n,i,s)=>{let a,l,o=e-r,d=t-n,u=i-r,c=s-n,h=u*u+c*c;if(0===h)return L(e,t,r,n);let p=(o*u+d*c)/h;return p<0?(a=r,l=n):p>1?(a=i,l=s):(a=r+p*u,l=n+p*c),L(e,t,a,l)},{isDriverOffRoute:I,minDistanceToRoute:$}=(0,i.useMemo)(()=>{if(!M||D.length<2)return{isDriverOffRoute:!1,minDistanceToRoute:0};let e=1/0;for(let t=0;t<D.length-1;t++){let[r,n]=D[t],[i,s]=D[t+1];e=Math.min(e,E(M.latitude,M.longitude,r,n,i,s))}let t=e>P;return S&&S(t,e),{isDriverOffRoute:t,minDistanceToRoute:e}},[M,D,P,S]),O=(e,t)=>{let r=`${e}|${t??""}`;U.current||(U.current=new Map);let n=U.current.get(r);if(n)return n;let i=`
      <div style="
        width: 32px;
        height: 32px;
        background-color: ${e};
        border: 3px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 16px;
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      ">
        ${t||""}
      </div>
    `,s=y().divIcon({html:i,className:"",iconSize:[32,32],iconAnchor:[16,16],popupAnchor:[0,-20]});return U.current.set(r,s),s},H=(e,t=!1,r=0)=>{let n=t?"#22C55E":(e=>{switch(e){case"Economy":return"#9CA3AF";case"Comfort":return"#60A5FA";case"ComfortPlus":return"#3B82F6";case"Business":return"#8B5CF6";case"Premium":return"#F59E0B";case"Vip":return"#EF4444";case"Luxury":return"#DC2626";default:return"#8EBFFF"}})(e),i=0!==r?`transform: rotate(${r}deg);`:"",s=`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 41 24" fill="none" width="56" height="32" style="${t?"filter: drop-shadow(0 0 8px #22c55e);":""} ${i} transform-origin: center;">
        <g clip-path="url(#clip0_42_2510)">
          <path d="M11.7415 5.22881C11.8153 4.34825 11.3567 3.34659 12.6569 3.01606L12.7498 5.027C14.4654 4.66129 16.0866 4.32077 17.7056 3.96753C20.0512 3.4575 22.4009 2.9697 24.733 2.40747C25.638 2.16049 26.5238 1.84568 27.3832 1.46561C30.2331 0.298448 33.2233 0.235652 36.2282 0.285838C36.9767 0.299309 37.471 0.750296 37.8466 1.38123C38.9376 3.20844 39.5421 5.23256 39.9148 7.29461C40.283 9.32646 40.4181 11.4059 40.5761 13.4698C40.674 14.7074 40.1256 15.5987 38.9536 16.0914C36.0574 17.3071 33.162 18.3387 29.9513 18.5157C27.4256 18.6547 24.926 19.3607 22.4247 19.8603C20.3821 20.2747 18.3515 20.7478 16.2235 21.2158L16.836 23.0363C16.0758 23.2122 16.0758 23.2122 15.0326 21.3316C13.962 21.7123 12.8791 22.2313 11.7456 22.4725C10.1125 22.8264 8.53393 23.5459 6.78885 23.3079C6.35022 23.2481 5.87241 23.4607 5.41619 23.5634C4.48482 23.7664 3.46457 23.3685 3.11709 22.4642C2.30821 20.3614 1.34025 18.2772 0.888129 16.0908C0.431324 13.871 0.472913 11.5449 0.381643 9.26176C0.33822 8.32098 1.06987 7.62934 2.00073 7.38149C3.84511 6.88339 5.66651 6.29513 7.52494 5.86361C8.64497 5.60383 9.81481 5.57383 10.9542 5.4268C11.2204 5.37737 11.4833 5.31126 11.7415 5.22881ZM17.2852 7.05379C15.6778 7.01434 14.2232 6.91981 12.7698 6.95487C10.9704 7.00111 10.5394 7.51561 10.5478 9.36078C10.5377 9.65339 10.556 9.94635 10.6023 10.2354C11.0815 12.6015 11.5422 14.9732 12.0866 17.325C12.2374 17.9775 12.6062 18.596 12.9562 19.1817C13.3386 19.8219 13.9723 20.0664 14.6519 19.7757C16.2725 19.083 17.8666 18.326 19.5297 17.5689C17.8466 14.1892 17.1703 10.7806 17.2852 7.05537L17.2852 7.05379ZM31.7694 14.8319C33.2584 14.5503 34.5568 14.3159 35.8493 14.0562C36.4225 13.9401 36.7368 13.5478 36.8239 12.9548C37.3243 9.82571 36.6332 6.61749 34.8923 3.98739C34.6945 3.67639 34.129 3.38236 33.7933 3.44588C32.4228 3.6804 31.0758 4.05149 29.5778 4.41035C31.221 7.7407 31.9583 11.0777 31.7695 14.8335L31.7694 14.8319ZM30.5847 3.32986L30.517 3.05287C27.326 2.91043 24.2295 3.52235 21.0804 4.28875C21.2131 4.88767 21.3317 5.41842 21.4754 6.05205L30.5847 3.32986ZM24.0576 18.4484C26.7556 18.0662 32.3728 16.5248 33.1672 15.3932L23.6954 16.7156L24.0576 18.4484ZM20.4873 4.38016L13.7881 5.78261C16.123 6.12942 18.4335 6.42295 20.8603 6.14287C20.7244 5.50888 20.6121 4.97785 20.4859 4.38343L20.4873 4.38016ZM16.7459 19.9303L16.8581 20.1525L23.4907 18.633C23.358 17.9989 23.2453 17.4599 23.0884 16.7116L16.7459 19.9303Z" fill="${n}"/>
          <path d="M17.2847 7.05528C17.1698 10.7805 17.8461 14.1891 19.5293 17.5704C17.8662 18.3275 16.2721 19.0845 14.6515 19.7772C13.9719 20.0679 13.3381 19.8234 12.9558 19.1832C12.6058 18.5975 12.237 17.979 12.0862 17.3265C11.5417 14.9747 11.0842 12.6028 10.6018 10.2369C10.5555 9.94786 10.5373 9.6549 10.5473 9.36228C10.539 7.51711 10.97 7.00262 12.7693 6.95638C14.2227 6.92131 15.6774 7.01582 17.2847 7.05528Z" fill="white"/>
          <path d="M31.7696 14.8334C31.9585 11.0776 31.2212 7.7406 29.578 4.41185C31.0761 4.05459 32.423 3.6819 33.7935 3.44738C34.1295 3.39025 34.6949 3.68267 34.8925 3.98888C36.6334 6.61899 37.3245 9.82719 36.8241 12.9562C36.737 13.5493 36.4225 13.9352 35.8496 14.0577C34.557 14.3174 33.2587 14.5518 31.7696 14.8334Z" fill="white"/>
          <path d="M30.5845 3.32976L21.472 6.0505C21.3314 5.41672 21.2129 4.88597 21.077 4.2872C24.2261 3.52239 27.3227 2.91047 30.5136 3.05131L30.5845 3.32976Z" fill="white"/>
          <path d="M24.0556 18.4516L23.6949 16.7187L33.1667 15.3963C32.371 16.5327 26.7537 18.0725 24.0556 18.4516Z" fill="white"/>
          <path d="M20.4857 4.38333C20.6119 4.97775 20.7242 5.50879 20.8587 6.14605C18.4316 6.41814 16.1214 6.1326 13.7865 5.78578L20.4857 4.38333Z" fill="white"/>
          <path d="M16.7446 19.9335L23.0887 16.7147C23.244 17.463 23.3563 17.9941 23.491 18.6361L16.8584 20.1556L16.7446 19.9335Z" fill="white"/>
        </g>
        <defs>
          <clipPath id="clip0_42_2510">
            <rect width="40" height="22" fill="white" transform="translate(40.9717 21.9766) rotate(177.357)"/>
          </clipPath>
        </defs>
      </svg>
    `;return y().divIcon({html:s,className:"driver-marker",iconSize:[56,32],iconAnchor:[28,16],popupAnchor:[0,-45]})},Q=e=>{switch(e){case"start":return"#22c55e";case"end":return"#ef4444";case"driver":return"#3b82f6";case"waypoint":return"#f59e0b";default:return"#8b5cf6"}};(0,i.useEffect)(()=>{if(!u||d.length<2)return void R([]);let e=!1,t=new AbortController;return(async t=>{try{R([]);let r=d.map(e=>`${e.longitude},${e.latitude}`).join(";"),n=await fetch(`https://router.project-osrm.org/route/v1/driving/${r}?overview=full&geometries=geojson`,{signal:t});if(!n.ok)throw Error("Ошибка построения маршрута");let i=await n.json();if(i.routes&&i.routes[0]){let t=i.routes[0].geometry.coordinates.map(e=>[e[1],e[0]]);e||R(t)}}catch(r){t.aborted||e||R(d.map(e=>[e.latitude,e.longitude]))}})(t.signal),()=>{e=!0,t.abort()}},[u,d]);let G=(e,t)=>{if(!e.latitude||!e.longitude)return null;let r=b.includes(e.id);return(0,n.jsx)(z,{position:[e.latitude,e.longitude],icon:t,children:(0,n.jsx)(A,{children:(0,n.jsxs)("div",{style:{minWidth:"200px"},children:[(0,n.jsx)("div",{style:{marginBottom:"8px",fontWeight:600},children:e.name}),(0,n.jsx)("div",{style:{fontSize:"12px",marginBottom:"8px"},children:(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Координаты:"})," ",e.latitude.toFixed(6),", ",e.longitude.toFixed(6)]})}),j&&(0,n.jsx)("button",{type:"button",onClick:()=>{j(e,!r)},style:{width:"100%",padding:"6px 12px",backgroundColor:r?"#ef4444":"#22c55e",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",transition:"background-color 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor=r?"#dc2626":"#16a34a"},onMouseOut:e=>{e.currentTarget.style.backgroundColor=r?"#ef4444":"#22c55e"},children:r?"❌ Убрать":"✅ Выбрать"})]})})},`loc-${e.id}`)},U=(0,i.useRef)(new Map);return(0,n.jsx)("div",{className:p,style:{height:s,width:a},children:(0,n.jsxs)(F,{center:k,zoom:r,style:{height:"100%",width:"100%"},scrollWheelZoom:!0,children:[(0,n.jsx)(T,{attribution:'\xa9 <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),x&&(0,n.jsx)(W,{onMapClick:x}),g&&(0,n.jsx)(Z,{onBoundsChange:g}),l&&(0,n.jsx)(z,{position:k,children:(0,n.jsx)(A,{children:o||`Координаты: ${e.toFixed(6)}, ${t.toFixed(6)}`})}),d.map((e,t)=>{if("driver"===e.type){let r="heading"in e?e.heading:0;return(0,n.jsx)(z,{position:[e.latitude,e.longitude],icon:H("Economy",!1,r),children:(0,n.jsx)(A,{children:(0,n.jsxs)("div",{style:{minWidth:"200px"},children:[(0,n.jsx)("div",{style:{marginBottom:"8px",fontWeight:600},children:"\uD83D\uDE97 Мое местоположение"}),(0,n.jsxs)("div",{style:{fontSize:"12px",marginBottom:"8px"},children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Координаты:"})," ",e.latitude.toFixed(6),", ",e.longitude.toFixed(6)]}),r>0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Направление:"})," ",r.toFixed(0),"\xb0"]})]})]})})},`driver-${t}`)}let r=String.fromCharCode(65+d.slice(0,t).filter(e=>"driver"!==e.type).length),i=Q(e.type),s="id"in e&&e.id;return(0,n.jsx)(z,{position:[e.latitude,e.longitude],icon:O(i,r),children:(0,n.jsx)(A,{children:(0,n.jsxs)("div",{style:{minWidth:"200px"},children:[(0,n.jsx)("div",{style:{marginBottom:"8px",fontWeight:600},children:e.name||`Точка ${t+1}`}),(0,n.jsxs)("div",{style:{fontSize:"12px",marginBottom:"8px"},children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Тип:"})," ",e.type||"Неизвестно"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Координаты:"})," ",e.latitude.toFixed(6),", ",e.longitude.toFixed(6)]})]}),s&&j&&(0,n.jsx)("button",{type:"button",onClick:()=>{j({id:e.id,name:e.name||"Точка",latitude:e.latitude,longitude:e.longitude,address:e.name||"Точка",city:"",country:"",region:"",type:q.i.Other,isActive:!0},!1)},style:{width:"100%",padding:"6px 12px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",transition:"background-color 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#dc2626"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#ef4444"},children:"❌ Убрать точку"})]})})},t)}),h&&c.map((e,t)=>{let r=f===e.id,i=C?.(e.id),s=0;if(d.length>0){let t=d.filter(e=>"driver"!==e.type);if(t.length>0){let r=t[0];s=(180*Math.atan2(r.longitude-e.currentLocation.longitude,r.latitude-e.currentLocation.latitude)/Math.PI+360)%360}}return(0,n.jsx)(z,{position:[e.currentLocation.latitude,e.currentLocation.longitude],icon:H(e.serviceClass,r,s),children:(0,n.jsx)(A,{children:(0,n.jsxs)("div",{style:{minWidth:"250px"},children:[(0,n.jsxs)("div",{style:{marginBottom:"12px"},children:[(0,n.jsx)("strong",{style:{fontSize:"14px"},children:i?.fullName||"Активный водитель"}),r&&(0,n.jsx)("span",{style:{marginLeft:"8px",fontSize:"10px",backgroundColor:"#22c55e",color:"white",padding:"2px 6px",borderRadius:"4px",fontWeight:"bold"},children:"✓ ВЫБРАН"})]}),i&&(0,n.jsxs)("div",{style:{fontSize:"12px",marginBottom:"12px"},children:[(0,n.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,n.jsx)("strong",{children:"\uD83D\uDCDE Телефон:"})," ",String(i.phoneNumber||"Не указан")]}),(0,n.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,n.jsx)("strong",{children:"⭐ Рейтинг:"})," ","number"==typeof i.rating?`${i.rating}/5`:"Нет рейтинга"]}),(0,n.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,n.jsx)("strong",{children:"\uD83D\uDFE2 Статус:"})," ",i.online?"Онлайн":"Оффлайн"]})]}),(0,n.jsxs)("div",{style:{fontSize:"12px",marginBottom:"12px"},children:[(0,n.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,n.jsx)("strong",{children:"\uD83D\uDE97 Автомобиль:"})," ",String(i?.activeCar?.make||"Неизвестно")," ",String(i?.activeCar?.model||e.type)]}),(()=>{let e=i&&i.activeCar?.licensePlate;return e?(0,n.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,n.jsx)("strong",{children:"\uD83D\uDD22 Номер:"}),(0,n.jsx)("span",{style:{marginLeft:"4px",backgroundColor:"#f3f4f6",padding:"2px 6px",borderRadius:"4px",fontWeight:"bold"},children:String(e)})]}):null})(),(0,n.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,n.jsx)("strong",{children:"\uD83C\uDFAF Класс:"})," ",e.serviceClass]}),(()=>{let e=i&&i.activeCar?.color;return e?(0,n.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,n.jsx)("strong",{children:"\uD83C\uDFA8 Цвет:"})," ",String(e)]}):null})(),(()=>{let e=i&&i.activeCar?.passengerCapacity;return e?(0,n.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,n.jsx)("strong",{children:"\uD83D\uDC65 Мест:"})," ",String(e)]}):null})()]}),(0,n.jsxs)("div",{style:{fontSize:"10px",color:"#6b7280",marginBottom:"8px"},children:[(0,n.jsx)("strong",{children:"\uD83D\uDCCD Координаты:"})," ",e.currentLocation.latitude.toFixed(6),", ",e.currentLocation.longitude.toFixed(6)]}),m&&(0,n.jsx)("button",{type:"button",onClick:()=>{r?m(""):m(e)},style:{width:"100%",padding:"8px 12px",backgroundColor:r?"#ef4444":"#3b82f6",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",transition:"background-color 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor=r?"#dc2626":"#2563eb"},onMouseOut:e=>{e.currentTarget.style.backgroundColor=r?"#ef4444":"#3b82f6"},children:r?"❌ Отписаться":"✅ Выбрать водителя"})]})})},`driver-${e.id}`)}),u&&D.length>=2&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(B,{positions:D,color:I?"#7f1d1d":"#1e40af",weight:4,opacity:.8}),(0,n.jsx)(B,{positions:D,color:I?"#ef4444":"#3b82f6",weight:2,opacity:.9}),I&&M&&(0,n.jsx)(_,{center:[M.latitude,M.longitude],radius:P,color:"#ef4444",weight:2,opacity:.7,fillColor:"#ef4444",fillOpacity:.2,dashArray:"5, 5"})]}),w&&d&&d.length>0&&d[0]&&"number"==typeof d[0].latitude&&"number"==typeof d[0].longitude&&!isNaN(d[0].latitude)&&!isNaN(d[0].longitude)&&(0,n.jsx)(_,{center:[d[0].latitude,d[0].longitude],radius:N,color:"#3b82f6",weight:2,opacity:.6,fillColor:"#3b82f6",fillOpacity:.1}),v.map(e=>G(e,O(Q("location"))))]})})},Q=({_ride:e,startLocation:t,endLocation:r,additionalStops:s=[]})=>{let{location:a,isTracking:l,error:o}=(0,x.t0)(),d=(0,i.useMemo)(()=>{let e=[];return t&&t.latitude&&t.longitude&&e.push({latitude:t.latitude,longitude:t.longitude,name:`📍 ${t.name}`,type:"start",id:t.id}),s.forEach((t,r)=>{t&&t.latitude&&t.longitude&&e.push({latitude:t.latitude,longitude:t.longitude,name:`🛑 ${t.name}`,type:"waypoint",id:t.id})}),r&&r.latitude&&r.longitude&&e.push({latitude:r.latitude,longitude:r.longitude,name:`🏁 ${r.name}`,type:"end",id:r.id}),a&&e.push({latitude:a.latitude,longitude:a.longitude,name:"\uD83D\uDE97 Мое местоположение",type:"driver",heading:a.heading}),e},[t,r,s,a]),u=(0,i.useMemo)(()=>a?{latitude:a.latitude,longitude:a.longitude}:t&&t.latitude&&t.longitude?{latitude:t.latitude,longitude:t.longitude}:d.length>0?{latitude:d[0].latitude,longitude:d[0].longitude}:{latitude:42.8746,longitude:74.5698},[a,t,d]),c=(0,i.useMemo)(()=>0===d.length?11:1===d.length?15:13,[d.length]);return(0,n.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,n.jsx)(H,{latitude:u.latitude,longitude:u.longitude,zoom:c,height:"100%",showMarker:!1,routePoints:d,showRoute:d.length>=2,currentDriverLocation:a?{latitude:a.latitude,longitude:a.longitude}:void 0,routeDeviationThreshold:100,className:"w-full h-full"}),!a&&(0,n.jsxs)("div",{className:"absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg z-10",children:[(0,n.jsx)("div",{className:"flex items-center space-x-2 text-sm",children:l?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"}),(0,n.jsx)("span",{className:"text-blue-600",children:"Получение геолокации..."})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"w-4 h-4 bg-gray-400 rounded-full"}),(0,n.jsx)("span",{className:"text-gray-600",children:"Геолокация недоступна"})]})}),o&&(0,n.jsx)("p",{className:"text-xs mt-1 text-red-500",children:o})]})]})},G=({orderId:e})=>{let t=(0,a.useRouter)(),r=(0,a.useSearchParams)(),s=e||r.get("orderId");r.get("orderType");let{orderData:c,isLoading:f,error:j,refetch:y}=m(s||""),{location:C}=(0,x.t0)(),{refreshRides:w}=(0,p.g0)(),{startLocation:N,endLocation:M,additionalStops:P,isLoading:S}=u(c?.order.startLocationId,c?.order.endLocationId,c?.order.additionalStops),k=async e=>{let t=await l.uE.get(`/Ride/for-order/${e}`);if(!t.data?.id)throw Error("Не удалось получить ID поездки");return t.data.id},D=async e=>{try{if(!c)return;let r=await k(c.order.id);switch(e.id){case"driver-heading":await (0,g.Z6)(r);break;case"driver-arrived":await (0,g.wp)(r);break;case"ride-started":await (0,g.uW)(r);break;case"ride-finished":await (0,g.$z)(r),await w();break;case"cancel":if(!global.confirm?.("Вы уверены, что хотите отменить поездку?"))return;await (0,g.pL)(r),await w(),setTimeout(()=>{t.push("/")},1e3);break;default:return}o.P.success("Действие выполнено успешно"),await y(),"ride-finished"===e.id&&setTimeout(()=>{t.push("/")},1e3)}catch(e){console.error("Ошибка выполнения действия:",e),o.P.error("Не удалось выполнить действие")}},R=(0,i.useMemo)(()=>c?.order.passengers?c.order.passengers.map(e=>({id:e.id,firstName:e.firstName,isMainPassenger:e.isMainPassenger})):[],[c?.order.passengers]);return s?f||S?(0,n.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,n.jsx)("p",{children:"Загрузка данных поездки..."})]})}):j||!c?(0,n.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"mb-4",children:j||"Данные заказа не найдены"}),(0,n.jsx)("button",{onClick:()=>t.push("/"),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:"Вернуться к заявкам"})]})}):(0,n.jsx)("div",{className:"h-full flex flex-col p-2 bg-gray-50 overflow-y-auto",children:(0,n.jsxs)("div",{className:"relative w-full h-full bg-gray-100 rounded-lg border border-gray-200",children:[(0,n.jsx)(h,{order:c.order,availableActions:(()=>{let e=[];return c&&(c.order.status===d.Re.InProgress&&c.order.subStatus===d.lk.DriverHeading&&e.push({id:"driver-arrived",label:"Я прибыл",endpoint:"",method:"POST",variant:"primary"}),c.order.status===d.Re.InProgress&&c.order.subStatus===d.lk.DriverArrived&&e.push({id:"ride-started",label:"Взял пассажира",endpoint:"",method:"POST",variant:"primary"}),c.order.status===d.Re.InProgress&&c.order.subStatus===d.lk.RideStarted&&e.push({id:"ride-finished",label:"Завершить поездку",endpoint:"",method:"POST",variant:"primary"}),c.order.status!==d.Re.Completed&&c.order.status!==d.Re.Cancelled&&e.push({id:"cancel",label:"Отменить",endpoint:"",method:"POST",variant:"danger"})),e})(),onActionExecute:D}),(0,n.jsx)(Q,{_ride:c.order,startLocation:N,endLocation:M,additionalStops:P}),(0,n.jsx)(v,{startLocation:N,endLocation:M,additionalStops:P,driverLocation:C,orderStatus:c.order.status,orderSubStatus:c.order.subStatus}),R.length>0&&(0,n.jsx)(b,{passengers:R,services:c.order.services||[],initialPrice:c.order.initialPrice})]})}):(0,n.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"mb-4",children:"Не указан ID заказа"}),(0,n.jsx)("button",{onClick:()=>t.push("/"),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:"Вернуться к заявкам"})]})})},U=({orderId:e})=>(0,n.jsx)("div",{className:"h-full",children:(0,n.jsx)(G,{orderId:e})})},93809:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(38828);function i(e){return Array.isArray(e)?e[0]:e}var s=r(15774);function a({searchParams:e}){let t=i(e.orderId),r=i(e.rideId);if(console.log("\uD83D\uDD0D Query params:",{orderId:t,rideId:r}),!t&&!r)return(0,n.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"Не указаны параметры поездки"}),(0,n.jsx)("a",{href:"/",className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:"Вернуться к заявкам"})]})});let a=t||r,l=r||t;return(0,n.jsx)(s.ActiveRidePage,{orderId:a,rideId:l})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[191,5208,9455,6596,4680,99,3639,2960,9100,1738,6527],()=>r(58324));module.exports=n})();