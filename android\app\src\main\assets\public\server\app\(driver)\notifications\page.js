(()=>{var e={};e.id=3457,e.ids=[3457],e.modules={321:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>n});var s=t(38828),i=t(89019);async function o(){return(0,s.jsx)(i.NotificationsPage,{})}let n={title:"",description:""}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32292:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(24332),i=t(48819),o=t(67851),n=t.n(o),a=t(97540),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["(driver)",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,321)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94058)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\(driver)\\notifications\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(driver)/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},49517:(e,r,t)=>{Promise.resolve().then(t.bind(t,89019))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64303:(e,r,t)=>{"use strict";t.r(r),t.d(r,{NotificationsPage:()=>x});var s=t(13486),i=t(2984),o=t(60159),n=t(70873),a=t(31071),l=t(13879);let d=({notifications:e,isLoading:r,isLoadingMore:t,error:i,hasMore:n,onLoadMore:a,onNotificationClick:d,onRefresh:c,onGoToRide:x,onGoToOrderDetails:p,onDelete:u,expandedNotificationId:m,onToggleExpanded:h})=>{let f=(0,o.useRef)(null),v=(0,o.useRef)(null),g=(0,o.useCallback)(()=>{f.current&&f.current.disconnect(),f.current=new IntersectionObserver(e=>{e[0].isIntersecting&&n&&!t&&!r&&a()},{threshold:.1,rootMargin:"100px"}),v.current&&f.current.observe(v.current)},[n,t,r,a]);return((0,o.useEffect)(()=>(g(),()=>{f.current&&f.current.disconnect()}),[g]),i)?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Ошибка загрузки уведомлений"}),(0,s.jsx)("button",{onClick:c,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Попробовать снова"})]})}):r&&0===e.length?(0,s.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Загрузка..."})}):0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-gray-500",children:"У вас пока нет уведомлений"})}):(0,s.jsxs)("div",{className:"flex flex-col gap-2 overflow-y-auto h-full p-2 rounded-2xl",children:[e.map(e=>(0,s.jsx)(l.Ow,{notification:e,priority:(0,l.CU)(e.type),onClick:d,onGoToRide:x,onGoToOrderDetails:p,onDelete:u,isExpanded:m===e.id,onToggleExpanded:h},e.id)),n&&(0,s.jsx)("div",{ref:v,className:"flex items-center justify-center py-4",children:t?(0,s.jsxs)("div",{className:"flex items-center gap-2 text-gray-500",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,s.jsx)("span",{children:"Загрузка..."})]}):(0,s.jsx)("button",{onClick:a,className:"px-4 py-2 text-blue-600 hover:text-blue-700 transition-colors",children:"Загрузить еще"})}),!n&&e.length>0&&(0,s.jsx)("div",{className:"text-center py-4 text-gray-500 text-sm",children:"Все уведомления загружены"})]})},c=({filterKey:e,label:r,icon:t,count:i,isActive:o,bgColor:n,borderColor:a,textColor:l,badgeColor:d,onClick:c})=>(0,s.jsx)("div",{onClick:c,className:`
        relative w-[268px] h-[85px] rounded-lg cursor-pointer transition-all duration-200
        ${n}
        ${o?`border-[1.5px] ${a}`:"border border-gray-200"}
        hover:shadow-md
      `,style:{backgroundColor:n.startsWith("bg-[")?n.slice(3,-1):void 0,borderColor:o&&a.startsWith("border-[")?a.slice(7,-1):void 0,borderWidth:o?"1.5px":"1px"},children:(0,s.jsxs)("div",{className:"flex items-center h-full px-4",children:[(0,s.jsx)("div",{className:"text-2xl mr-3",children:t}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("h3",{className:`font-medium text-base ${l}`,children:r})}),i>0&&(0,s.jsx)("div",{className:`
            absolute -top-2 -right-2 
            ${d} text-white 
            rounded-full min-w-[24px] h-6 
            flex items-center justify-center 
            text-sm font-bold
            px-2
          `,children:i>99?"99+":i})]})}),x=()=>{let e=(0,i.useRouter)(),[r,t]=(0,o.useState)("order"),[l,x]=(0,o.useState)(null),{notifications:p,isLoading:u,isLoadingMore:m,error:h,hasMore:f,unreadCount:v,unreadCountsByPriority:g,totalCount:b,actions:j}=(0,a.M)(),y=async e=>{try{await j.deleteNotification(e),console.log("✅ Уведомление удалено:",e)}catch(e){console.error("❌ Ошибка удаления уведомления:",e)}},N=p.filter(e=>(0,n.CU)(e.type)===r);return(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsx)("div",{className:"border-gray-200",children:(0,s.jsx)("div",{className:"w-full flex items-center justify-between",children:(0,s.jsxs)("div",{className:"w-full px-3 py-2 flex flex-row justify-between items-center",children:[(0,s.jsx)("div",{className:"flex flex-row gap-4 items-center",children:(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Уведомления"})}),v>0&&(0,s.jsxs)("div",{className:"bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:[v," новых"]})]})})}),(0,s.jsx)("div",{children:(0,s.jsx)("div",{className:"flex flex-row gap-4 overflow-x-hidden px-3 py-2",children:n._J.map(e=>(0,s.jsx)(c,{filterKey:e.key,label:e.label,icon:e.icon,count:g[e.key],isActive:r===e.key,bgColor:e.bgColor,borderColor:e.borderColor,textColor:e.textColor,badgeColor:e.badgeColor,onClick:()=>t(e.key)},e.key))})}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden p-2",children:0===N.length&&p.length>0&&!u?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD0D"})}),(0,s.jsxs)("h3",{className:"text-lg font-medium  mb-2",children:["Нет уведомлений в категории “",n._J.find(e=>e.key===r)?.label,"”"]}),(0,s.jsx)("p",{children:"Попробуйте выбрать другую категорию"})]}):(0,s.jsx)(d,{notifications:N,isLoading:u,isLoadingMore:m,error:h,hasMore:f,onLoadMore:j.loadMore,onNotificationClick:e=>{console.log("\uD83D\uDD14 Клик по уведомлению:",e),e.isRead||j.markAsRead(e.id)},onRefresh:j.refresh,onGoToRide:(r,t,s)=>{console.log("\uD83D\uDE97 Переход к поездке:",{orderId:r,rideId:t,orderType:s});let i=s?`&orderType=${s}`:"",o=t?`/active-ride?orderId=${r}&rideId=${t}${i}`:`/active-ride?orderId=${r}${i}`;e.push(o)},onGoToOrderDetails:(r,t,s)=>{console.log("\uD83D\uDCCB Переход к детальному просмотру заказа:",{orderId:r,rideId:t,orderType:s});let i=s?`&orderType=${s}`:"",o=t?`/active-ride?orderId=${r}&rideId=${t}${i}`:`/active-ride?orderId=${r}${i}`;e.push(o)},onDelete:y,expandedNotificationId:l,onToggleExpanded:e=>{console.log("\uD83D\uDCCB Переключение расширения:",e),x(l===e.id?null:e.id)}})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89019:(e,r,t)=>{"use strict";t.r(r),t.d(r,{NotificationsPage:()=>s});let s=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call NotificationsPage() from the server but NotificationsPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\pages\\driver\\notifications\\NotificationsPage.tsx","NotificationsPage")},89837:(e,r,t)=>{Promise.resolve().then(t.bind(t,64303))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,5208,9455,6596,4680,99,3639,2960,9100,1738],()=>t(32292));module.exports=s})();