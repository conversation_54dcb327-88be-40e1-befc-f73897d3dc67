import type { GetOrdersParams } from '../interface/GetOrdersParams';
import {
  ORDER_TYPES,
  ORDER_STATUSES,
  TIME_OPERATORS,
  SORT_ORDER,
} from '../interface/GetOrdersParams';

/**
 * Вспомогательные функции для работы с сортировкой и фильтрацией заказов (согласно реальному API)
 */

/**
 * Создает параметры для получения "свежих" заказов (сортировка по ScheduledTime DESC)
 */
export function getLatestOrdersParams(size: number = 20): GetOrdersParams {
  return {
    SortBy: 'ScheduledTime',
    SortOrder: SORT_ORDER.DESC,
    Size: size,
    First: true,
  };
}

/**
 * Создает параметры для получения заказов запланированных после определенной даты
 */
export function getOrdersScheduledAfterParams(
  after: Date | string,
  options?: Partial<GetOrdersParams>,
): GetOrdersParams {
  const afterISO = typeof after === 'string' ? after : after.toISOString();

  return {
    ScheduledTime: afterISO,
    ScheduledTimeOp: TIME_OPERATORS.GREATER_THAN_OR_EQUAL,
    SortBy: 'ScheduledTime',
    SortOrder: SORT_ORDER.DESC,
    ...options,
  };
}

/**
 * Создает параметры для получения заказов запланированных до определенной даты
 */
export function getOrdersScheduledBeforeParams(
  before: Date | string,
  options?: Partial<GetOrdersParams>,
): GetOrdersParams {
  const beforeISO = typeof before === 'string' ? before : before.toISOString();

  return {
    ScheduledTime: beforeISO,
    ScheduledTimeOp: TIME_OPERATORS.LESS_THAN_OR_EQUAL,
    SortBy: 'ScheduledTime',
    SortOrder: SORT_ORDER.DESC,
    ...options,
  };
}

/**
 * Создает параметры для получения запланированных заказов на сегодня
 */
export function getTodayScheduledOrdersParams(): GetOrdersParams {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  return getOrdersScheduledAfterParams(startOfDay, {
    Type: [ORDER_TYPES.SCHEDULED],
  });
}

/**
 * Создает параметры для получения запланированных заказов на завтра
 */
export function getTomorrowScheduledOrdersParams(): GetOrdersParams {
  const tomorrow = new Date();

  tomorrow.setDate(tomorrow.getDate() + 1);

  const startOfDay = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate());

  return getOrdersScheduledAfterParams(startOfDay, {
    Type: [ORDER_TYPES.SCHEDULED],
  });
}

/**
 * Создает параметры для получения заказов за текущую неделю
 */
export function getThisWeekOrdersParams(): GetOrdersParams {
  const today = new Date();
  const firstDayOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));

  // Устанавливаем время
  firstDayOfWeek.setHours(0, 0, 0, 0);

  return getOrdersScheduledAfterParams(firstDayOfWeek);
}

/**
 * Создает параметры для получения заказов с определенным статусом
 */
export function getOrdersByStatusParams(
  statuses: string[],
  options?: Partial<GetOrdersParams>,
): GetOrdersParams {
  return {
    status: statuses,
    statusOp: 'Equals',
    SortBy: 'ScheduledTime',
    SortOrder: SORT_ORDER.DESC,
    ...options,
  };
}

/**
 * Создает параметры для получения истекших заказов (как в примере пользователя)
 */
export function getExpiredOrdersParams(size: number = 100): GetOrdersParams {
  return {
    status: [ORDER_STATUSES.EXPIRED],
    statusOp: 'Equals',
    Size: size,
    First: true,
  };
}

/**
 * Форматирует дату в ISO строку для API
 */
export function formatDateForAPI(date: Date): string {
  return date.toISOString();
}

/**
 * Парсит дату из ISO строки
 */
export function parseDateFromAPI(dateString: string): Date {
  return new Date(dateString);
}

/**
 * Проверяет, является ли заказ запланированным на сегодня
 */
export function isOrderScheduledToday(scheduledTime: string | null): boolean {
  if (!scheduledTime) return false;

  const orderDate = new Date(scheduledTime);
  const today = new Date();

  return (
    orderDate.getFullYear() === today.getFullYear() &&
    orderDate.getMonth() === today.getMonth() &&
    orderDate.getDate() === today.getDate()
  );
}

/**
 * Возвращает человеко-читаемое описание временного диапазона
 */
export function getDateRangeDescription(from: string, to: string): string {
  const fromDate = new Date(from);
  const toDate = new Date(to);

  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };

  return `${fromDate.toLocaleDateString('ru-RU', options)} - ${toDate.toLocaleDateString('ru-RU', options)}`;
}
