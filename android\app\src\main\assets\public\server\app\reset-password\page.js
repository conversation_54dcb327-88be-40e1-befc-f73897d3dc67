"use strict";(()=>{var e={};e.id=4700,e.ids=[4700],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},15418:(e,r,s)=>{s.r(r),s.d(r,{default:()=>p,metadata:()=>i});var t=s(38828);s(61365);var o=s(72437);let a=()=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(o.FN,{})});function p(){return(0,t.jsx)(a,{})}let i={title:"Сброс пароля | Compass 2.0",description:"Сброс пароля в системе Compass 2.0"}},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65064:(e,r,s)=>{s.r(r),s.d(r,{GlobalError:()=>p.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>x,tree:()=>d});var t=s(24332),o=s(48819),a=s(67851),p=s.n(a),i=s(97540),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(r,n);let d={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,15418)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\reset-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,20685)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,87239)),"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\reset-password\\page.tsx"],l={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")},79748:e=>{e.exports=require("fs/promises")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},94735:e=>{e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[191,5208,9455,6596,4680,3639,2960,9100,1466,7015],()=>s(65064));module.exports=t})();