import { baseCarFormConfig } from '@entities/cars/forms';
import type { FormConfig } from '@entities/collections/types/formTypes';

/**
 * Реестр конфигураций форм для сущности cars
 */
export const carFormConfigs = {
  base: baseCarFormConfig,
  create: baseCarFormConfig,
  update: baseCarFormConfig,
};
/**
 * Общая конфигурация формы для сущности cars
 */
export const carFormConfig: FormConfig = baseCarFormConfig;
