(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2886],{64917:(e,s,a)=>{Promise.resolve().then(a.bind(a,75513))},75513:(e,s,a)=>{"use strict";a.d(s,{MyCarPage:()=>x});var t=a(54568),l=a(7620),i=a(66478),r=a(27736),n=a(80745);class c extends r.vA{async getCar(e){let s=await this.get("/".concat(e));return this.handleApiResult(s)}async getCarSafe(e){let s=await this.get("/".concat(e));return this.handleApiResultSafe(s)}async getCars(e){let s=new URLSearchParams;(null==e?void 0:e.first)!==void 0&&s.append("first",e.first.toString()),(null==e?void 0:e.before)&&s.append("before",e.before),(null==e?void 0:e.after)&&s.append("after",e.after),(null==e?void 0:e.last)!==void 0&&s.append("last",e.last.toString()),(null==e?void 0:e.size)!==void 0&&s.append("size",e.size.toString()),(null==e?void 0:e.search)&&s.append("search",e.search),(null==e?void 0:e.make)&&s.append("make",e.make),(null==e?void 0:e.model)&&s.append("model",e.model),(null==e?void 0:e.available)!==void 0&&s.append("available",e.available.toString()),(null==e?void 0:e.driverId)&&s.append("driverId",e.driverId);let a=s.toString()?"?".concat(s.toString()):"",t=await this.get(a);return this.handleApiResult(t)}async getMyCars(){let e=await this.get("/my");return this.handleApiResult(e)}async getMyCarsSafe(){let e=await this.get("/my");return this.handleApiResultSafe(e)}async setActiveCar(e){let s=await this.post("/my/".concat(e,"/set-active"));this.handleApiResult(s)}async setInactiveCar(){let e=await this.post("/my/set-inactive");this.handleApiResult(e)}async createCar(e){let s=await this.post("",e);return this.handleApiResult(s)}async updateCar(e,s){let a=await this.put("/".concat(e),s);return this.handleApiResult(a)}async deleteCar(e){let s=await this.delete("/".concat(e));this.handleApiResult(s)}constructor(...e){super(...e),this.baseUrl=n.QQ.CAR.LIST}}let d=new c,h=()=>{let[e,s]=(0,l.useState)([]),[a,t]=(0,l.useState)(!0),[i,r]=(0,l.useState)(null),n=async()=>{try{t(!0),r(null);let e=await d.getMyCars();s(e.data)}catch(e){r(e.message||"Ошибка загрузки автомобилей")}finally{t(!1)}},c=async e=>{try{await d.setActiveCar(e),await n()}catch(e){throw Error(e.message||"Ошибка активации автомобиля")}},h=async()=>{try{await d.setInactiveCar(),await n()}catch(e){throw Error(e.message||"Ошибка деактивации автомобиля")}};return(0,l.useEffect)(()=>{n()},[]),{cars:e,loading:a,error:i,refetch:n,setActiveCar:c,setInactiveCar:h}},x=()=>{var e;let{cars:s,loading:a,error:r,refetch:n,setActiveCar:c,setInactiveCar:d}=h(),{driverProfile:x,refreshDriverData:m}=(0,i.g0)(),[u,o]=(0,l.useState)(!1),[p,f]=(0,l.useState)(null);(0,l.useLayoutEffect)(()=>{a||o(!0)},[a]);let g=(null==x||null==(e=x.activeCar)?void 0:e.id)||(null==x?void 0:x.activeCarId),b=async(e,s)=>{f(e.id);try{s?await c(e.id):await d(),await m()}catch(e){alert(e.message)}finally{f(null)}};return a||!u?(0,t.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{children:"Загрузка автомобилей..."})]})}):r?(0,t.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:"⚠️"})}),(0,t.jsx)("h3",{className:"text-lg font-medium  mb-2",children:"Ошибка загрузки"}),(0,t.jsx)("p",{className:"mb-4",children:r}),(0,t.jsx)("button",{onClick:n,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Попробовать снова"})]})}):(0,t.jsxs)("div",{className:"h-full flex flex-col",children:[(0,t.jsxs)("div",{className:"border-b border-gray-200 p-4",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Мои автомобили"}),(0,t.jsxs)("p",{children:["Всего автомобилей: ",s.length]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-auto p-4",children:0===s.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("h3",{className:"text-lg font-medium  mb-2",children:"У вас нет автомобилей"}),(0,t.jsx)("p",{children:"Обратитесь к администратору для назначения автомобилей"})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:s.map(e=>{let s=g===e.id,a=p===e.id;return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow border p-4 ".concat(s?"border-blue-500 ring-2 ring-blue-200":"border-gray-200"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold",children:[e.make," ",e.model]}),(0,t.jsxs)("p",{className:"text-sm",children:[e.year," • ",e.licensePlate]})]}),s&&(0,t.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:"Активный"})]}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Цвет:"}),(0,t.jsx)("span",{className:"text-sm",children:e.color})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Тип:"}),(0,t.jsx)("span",{className:"text-sm",children:e.type})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Класс:"}),(0,t.jsx)("span",{className:"text-sm",children:e.serviceClass})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Статус:"}),(0,t.jsx)("span",{className:"text-sm",children:e.status})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Мест:"}),(0,t.jsx)("span",{className:"text-sm",children:e.passengerCapacity})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-3 border-t border-gray-200",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Активировать автомобиль"}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",checked:s,disabled:a,onChange:s=>b(e,s.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 disabled:opacity-50"}),a&&(0,t.jsx)("div",{className:"ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"})]})]})]},e.id)})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7261,3766,1938,2906,6467,2634,3822,587,8315,7358],()=>s(64917)),_N_E=e.O()}]);