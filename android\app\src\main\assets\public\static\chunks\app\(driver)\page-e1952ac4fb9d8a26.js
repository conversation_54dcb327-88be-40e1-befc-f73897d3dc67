(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[450],{35035:(e,s,t)=>{"use strict";t.d(s,{I:()=>r});let r=(0,t(7620).createContext)(null)},48484:(e,s,t)=>{"use strict";t.d(s,{Hq:()=>g,c7:()=>m});var r=t(62942),l=t(7620),i=t(66478);let n=null,d=[],a=!1,c=[],o=e=>{n=e,d.forEach(s=>s(e))},u=e=>{a=e,c.forEach(s=>s(e))},x=e=>(c.push(e),()=>{c=c.filter(s=>s!==e)}),h=e=>(d.push(e),()=>{d=d.filter(s=>s!==e)}),m=()=>{let e=(0,r.useRouter)(),[s,t]=(0,l.useState)(n),[d,c]=(0,l.useState)(a),{driverId:m,activeRides:g,isInitialized:f,isLoadingRides:v,refreshRides:j}=(0,i.g0)();(0,l.useEffect)(()=>{let e=h(e=>{t(e)}),s=x(e=>{c(e)});return a&&!d&&c(!0),()=>{e(),s()}},[d]),(0,l.useEffect)(()=>{if(f&&g.length>0){let e=g[0],t={orderId:e.orderId||e.id,rideId:e.id,status:e.status,ride:e};s&&s.rideId===t.rideId||o(t)}else f&&0===g.length&&s&&(console.log("\uD83D\uDDD1️ Нет активных поездок в централизованных данных, очищаем локальное состояние"),o(null))},[g,f,s]);let p=(0,l.useCallback)(async()=>(f&&m?(c(!0),u(!0)):console.log("⏳ Ждем инициализации водителя для проверки активных поездок..."),s),[s,m,f]),b=(0,l.useCallback)((e,s)=>{console.log("\uD83D\uDE80 setActiveRideData вызван - устанавливаем активную поездку глобально");let t={orderId:e,rideId:s,status:"Accepted",ride:null};o(t),console.log("✅ Активная поездка установлена глобально:",t)},[]),N=(0,l.useCallback)(async()=>(await j(),await p()),[p,j]),y=(0,l.useCallback)(()=>{console.log("\uD83D\uDDD1️ clearActiveRide вызван - очищаем активную поездку глобально"),localStorage.removeItem("activeRide"),o(null),console.log("✅ Активная поездка очищена глобально")},[]),R=(0,l.useCallback)(()=>{if(s){let t=s.orderType?"&orderType=".concat(s.orderType):"",r="/active-ride/?orderId=".concat(s.orderId,"&rideId=").concat(s.rideId).concat(t);e.push(r)}},[s,e]);return{activeRide:s,hasActiveRide:!!s,isLoading:v,isInitialized:d,isRideActive:e=>["Accepted","DriverHeading","Arrived","InProgress"].includes(e),checkActiveRides:p,setActiveRideData:b,clearActiveRide:y,navigateToActiveRide:R,forceUpdateActiveRide:N}},g=(e,s)=>{console.log("\uD83C\uDF0D setActiveRideGlobally вызван:",{orderId:e,rideId:s}),o({orderId:e,rideId:s,status:"Accepted",ride:null}),console.log("✅ Активная поездка установлена глобально через прямой вызов")}},59959:(e,s,t)=>{"use strict";t.d(s,{k:()=>i});var r=t(7620),l=t(35035);function i(){let e=(0,r.useContext)(l.I);if(!e)throw Error("useSignalR must be used within a SignalRProvider");return e}},62942:(e,s,t)=>{"use strict";var r=t(42418);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},72390:(e,s,t)=>{"use strict";t.r(s),t.d(s,{OrderPage:()=>u});var r=t(54568),l=t(62942),i=t(7620),n=t(1839),d=t(48484),a=t(32959),c=t(97414);let o=e=>{let{showPosition:s=!0}=e,t=(0,l.useRouter)(),[n,d]=(0,i.useState)(!1),{isInQueue:o,position:u,isLoading:x,isActionLoading:h,error:m,actions:g}=(0,a.o)(),{hasUpcomingRideWithin1_5Hours:f,hoursUntilNextRide:v,hasOverdueRequestedRides:j,overdueRidesCount:p,isLoading:b}=(0,c.U)(),N=!o&&(f||j);return((0,i.useLayoutEffect)(()=>{x||b||d(!0)},[x,b]),n)?x?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,r.jsx)("span",{className:"ml-2 text-sm",children:"Проверка статуса..."})]}):m?(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("p",{className:"text-red-600 text-sm mb-2",children:["Ошибка: ",m]}),(0,r.jsx)("button",{onClick:g.refresh,className:"px-3 py-1 text-xs bg-gray-200  rounded hover:bg-gray-300 transition-colors",children:"Повторить"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[o&&s&&null!==u&&(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm",children:"Позиция в очереди"}),(0,r.jsxs)("div",{className:"text-lg font-bold text-blue-600",children:["№",u]})]}),(0,r.jsx)("button",{onClick:()=>{if(o)g.leaveQueue();else{if(N)return void t.push("/scheduled-orders");g.joinQueue()}},disabled:h||N,className:"\n          w-full px-4 py-3 rounded-lg font-medium transition-all duration-200\n          ".concat(N?j?"bg-red-500 text-white cursor-not-allowed":"bg-orange-500 text-white cursor-not-allowed":o?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white","\n          ").concat(h||N?"opacity-75":"hover:shadow-md","\n        "),children:h?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),o?"Выходим...":"Входим..."]}):N?j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"text-lg mr-2",children:"⚠️"}),"У вас просроченные заказы (",p,")"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"text-lg mr-2",children:"\uD83D\uDEAB"}),"У вас запланированный заказ"]}):(0,r.jsx)(r.Fragment,{children:o?(0,r.jsx)(r.Fragment,{children:"Выйти из очереди"}):(0,r.jsx)(r.Fragment,{children:"Встать в очередь"})})}),N?j?(0,r.jsxs)("div",{className:"text-center text-xs text-red-600 bg-red-50 p-2 rounded",children:["Вход в очередь временно отключен.",(0,r.jsx)("br",{}),"Сначала обработайте просроченные заказы."]}):(0,r.jsxs)("div",{className:"text-center text-xs text-orange-600 bg-orange-50 p-2 rounded",children:["Вход в очередь временно отключен.",(0,r.jsx)("br",{}),"Запланированная поездка через ",v?Math.round(60*v):"?"," мин."]}):o?(0,r.jsx)("div",{className:"text-center text-xs text-gray-500",children:"Вы получите уведомление, когда подойдет ваша очередь"}):null]}):(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsx)("div",{className:"w-full px-4 py-3 rounded-lg bg-gray-200 animate-pulse",children:(0,r.jsx)("div",{className:"h-6 bg-gray-300 rounded"})})})},u=()=>{let e=(0,l.useRouter)(),{activeRide:s,isLoading:t,isInitialized:i}=(0,d.c7)();return!i||t?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"}),(0,r.jsx)("p",{children:"Загрузка..."})]})}):(0,r.jsxs)("div",{className:"h-full flex flex-col p-4",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Доступные заказы"}),(0,r.jsx)("p",{children:"Выберите заказ для выполнения"})]}),s&&(0,r.jsx)("div",{className:"g-full flex justyfy-center items-center",children:(0,r.jsxs)("div",{className:"p-4 w-full flex flex-col items-center justify-between gap-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)("h3",{className:"font-medium text-blue-900",children:"У вас есть активная поездка"})}),(0,r.jsxs)("button",{onClick:()=>e.push("/active-ride?orderId=".concat(s.orderId,"&rideId=").concat(s.rideId)),className:"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"Перейти к поездке"}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})}),(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[!s&&(0,r.jsx)("div",{className:"h-full flex flex-col justify-center items-center",children:(0,r.jsx)(o,{})}),!s&&(0,r.jsx)("div",{className:"space-y-4",children:[].map(e=>(0,r.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsxs)("span",{className:"text-lg font-semibold",children:["Заказ #",e.id.slice(-8).toUpperCase()]}),(0,r.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full",children:(0,n.jY)(e.status)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Расстояние:"})," ",e.distance?"".concat(e.distance," км"):"Не указано"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Длительность:"})," ",e.duration||"Не указано"]})]}),e.waypoints&&(0,r.jsxs)("div",{className:"mt-2 text-sm",children:[(0,r.jsx)("span",{className:"font-medium",children:"Точек маршрута:"})," ",Array.isArray(e.waypoints)?e.waypoints.length:1]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("button",{className:"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium",children:"Принять заказ"}),(0,r.jsx)("button",{className:"bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm font-medium",children:"Подробнее"})]})]})},e.id))})]})]})}},97299:(e,s,t)=>{Promise.resolve().then(t.bind(t,72390))},97414:(e,s,t)=>{"use strict";t.d(s,{U:()=>n});var r=t(7620),l=t(59959),i=t(34744);function n(){let e=(0,l.k)(),[s,t]=(0,r.useState)(0),{data:n,isLoading:d,error:a,refetch:c}=(0,i.C)({size:50,status:["Requested","Searching","Accepted","Arrived","InProgress"]});(0,r.useEffect)(()=>{if(e.isConnected){let s=e=>{console.log("\uD83D\uDCC5 Новая запланированная поездка:",e),(null==e?void 0:e.orderType)==="Scheduled"&&(t(e=>e+1),c())};return e.on("RideRequestNotification",s),e.on("OrderStatusChanged",s),e.on("DriverNotification",s),()=>{e.off("RideRequestNotification",s),e.off("OrderStatusChanged",s),e.off("DriverNotification",s)}}},[e,c]);let o=(0,r.useCallback)(()=>{t(0)},[]),{hasUpcomingRideWithin1_5Hours:u,hoursUntilNextRide:x,hasOverdueRequestedRides:h,overdueRidesCount:m}=(0,r.useMemo)(()=>{if(!n||0===n.length)return{hasUpcomingRideWithin1_5Hours:!1,hoursUntilNextRide:null,hasOverdueRequestedRides:!1,overdueRidesCount:0};let e=new Date().getTime()+216e5,s=n.map(s=>({...s,timeUntilRide:new Date(s.scheduledTime).getTime()-e})),t=s.filter(e=>e.timeUntilRide>0&&("Requested"===e.status||"Accepted"===e.status)).sort((e,s)=>e.timeUntilRide-s.timeUntilRide),r=s.filter(e=>e.timeUntilRide<=0&&"Requested"===e.status),l=!1,i=null;return t.length>0&&(l=(i=t[0].timeUntilRide/36e5)<=1.5),{hasUpcomingRideWithin1_5Hours:l,hoursUntilNextRide:i,hasOverdueRequestedRides:r.length>0,overdueRidesCount:r.length}},[n]);return{newScheduledRidesCount:s,totalScheduledRidesCount:n.length,hasUpcomingRideWithin1_5Hours:u,hoursUntilNextRide:x,hasOverdueRequestedRides:h,overdueRidesCount:m,isLoading:d,error:a,resetNewRidesCount:o,refetch:c}}}},e=>{var s=s=>e(e.s=s);e.O(0,[7261,3766,1938,2906,6467,2634,3822,8128,587,8315,7358],()=>s(97299)),_N_E=e.O()}]);