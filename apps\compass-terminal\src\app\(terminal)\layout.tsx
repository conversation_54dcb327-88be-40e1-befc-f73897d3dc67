import { SignalRProvider , TerminalDataProvider , TerminalTariffProvider , TerminalLocationsProvider } from '@app/providers';
import { AnimatedBackground } from "@shared/animated";
import { getRawCookie } from '@shared/lib/parse-cookie';
import { TerminalReceiptProvider } from '@entities/order/context';
import { TerminalFooter } from '@widgets/footer';
import { HeroBanner } from '@widgets/hero-banner';
import { ModalManagerComponent } from '@widgets/modals';
import { initializeTerminalComponents } from '@compass-terminal/config/register-terminal-components';

export default async function ClientLayout({ children }: { children: React.ReactNode }) {
  const accessToken = await getRawCookie('.AspNetCore.Identity.Application');

  return (
    <SignalRProvider accessToken={accessToken || undefined}>
      <TerminalDataProvider>
        <TerminalTariffProvider>
          <TerminalLocationsProvider>
            <TerminalReceiptProvider>
              <AnimatedBackground />
              <div className="h-full flex flex-col">
                <HeroBanner />
                
                {/* Основной контент - занимает оставшееся пространство */}
                <main className="flex flex-col flex-1 items-center justify-start overflow-y-auto py-10">
                  {children}
                </main>
                
                {/* Footer - всегда внизу */}
                <TerminalFooter />
                
                {/* Модалы - вне основного flow */}
                <ModalManagerComponent />
              </div>
            </TerminalReceiptProvider>
          </TerminalLocationsProvider>
        </TerminalTariffProvider>
      </TerminalDataProvider>
    </SignalRProvider>
  );
}

// Инициализируем компоненты терминала при загрузке модуля
initializeTerminalComponents();
