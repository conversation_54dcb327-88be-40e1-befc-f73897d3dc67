import { BaseApiService } from '@shared/api/base-service';
import { ApiErrorType } from '@shared/api/client';
import { API_ROUTES } from '@shared/api/constants';
import type {
  ILoginRequest,
  ILoginResponse,
  RegisterDTO,
  RefreshTokenDTO,
  ResetPasswordDTO,
  ChangePasswordDTO,
} from '@entities/auth/interface';
import type {
  LoginFormData,
  PartnerRegisterFormData,
  ForgotPasswordFormData,
  ResetPasswordFormData,
} from '@entities/auth/model/schemas';

/**
 * Результат логина с детальной информацией об ошибках
 */
interface LoginWithValidationResult {
  success: boolean;
  error?: string;
  fieldErrors?: Record<string, boolean>;
  data?: ILoginResponse;
}

/**
 * Результат операции с детальной информацией об ошибках
 */
interface AuthOperationResult {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Сервис для работы с аутентификацией
 * Использует BaseApiService для унификации API паттернов
 */
export class AuthService extends BaseApiService {
  protected baseUrl = API_ROUTES.AUTH.BASE;

  /**
   * Авторизация пользователя
   */
  async login(data: ILoginRequest): Promise<ILoginResponse> {
    const result = await this.post<ILoginResponse, ILoginRequest>('/login', data);

    return this.handleApiResult(result);
  }

  /**
   * Авторизация пользователя с детальной обработкой ошибок для хуков
   */
  async loginWithValidation(data: LoginFormData): Promise<LoginWithValidationResult> {
    try {
      const result = await this.post<ILoginResponse, ILoginRequest>('/login', {
        email: data.email,
        password: data.password,
      });

      if (result.error) {
        let errorMessage = result.error.message;
        const fieldErrors: Record<string, boolean> = {};

        // Дополнительная обработка в зависимости от типа ошибки
        if (result.error.type === ApiErrorType.Auth) {
          errorMessage =
            'Неверный email или пароль. Пожалуйста, проверьте правильность учетных данных.';
          fieldErrors.email = true;
          fieldErrors.password = true;
        } else if (result.error.type === ApiErrorType.Network) {
          errorMessage = 'Проблема с сетевым подключением. Проверьте интернет-соединение.';
        } else if (result.error.type === ApiErrorType.Validation) {
          // Если сервер вернул детальную информацию об ошибках валидации
          if (result.error.data && typeof result.error.data === 'object') {
            const errorData = result.error.data as Record<string, unknown>;

            if (errorData.errors && typeof errorData.errors === 'object') {
              const errors = errorData.errors as Record<string, string[]>;

              // Проверяем наличие специфических ошибок и формируем понятные сообщения
              if (errors.UserNotFound && errors.UserNotFound.length > 0) {
                errorMessage = `Пользователь с email '${data.email}' не найден.`;
                fieldErrors.email = true;
              } else if (errors.InvalidEmail && errors.InvalidEmail.length > 0) {
                errorMessage = 'Указан некорректный email.';
                fieldErrors.email = true;
              } else {
                // Для других типов ошибок формируем общее сообщение
                const errorMessages = Object.entries(errors)
                  .map(([field, messages]) => {
                    const fieldName = field === 'RequiredField' ? 'Обязательное поле' : field;

                    return `${fieldName}: ${messages.join(', ')}`;
                  })
                  .join('\n');

                if (errorMessages) {
                  errorMessage = errorMessages;
                }
              }
            }
          }
        }

        return {
          success: false,
          error: errorMessage,
          fieldErrors: Object.keys(fieldErrors).length > 0 ? fieldErrors : undefined,
        };
      }

      return {
        success: true,
        data: this.handleApiResult(result),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Произошла неизвестная ошибка',
      };
    }
  }

  /**
   * Авторизация водителя
   */
  async loginDriver(data: IDriverLoginRequest): Promise<ILoginResponse> {
    const result = await this.post<ILoginResponse, IDriverLoginRequest>('/login/driver', data);

    return this.handleApiResult(result);
  }

  /**
   * Авторизация водителя с детальной обработкой ошибок
   */
  async loginDriverWithValidation(data: DriverLoginFormData): Promise<LoginWithValidationResult> {
    try {
      const result = await this.post<ILoginResponse, IDriverLoginRequest>('/login/driver', {
        phoneNumber: data.phoneNumber,
        licensePlate: data.licensePlate,
        password: data.password,
        twoFactorCode: data.twoFactorCode,
        twoFactorRecoveryCode: data.twoFactorRecoveryCode,
      });

      if (result.error) {
        let errorMessage = result.error.message;
        const fieldErrors: Record<string, boolean> = {};

        if (result.error.type === ApiErrorType.Auth) {
          errorMessage = 'Неверные учетные данные. Проверьте телефон, гос. номер и пароль.';
          fieldErrors.phoneNumber = true;
          fieldErrors.licensePlate = true;
          fieldErrors.password = true;
        } else if (result.error.type === ApiErrorType.Network) {
          errorMessage = 'Проблема с сетевым подключением. Проверьте интернет-соединение.';
        } else if (result.error.type === ApiErrorType.Validation) {
          if (result.error.data && typeof result.error.data === 'object') {
            const errorData = result.error.data as Record<string, unknown>;

            if (errorData.errors && typeof errorData.errors === 'object') {
              const errors = errorData.errors as Record<string, string[]>;

              if (errors.CarNotFound && errors.CarNotFound.length > 0) {
                errorMessage = `Автомобиль с гос. номером '${data.licensePlate}' не найден или не привязан к вашему аккаунту.`;
                fieldErrors.licensePlate = true;
              } else if (errors.DriverNotFound && errors.DriverNotFound.length > 0) {
                errorMessage = `Водитель с номером '${data.phoneNumber}' не найден.`;
                fieldErrors.phoneNumber = true;
              } else {
                const errorMessages = Object.entries(errors)
                  .map(([field, messages]) => messages.join(', '))
                  .join('\n');

                if (errorMessages) {
                  errorMessage = errorMessages;
                }
              }
            }
          }
        }

        return {
          success: false,
          error: errorMessage,
          fieldErrors: Object.keys(fieldErrors).length > 0 ? fieldErrors : undefined,
        };
      }

      return {
        success: true,
        data: this.handleApiResult(result),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Произошла неизвестная ошибка',
      };
    }
  }

  /**
   * Авторизация терминала
   */
  async loginTerminal(data: ITerminalLoginRequest): Promise<ILoginResponse> {
    const result = await this.post<ILoginResponse, ITerminalLoginRequest>('/login/terminal', data);

    return this.handleApiResult(result);
  }

  /**
   * Авторизация терминала с детальной обработкой ошибок
   */
  async loginTerminalWithValidation(
    data: TerminalLoginFormData,
  ): Promise<LoginWithValidationResult> {
    try {
      const result = await this.post<ILoginResponse, ITerminalLoginRequest>('/login/terminal', {
        email: data.email,
        password: data.password,
        twoFactorCode: data.twoFactorCode,
        twoFactorRecoveryCode: data.twoFactorRecoveryCode,
      });

      if (result.error) {
        let errorMessage = result.error.message;
        const fieldErrors: Record<string, boolean> = {};

        if (result.error.type === ApiErrorType.Auth) {
          errorMessage = 'Неверный email или пароль терминала.';
          fieldErrors.email = true;
          fieldErrors.password = true;
        } else if (result.error.type === ApiErrorType.Network) {
          errorMessage = 'Проблема с сетевым подключением. Проверьте интернет-соединение.';
        }

        return {
          success: false,
          error: errorMessage,
          fieldErrors: Object.keys(fieldErrors).length > 0 ? fieldErrors : undefined,
        };
      }

      return {
        success: true,
        data: this.handleApiResult(result),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Произошла неизвестная ошибка',
      };
    }
  }

  /**
   * Регистрация пользователя
   */
  async register(data: RegisterDTO): Promise<ILoginResponse> {
    const result = await this.post<ILoginResponse, RegisterDTO>('/register', data);

    return this.handleApiResult(result);
  }

  /**
   * Регистрация партнера с детальной обработкой ошибок
   */
  async registerPartnerWithValidation(data: PartnerRegisterFormData): Promise<AuthOperationResult> {
    try {
      const result = await this.post<ILoginResponse, PartnerRegisterFormData>(
        '/register/partner',
        data,
      );

      if (result.error) {
        return {
          success: false,
          error: result.error.message,
        };
      }

      return {
        success: true,
        data: this.handleApiResult(result),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Произошла ошибка при регистрации',
      };
    }
  }

  /**
   * Обновление токена
   */
  async refreshToken(data: RefreshTokenDTO): Promise<ILoginResponse> {
    const result = await this.post<ILoginResponse, RefreshTokenDTO>('/refresh', data);

    return this.handleApiResult(result);
  }

  /**
   * Выход из системы
   */
  async logout(): Promise<void> {
    const result = await this.post<void>('/logout');

    this.handleApiResult(result);
  }

  /**
   * Выход из системы с дополнительной логикой очистки
   */
  async logoutWithCleanup(): Promise<AuthOperationResult> {
    try {
      const result = await this.post<void>('/logout');

      if (result.error) {
        return {
          success: false,
          error: result.error.message,
        };
      }

      this.handleApiResult(result);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Произошла ошибка при выходе',
      };
    }
  }

  /**
   * Сброс пароля
   */
  async resetPassword(data: ResetPasswordDTO): Promise<void> {
    const result = await this.post<void, ResetPasswordDTO>('/reset-password', data);

    this.handleApiResult(result);
  }

  /**
   * Запрос на восстановление пароля с детальной обработкой ошибок
   */
  async forgotPasswordWithValidation(data: ForgotPasswordFormData): Promise<AuthOperationResult> {
    try {
      const result = await this.post<void, ForgotPasswordFormData>('/forgot-password', data);

      if (result.error) {
        return {
          success: false,
          error: result.error.message,
        };
      }

      this.handleApiResult(result);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Произошла ошибка при отправке запроса',
      };
    }
  }

  /**
   * Сброс пароля с детальной обработкой ошибок
   */
  async resetPasswordWithValidation(data: ResetPasswordFormData): Promise<AuthOperationResult> {
    try {
      const result = await this.post<void, ResetPasswordFormData>('/reset-password', data);

      if (result.error) {
        return {
          success: false,
          error: result.error.message,
        };
      }

      this.handleApiResult(result);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Произошла ошибка при сбросе пароля',
      };
    }
  }

  /**
   * Изменение пароля
   */
  async changePassword(data: ChangePasswordDTO): Promise<void> {
    const result = await this.post<void, ChangePasswordDTO>('/change-password', data);

    this.handleApiResult(result);
  }

  /**
   * Проверка токена
   */
  async verifyToken(): Promise<boolean> {
    try {
      const result = await this.get<{ valid: boolean }>('/verify');
      const response = this.handleApiResult(result);

      return response.valid;
    } catch {
      return false;
    }
  }

  /**
   * Получение текущего пользователя
   */
  async getCurrentUser(): Promise<any> {
    const result = await this.get<any>('/me');

    return this.handleApiResult(result);
  }
}

// Создаем экземпляр сервиса для использования
export const authService = new AuthService();
