'use client';

import React, { createContext, useContext, type ReactNode } from 'react';
import type { FieldValues } from 'react-hook-form';
import type { ProcessedValidationErrors } from '@shared/utils';
import type { ApiEndpoints } from '@entities/collections/config/apiEndpointRegistry';
import type { ZodValidationSchema } from '@entities/collections/config/validationSchemaRegistry';
import type {
  FormConfig,
  FieldOptionsProviders,
} from '@entities/collections/types/formTypes';
import type { ProcessedUserValidationErrors } from '@entities/users';

/**
 * Интерфейс контекста управления коллекцией
 */
export interface ManageCollectionContextValue<T extends FieldValues = FieldValues> {
  // Основные данные
  collectionName: string;
  mode: 'create' | 'edit';
  role?: string | null;
  id?: string | null;
  
  // Данные формы
  formData: T | null;
  formConfig: FormConfig | null;
  
  // Состояние
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  validationErrors: ProcessedValidationErrors | ProcessedUserValidationErrors | null;
  
  // Конфигурации
  validationSchema: ZodValidationSchema;
  fieldOptionsProviders: FieldOptionsProviders;
  endpoints: ApiEndpoints;
  
  // Функции
  onSubmit: (data: T) => Promise<void>;
  onCancel: () => void;
  onRedirect: (url: string) => void;
  
  // Функции управления состоянием
  setFormData: (data: T) => void;
  setIsLoading: (loading: boolean) => void;
  setIsSaving: (saving: boolean) => void;
  setError: (error: string | null) => void;
  setValidationErrors: (errors: ProcessedValidationErrors | ProcessedUserValidationErrors | null) => void;
}

/**
 * Контекст управления коллекцией
 */
const ManageCollectionContext = createContext<ManageCollectionContextValue | null>(null);

/**
 * Интерфейс пропсов провайдера
 */
interface ManageCollectionProviderProps<T extends FieldValues = FieldValues> {
  children: ReactNode;
  value: ManageCollectionContextValue<T>;
}

/**
 * Провайдер контекста управления коллекцией
 */
export function ManageCollectionProvider<T extends FieldValues = FieldValues>({
  children,
  value,
}: ManageCollectionProviderProps<T>) {
  return (
    <ManageCollectionContext.Provider value={value as ManageCollectionContextValue}>
      {children}
    </ManageCollectionContext.Provider>
  );
}

/**
 * Хук для использования контекста управления коллекцией
 * @returns Контекст управления коллекцией
 * @throws {Error} Если хук используется вне провайдера
 */
export function useManageCollectionContext<T extends FieldValues = FieldValues>(): ManageCollectionContextValue<T> {
  const context = useContext(ManageCollectionContext);
  
  if (!context) {
    throw new Error(
      'useManageCollectionContext должен использоваться внутри ManageCollectionProvider'
    );
  }
  
  return context as ManageCollectionContextValue<T>;
}

/**
 * Хук для получения данных коллекции (только для чтения)
 * @returns Данные коллекции
 */
export function useCollectionData<T extends FieldValues = FieldValues>() {
  const context = useManageCollectionContext<T>();
  
  return {
    collectionName: context.collectionName,
    mode: context.mode,
    role: context.role,
    id: context.id,
    formData: context.formData,
    formConfig: context.formConfig,
    isLoading: context.isLoading,
    isSaving: context.isSaving,
    error: context.error,
    validationErrors: context.validationErrors,
  };
} 