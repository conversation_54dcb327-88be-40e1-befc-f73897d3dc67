"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9795],{19795:(e,r,s)=>{s.r(r),s.d(r,{SelectUserRoleModal:()=>x});var l=s(54568),a=s(41186),o=s(62942);s(7620);var c=s(94387),n=s(11447),i=s(31668),t=s(66517),d=s(24723),u=s(55115),m=s(54912);let x=()=>{let e=(0,o.useRouter)(),r=(0,a.e3)(n.t0),s=(0,a.e3)(n.jB),i=(0,a.e3)(n.Oo),t=(0,a.e3)(n.$A);return(0,l.jsx)("div",{className:"absolute inset-0 z-50 w-full h-full flex justify-between items-center backdrop-filter backdrop-blur-[7px]",children:(0,l.jsxs)("div",{className:"rounded-lg shadow-lg p-6 w-full max-w-6xl mx-auto",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Выбор роли пользователя"}),(0,l.jsx)("button",{onClick:()=>{i(void 0)},className:"w-10 h-10 bg-gray-50/30 rounded-full flex items-center justify-center text-red-500 hover:bg-red-100/30 transition-all duration-200 text-2xl leading-none cursor-pointer",children:(0,l.jsx)(c.default,{size:20})})]}),(0,l.jsx)(h,{selectedRole:r,onRoleSelect:r=>{s(r),t(r),i(void 0),e.push("/users/create")}})]})})};function h(e){let{selectedRole:r,onRoleSelect:s}=e;return(0,l.jsx)("div",{className:"w-full h-full flex flex-col gap-6",children:(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Object.keys(t.D).filter(e=>e!==d.Xh.Unknown).map(e=>(0,l.jsxs)("div",{onClick:()=>s(e),className:"backdrop-blur-md bg-black/80 rounded-lg shadow-[0_0_10px_rgba(0,0,0,0.1)] hover:shadow-[0_0_15px_rgba(0,0,0,0.2)] transition-shadow duration-300 p-6 h-full border cursor-pointer ".concat(r===e?"border-[color:var(--color-primary)] bg-[color:var(--color-primary-light)]":"border-[color:var(--color-neutral-200)] hover:border-[color:var(--color-primary)]"),children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"min-w-12 min-h-12 rounded-full bg-[color:var(--color-neutral-100)] flex items-center justify-center text-[color:var(--color-primary)] border-2 border-[color:var(--color-neutral-100)]",children:(0,l.jsx)(i.J,{iconName:(0,m.mp)(e)})}),(0,l.jsx)("h2",{className:"text-xl font-semibold ml-4",children:(0,u.r4)(e)})]}),(0,l.jsx)("p",{children:(0,m.PA)(e)})]},e))})})}},31668:(e,r,s)=>{s.d(r,{J:()=>x});var l=s(54568),a=s(7620);let o=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,98671))),c=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,76704))),n=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,55211))),i=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,33471))),t=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,36760))),d=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,8382))),u=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,91812))),m=(0,a.lazy)(()=>Promise.resolve().then(s.bind(s,92601))),x=e=>{let{iconName:r,className:s="w-6 h-6",size:x=24}=e,h=(e=>{switch(e){case"DashboardIcon":return(0,l.jsx)(o,{className:s,size:x});case"ProfileIcon":return(0,l.jsx)(c,{className:s,size:x});case"IdentityCardIcon":return(0,l.jsx)(n,{className:s,size:x});case"UsersIcon":return(0,l.jsx)(i,{className:s,size:x});case"VehiclesIcon":return(0,l.jsx)(t,{className:s,size:x});case"TariffsIcon":return(0,l.jsx)(d,{className:s,size:x});case"OrdersIcon":return(0,l.jsx)(u,{className:s,size:x});case"ReferencesIcon":return(0,l.jsx)(m,{className:s,size:x});default:return null}})(r);return h?(0,l.jsx)(a.Suspense,{fallback:(0,l.jsx)("div",{className:s}),children:h}):(0,l.jsx)("div",{className:s})}}}]);