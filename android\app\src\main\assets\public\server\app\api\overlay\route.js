(()=>{var e={};e.id=5838,e.ids=[5838],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39738:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>d,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>u});var o=t(48106),a=t(48819),n=t(12050),i=t(65208),p=t(4235);async function u(e){try{let{opacity:r,blur:t,componentOpacity:s,componentBlur:o,overlayColor:a}=await e.json();if("number"!=typeof r||r<0||r>1)return p.NextResponse.json({error:"Неверное значение прозрачности планшета"},{status:400});if("number"!=typeof t||t<0||t>20)return p.NextResponse.json({error:"Неверное значение размытия планшета"},{status:400});if("number"!=typeof s||s<0||s>1)return p.NextResponse.json({error:"Неверное значение прозрачности компонентов"},{status:400});if("number"!=typeof o||o<0||o>20)return p.NextResponse.json({error:"Неверное значение размытия компонентов"},{status:400});if(a&&"string"!=typeof a)return p.NextResponse.json({error:"Неверное значение цвета планшета"},{status:400});let n=await (0,i.UL)();return n.set("driver-overlay-opacity",r.toString(),{maxAge:31536e3,httpOnly:!1,secure:!0,sameSite:"lax"}),n.set("driver-overlay-blur",t.toString(),{maxAge:31536e3,httpOnly:!1,secure:!0,sameSite:"lax"}),a&&n.set("driver-overlay-color",a,{maxAge:31536e3,httpOnly:!1,secure:!0,sameSite:"lax"}),n.set("driver-component-opacity",s.toString(),{maxAge:31536e3,httpOnly:!1,secure:!0,sameSite:"lax"}),n.set("driver-component-blur",o.toString(),{maxAge:31536e3,httpOnly:!1,secure:!0,sameSite:"lax"}),p.NextResponse.json({success:!0})}catch(e){return console.error("Ошибка при сохранении настроек оверлея:",e),p.NextResponse.json({error:"Внутренняя ошибка сервера"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/overlay/route",pathname:"/api/overlay",filename:"route",bundlePath:"app/api/overlay/route"},resolvedPagePath:"C:\\games\\compass2.0\\apps\\compass-driver\\src\\app\\api\\overlay\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:d}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80408:()=>{},87032:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,5208,3744],()=>t(39738));module.exports=s})();