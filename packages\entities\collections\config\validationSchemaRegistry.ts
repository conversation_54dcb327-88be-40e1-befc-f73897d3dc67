import { z } from 'zod';
import { carCreateSchema, carUpdateSchema } from '@entities/cars/schemas';
import type { CollectionName } from '@entities/collections/config';
import {
  LocationBaseDTOSchema as createLocationSchema,
  GetLocationDTOSchema as updateLocationSchema,
} from '@entities/locations/schemas';
import {
  CreateNotificationDTOSchema,
  GetNotificationDTOSchema,
  ReadNotificationDTOSchema,
} from '@entities/notifications/schemas';
import {
  CreateInstantOrderDTOSchema as createOrderSchema,
  UpdateInstantOrderDTOSchema as updateOrderSchema,
} from '@entities/order/schemas';
import { ServiceDTOSchema as serviceSchema } from '@entities/services/schemas';
import {
  TariffBaseDTOSchema as createTariffSchema,
  TariffBaseDTOSchema as updateTariffSchema,
} from '@entities/tariff/schemas';
import {
  adminCreateSchema,
  customerCreateSchema,
  driverCreateSchema,
  operatorCreateSchema,
  partnerCreateSchema,
  terminalCreateSchema,
  adminUpdateSchema,
  customerUpdateSchema,
  driverUpdateApiSchema,
  operatorUpdateSchema,
  partnerUpdateSchema,
  terminalUpdateSchema,
} from '@entities/users/schemas';

/**
 * Тип для схемы валидации Zod
 */
export type ZodValidationSchema = z.ZodTypeAny;
/**
 * Интерфейс для реестра схем валидации
 */
export interface ValidationSchemaRegistry {
  create: Record<CollectionName, ZodValidationSchema>;
  update: Record<CollectionName, ZodValidationSchema>;
}
/**
 * Реестр схем валидации для создания записей
 */
export const createSchemaRegistry: Record<CollectionName, ZodValidationSchema> = {
  cars: carCreateSchema,
  locations: createLocationSchema,
  notifications: CreateNotificationDTOSchema,
  orders: createOrderSchema,
  services: serviceSchema,
  tariffs: createTariffSchema,
  users: z.object({}), // Базовая схема, конкретная схема выбирается в зависимости от роли
};
/**
 * Реестр схем валидации для обновления записей
 */
export const updateSchemaRegistry: Record<CollectionName, ZodValidationSchema> = {
  cars: carUpdateSchema, // Используем схему для формы (с полем drivers для UI)
  locations: updateLocationSchema,
  notifications: GetNotificationDTOSchema,
  orders: updateOrderSchema,
  services: serviceSchema,
  tariffs: updateTariffSchema,
  users: z.object({}), // Базовая схема, конкретная схема выбирается в зависимости от роли
};
/**
 * Реестр схем валидации для создания пользователей разных ролей
 */
export const userCreateSchemaRegistry: Record<string, ZodValidationSchema> = {
  Admin: adminCreateSchema,
  Customer: customerCreateSchema,
  Driver: driverCreateSchema,
  Operator: operatorCreateSchema,
  Partner: partnerCreateSchema,
  Terminal: terminalCreateSchema,
};
/**
 * Реестр схем валидации для обновления пользователей разных ролей
 */
export const userUpdateSchemaRegistry: Record<string, ZodValidationSchema> = {
  Admin: adminUpdateSchema,
  Customer: customerUpdateSchema,
  Driver: driverUpdateApiSchema, // Используем API схему с лучшими сообщениями об ошибках
  Operator: operatorUpdateSchema,
  Partner: partnerUpdateSchema,
  Terminal: terminalUpdateSchema,
};
/**
 * Реестр схем валидации для специальных операций с уведомлениями
 */
export const notificationSchemaRegistry: Record<string, ZodValidationSchema> = {
  read: ReadNotificationDTOSchema,
};
/**
 * Объединенный реестр схем валидации
 */
export const validationSchemaRegistry: ValidationSchemaRegistry = {
  create: createSchemaRegistry,
  update: updateSchemaRegistry,
};
