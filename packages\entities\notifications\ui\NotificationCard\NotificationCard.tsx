'use client';

import React from 'react';
import { formatTime } from '@shared/utils/formatTime';
import type { NotificationPriority } from '@entities/notifications/config/notificationFilters';
import { getNotificationPriorityLabel } from '@entities/notifications/helpers/NotificationPriority.helpers';
import { getNotificationTypeLabel } from '@entities/notifications/helpers/NotificationType.helpers';
import type { GetNotificationDTO } from '@entities/notifications/interface';

interface NotificationCardProps {
  notification: GetNotificationDTO;
  priority?: NotificationPriority;
  onClick: (notification: GetNotificationDTO) => void;
  onGoToRide?: (orderId: string, rideId?: string, orderType?: 'Instant' | 'Scheduled') => void;
  onGoToOrderDetails?: (orderId: string, rideId?: string, orderType?: 'Instant' | 'Scheduled') => void;
  onDelete?: (id: string) => void;
  isExpanded?: boolean;
  onToggleExpanded?: (notification: GetNotificationDTO) => void;
}

/**
 * Карточка уведомления
 * Следует принципу Single Responsibility - только UI отображение
 * Вся логика приоритетов должна приходить извне (из features)
 */
export const NotificationCard: React.FC<NotificationCardProps> = ({
  notification,
  priority = 'order',
  onClick,
  onGoToRide,
  onGoToOrderDetails,
  onDelete,
  isExpanded = false,
  onToggleExpanded,
}) => {
  const getNotificationStyle = (priority: NotificationPriority, isRead: boolean) => {
    if (isRead) {
      return 'bg-gray-50 border-gray-200';
    }
    switch (priority) {
      case 'order':
        return 'border-blue-200 bg-blue-50';
      case 'important':
        return 'border-green-200 bg-green-50';
      case 'warning':
        return 'border-red-200 bg-red-50';
      case 'completed':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  // Извлекаем orderId из данных уведомления
  const getOrderId = (): string | null => {
    return notification.orderId || null;
  };
  // Проверяем, является ли уведомление связанным с поездкой
  const isRideRelated = (): boolean => {
    const rideTypes = [
      'RideRequestNotification',
      'RideAcceptedNotification',
      'RideAssignedNotification',
      'RideCancelledNotification',
      'RideCompletedNotification',
      'DriverArrivedNotification',
      'DriverHeadingNotification',
      'RideStartedNotification',
    ];

    return rideTypes.includes(notification.type);
  };
  const orderId = getOrderId();
  const showRideButton = isRideRelated() && orderId && onGoToRide;
  const showDetailsButton = onToggleExpanded;
  // TODO: Когда бэк добавит поле priority в GetNotificationDTO, раскомментировать эту строку:
  // const showOrderDetailsButton = priority === 'order' && orderId && onGoToOrderDetails;
  // А пока используем временную логику:
  const showOrderDetailsButton = orderId && onGoToOrderDetails;
  const styleClass = getNotificationStyle(priority, notification.isRead || false);

  return (
    <div
      className={`
        flex flex-col gap-4 justify-between p-4 rounded-xl border transition-all bg-[#F0F0F0]
        ${styleClass}
        hover:shadow-md
      `}
    >
      {/* Основной контент */}
      <div
        className="w-full flex items-center gap-4 cursor-pointer"
        onClick={() => onClick(notification)}
      >
        {/* Индикатор непрочитанного */}
        <div
          className={`
          w-3 h-3 rounded-full flex-shrink-0
          ${notification.isRead ? 'bg-gray-400' : 'bg-blue-500'}
        `}
        />
        {/* Контент */}
        <div className="flex-1 min-w-0">
          <h3 className="font-medium  truncate">{notification.title}</h3>
          {notification.content && (
            <p className="text-sm  mt-1 line-clamp-2">{notification.content}</p>
          )}
          <div className="text-xs text-gray-500 mt-1">
            Тип: {getNotificationTypeLabel(notification.type)} | Приоритет: {priority ? getNotificationPriorityLabel(priority) : 'Неизвестно'}
          </div>
        </div>
        {/* Кнопки действий */}
        {(showRideButton || showOrderDetailsButton || showDetailsButton) && (
          <div className="flex gap-2">
            {/* Кнопка перехода к поездке */}
            {showRideButton && (
              <button
                onClick={e => {
                  e.stopPropagation();
                  onGoToRide!(orderId!, notification.rideId || undefined, notification.orderType);
                }}
                className={`
                  w-10 h-10 rounded-lg transition-colors flex items-center justify-center
                  ${
                    isExpanded
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }
                `}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                  />
                </svg>
              </button>
            )}
            {/* Кнопка детального просмотра заказа */}
            {showOrderDetailsButton && (
              <button
                onClick={e => {
                  e.stopPropagation();
                  onGoToOrderDetails!(orderId!, notification.rideId || undefined, notification.orderType);
                }}
                className={`
                  px-2 py-2 rounded-lg transition-colors flex items-center justify-center
                  ${
                    isExpanded
                      ? 'bg-gray-600 text-white hover:bg-gray-700'
                      : 'bg-gray-500 text-white hover:bg-gray-600'
                  }
                `}
              >
                Перейти к заказу
              </button>
            )}
            {/* Кнопка подробностей - только иконка */}
            {showDetailsButton && (
              <button
                onClick={e => {
                  e.stopPropagation();
                  onToggleExpanded!(notification);
                }}
                className={`
                  w-10 h-10 rounded-lg transition-colors flex items-center justify-center
                  ${
                    isExpanded
                      ? 'bg-gray-600 text-white hover:bg-gray-700'
                      : 'bg-gray-500 text-white hover:bg-gray-600'
                  }
                `}
              >
                <svg
                  className={`w-5 h-5 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            )}
          </div>
        )}
        {/* Время */}
        <div className="text-sm text-gray-500 flex-shrink-0">{formatTime(notification.createdAt)}</div>
      </div>
      {/* Расширенная секция с подробностями */}
      {isExpanded && (
        <div className="p-4 bg-white rounded-lg text-sm space-y-3">
          {/* Полное содержание */}
          <div>
            <h4 className="font-semibold  mb-1">Полное содержание</h4>
            <p >
              {notification.content ||
                'Уважаемый водитель, в вашем районе появился новый заказ. Обратите внимание: – Клиент ожидает быстрое подтверждение – Расстояние до точки подачи — минимальное – Оплата — по стандартному тарифу или выше (если действует повышенный спрос). Чем быстрее вы принимаете заказы, тем выше ваш приоритет в системе и больше шанс получать выгодные поездки. Не забывайте: частый отказ от заказов может повлиять на ваш рейтинг. Спасибо, что работаете с нами!'}
            </p>
          </div>
          {/* Тип и статус в две колонки */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <h4 className="font-semibold  mb-1">Тип уведомления</h4>
              <p >{getNotificationTypeLabel(notification.type)}</p>
            </div>
            <div>
              <h4 className="font-semibold  mb-1">Статус</h4>
              <p >{notification.isRead ? 'Прочитано' : 'Не прочитано'}</p>
            </div>
            {/* Кнопка удаления
          {onDelete && (
            <div className="flex justify-end pt-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(notification.id);
                }}
                className="flex items-center gap-2 px-3 py-1.5 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Удалить уведомление
              </button>
            </div>
          )} */}
          </div>
        </div>
      )}
    </div>
  );
};