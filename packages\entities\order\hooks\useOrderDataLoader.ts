'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Базовый хук для загрузки данных заказа для форм редактирования
 */
export const useOrderDataLoader = <TInput, TOutput>(
  id: string | undefined,
  mode: string | undefined,
  loadOrderFn: (id: string) => Promise<TInput>,
  transformFn: (order: TInput) => TOutput | Promise<TOutput>,
) => {
  const [orderData, setOrderData] = useState<TOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ✅ ИСПРАВЛЕНИЕ: Флаг для предотвращения повторных загрузок
  const isInitialized = useRef(false);
  const currentId = useRef<string | undefined>(undefined);

  const loadOrderData = useCallback(
    async (orderId: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const order = await loadOrderFn(orderId);
        const transformedData = await transformFn(order);

        setOrderData(transformedData);
        isInitialized.current = true;
        currentId.current = orderId;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Ошибка загрузки данных заказа';

        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [loadOrderFn, transformFn],
  );

  // ✅ ИСПРАВЛЕНИЕ: Используем стабильные зависимости и флаг инициализации
  useEffect(() => {
    if (mode === 'edit' && id && (!isInitialized.current || currentId.current !== id)) {
      loadOrderData(id);
    }
  }, [id, mode]); // ← Убираем loadOrderData из зависимостей

  // ✅ ИСПРАВЛЕНИЕ: Сброс при изменении режима или ID
  useEffect(() => {
    if (mode !== 'edit' || !id) {
      isInitialized.current = false;
      currentId.current = undefined;
      setOrderData(null);
      setError(null);
    }
  }, [mode, id]);

  // Функция для принудительной перезагрузки данных
  const refetch = useCallback(() => {
    if (mode === 'edit' && id) {
      isInitialized.current = false; // Сбрасываем флаг для принудительной загрузки
      loadOrderData(id);
    }
  }, [id, mode, loadOrderData]);

  return { orderData, isLoading, error, refetch };
};
