"use strict";exports.id=1466,exports.ids=[1466],exports.modules={35925:(e,r,t)=>{t.d(r,{G7:()=>d,Wn:()=>u,dO:()=>c,Qc:()=>h}),t(58277);var s=t(2984),a=t(60159),i=t(96274),l=t(5448),o=t(18673),n=t(75385);let u=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)(!1),[p,y]=(0,a.useState)({});return{logout:async r=>{try{if(t(!0),c(null),h(!1),y({}),r?.isInQueue&&r?.leaveQueue)try{await r.leaveQueue(),await new Promise(e=>setTimeout(e,500))}catch(e){console.warn("Ошибка при выходе из очереди:",e),o.P.warn("Не удалось корректно выйти из очереди, но выход из системы продолжается")}let s=await (0,l.n)(n.y.logoutWithCleanup());if(s.success)h(!0),o.P.success("Выход выполнен успешно!"),e.push(i.vc.AUTH.LOGIN);else{let e=s.error||"Ошибка при выходе из системы";c(e),o.P.error(e)}}catch(r){let e=r instanceof Error?r.message:"Произошла непредвиденная ошибка при выходе из системы";c(e),o.P.error(e)}finally{t(!1)}},isLoggingOut:r,error:u,success:d,fieldErrors:p,clearFieldError:e=>{y(r=>{let t={...r};return delete t[e],t})}}},c=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)(!1),[p,y]=(0,a.useState)({}),w=async(r,t)=>{let s=await (0,l.n)(n.y.loginWithValidation({email:r,password:t}));s.success?setTimeout(()=>{e.push(i.vc.MAIN.HOME)},2e3):(console.error("Ошибка при автоматическом входе:",s.error),setTimeout(()=>{e.push("/login?registered=true")},2e3))};return{registerPartner:async e=>{t(!0),c(null),h(!1),y({});let r=await (0,l.n)(n.y.registerPartnerWithValidation(e));if(r.success)h(!0),o.P.success("Регистрация успешно завершена! Перенаправление на страницу входа..."),await w(e.email,e.password);else{let e=r.error||"Произошла ошибка при регистрации";e.includes("Email")&&e.includes("уже используется")&&y(e=>({...e,email:!0})),c(e),o.P.error(e)}t(!1)},isLoading:r,error:u,success:d,fieldErrors:p,clearFieldError:e=>{y(r=>{let t={...r};return delete t[e],t})}}},d=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)(!1),[p,y]=(0,a.useState)({});return{forgotPassword:async r=>{t(!0),c(null),h(!1),y({});let s=await (0,l.n)(n.y.forgotPasswordWithValidation(r));if(s.success)h(!0),o.P.success("Инструкции по восстановлению пароля отправлены на указанный email."),o.P.info("Переход на страницу сброса пароля...",{autoClose:2e3}),setTimeout(()=>{e.push(`${i.vc.AUTH.RESET_PASSWORD}?email=${encodeURIComponent(r.email)}`)},2e3);else{let e=s.error||"Произошла ошибка при отправке запроса";(e.includes("не найден")||e.includes("некорректный email"))&&y(e=>({...e,email:!0})),c(e),o.P.error(e)}t(!1)},isLoading:r,error:u,success:d,fieldErrors:p,clearFieldError:e=>{y(r=>{let t={...r};return delete t[e],t})}}},h=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)(!1),[p,y]=(0,a.useState)({});return{resetPassword:async r=>{t(!0),c(null),h(!1),y({});let s=await (0,l.n)(n.y.resetPasswordWithValidation(r));if(s.success)h(!0),o.P.success("Пароль успешно изменен! Вы будете перенаправлены на страницу входа."),setTimeout(()=>{e.push(i.vc.AUTH.LOGIN)},2e3);else{let e=s.error||"Произошла ошибка при сбросе пароля";e.includes("неверный код")||e.includes("истек")?y(e=>({...e,resetCode:!0})):e.includes("не найден")?y(e=>({...e,email:!0})):e.includes("слишком короткий")&&y(e=>({...e,newPassword:!0})),c(e),o.P.error(e)}t(!1)},isLoading:r,error:u,success:d,fieldErrors:p,clearFieldError:e=>{y(r=>{let t={...r};return delete t[e],t})}}}},50534:(e,r,t)=>{t.d(r,{yI:()=>s.y});var s=t(65363);t(4191),t(5615),t(13486),t(60159),t(96274),t(64556),t(59993),t(72456),t(6901),t(28172),t(14525),t(22234),t(36651),t(809),t(12958),t(4392)},58277:(e,r,t)=>{t.d(r,{v:()=>u});var s=t(2984),a=t(60159),i=t(50947),l=t(5448),o=t(18673),n=t(75385);let u=()=>{let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,h]=(0,a.useState)({});return{login:(0,a.useCallback)(async r=>{t(!0),c(null),h({});let s=await (0,l.n)(n.y.loginWithValidation(r));if(s.success)o.P.success("Вход выполнен успешно!"),e.push(i.vc.MAIN.HOME);else{let e=s.error||"Произошла неизвестная ошибка";c(e),s.fieldErrors&&h(s.fieldErrors),o.P.error(e)}t(!1)},[e]),isLoading:r,error:u,fieldErrors:d,clearFieldError:(0,a.useCallback)(e=>{h(r=>{let t={...r};return delete t[e],t})},[])}}},65363:(e,r,t)=>{t.d(r,{y:()=>s.y});var s=t(64556)},75385:(e,r,t)=>{t.d(r,{y:()=>o});var s=t(77243),a=t(75111),i=t(50947);class l extends s.v{async login(e){let r=await this.post("/login",e);return this.handleApiResult(r)}async loginWithValidation(e){try{let r=await this.post("/login",{email:e.email,password:e.password});if(r.error){let t=r.error.message,s={};if(r.error.type===a.r6.Auth)t="Неверный email или пароль. Пожалуйста, проверьте правильность учетных данных.",s.email=!0,s.password=!0;else if(r.error.type===a.r6.Network)t="Проблема с сетевым подключением. Проверьте интернет-соединение.";else if(r.error.type===a.r6.Validation&&r.error.data&&"object"==typeof r.error.data){let a=r.error.data;if(a.errors&&"object"==typeof a.errors){let r=a.errors;if(r.UserNotFound&&r.UserNotFound.length>0)t=`Пользователь с email '${e.email}' не найден.`,s.email=!0;else if(r.InvalidEmail&&r.InvalidEmail.length>0)t="Указан некорректный email.",s.email=!0;else{let e=Object.entries(r).map(([e,r])=>`${"RequiredField"===e?"Обязательное поле":e}: ${r.join(", ")}`).join("\n");e&&(t=e)}}}return{success:!1,error:t,fieldErrors:Object.keys(s).length>0?s:void 0}}return{success:!0,data:this.handleApiResult(r)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла неизвестная ошибка"}}}async register(e){let r=await this.post("/register",e);return this.handleApiResult(r)}async registerPartnerWithValidation(e){try{let r=await this.post("/register/partner",e);if(r.error)return{success:!1,error:r.error.message};return{success:!0,data:this.handleApiResult(r)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при регистрации"}}}async refreshToken(e){let r=await this.post("/refresh",e);return this.handleApiResult(r)}async logout(){let e=await this.post("/logout");this.handleApiResult(e)}async logoutWithCleanup(){try{let e=await this.post("/logout");if(e.error)return{success:!1,error:e.error.message};return this.handleApiResult(e),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при выходе"}}}async resetPassword(e){let r=await this.post("/reset-password",e);this.handleApiResult(r)}async forgotPasswordWithValidation(e){try{let r=await this.post("/forgot-password",e);if(r.error)return{success:!1,error:r.error.message};return this.handleApiResult(r),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при отправке запроса"}}}async resetPasswordWithValidation(e){try{let r=await this.post("/reset-password",e);if(r.error)return{success:!1,error:r.error.message};return this.handleApiResult(r),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при сбросе пароля"}}}async changePassword(e){let r=await this.post("/change-password",e);this.handleApiResult(r)}async verifyToken(){try{let e=await this.get("/verify");return this.handleApiResult(e).valid}catch{return!1}}async getCurrentUser(){let e=await this.get("/me");return this.handleApiResult(e)}constructor(...e){super(...e),this.baseUrl=i.QQ.AUTH.BASE}}let o=new l}};