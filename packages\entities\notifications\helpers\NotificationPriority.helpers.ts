import type { NotificationPriority } from '../config/notificationFilters';

/**
 * Получает переведенную метку для приоритета уведомления
 * @param priority Приоритет уведомления
 * @returns Переведенная метка приоритета
 */
export function getNotificationPriorityLabel(priority: NotificationPriority): string {
  switch (priority) {
    case 'order':
      return 'О заказе';
    case 'completed':
      return 'Завершенные';
    case 'important':
      return 'Важные';
    case 'warning':
      return 'Предупреждения';
    default:
      return priority;
  }
}

/**
 * Получает массив всех приоритетов
 * @returns Массив всех приоритетов
 */
export function getNotificationPriorityValues(): NotificationPriority[] {
  return ['order', 'completed', 'important', 'warning'];
}

/**
 * Получает массив всех переведенных меток приоритетов
 * @returns Массив всех переведенных меток
 */
export function getNotificationPriorityLabels(): string[] {
  return getNotificationPriorityValues().map(priority => getNotificationPriorityLabel(priority));
}

/**
 * Получает массив объектов { value, label } для приоритетов
 * @returns Массив объектов для выпадающих списков
 */
export function getNotificationPriorityOptions(): { value: NotificationPriority, label: string }[] {
  return getNotificationPriorityValues().map(priority => ({
    value: priority,
    label: getNotificationPriorityLabel(priority)
  }));
}
