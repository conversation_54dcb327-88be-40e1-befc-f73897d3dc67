(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8030],{3884:()=>{},25211:(e,t,n)=>{"use strict";n.d(t,{ActiveRidePage:()=>Y});var r=n(54568),i=n(7620),l=n(62942),a=n(27736),s=n(68327),o=n(12691);n(98893),n(32959);let d=(e,t,n)=>{let[r,l]=(0,i.useState)(null),[s,o]=(0,i.useState)(null),[d,u]=(0,i.useState)([]),[c,h]=(0,i.useState)(!1),[g,x]=(0,i.useState)(null);return(0,i.useEffect)(()=>{let r=async()=>{if(e||t){h(!0),x(null);try{let r=[],i=[];if(e&&(r.push(a.uE.get(a.QQ.LOCATION.GET_BY_ID.replace("{uuid}",e)).then(e=>e.data)),i.push("start")),t&&(r.push(a.uE.get(a.QQ.LOCATION.GET_BY_ID.replace("{uuid}",t)).then(e=>e.data)),i.push("end")),n&&n.length>0){n.forEach(()=>{i.push("additional")});let e=n.map(e=>a.uE.get(a.QQ.LOCATION.GET_BY_ID.replace("{uuid}",e)).then(e=>e.data));r.push(...e)}console.log("\uD83D\uDDFA️ Загружаем локации:",{startLocationId:e,endLocationId:t,additionalStopIds:n});let s=await Promise.all(r),d=0,c=[];i.forEach(e=>{"start"===e?(l(s[d]),console.log("\uD83D\uDCCD Стартовая локация:",s[d])):"end"===e?(o(s[d]),console.log("\uD83C\uDFC1 Конечная локация:",s[d])):"additional"===e&&(c.push(s[d]),console.log("\uD83D\uDED1 Дополнительная остановка:",s[d])),d++}),c.length>0&&(u(c),console.log("\uD83D\uDED1 Все дополнительные остановки установлены:",c))}catch(e){console.error("❌ Ошибка загрузки локаций:",e),x("Не удалось загрузить данные локаций")}finally{h(!1)}}};l(null),o(null),u([]),r()},[e,t,null==n?void 0:n.join(",")]),{startLocation:r,endLocation:s,additionalStops:d,isLoading:c,error:g}};n(34744);var u=n(1839);let c=e=>{let{order:t,availableActions:n,onActionExecute:a}=e,s=(0,l.useRouter)(),[d,c]=(0,i.useState)(null),h=t.status===o.Re.Completed,g=t.status===o.Re.Cancelled,x=async e=>{c(e.id);try{await a(e)}catch(e){}finally{c(null)}},p=e=>{switch(e){case"primary":default:return"bg-blue-500 hover:bg-blue-600 text-white";case"secondary":return"bg-gray-500 hover:bg-gray-600 text-white";case"danger":return"bg-red-500 hover:bg-red-600 text-white"}};return(0,r.jsx)("div",{className:"absolute top-0 left-0 right-0 mx-auto p-2 z-30",children:(0,r.jsx)("div",{className:"bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-lg",children:(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Информация о заказе"}),(0,r.jsxs)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(h?"bg-green-100 text-green-800":t.status===o.Re.InProgress?"bg-blue-100 text-blue-800":g?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:[(0,u.Vh)(t.status),t.subStatus&&" (".concat((0,u.l1)(t.subStatus),")")]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[n.map((e,t)=>(0,r.jsx)("button",{type:"button",onClick:()=>x(e),disabled:null!==d,className:"py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed ".concat(p(e.variant)),children:d===e.id?"Выполняется...":e.label},e.id)),(h||g)&&(0,r.jsx)("button",{onClick:()=>s.push("/"),className:"bg-gray-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-600 transition-colors",children:"К заказам"})]})]})})})})};n(44090);var h=n(66478),g=n(8426),x=n(12722);let p=e=>{let[t,n]=(0,i.useState)(null),[r,l]=(0,i.useState)(!0),[o,d]=(0,i.useState)(null),u=async e=>{try{console.log("\uD83D\uDD04 Выполняем действие:",e);let t=await a.uE.post(e.endpoint);if(200===t.status)s.P.success("".concat(e.label," - выполнено")),await h();else throw Error("Неожиданный ответ от сервера")}catch(t){throw console.error("❌ Ошибка при выполнении действия:",t),s.P.error("Ошибка: ".concat(e.label)),t}},c=async()=>{if(!e){d("Нет orderId"),l(!1);return}try{l(!0),d(null),console.log("\uD83D\uDCE6 Загружаем данные заказа:",e);let t=a.QQ.ORDER.MY_PARTICIPANT.replace("{order_id}",e),r=(await a.uE.get(t)).data;console.log("✅ Данные заказа получены:",r),n({order:r})}catch(t){let e=t instanceof Error?t.message:"Ошибка загрузки данных заказа";console.error("❌ Ошибка загрузки данных заказа:",t),d(e),s.P.error(e)}finally{l(!1)}},h=async()=>{await c()};(0,i.useEffect)(()=>{c()},[e]);let g=t?(e=>{let t=[];switch(e.status){case"Scheduled":"DriverAssigned"===e.subStatus&&t.push({id:"driver-heading",label:"Принял еду",endpoint:"/Ride/for-order/".concat(e.id,"/status/driver-heading-to-client"),method:"POST",variant:"primary"});break;case"InProgress":switch(e.subStatus){case"DriverHeading":t.push({id:"driver-arrived",label:"Я прибыл",endpoint:"/Ride/for-order/".concat(e.id,"/status/driver-arrived"),method:"POST",variant:"primary"});break;case"DriverArrived":t.push({id:"ride-started",label:"Взял пассажира",endpoint:"/Ride/for-order/".concat(e.id,"/status/ride-started"),method:"POST",variant:"primary"});break;case"RideStarted":t.push({id:"ride-finished",label:"Завершить поездку",endpoint:"/Ride/for-order/".concat(e.id,"/status/ride-finished"),method:"POST",variant:"primary"})}}return"Completed"!==e.status&&"Cancelled"!==e.status&&t.push({id:"cancel",label:"Отменить",endpoint:"/Ride/for-order/".concat(e.id,"/status/ride-cancelled"),method:"POST",variant:"danger"}),t})(t.order):[];return{orderData:t,isLoading:r,error:o,availableActions:g,executeAction:u,refetch:h}},f=(e,t,n,r)=>{let i=(n-e)*Math.PI/180,l=(r-t)*Math.PI/180,a=Math.sin(i/2)*Math.sin(i/2)+Math.cos(e*Math.PI/180)*Math.cos(n*Math.PI/180)*Math.sin(l/2)*Math.sin(l/2);return 2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))*6371},m=e=>{let{startLocation:t,endLocation:n,additionalStops:l=[],driverLocation:a,orderStatus:s,orderSubStatus:d,className:u=""}=e,{location:c}=(0,g.t0)(),h=c||a,x=(0,i.useMemo)(()=>{let e=[];return t&&t.id&&e.push({id:t.id,name:t.name,address:t.address,latitude:t.latitude,longitude:t.longitude,type:"start"}),l.forEach(t=>{t&&t.id&&e.push({id:t.id,name:t.name,address:t.address,latitude:t.latitude,longitude:t.longitude,type:"stop"})}),n&&n.id&&e.push({id:n.id,name:n.name,address:n.address,latitude:n.latitude,longitude:n.longitude,type:"end"}),e},[t,n,l]),p=(0,i.useMemo)(()=>{if(x.length<2)return{totalDistance:0,progress:0,remainingDistance:0,isRideStarted:!1};let e=0;for(let t=0;t<x.length-1;t++){let n=x[t],r=x[t+1];e+=f(n.latitude,n.longitude,r.latitude,r.longitude)}let t=s===o.Re.InProgress&&d===o.lk.RideStarted,n=0,r=e;if(t&&h&&x.length>0){let t=x[0],i=x[x.length-1],l=f(t.latitude,t.longitude,h.latitude,h.longitude),a=f(h.latitude,h.longitude,i.latitude,i.longitude),s=f(t.latitude,t.longitude,i.latitude,i.longitude);Math.abs(l+a-s)<.3*s&&e>0?(n=Math.min((Math.min(l/e*100,100)+Math.max(0,(1-a/s)*100))/2,100),l<.1&&(n=Math.min(n,5)),a<.1&&(n=Math.max(n,95)),r=a):(n=0,r=e),n>20&&l<.5&&(n=Math.min(n,15))}return{totalDistance:e,progress:Math.max(0,Math.min(n,100)),remainingDistance:r,isRideStarted:t}},[x,h,s,d]);if(0===x.length)return(0,r.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 w-[85%] bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-4 z-20 ".concat(u),children:(0,r.jsx)("div",{className:"text-center text-gray-500",children:"Загрузка данных маршрута..."})});let m=e=>e<1?"".concat(Math.round(1e3*e)," м"):"".concat(e.toFixed(1)," км");return(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 mx-auto p-2 z-20",children:(0,r.jsxs)("div",{className:"relative bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4 rounded-2xl w-full flex flex-col gap-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center text-xs text-gray-600",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("span",{children:["\uD83D\uDCCD Общее расстояние: ",(0,r.jsx)("strong",{children:m(p.totalDistance)})]}),p.isRideStarted&&(0,r.jsx)("span",{className:"text-green-600",children:"\uD83D\uDE97 Поездка начата"}),!p.isRideStarted&&(0,r.jsx)("span",{className:"text-orange-600",children:"\uD83D\uDEB6 Еду к пассажиру"})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[p.isRideStarted&&(0,r.jsxs)("span",{children:["\uD83D\uDCCD Осталось: ",(0,r.jsx)("strong",{children:m(p.remainingDistance)})]}),(0,r.jsx)("span",{className:"text-gray-500",children:p.isRideStarted?"Прогресс поездки: ".concat(p.progress.toFixed(1),"%"):"Ожидание начала поездки (0%)"})]})]}),(0,r.jsx)("div",{className:"flex justify-between items-center gap-4",children:x.map((e,t)=>{let n=t<x.length-1?f(e.latitude,e.longitude,x[t+1].latitude,x[t+1].longitude):0;return(0,r.jsxs)(i.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-row gap-2 items-center text-start flex-1",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full ".concat("start"===e.type?"bg-green-500":"end"===e.type?"bg-red-500":"bg-blue-500")}),(0,r.jsxs)("div",{className:"flex flex-col gap-1 text-xs",children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"truncate max-w-[120px] text-gray-500",children:e.address})]})]}),n>0&&(0,r.jsx)("div",{className:"flex items-center justify-center px-2",children:(0,r.jsxs)("div",{className:"text-gray-400 text-xs whitespace-nowrap",children:["→ ",m(n)," →"]})})]},e.id)})}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"w-full h-3 bg-gray-200 rounded-full",children:(0,r.jsx)("div",{className:"h-3 rounded-full transition-all duration-1000 ease-out ".concat(p.isRideStarted?"bg-gradient-to-r from-green-500 to-blue-500":"bg-gray-400"),style:{width:"".concat(p.progress,"%")}})})})]})})},v=e=>{let{passengers:t,services:n,initialPrice:l}=e,[a,s]=(0,i.useState)(!1);return t.find(e=>e.isMainPassenger)||t[0]?a?(0,r.jsxs)("div",{className:"absolute bottom-34 right-2 bg-white rounded-lg shadow-lg p-4 w-[98%] z-20 h-[240px] flex flex-col",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4 flex-shrink-0",children:[(0,r.jsx)("div",{className:"text-lg font-semibold",children:"Детали заказа"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[l," Сом"]}),(0,r.jsx)("button",{onClick:()=>s(!1),className:"w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center hover:bg-blue-200 transition-colors",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),(0,r.jsxs)("div",{className:"flex flex-row justify-between gap-4 flex-1 overflow-hidden h-full",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden h-full",children:[(0,r.jsxs)("div",{className:"text-sm font-medium mb-2 flex-shrink-0",children:["Пассажиры (",t.length,"):"]}),(0,r.jsx)("div",{className:"space-y-2 overflow-y-auto flex-1 min-h-0 max-h-[120px]",children:t.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-2 rounded ".concat(e.isMainPassenger?"bg-blue-50 border border-blue-200":"bg-gray-50"),children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded-full flex items-center justify-center ".concat(e.isMainPassenger?"bg-blue-500 text-white":"bg-gray-300"),children:(0,r.jsx)("span",{className:"text-xs",children:t+1})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:e.firstName}),e.isMainPassenger&&(0,r.jsx)("div",{className:"text-xs text-blue-600",children:"Основной пассажир"})]})]},e.id))})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden h-full",children:[(0,r.jsxs)("div",{className:"text-sm font-medium mb-2 flex-shrink-0",children:["Дополнительные услуги (",n.length,"):"]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto min-h-0 max-h-[120px]",children:n.length>0?(0,r.jsx)("div",{className:"space-y-2",children:n.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-50 rounded border border-green-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:e.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Количество: ",e.quantity]})]}),(0,r.jsx)("div",{className:"w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs",children:e.quantity})})]},e.serviceId))}):(0,r.jsx)("div",{className:"text-sm text-gray-500 italic",children:"Нет дополнительных услуг"})})]})]})]}):(0,r.jsx)("div",{className:"absolute bottom-34 right-4 z-20",children:(0,r.jsx)("button",{onClick:()=>s(!0),className:"w-12 h-12 bg-blue-100 rounded-md flex items-center justify-center hover:bg-blue-200 transition-colors shadow-lg cursor-pointer",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})}):null};var b=n(62117),j=n.n(b);function y(e,t){return Object.freeze({...e,...t})}n(3884);let C=(0,i.createContext)(null);function w(){let e=(0,i.use)(C);if(null==e)throw Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return e}function N(){return w().map}var M=n(97509);function D(e){return(0,i.forwardRef)(function(t,n){let{instance:r,context:l}=e(t).current;(0,i.useImperativeHandle)(n,()=>r);let{children:a}=t;return null==a?null:i.createElement(C,{value:l},a)})}function S(e,t){let n=(0,i.useRef)(t);(0,i.useEffect)(function(){t!==n.current&&null!=e.attributionControl&&(null!=n.current&&e.attributionControl.removeAttribution(n.current),null!=t&&e.attributionControl.addAttribution(t)),n.current=t},[e,t])}function k(e,t){let n=(0,i.useRef)(void 0);(0,i.useEffect)(function(){return null!=t&&e.instance.on(t),n.current=t,function(){null!=n.current&&e.instance.off(n.current),n.current=null}},[e,t])}function L(e,t){let n=e.pane??t.pane;return n?{...e,pane:n}:e}function R(e,t,n){return Object.freeze({instance:e,context:t,container:n})}function E(e,t){return null==t?function(t,n){let r=(0,i.useRef)(void 0);return r.current||(r.current=e(t,n)),r}:function(n,r){let l=(0,i.useRef)(void 0);l.current||(l.current=e(n,r));let a=(0,i.useRef)(n),{instance:s}=l.current;return(0,i.useEffect)(function(){a.current!==n&&(t(s,n,a.current),a.current=n)},[s,n,t]),l}}function P(e,t){(0,i.useEffect)(function(){return(t.layerContainer??t.map).addLayer(e.instance),function(){t.layerContainer?.removeLayer(e.instance),t.map.removeLayer(e.instance)}},[t,e])}function I(e){return function(t){let n=w(),r=e(L(t,n),n);return S(n.map,t.attribution),k(r.current,t.eventHandlers),P(r.current,n),r}}function O(e,t){var n;return D((n=E(e,t),function(e){let t=w(),r=n(L(e,t),t);k(r.current,e.eventHandlers),P(r.current,t);var l=r.current;let a=(0,i.useRef)(void 0);return(0,i.useEffect)(function(){if(e.pathOptions!==a.current){let t=e.pathOptions??{};l.instance.setStyle(t),a.current=t}},[l,e]),r}))}let z=D(I(E(function({position:e,...t},n){let r=new b.Marker(e,t);return R(r,y(n,{overlayContainer:r}))},function(e,t,n){t.position!==n.position&&e.setLatLng(t.position),null!=t.icon&&t.icon!==n.icon&&e.setIcon(t.icon),null!=t.zIndexOffset&&t.zIndexOffset!==n.zIndexOffset&&e.setZIndexOffset(t.zIndexOffset),null!=t.opacity&&t.opacity!==n.opacity&&e.setOpacity(t.opacity),null!=e.dragging&&t.draggable!==n.draggable&&(!0===t.draggable?e.dragging.enable():e.dragging.disable())}))),F=function(e,t){var n,r;return n=E(e),r=function(e,r){let i=w(),l=n(L(e,i),i);return S(i.map,e.attribution),k(l.current,e.eventHandlers),t(l.current,i,e,r),l},(0,i.forwardRef)(function(e,t){let[n,l]=(0,i.useState)(!1),{instance:a}=r(e,l).current;(0,i.useImperativeHandle)(t,()=>a),(0,i.useEffect)(function(){n&&a.update()},[a,n,e.children]);let s=a._contentNode;return s?(0,M.createPortal)(e.children,s):null})}(function(e,t){return R(new b.Popup(e,t.overlayContainer),t)},function(e,t,{position:n},r){(0,i.useEffect)(function(){let{instance:i}=e;function l(e){e.popup===i&&(i.update(),r(!0))}function a(e){e.popup===i&&r(!1)}return t.map.on({popupopen:l,popupclose:a}),null==t.overlayContainer?(null!=n&&i.setLatLng(n),i.openOn(t.map)):t.overlayContainer.bindPopup(i),function(){t.map.off({popupopen:l,popupclose:a}),t.overlayContainer?.unbindPopup(),t.map.removeLayer(i)}},[e,t,r,n])}),T=(0,i.forwardRef)(function({bounds:e,boundsOptions:t,center:n,children:r,className:l,id:a,placeholder:s,style:o,whenReady:d,zoom:u,...c},h){let[g]=(0,i.useState)({className:l,id:a,style:o}),[x,p]=(0,i.useState)(null),f=(0,i.useRef)(void 0);(0,i.useImperativeHandle)(h,()=>x?.map??null,[x]);let m=(0,i.useCallback)(r=>{if(null!==r&&!f.current){let i=new b.Map(r,c);f.current=i,null!=n&&null!=u?i.setView(n,u):null!=e&&i.fitBounds(e,t),null!=d&&i.whenReady(d),p(Object.freeze({__version:1,map:i}))}},[]);(0,i.useEffect)(()=>()=>{x?.map.remove()},[x]);let v=x?i.createElement(C,{value:x},r):s??null;return i.createElement("div",{...g,ref:m},v)}),B=function(e,t){var n;return n=I(E(e,t)),(0,i.forwardRef)(function(e,t){let{instance:r}=n(e).current;return(0,i.useImperativeHandle)(t,()=>r),null})}(function({url:e,...t},n){return R(new b.TileLayer(e,L(t,n)),n)},function(e,t,n){let{opacity:r,zIndex:i}=t;null!=r&&r!==n.opacity&&e.setOpacity(r),null!=i&&i!==n.zIndex&&e.setZIndex(i);let{url:l}=t;null!=l&&l!==n.url&&e.setUrl(l)}),A=O(function({positions:e,...t},n){let r=new b.Polyline(e,t);return R(r,y(n,{overlayContainer:r}))},function(e,t,n){t.positions!==n.positions&&e.setLatLngs(t.positions)}),_=O(function({center:e,children:t,...n},r){let i=new b.Circle(e,n);return R(i,y(r,{overlayContainer:i}))},function(e,t,n){t.center!==n.center&&e.setLatLng(t.center),null!=t.radius&&t.radius!==n.radius&&e.setRadius(t.radius)});var W=n(55915);delete j().Icon.Default.prototype._getIconUrl,j().Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});let Z=e=>{let{onMapClick:t}=e;return!function(e){let t=w().map;(0,i.useEffect)(function(){return t.on(e),function(){t.off(e)}},[t,e])}({click:e=>{t&&t(e.latlng.lat,e.latlng.lng)}}),null},H=e=>{let{onBoundsChange:t}=e,n=w().map,r=(0,i.useCallback)(function(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0],!t)return;let e=n.getBounds();t({latFrom:e.getSouth(),latTo:e.getNorth(),longFrom:e.getWest(),longTo:e.getEast()})},[n,t]);return(0,i.useEffect)(()=>{if(!t)return;let e=null,i=null,l=()=>{let t=n.getBounds(),l="".concat(t.getSouth().toFixed(6),",").concat(t.getNorth().toFixed(6),",").concat(t.getWest().toFixed(6),",").concat(t.getEast().toFixed(6));i!==l&&(i=l,e&&clearTimeout(e),e=setTimeout(()=>{r(!1)},500))};return r(!0),n.on("moveend",l),n.on("zoomend",l),()=>{e&&clearTimeout(e),n.off("moveend",l),n.off("zoomend",l)}},[n,t,r]),null},Q=e=>{let{latitude:t,longitude:n,zoom:l=13,height:a="400px",width:s="100%",showMarker:o=!0,markerText:d,routePoints:u=[],showRoute:c=!1,activeDrivers:h=[],showActiveDrivers:g=!1,className:x="",onMapClick:p,onBoundsChange:f,onDriverSelect:m,selectedDriverId:v,mapLocations:b=[],selectedLocationIds:y=[],onLocationToggle:C,getDriverById:w,showDriverSearchZone:N=!1,driverSearchRadius:M=2e3,currentDriverLocation:D,routeDeviationThreshold:S=100,onRouteDeviation:k}=e,L=[t,n],[R,E]=(0,i.useState)([]),P=(e,t,n,r)=>{let i=(n-e)*Math.PI/180,l=(r-t)*Math.PI/180,a=Math.sin(i/2)*Math.sin(i/2)+Math.cos(e*Math.PI/180)*Math.cos(n*Math.PI/180)*Math.sin(l/2)*Math.sin(l/2);return 2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))*6371e3},I=(e,t,n,r,i,l)=>{let a,s,o=e-n,d=t-r,u=i-n,c=l-r,h=u*u+c*c;if(0===h)return P(e,t,n,r);let g=(o*u+d*c)/h;return g<0?(a=n,s=r):g>1?(a=i,s=l):(a=n+g*u,s=r+g*c),P(e,t,a,s)},{isDriverOffRoute:O,minDistanceToRoute:Q}=(0,i.useMemo)(()=>{if(!D||R.length<2)return{isDriverOffRoute:!1,minDistanceToRoute:0};let e=1/0;for(let t=0;t<R.length-1;t++){let[n,r]=R[t],[i,l]=R[t+1];e=Math.min(e,I(D.latitude,D.longitude,n,r,i,l))}let t=e>S;return k&&k(t,e),{isDriverOffRoute:t,minDistanceToRoute:e}},[D,R,S,k]),q=(e,t)=>{let n="".concat(e,"|").concat(null!=t?t:"");V.current||(V.current=new Map);let r=V.current.get(n);if(r)return r;let i='\n      <div style="\n        width: 32px;\n        height: 32px;\n        background-color: '.concat(e,';\n        border: 3px solid white;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-weight: bold;\n        font-size: 16px;\n        color: white;\n        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n        box-shadow: 0 2px 8px rgba(0,0,0,0.3);\n      ">\n        ').concat(t||"","\n      </div>\n    "),l=j().divIcon({html:i,className:"",iconSize:[32,32],iconAnchor:[16,16],popupAnchor:[0,-20]});return V.current.set(n,l),l},U=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=t?"#22C55E":(e=>{switch(e){case"Economy":return"#9CA3AF";case"Comfort":return"#60A5FA";case"ComfortPlus":return"#3B82F6";case"Business":return"#8B5CF6";case"Premium":return"#F59E0B";case"Vip":return"#EF4444";case"Luxury":return"#DC2626";default:return"#8EBFFF"}})(e),i='\n      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 41 24" fill="none" width="56" height="32" style="'.concat(t?"filter: drop-shadow(0 0 8px #22c55e);":""," ").concat(0!==n?"transform: rotate(".concat(n,"deg);"):"",' transform-origin: center;">\n        <g clip-path="url(#clip0_42_2510)">\n          <path d="M11.7415 5.22881C11.8153 4.34825 11.3567 3.34659 12.6569 3.01606L12.7498 5.027C14.4654 4.66129 16.0866 4.32077 17.7056 3.96753C20.0512 3.4575 22.4009 2.9697 24.733 2.40747C25.638 2.16049 26.5238 1.84568 27.3832 1.46561C30.2331 0.298448 33.2233 0.235652 36.2282 0.285838C36.9767 0.299309 37.471 0.750296 37.8466 1.38123C38.9376 3.20844 39.5421 5.23256 39.9148 7.29461C40.283 9.32646 40.4181 11.4059 40.5761 13.4698C40.674 14.7074 40.1256 15.5987 38.9536 16.0914C36.0574 17.3071 33.162 18.3387 29.9513 18.5157C27.4256 18.6547 24.926 19.3607 22.4247 19.8603C20.3821 20.2747 18.3515 20.7478 16.2235 21.2158L16.836 23.0363C16.0758 23.2122 16.0758 23.2122 15.0326 21.3316C13.962 21.7123 12.8791 22.2313 11.7456 22.4725C10.1125 22.8264 8.53393 23.5459 6.78885 23.3079C6.35022 23.2481 5.87241 23.4607 5.41619 23.5634C4.48482 23.7664 3.46457 23.3685 3.11709 22.4642C2.30821 20.3614 1.34025 18.2772 0.888129 16.0908C0.431324 13.871 0.472913 11.5449 0.381643 9.26176C0.33822 8.32098 1.06987 7.62934 2.00073 7.38149C3.84511 6.88339 5.66651 6.29513 7.52494 5.86361C8.64497 5.60383 9.81481 5.57383 10.9542 5.4268C11.2204 5.37737 11.4833 5.31126 11.7415 5.22881ZM17.2852 7.05379C15.6778 7.01434 14.2232 6.91981 12.7698 6.95487C10.9704 7.00111 10.5394 7.51561 10.5478 9.36078C10.5377 9.65339 10.556 9.94635 10.6023 10.2354C11.0815 12.6015 11.5422 14.9732 12.0866 17.325C12.2374 17.9775 12.6062 18.596 12.9562 19.1817C13.3386 19.8219 13.9723 20.0664 14.6519 19.7757C16.2725 19.083 17.8666 18.326 19.5297 17.5689C17.8466 14.1892 17.1703 10.7806 17.2852 7.05537L17.2852 7.05379ZM31.7694 14.8319C33.2584 14.5503 34.5568 14.3159 35.8493 14.0562C36.4225 13.9401 36.7368 13.5478 36.8239 12.9548C37.3243 9.82571 36.6332 6.61749 34.8923 3.98739C34.6945 3.67639 34.129 3.38236 33.7933 3.44588C32.4228 3.6804 31.0758 4.05149 29.5778 4.41035C31.221 7.7407 31.9583 11.0777 31.7695 14.8335L31.7694 14.8319ZM30.5847 3.32986L30.517 3.05287C27.326 2.91043 24.2295 3.52235 21.0804 4.28875C21.2131 4.88767 21.3317 5.41842 21.4754 6.05205L30.5847 3.32986ZM24.0576 18.4484C26.7556 18.0662 32.3728 16.5248 33.1672 15.3932L23.6954 16.7156L24.0576 18.4484ZM20.4873 4.38016L13.7881 5.78261C16.123 6.12942 18.4335 6.42295 20.8603 6.14287C20.7244 5.50888 20.6121 4.97785 20.4859 4.38343L20.4873 4.38016ZM16.7459 19.9303L16.8581 20.1525L23.4907 18.633C23.358 17.9989 23.2453 17.4599 23.0884 16.7116L16.7459 19.9303Z" fill="').concat(r,'"/>\n          <path d="M17.2847 7.05528C17.1698 10.7805 17.8461 14.1891 19.5293 17.5704C17.8662 18.3275 16.2721 19.0845 14.6515 19.7772C13.9719 20.0679 13.3381 19.8234 12.9558 19.1832C12.6058 18.5975 12.237 17.979 12.0862 17.3265C11.5417 14.9747 11.0842 12.6028 10.6018 10.2369C10.5555 9.94786 10.5373 9.6549 10.5473 9.36228C10.539 7.51711 10.97 7.00262 12.7693 6.95638C14.2227 6.92131 15.6774 7.01582 17.2847 7.05528Z" fill="white"/>\n          <path d="M31.7696 14.8334C31.9585 11.0776 31.2212 7.7406 29.578 4.41185C31.0761 4.05459 32.423 3.6819 33.7935 3.44738C34.1295 3.39025 34.6949 3.68267 34.8925 3.98888C36.6334 6.61899 37.3245 9.82719 36.8241 12.9562C36.737 13.5493 36.4225 13.9352 35.8496 14.0577C34.557 14.3174 33.2587 14.5518 31.7696 14.8334Z" fill="white"/>\n          <path d="M30.5845 3.32976L21.472 6.0505C21.3314 5.41672 21.2129 4.88597 21.077 4.2872C24.2261 3.52239 27.3227 2.91047 30.5136 3.05131L30.5845 3.32976Z" fill="white"/>\n          <path d="M24.0556 18.4516L23.6949 16.7187L33.1667 15.3963C32.371 16.5327 26.7537 18.0725 24.0556 18.4516Z" fill="white"/>\n          <path d="M20.4857 4.38333C20.6119 4.97775 20.7242 5.50879 20.8587 6.14605C18.4316 6.41814 16.1214 6.1326 13.7865 5.78578L20.4857 4.38333Z" fill="white"/>\n          <path d="M16.7446 19.9335L23.0887 16.7147C23.244 17.463 23.3563 17.9941 23.491 18.6361L16.8584 20.1556L16.7446 19.9335Z" fill="white"/>\n        </g>\n        <defs>\n          <clipPath id="clip0_42_2510">\n            <rect width="40" height="22" fill="white" transform="translate(40.9717 21.9766) rotate(177.357)"/>\n          </clipPath>\n        </defs>\n      </svg>\n    ');return j().divIcon({html:i,className:"driver-marker",iconSize:[56,32],iconAnchor:[28,16],popupAnchor:[0,-45]})},Y=e=>{switch(e){case"start":return"#22c55e";case"end":return"#ef4444";case"driver":return"#3b82f6";case"waypoint":return"#f59e0b";default:return"#8b5cf6"}};(0,i.useEffect)(()=>{if(!c||u.length<2)return void E([]);let e=!1,t=new AbortController;return(async t=>{try{E([]);let n=u.map(e=>"".concat(e.longitude,",").concat(e.latitude)).join(";"),r=await fetch("https://router.project-osrm.org/route/v1/driving/".concat(n,"?overview=full&geometries=geojson"),{signal:t});if(!r.ok)throw Error("Ошибка построения маршрута");let i=await r.json();if(i.routes&&i.routes[0]){let t=i.routes[0].geometry.coordinates.map(e=>[e[1],e[0]]);e||E(t)}}catch(n){t.aborted||e||E(u.map(e=>[e.latitude,e.longitude]))}})(t.signal),()=>{e=!0,t.abort()}},[c,u]);let G=(e,t)=>{if(!e.latitude||!e.longitude)return null;let n=y.includes(e.id);return(0,r.jsx)(z,{position:[e.latitude,e.longitude],icon:t,children:(0,r.jsx)(F,{children:(0,r.jsxs)("div",{style:{minWidth:"200px"},children:[(0,r.jsx)("div",{style:{marginBottom:"8px",fontWeight:600},children:e.name}),(0,r.jsx)("div",{style:{fontSize:"12px",marginBottom:"8px"},children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Координаты:"})," ",e.latitude.toFixed(6),", ",e.longitude.toFixed(6)]})}),C&&(0,r.jsx)("button",{type:"button",onClick:()=>{C(e,!n)},style:{width:"100%",padding:"6px 12px",backgroundColor:n?"#ef4444":"#22c55e",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",transition:"background-color 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor=n?"#dc2626":"#16a34a"},onMouseOut:e=>{e.currentTarget.style.backgroundColor=n?"#ef4444":"#22c55e"},children:n?"❌ Убрать":"✅ Выбрать"})]})})},"loc-".concat(e.id))},V=(0,i.useRef)(new Map);return(0,r.jsx)("div",{className:x,style:{height:a,width:s},children:(0,r.jsxs)(T,{center:L,zoom:l,style:{height:"100%",width:"100%"},scrollWheelZoom:!0,children:[(0,r.jsx)(B,{attribution:'\xa9 <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),p&&(0,r.jsx)(Z,{onMapClick:p}),f&&(0,r.jsx)(H,{onBoundsChange:f}),o&&(0,r.jsx)(z,{position:L,children:(0,r.jsx)(F,{children:d||"Координаты: ".concat(t.toFixed(6),", ").concat(n.toFixed(6))})}),u.map((e,t)=>{if("driver"===e.type){let n="heading"in e?e.heading:0;return(0,r.jsx)(z,{position:[e.latitude,e.longitude],icon:U("Economy",!1,n),children:(0,r.jsx)(F,{children:(0,r.jsxs)("div",{style:{minWidth:"200px"},children:[(0,r.jsx)("div",{style:{marginBottom:"8px",fontWeight:600},children:"\uD83D\uDE97 Мое местоположение"}),(0,r.jsxs)("div",{style:{fontSize:"12px",marginBottom:"8px"},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Координаты:"})," ",e.latitude.toFixed(6),", ",e.longitude.toFixed(6)]}),n>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Направление:"})," ",n.toFixed(0),"\xb0"]})]})]})})},"driver-".concat(t))}let n=String.fromCharCode(65+u.slice(0,t).filter(e=>"driver"!==e.type).length),i=Y(e.type),l="id"in e&&e.id;return(0,r.jsx)(z,{position:[e.latitude,e.longitude],icon:q(i,n),children:(0,r.jsx)(F,{children:(0,r.jsxs)("div",{style:{minWidth:"200px"},children:[(0,r.jsx)("div",{style:{marginBottom:"8px",fontWeight:600},children:e.name||"Точка ".concat(t+1)}),(0,r.jsxs)("div",{style:{fontSize:"12px",marginBottom:"8px"},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Тип:"})," ",e.type||"Неизвестно"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Координаты:"})," ",e.latitude.toFixed(6),", ",e.longitude.toFixed(6)]})]}),l&&C&&(0,r.jsx)("button",{type:"button",onClick:()=>{C({id:e.id,name:e.name||"Точка",latitude:e.latitude,longitude:e.longitude,address:e.name||"Точка",city:"",country:"",region:"",type:W.i.Other,isActive:!0},!1)},style:{width:"100%",padding:"6px 12px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",transition:"background-color 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#dc2626"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#ef4444"},children:"❌ Убрать точку"})]})})},t)}),g&&h.map((e,t)=>{var n,i;let l=v===e.id,a=null==w?void 0:w(e.id),s=0;if(u.length>0){let t=u.filter(e=>"driver"!==e.type);if(t.length>0){let n=t[0];s=(180*Math.atan2(n.longitude-e.currentLocation.longitude,n.latitude-e.currentLocation.latitude)/Math.PI+360)%360}}return(0,r.jsx)(z,{position:[e.currentLocation.latitude,e.currentLocation.longitude],icon:U(e.serviceClass,l,s),children:(0,r.jsx)(F,{children:(0,r.jsxs)("div",{style:{minWidth:"250px"},children:[(0,r.jsxs)("div",{style:{marginBottom:"12px"},children:[(0,r.jsx)("strong",{style:{fontSize:"14px"},children:(null==a?void 0:a.fullName)||"Активный водитель"}),l&&(0,r.jsx)("span",{style:{marginLeft:"8px",fontSize:"10px",backgroundColor:"#22c55e",color:"white",padding:"2px 6px",borderRadius:"4px",fontWeight:"bold"},children:"✓ ВЫБРАН"})]}),a&&(0,r.jsxs)("div",{style:{fontSize:"12px",marginBottom:"12px"},children:[(0,r.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,r.jsx)("strong",{children:"\uD83D\uDCDE Телефон:"})," ",String(a.phoneNumber||"Не указан")]}),(0,r.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,r.jsx)("strong",{children:"⭐ Рейтинг:"})," ","number"==typeof a.rating?"".concat(a.rating,"/5"):"Нет рейтинга"]}),(0,r.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,r.jsx)("strong",{children:"\uD83D\uDFE2 Статус:"})," ",a.online?"Онлайн":"Оффлайн"]})]}),(0,r.jsxs)("div",{style:{fontSize:"12px",marginBottom:"12px"},children:[(0,r.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,r.jsx)("strong",{children:"\uD83D\uDE97 Автомобиль:"})," ",String((null==a||null==(n=a.activeCar)?void 0:n.make)||"Неизвестно")," ",String((null==a||null==(i=a.activeCar)?void 0:i.model)||e.type)]}),(()=>{var e;let t=a&&(null==(e=a.activeCar)?void 0:e.licensePlate);return t?(0,r.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,r.jsx)("strong",{children:"\uD83D\uDD22 Номер:"}),(0,r.jsx)("span",{style:{marginLeft:"4px",backgroundColor:"#f3f4f6",padding:"2px 6px",borderRadius:"4px",fontWeight:"bold"},children:String(t)})]}):null})(),(0,r.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,r.jsx)("strong",{children:"\uD83C\uDFAF Класс:"})," ",e.serviceClass]}),(()=>{var e;let t=a&&(null==(e=a.activeCar)?void 0:e.color);return t?(0,r.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,r.jsx)("strong",{children:"\uD83C\uDFA8 Цвет:"})," ",String(t)]}):null})(),(()=>{var e;let t=a&&(null==(e=a.activeCar)?void 0:e.passengerCapacity);return t?(0,r.jsxs)("div",{style:{marginBottom:"6px"},children:[(0,r.jsx)("strong",{children:"\uD83D\uDC65 Мест:"})," ",String(t)]}):null})()]}),(0,r.jsxs)("div",{style:{fontSize:"10px",color:"#6b7280",marginBottom:"8px"},children:[(0,r.jsx)("strong",{children:"\uD83D\uDCCD Координаты:"})," ",e.currentLocation.latitude.toFixed(6),", ",e.currentLocation.longitude.toFixed(6)]}),m&&(0,r.jsx)("button",{type:"button",onClick:()=>{l?m(""):m(e)},style:{width:"100%",padding:"8px 12px",backgroundColor:l?"#ef4444":"#3b82f6",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",transition:"background-color 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor=l?"#dc2626":"#2563eb"},onMouseOut:e=>{e.currentTarget.style.backgroundColor=l?"#ef4444":"#3b82f6"},children:l?"❌ Отписаться":"✅ Выбрать водителя"})]})})},"driver-".concat(e.id))}),c&&R.length>=2&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A,{positions:R,color:O?"#7f1d1d":"#1e40af",weight:4,opacity:.8}),(0,r.jsx)(A,{positions:R,color:O?"#ef4444":"#3b82f6",weight:2,opacity:.9}),O&&D&&(0,r.jsx)(_,{center:[D.latitude,D.longitude],radius:S,color:"#ef4444",weight:2,opacity:.7,fillColor:"#ef4444",fillOpacity:.2,dashArray:"5, 5"})]}),N&&u&&u.length>0&&u[0]&&"number"==typeof u[0].latitude&&"number"==typeof u[0].longitude&&!isNaN(u[0].latitude)&&!isNaN(u[0].longitude)&&(0,r.jsx)(_,{center:[u[0].latitude,u[0].longitude],radius:M,color:"#3b82f6",weight:2,opacity:.6,fillColor:"#3b82f6",fillOpacity:.1}),b.map(e=>G(e,q(Y("location"))))]})})},q=e=>{let{_ride:t,startLocation:n,endLocation:l,additionalStops:a=[]}=e,{location:s,isTracking:o,error:d}=(0,g.t0)(),u=(0,i.useMemo)(()=>{let e=[];return n&&n.latitude&&n.longitude&&e.push({latitude:n.latitude,longitude:n.longitude,name:"\uD83D\uDCCD ".concat(n.name),type:"start",id:n.id}),a.forEach((t,n)=>{t&&t.latitude&&t.longitude&&e.push({latitude:t.latitude,longitude:t.longitude,name:"\uD83D\uDED1 ".concat(t.name),type:"waypoint",id:t.id})}),l&&l.latitude&&l.longitude&&e.push({latitude:l.latitude,longitude:l.longitude,name:"\uD83C\uDFC1 ".concat(l.name),type:"end",id:l.id}),s&&e.push({latitude:s.latitude,longitude:s.longitude,name:"\uD83D\uDE97 Мое местоположение",type:"driver",heading:s.heading}),e},[n,l,a,s]),c=(0,i.useMemo)(()=>s?{latitude:s.latitude,longitude:s.longitude}:n&&n.latitude&&n.longitude?{latitude:n.latitude,longitude:n.longitude}:u.length>0?{latitude:u[0].latitude,longitude:u[0].longitude}:{latitude:42.8746,longitude:74.5698},[s,n,u]),h=(0,i.useMemo)(()=>0===u.length?11:1===u.length?15:13,[u.length]);return(0,r.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,r.jsx)(Q,{latitude:c.latitude,longitude:c.longitude,zoom:h,height:"100%",showMarker:!1,routePoints:u,showRoute:u.length>=2,currentDriverLocation:s?{latitude:s.latitude,longitude:s.longitude}:void 0,routeDeviationThreshold:100,className:"w-full h-full"}),!s&&(0,r.jsxs)("div",{className:"absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg z-10",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2 text-sm",children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("span",{className:"text-blue-600",children:"Получение геолокации..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 bg-gray-400 rounded-full"}),(0,r.jsx)("span",{className:"text-gray-600",children:"Геолокация недоступна"})]})}),d&&(0,r.jsx)("p",{className:"text-xs mt-1 text-red-500",children:d})]})]})},U=e=>{let{orderId:t}=e,u=(0,l.useRouter)(),f=(0,l.useSearchParams)(),b=t||f.get("orderId");f.get("orderType");let{orderData:j,isLoading:y,error:C,refetch:w}=p(b||""),{location:N}=(0,g.t0)(),{refreshRides:M}=(0,h.g0)(),{startLocation:D,endLocation:S,additionalStops:k,isLoading:L}=d(null==j?void 0:j.order.startLocationId,null==j?void 0:j.order.endLocationId,null==j?void 0:j.order.additionalStops),R=async e=>{var t;let n=await a.uE.get("/Ride/for-order/".concat(e));if(!(null==(t=n.data)?void 0:t.id))throw Error("Не удалось получить ID поездки");return n.data.id},E=async e=>{try{if(!j)return;let i=await R(j.order.id);switch(e.id){case"driver-heading":await (0,x.Z6)(i);break;case"driver-arrived":await (0,x.wp)(i);break;case"ride-started":await (0,x.uW)(i);break;case"ride-finished":await (0,x.$z)(i),await M();break;case"cancel":var t,r;if(!(null==(t=(r=n.g).confirm)?void 0:t.call(r,"Вы уверены, что хотите отменить поездку?")))return;await (0,x.pL)(i),await M(),setTimeout(()=>{u.push("/")},1e3);break;default:return}s.P.success("Действие выполнено успешно"),await w(),"ride-finished"===e.id&&setTimeout(()=>{u.push("/")},1e3)}catch(e){console.error("Ошибка выполнения действия:",e),s.P.error("Не удалось выполнить действие")}},P=(0,i.useMemo)(()=>(null==j?void 0:j.order.passengers)?j.order.passengers.map(e=>({id:e.id,firstName:e.firstName,isMainPassenger:e.isMainPassenger})):[],[null==j?void 0:j.order.passengers]);return b?y||L?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{children:"Загрузка данных поездки..."})]})}):C||!j?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"mb-4",children:C||"Данные заказа не найдены"}),(0,r.jsx)("button",{onClick:()=>u.push("/"),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:"Вернуться к заявкам"})]})}):(0,r.jsx)("div",{className:"h-full flex flex-col p-2 bg-gray-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"relative w-full h-full bg-gray-100 rounded-lg border border-gray-200",children:[(0,r.jsx)(c,{order:j.order,availableActions:(()=>{let e=[];return j&&(j.order.status===o.Re.InProgress&&j.order.subStatus===o.lk.DriverHeading&&e.push({id:"driver-arrived",label:"Я прибыл",endpoint:"",method:"POST",variant:"primary"}),j.order.status===o.Re.InProgress&&j.order.subStatus===o.lk.DriverArrived&&e.push({id:"ride-started",label:"Взял пассажира",endpoint:"",method:"POST",variant:"primary"}),j.order.status===o.Re.InProgress&&j.order.subStatus===o.lk.RideStarted&&e.push({id:"ride-finished",label:"Завершить поездку",endpoint:"",method:"POST",variant:"primary"}),j.order.status!==o.Re.Completed&&j.order.status!==o.Re.Cancelled&&e.push({id:"cancel",label:"Отменить",endpoint:"",method:"POST",variant:"danger"})),e})(),onActionExecute:E}),(0,r.jsx)(q,{_ride:j.order,startLocation:D,endLocation:S,additionalStops:k}),(0,r.jsx)(m,{startLocation:D,endLocation:S,additionalStops:k,driverLocation:N,orderStatus:j.order.status,orderSubStatus:j.order.subStatus}),P.length>0&&(0,r.jsx)(v,{passengers:P,services:j.order.services||[],initialPrice:j.order.initialPrice})]})}):(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"mb-4",children:"Не указан ID заказа"}),(0,r.jsx)("button",{onClick:()=>u.push("/"),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:"Вернуться к заявкам"})]})})},Y=e=>{let{orderId:t}=e;return(0,r.jsx)("div",{className:"h-full",children:(0,r.jsx)(U,{orderId:t})})}},25717:(e,t,n)=>{Promise.resolve().then(n.bind(n,25211))},98893:(e,t,n)=>{"use strict";n.d(t,{C:()=>r.C});var r=n(62571)}},e=>{var t=t=>e(e.s=t);e.O(0,[3257,5233,9031,7261,3766,1938,7914,2906,6467,2634,3822,8128,510,587,8315,7358],()=>t(25717)),_N_E=e.O()}]);