"use strict";exports.id=9533,exports.ids=[9533],exports.modules={89533:(e,r,s)=>{s.r(r),s.d(r,{SelectUserRoleModal:()=>x});var l=s(13486),o=s(97008),a=s(2984);s(60159);var i=s(34319),c=s(38655),n=s(97743),t=s(71483),d=s(1106),m=s(86157),u=s(35293);let x=()=>{let e=(0,a.useRouter)(),r=(0,o.e3)(c.t0),s=(0,o.e3)(c.jB),n=(0,o.e3)(c.Oo),t=(0,o.e3)(c.$A);return(0,l.jsx)("div",{className:"absolute inset-0 z-50 w-full h-full flex justify-between items-center backdrop-filter backdrop-blur-[7px]",children:(0,l.jsxs)("div",{className:"rounded-lg shadow-lg p-6 w-full max-w-6xl mx-auto",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Выбор роли пользователя"}),(0,l.jsx)("button",{onClick:()=>{n(void 0)},className:"w-10 h-10 bg-gray-50/30 rounded-full flex items-center justify-center text-red-500 hover:bg-red-100/30 transition-all duration-200 text-2xl leading-none cursor-pointer",children:(0,l.jsx)(i.default,{size:20})})]}),(0,l.jsx)(h,{selectedRole:r,onRoleSelect:r=>{s(r),t(r),n(void 0),e.push("/users/create")}})]})})};function h({selectedRole:e,onRoleSelect:r}){return(0,l.jsx)("div",{className:"w-full h-full flex flex-col gap-6",children:(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Object.keys(t.D).filter(e=>e!==d.Xh.Unknown).map(s=>(0,l.jsxs)("div",{onClick:()=>r(s),className:`backdrop-blur-md bg-black/80 rounded-lg shadow-[0_0_10px_rgba(0,0,0,0.1)] hover:shadow-[0_0_15px_rgba(0,0,0,0.2)] transition-shadow duration-300 p-6 h-full border cursor-pointer ${e===s?"border-[color:var(--color-primary)] bg-[color:var(--color-primary-light)]":"border-[color:var(--color-neutral-200)] hover:border-[color:var(--color-primary)]"}`,children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"min-w-12 min-h-12 rounded-full bg-[color:var(--color-neutral-100)] flex items-center justify-center text-[color:var(--color-primary)] border-2 border-[color:var(--color-neutral-100)]",children:(0,l.jsx)(n.J,{iconName:(0,u.mp)(s)})}),(0,l.jsx)("h2",{className:"text-xl font-semibold ml-4",children:(0,m.r4)(s)})]}),(0,l.jsx)("p",{children:(0,u.PA)(s)})]},s))})})}},97743:(e,r,s)=>{s.d(r,{J:()=>x});var l=s(13486),o=s(60159);let a=(0,o.lazy)(()=>Promise.resolve().then(s.bind(s,35235))),i=(0,o.lazy)(()=>Promise.resolve().then(s.bind(s,97376))),c=(0,o.lazy)(()=>Promise.resolve().then(s.bind(s,51249))),n=(0,o.lazy)(()=>Promise.resolve().then(s.bind(s,29383))),t=(0,o.lazy)(()=>Promise.resolve().then(s.bind(s,27766))),d=(0,o.lazy)(()=>Promise.resolve().then(s.bind(s,2262))),m=(0,o.lazy)(()=>Promise.resolve().then(s.bind(s,24986))),u=(0,o.lazy)(()=>Promise.resolve().then(s.bind(s,71015))),x=({iconName:e,className:r="w-6 h-6",size:s=24})=>{let x=(e=>{switch(e){case"DashboardIcon":return(0,l.jsx)(a,{className:r,size:s});case"ProfileIcon":return(0,l.jsx)(i,{className:r,size:s});case"IdentityCardIcon":return(0,l.jsx)(c,{className:r,size:s});case"UsersIcon":return(0,l.jsx)(n,{className:r,size:s});case"VehiclesIcon":return(0,l.jsx)(t,{className:r,size:s});case"TariffsIcon":return(0,l.jsx)(d,{className:r,size:s});case"OrdersIcon":return(0,l.jsx)(m,{className:r,size:s});case"ReferencesIcon":return(0,l.jsx)(u,{className:r,size:s});default:return null}})(e);return x?(0,l.jsx)(o.Suspense,{fallback:(0,l.jsx)("div",{className:r}),children:x}):(0,l.jsx)("div",{className:r})}}};