import type { z } from 'zod';
import type {
  FormConfig,
  FormField,
  FormFieldGroup,
  FieldOptionsProviders,
  FormFieldOption,
} from '@entities/collections/types/formTypes';

/**
 * Интерфейс для пропсов компонента DynamicForm
 */
export interface DynamicFormProps<T> {
  config: FormConfig;
  initialData?: T;
  validationSchema?: z.ZodTypeAny;
  onSubmit: (data: T) => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
  onCancel?: () => void;
  fieldOptionsProviders?: FieldOptionsProviders;
  FieldGroupComponent?: React.ComponentType<FieldGroupProps>;
}
/**
 * Тип для ошибок формы
 */
export type FormError = {
  message?: string;
  type?: string;
  [key: string]: unknown;
};
/**
 * Интерфейс для пропсов компонента FormErrors
 */
export interface FormErrorsProps {
  errors: Record<string, FormError>;
}
export interface FormActionsProps {
  onCancel?: () => void;
  isLoading: boolean;
  mode: 'create' | 'edit';
}
export interface FieldGroupProps {
  group: FormFieldGroup;
  renderField: (field: FormField) => React.ReactNode;
}
export interface RenderFieldProps {
  field: FormField;
  errors: Record<string, FormError>;
  isLoading: boolean;
  lazyOptions: Record<string, FormFieldOption[]>;
  loadingOptions: Record<string, boolean>;
  fieldOptionsProviders?: FieldOptionsProviders;
}
