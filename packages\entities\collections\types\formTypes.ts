import type React from 'react';
import type { CollectionName } from '@entities/collections/config';
import type { ApiEndpoints } from '@entities/collections/config/apiEndpointRegistry';

/**
 * Типы полей ввода для форм
 */
export enum FormFieldType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  EMAIL = 'email',
  PASSWORD = 'password',
  PHONE = 'phone',
  DATE = 'date',
  TIME = 'time',
  DATETIME = 'datetime',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  BUTTON_GROUP = 'button_group', // Группа кнопок для переключения (как radio, но в виде кнопок)
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  FILE = 'file',
  IMAGE = 'image',
  HIDDEN = 'hidden',
  GROUP = 'group',
  DRIVER_INFO = 'driver_info', // Кастомное поле для отображения информации о водителе
  MAP_LOCATION_PICKER = 'map_location_picker', // Интерактивная карта для выбора координат
}
/**
 * Интерфейс для опций выбора (для select, multiselect, radio)
 */
export interface FormFieldOption {
  value: string | number | boolean;
  label: string;
  description?: string; // Описание для больших карточек
  data?: Record<string, unknown>; // Дополнительные данные из API (например, type, address, etc.)
}
/**
 * Типы данных для полей формы
 */
export enum FormFieldDataType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  ARRAY = 'array',
  OBJECT = 'object',
  ENUM = 'enum',
}
/**
 * Интерфейс для поля формы
 */
export interface FormField {
  name: string;
  label?: string;
  type: FormFieldType;
  dataType?: FormFieldDataType; // Тип данных для поля (для валидации и преобразования)
  placeholder?: string;
  description?: string; // Описание для группы полей
  helperText?: string;
  helperSmallText?: string; // Короткий текст подсказки для отображения рядом с иконкой
  helperBigText?: string; // Полный текст подсказки для отображения в тултипе
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  hidden?: boolean;
  min?: number;
  max?: number;
  step?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  options?: FormFieldOption[];
  className?: string; // CSS-класс для поля
  dependsOn?: {
    field: string;
    value: unknown;
    condition?: 'equals' | 'notEquals' | 'contains' | 'notContains' | 'greaterThan' | 'lessThan';
  };
  preserveType?: boolean; // Флаг, указывающий, что тип данных должен быть сохранен (не преобразован)
  fields?: FormField[]; // <-- Теперь можно создавать вложенные группы
  layout?: FormFieldGroupLayout; // <-- Настройки макета для вложенных групп
  defaultValue?: unknown;
  optionsProviderKey?: string; // Ключ для динамической подгрузки options
  dynamicReference?: DynamicReference; // Конфигурация для динамической подгрузки
  showNavigateButton?: boolean; // Показывать кнопку навигации (для MULTISELECT)
  multiselectDataType?: 'static' | 'dynamic'; // Тип данных для определения цветов (для MULTISELECT)
  colorFieldName?: string; // Имя поля для получения цвета (для MULTISELECT)

  // Свойства для MapLocationPicker с поиском адресов
  enableAddressSearch?: boolean; // Включить поиск адресов
  searchPlaceholder?: string; // Плейсхолдер для поля поиска
  maxSearchResults?: number; // Максимальное количество результатов поиска
  minSearchLength?: number; // Минимальная длина запроса для поиска
  height?: string; // Высота карты
  zoom?: number; // Уровень зума карты
  getColorFromValue?: (value: string) => string; // Функция для получения цвета из значения
  getDescriptionFromValue?: (value: string) => string; // Функция для получения описания по значению (например, getLocationTypeLabel)
  writeOperations?: DynamicWriteOperation[]; // Операции записи для динамических полей
  searchable?: boolean; // Включить поиск в селекте (для SELECT полей)
  showDescription?: boolean; // Показывать описание в опциях (для SELECT полей)
  isServiceField?: boolean; // Флаг, указывающий, что поле служебное и не должно отправляться на API
}
/**
 * Интерфейс для настройки верстки группы полей
 */
export interface FormFieldGroupLayout {
  gridCols?: number; // общее количество колонок
  gapX?: number; // горизонтальный отступ между колонками
  gapY?: number; // вертикальный отступ между строками
  className?: string;
  flexDirection?: 'row' | 'column'; // направление элементов (по умолчанию 'row')
  maxItemsPerRow?: number; // максимальное количество элементов в строке при flexDirection='row'
  itemsAlignment?: 'start' | 'center' | 'end' | 'stretch'; // выравнивание элементов
}
/**
 * Интерфейс для группы полей формы
 */
export interface FormFieldGroup {
  id: string;
  title: string;
  description?: string;
  fields?: FormField[];
  collapsed?: boolean; // Свернута ли группа по умолчанию
  order?: number; // Порядок отображения группы
  layout?: FormFieldGroupLayout; // Настройки верстки группы
  customComponent?: React.ComponentType<{}>; // Кастомный компонент для группы полей (без пропсов)
}
/**
 * Интерфейс для конфигурации формы
 * @template TData Тип данных формы
 */
export interface FormConfig {
  title: {
    create: string;
    edit: string;
  };
  description?: {
    create?: string;
    edit?: string;
  };
  groups: FormFieldGroup[];
  writeOperations?: DynamicWriteOperation[]; // Операции записи после сохранения формы
}
/**
 * Тип для реестра конфигураций форм
 */
export type FormConfigRegistry = {
  [key in CollectionName]?: FormConfig;
};
export type FieldOptionsProvider = () => Promise<FormFieldOption[]> | FormFieldOption[];
export type FieldOptionsProviders = Record<string, FieldOptionsProvider>;
/**
 * Интерфейс для фильтрации данных
 */
export interface DynamicReferenceFilter {
  field: string; // Поле для фильтрации
  operator:
    | 'eq'
    | 'ne'
    | 'gt'
    | 'gte'
    | 'lt'
    | 'lte'
    | 'contains'
    | 'startsWith'
    | 'endsWith'
    | 'in'
    | 'notIn'; // Оператор сравнения
  value: string | number | boolean | string[] | number[]; // Значение для фильтрации
} /**
 * Интерфейс для сортировки данных
 */
export interface DynamicReferenceSort {
  field: string; // Поле для сортировки
  direction: 'asc' | 'desc'; // Направление сортировки
} /**
 * Интерфейс для связанного поля
 */
export interface RelatedFieldReference {
  sourceField: string; // Поле-источник данных (например, 'car' или 'drivers')
  sourceEndpoint?: string; // Эндпоинт для получения данных источника (если нужно)
  sourceArrayField?: string; // Поле в ответе источника, содержащее массив ID (например, 'drivers')
  targetEndpoint: string; // Эндпоинт для получения данных по ID (например, '/User/{uuid}')
  mode: 'single' | 'batch' | 'array'; // Режим работы
  // single - одиночный запрос по ID
  // batch - множественные запросы по каждому ID
  // array - один запрос с массивом ID в параметрах
  batchParam?: string; // Параметр для передачи массива ID (для режима array)
}
/**
 * Интерфейс для динамической подгрузки данных
 */
export interface DynamicReference {
  api: keyof ApiEndpoints; // Ключ API эндпоинта из реестра
  endpoint: string; // Явный API эндпоинт
  limit?: number; // Максимальное количество записей (по умолчанию 10)
  filters?: DynamicReferenceFilter[] | Record<string, string | number | boolean>; // Фильтры для данных (массив или объект)
  sort?: DynamicReferenceSort[]; // Сортировка данных
  relatedField?: RelatedFieldReference; // Конфигурация связанного поля
}

/**
 * Интерфейс для операций записи с динамическими данными
 */
export interface DynamicWriteOperation {
  api: 'post' | 'put' | 'patch' | 'delete'; // Тип HTTP операции
  endpoint: string; // Шаблон эндпоинта, например: '/Car/{uuid}/drivers/{driver_id}'
  method?: 'assign' | 'update' | 'remove' | 'setActive' | 'setInactive'; // Тип операции для логики
  condition?: 'onTrue' | 'onFalse' | 'always'; // Условие выполнения операции
  pathParams?: Record<string, string>; // Параметры для подстановки в URL (например, {uuid: 'formData.id', driver_id: 'fieldValue'})
  queryParams?: Record<string, string>; // Query параметры
  bodyData?: 'fieldValue' | Record<string, unknown>; // Данные для тела запроса
  order?: number; // Порядок выполнения операции (опциональный, по умолчанию определяется типом операции)
  description?: string; // Описание операции для логирования
}
/**
 * Интерфейс для операций записи с динамическими данными
 */
export interface DynamicWriteOperation {
  api: 'post' | 'put' | 'patch' | 'delete'; // Тип операции
  endpoint: string; // Шаблон эндпоинта, например: '/Car/{uuid}/drivers/{driver_id}'
  method?: 'assign' | 'update' | 'remove' | 'setActive' | 'setInactive'; // Тип операции для логики
  condition?: 'onTrue' | 'onFalse' | 'always'; // Условие выполнения операции
  pathParams?: Record<string, string>; // Параметры для подстановки в URL
  queryParams?: Record<string, string>; // Query параметры
  bodyData?: 'fieldValue' | Record<string, unknown>; // Данные для тела запроса
  order?: number; // Порядок выполнения операции (опциональный, по умолчанию определяется типом операции)
}
