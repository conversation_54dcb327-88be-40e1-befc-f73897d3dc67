"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9567],{59567:(c,r,e)=>{e.r(r),e.d(r,{default:()=>i});var s=e(54568);function i(c){let{size:r=24,className:e=""}=c;return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:e,children:[(0,s.jsx)("circle",{cx:"9",cy:"5",r:"1"}),(0,s.jsx)("circle",{cx:"9",cy:"12",r:"1"}),(0,s.jsx)("circle",{cx:"9",cy:"19",r:"1"}),(0,s.jsx)("circle",{cx:"15",cy:"5",r:"1"}),(0,s.jsx)("circle",{cx:"15",cy:"12",r:"1"}),(0,s.jsx)("circle",{cx:"15",cy:"19",r:"1"})]})}e(7620)}}]);