(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{4499:(e,r,s)=>{"use strict";s.d(r,{v:()=>d});var t=s(62942),a=s(7620),i=s(80745),o=s(79702),l=s(8703),n=s(17691);let d=()=>{let e=(0,t.useRouter)(),[r,s]=(0,a.useState)(!1),[d,u]=(0,a.useState)(null),[c,m]=(0,a.useState)({});return{login:(0,a.useCallback)(async r=>{s(!0),u(null),m({});let t=await (0,o.n)(n.y.loginWithValidation(r));if(t.success)l.P.success("Вход выполнен успешно!"),e.push(i.vc.MAIN.HOME);else{let e=t.error||"Произошла неизвестная ошибка";u(e),t.fieldErrors&&m(t.fieldErrors),l.P.error(e)}s(!1)},[e]),isLoading:r,error:d,fieldErrors:c,clearFieldError:(0,a.useCallback)(e=>{m(r=>{let s={...r};return delete s[e],s})},[])}}},4656:(e,r,s)=>{"use strict";s.d(r,{E9:()=>o,X5:()=>a,oW:()=>l,rA:()=>i});var t=s(75006);let a=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}),password:t.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"})}),i=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:t.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"}),fullName:t.z.string().min(1,{message:"ФИО обязательно"}).max(255,{message:"ФИО не должно превышать 255 символов"}),companyName:t.z.string().min(1,{message:"Название компании обязательно"}).max(255,{message:"Название компании не должно превышать 255 символов"}),legalAddress:t.z.string().min(1,{message:"Юридический адрес обязателен"}).max(500,{message:"Юридический адрес не должен превышать 500 символов"})}),o=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"})}),l=t.z.object({email:t.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"}),resetCode:t.z.string().min(1,{message:"Код сброса пароля обязателен"}),newPassword:t.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"})})},17691:(e,r,s)=>{"use strict";s.d(r,{y:()=>l});var t=s(22223),a=s(21715),i=s(80745);class o extends t.v{async login(e){let r=await this.post("/login",e);return this.handleApiResult(r)}async loginWithValidation(e){try{let r=await this.post("/login",{email:e.email,password:e.password});if(r.error){let s=r.error.message,t={};if(r.error.type===a.r6.Auth)s="Неверный email или пароль. Пожалуйста, проверьте правильность учетных данных.",t.email=!0,t.password=!0;else if(r.error.type===a.r6.Network)s="Проблема с сетевым подключением. Проверьте интернет-соединение.";else if(r.error.type===a.r6.Validation&&r.error.data&&"object"==typeof r.error.data){let a=r.error.data;if(a.errors&&"object"==typeof a.errors){let r=a.errors;if(r.UserNotFound&&r.UserNotFound.length>0)s="Пользователь с email '".concat(e.email,"' не найден."),t.email=!0;else if(r.InvalidEmail&&r.InvalidEmail.length>0)s="Указан некорректный email.",t.email=!0;else{let e=Object.entries(r).map(e=>{let[r,s]=e;return"".concat("RequiredField"===r?"Обязательное поле":r,": ").concat(s.join(", "))}).join("\n");e&&(s=e)}}}return{success:!1,error:s,fieldErrors:Object.keys(t).length>0?t:void 0}}return{success:!0,data:this.handleApiResult(r)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла неизвестная ошибка"}}}async register(e){let r=await this.post("/register",e);return this.handleApiResult(r)}async registerPartnerWithValidation(e){try{let r=await this.post("/register/partner",e);if(r.error)return{success:!1,error:r.error.message};return{success:!0,data:this.handleApiResult(r)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при регистрации"}}}async refreshToken(e){let r=await this.post("/refresh",e);return this.handleApiResult(r)}async logout(){let e=await this.post("/logout");this.handleApiResult(e)}async logoutWithCleanup(){try{let e=await this.post("/logout");if(e.error)return{success:!1,error:e.error.message};return this.handleApiResult(e),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при выходе"}}}async resetPassword(e){let r=await this.post("/reset-password",e);this.handleApiResult(r)}async forgotPasswordWithValidation(e){try{let r=await this.post("/forgot-password",e);if(r.error)return{success:!1,error:r.error.message};return this.handleApiResult(r),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при отправке запроса"}}}async resetPasswordWithValidation(e){try{let r=await this.post("/reset-password",e);if(r.error)return{success:!1,error:r.error.message};return this.handleApiResult(r),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Произошла ошибка при сбросе пароля"}}}async changePassword(e){let r=await this.post("/change-password",e);this.handleApiResult(r)}async verifyToken(){try{let e=await this.get("/verify");return this.handleApiResult(e).valid}catch(e){return!1}}async getCurrentUser(){let e=await this.get("/me");return this.handleApiResult(e)}constructor(...e){super(...e),this.baseUrl=i.QQ.AUTH.BASE}}let l=new o},21715:(e,r,s)=>{"use strict";s.d(r,{$P:()=>u,$Y:()=>E,Al:()=>m,Ay:()=>h,Vg:()=>d,mu:()=>c,r6:()=>i});var t=s(44089),a=s(89329),i=function(e){return e.Network="network",e.Auth="auth",e.Forbidden="forbidden",e.NotFound="not_found",e.Validation="validation",e.Server="server",e.Unknown="unknown",e}({});let o=e=>{if((0,t.F0)(e)){var r,s,a,i;let t,o=null==(r=e.response)?void 0:r.status,l="unknown",n=e.message;if(null==(s=e.response)?void 0:s.data){let r=e.response.data;if(r.errors&&"object"==typeof r.errors&&(t=r.errors)&&Object.keys(t).length>0){let e=Object.keys(t)[0];e&&(null==(i=t[e])?void 0:i.length)>0&&(n=t[e][0])}r.title&&"string"==typeof r.title&&(n=r.title)}switch(o){case 401:l="auth",t||(n="Ошибка авторизации. Пожалуйста, войдите в систему.");break;case 403:l="forbidden",t||(n="Доступ запрещен. У вас нет прав для выполнения этого действия.");break;case 404:l="not_found",t||(n="Ресурс не найден.");break;case 400:case 422:l="validation",t||(n="Ошибка валидации данных.");break;case 500:case 502:case 503:case 504:l="server",t||(n="Ошибка сервера. Пожалуйста, попробуйте позже.");break;default:o?o>=400&&o<500?(l="validation",t||(n="Ошибка в запросе.")):o>=500&&(l="server",t||(n="Ошибка сервера. Пожалуйста, попробуйте позже.")):(l="network",t||(n="Ошибка сети. Пожалуйста, проверьте подключение к интернету."))}return{type:l,message:n,statusCode:o,data:null==(a=e.response)?void 0:a.data,errors:t,originalError:e}}return e instanceof Error?{type:"unknown",message:e.message,originalError:e}:{type:"unknown",message:"Произошла неизвестная ошибка",originalError:e}},l=(e=>{let r={},s=a.A.create({baseURL:"https://api.testingkg.su",withCredentials:!0,headers:{"Content-Type":"application/json"},...void 0});return s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>{var s,t,a,i,l,n;let d=o(e);switch(r.onError&&r.onError(d),d.type){case"auth":null==(s=r.onAuthError)||s.call(r,d);break;case"forbidden":null==(t=r.onForbiddenError)||t.call(r,d);break;case"not_found":null==(a=r.onNotFoundError)||a.call(r,d);break;case"validation":null==(i=r.onValidationError)||i.call(r,d);break;case"server":null==(l=r.onServerError)||l.call(r,d);break;case"network":null==(n=r.onNetworkError)||n.call(r,d)}return Promise.reject(d)}),s})(),n=async e=>{try{return{data:await e()}}catch(e){if(e&&"object"==typeof e&&"type"in e)return{error:e};return{error:o(e)}}},d=async(e,r)=>n(async()=>(await l.get(e,r)).data),u=async(e,r,s)=>n(()=>l.post(e,r,s).then(e=>e.data)),c=async(e,r,s)=>n(()=>l.put(e,r,s).then(e=>e.data)),m=async(e,r)=>n(()=>l.delete(e,r).then(e=>e.data)),E=async(e,r,s)=>n(()=>l.patch(e,r,s).then(e=>e.data)),h={client:l,get:d,post:u,put:c,patch:E,delete:m}},22223:(e,r,s)=>{"use strict";s.d(r,{v:()=>a});var t=s(21715);class a{async get(e,r){let s=this.buildUrl(e);return(0,t.Vg)(s,r)}async post(e,r,s){let a=this.buildUrl(e);return(0,t.$P)(a,r,s)}async put(e,r,s){let a=this.buildUrl(e);return(0,t.mu)(a,r,s)}async patch(e,r,s){let a=this.buildUrl(e);return(0,t.$Y)(a,r,s)}async delete(e,r){let s=this.buildUrl(e);return(0,t.Al)(s,r)}buildUrl(e){let r=e.startsWith("/")?e.slice(1):e,s=this.baseUrl.endsWith("/")?this.baseUrl.slice(0,-1):this.baseUrl;return"".concat(s,"/").concat(r)}handleApiResult(e){if(e.error)throw e.error;if(void 0===e.data)throw Error("API returned undefined data");return e.data}handleApiResultSafe(e){return e.error||void 0===e.data?null:e.data}}},62015:(e,r,s)=>{"use strict";s.d(r,{y:()=>t.y});var t=s(83418)},69969:(e,r,s)=>{Promise.resolve().then(s.bind(s,93248))},80745:(e,r,s)=>{"use strict";s.d(r,{QQ:()=>t,vc:()=>a});let t={AUTH:{BASE:"/Auth",LOGIN:"/Auth/login",FORGOT_PASSWORD:"/Auth/forgot_password",RESET_PASSWORD:"/Auth/reset_password",REGISTER_PARTNER:"/Auth/register/partner",LOGOUT:"/Auth/logout"},USER:{SELF:"/User/self",AVATAR:"/User/avatar",LIST:"/User",SEARCH:"/User",GET_BY_ID:"/User/{uuid}",ADMIN:{GET_BY_ID:"/User/Admin/{uuid}",UPDATE:"/User/Admin/{uuid}",CREATE:"/User/Admin"},CUSTOMER:{GET_BY_ID:"/User/Customer/{uuid}",UPDATE:"/User/Customer/{uuid}",CREATE:"/User/Customer"},DRIVER:{LIST:"/User/Driver",SELF:"/User/Driver/self",GET_BY_ID:"/User/Driver/{uuid}",UPDATE:"/User/Driver/{uuid}",CREATE:"/User/Driver"},OPERATOR:{GET_BY_ID:"/User/Operator/{uuid}",UPDATE:"/User/Operator/{uuid}",CREATE:"/User/Operator"},PARTNER:{GET_BY_ID:"/User/Partner/{uuid}",UPDATE:"/User/Partner/{uuid}",CREATE:"/User/Partner"},TERMINAL:{GET_BY_ID:"/User/Terminal/{uuid}",UPDATE:"/User/Terminal/{uuid}",CREATE:"/User/Terminal"}},CAR:{LIST:"/Car",MY:"/Car/my",CREATE:"/Car",UPDATE:"/Car/{uuid}",GET_BY_ID:"/Car/{uuid}",ASSIGN_DRIVER:"/Car/{uuid}/drivers/{driver_id}",UPDATE_DRIVER:"/Car/{uuid}/drivers/{driver_id}",REMOVE_DRIVER:"/Car/{uuid}/drivers/{driver_id}",MY_SET_ACTIVE:"/Car/my/{uuid}/set-active",MY_SET_INACTIVE:"/Car/my/set-inactive"},LOCATION:{LIST:"/Location",SEARCH:"/Location",CREATE:"/Location",UPDATE:"/Location/{uuid}",GET_BY_ID:"/Location/{uuid}",CURRENT_LOCATION:"/Location/CurrentLocation/self"},SERVICE:{LIST:"/Service",CREATE:"/Service",UPDATE:"/Service/{uuid}",DELETE:"/Service/{uuid}",GET_BY_ID:"/Service/{uuid}"},ORDER:{LIST:"/Order",MY_CREATOR:"/Order/my/creator",MY_PARTICIPANT:"/Order/my/participant/{order_id}",CREATE:"/Order",CREATE_SCHEDULED:"/Order/scheduled",CREATE_SCHEDULED_RIDE:"/Order/scheduled/{uuid}/ride",CREATE_INSTANT_BY_OPERATOR:"/Order/instant/by-operator",CREATE_INSTANT_BY_CUSTOMER:"/Order/instant/by-customer",UPDATE:"/Order/{uuid}",UPDATE_SCHEDULED:"/Order/scheduled/{uuid}",UPDATE_INSTANT:"/Order/instant/{uuid}",UPDATE_PASSENGERS:"/Order/{uuid}/passengers",GET_BY_ID:"/Order/{uuid}",GET_SCHEDULED_BY_ID:"/Order/scheduled/{uuid}",GET_INSTANT_BY_ID:"/Order/instant/{uuid}"},ROUTE:{LIST:"/Route",CREATE:"/Route",UPDATE:"/Route/{uuid}",GET_BY_ID:"/Route/{uuid}",DELETE:"/Route/{uuid}"},TARIFF:{LIST:"/Tariff",CREATE:"/Tariff",UPDATE:"/Tariff/{uuid}",GET_BY_ID:"/Tariff/{uuid}"},NOTIFICATION:{LIST:"/Notification",ME:"/Notification/me",CREATE:"/Notification",UPDATE:"/Notification/{uuid}",GET_BY_ID:"/Notification/{uuid}",READ:"/Notification/read"},GIS:{BASE:"/GIS",ACTIVE_DRIVERS:"/GIS/ActiveDrivers"},RIDE:{LIST:"/Ride",MY:"/Ride/my",MY_ASSIGNED:"/Ride/my/assigned",GET_BY_ID:"/Ride/{uuid}",UPDATE:"/Ride/{uuid}",DELETE:"/Ride/{uuid}",FOR_ORDER:"/Ride/for-order/{order_id}",ACCEPT:"/Ride/{uuid}/accept-by-driver",REJECT:"/Ride/{uuid}/reject-by-driver",NOTIFICATION_BY_DRIVER:"/Ride/{uuid}/notification-by-driver",DRIVER_HEADING:"/Ride/{uuid}/status/driver-heading-to-client",DRIVER_ARRIVED:"/Ride/{uuid}/status/driver-arrived",RIDE_STARTED:"/Ride/{uuid}/status/ride-started",RIDE_FINISHED:"/Ride/{uuid}/status/ride-finished",RIDE_CANCELLED:"/Ride/{uuid}/status/ride-cancelled"}},a={AUTH:{LOGIN:"/login",FORGOT_PASSWORD:"/forgot-password",RESET_PASSWORD:"/reset-password",REGISTER:"/register"},MAIN:{HOME:"/"},PROFILE:{SELF:"/profile"},TERMINAL:{MAIN:"/"},LOCATIONS:{TERMINAL_LOCATION:"/locations"},PAYMENT:{TERMINAL_PAYMENT:"/payment"}}},93248:(e,r,s)=>{"use strict";s.r(r),s.d(r,{LoginForm:()=>A});var t=s(54568),a=s(90739),i=s(27261),o=s.n(i),l=s(62942),n=s(7620),d=s(61938),u=s(80745),c=s(62015),m=s(53850),E=s(34763),h=s(4656),p=s(4499);let T=(0,n.lazy)(()=>Promise.resolve().then(s.bind(s,87036))),R=(0,n.lazy)(()=>Promise.resolve().then(s.bind(s,66085))),A=e=>{var r,s;let{defaultValues:i,autoLogin:A=!1}=e,f=(0,l.useRouter)(),{login:g,isLoading:v,fieldErrors:y,clearFieldError:_}=(0,p.v)(),[b,I]=(0,n.useState)(!1),[w,D]=(0,n.useState)(!1),[S,x]=(0,n.useState)(!1),{register:N,handleSubmit:U,formState:{errors:C},getValues:O}=(0,d.mN)({resolver:(0,a.u)(h.X5),defaultValues:i});return(0,n.useEffect)(()=>{A&&(null==i?void 0:i.email)&&(null==i?void 0:i.password)&&!v&&!S&&(x(!0),g(O()))},[A,null==i?void 0:i.email,null==i?void 0:i.password,v,S,O,g]),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(E.w,{isVisible:v,message:"Выполняется вход в систему..."}),(0,t.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,t.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Вход в систему"}),(0,t.jsx)("p",{children:"Введите свои учетные данные для входа"})]}),(0,t.jsxs)("form",{onSubmit:U(e=>g(e)),className:"flex flex-col gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,t.jsx)("input",{id:"email",type:"email",...N("email",{onChange:()=>{y.email&&_("email")}}),className:"block w-full px-2 py-1  text-xs border ".concat(C.email||y.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:v}),(0,t.jsx)(c.y,{message:null==(r=C.email)?void 0:r.message}),(0,t.jsx)("label",{htmlFor:"password",className:"block text-xs font-medium",children:"Пароль"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"password",type:b?"text":"password",...N("password",{onChange:()=>{y.password&&_("password")}}),className:"block w-full px-3 py-2 pr-10  border ".concat(C.password||y.password?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:v}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3  hover:",onClick:()=>I(!b),tabIndex:-1,children:(0,t.jsx)(n.Suspense,{fallback:(0,t.jsx)("div",{className:"w-5 h-5"}),children:b?(0,t.jsx)(R,{size:20}):(0,t.jsx)(T,{size:20})})})]}),(0,t.jsx)(c.y,{message:null==(s=C.password)?void 0:s.message})]}),(0,t.jsx)("div",{className:"flex justify-end",children:w?(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-[color:var(--color-primary)] transition-opacity duration-300",children:[(0,t.jsx)(m.Z,{size:"sm",className:"text-[color:var(--color-primary)]"}),(0,t.jsx)("span",{children:"Переход на страницу сброса пароля..."})]}):(0,t.jsx)("a",{href:u.vc.AUTH.FORGOT_PASSWORD,onClick:e=>{e.preventDefault(),D(!0),setTimeout(()=>{f.push(u.vc.AUTH.FORGOT_PASSWORD)},1e3)},className:"text-sm text-[color:var(--color-primary)] hover:underline ".concat(w?"pointer-events-none opacity-50":""),children:"Забыли пароль?"})}),(0,t.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,t.jsx)("button",{type:"submit",disabled:v,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-brand)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:v?"Вход...":"Войти"}),(0,t.jsx)(o(),{href:u.vc.AUTH.REGISTER,className:"w-full block",children:(0,t.jsx)("button",{type:"button",className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-primary)] rounded-md shadow-sm text-sm font-medium text-[color:var(--color-brand)] bg-transparent hover:text-white hover:bg-[color:var(--color-primary)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)]",children:"Зарегистрироваться"})})]})]})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[3257,7261,3766,1938,7914,5490,3720,587,8315,7358],()=>r(69969)),_N_E=e.O()}]);