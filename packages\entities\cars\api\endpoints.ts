import { API_ROUTES } from '@shared/api';
import type { ApiEndpoints } from '@entities/collections/config/apiEndpointRegistry';

/**
 * Получение API-эндпоинтов для автомобилей
 * @param id ID автомобиля (для редактирования)
 * @param isCreateMode Режим создания
 * @returns API-эндпоинты
 */
export function getCarApiEndpoints(
  id: string | null,
  isCreateMode: boolean
): ApiEndpoints {
  // Если запрашивается список автомобилей (id === null и не режим создания)
  if (id === null && !isCreateMode) {
    return {
      list: API_ROUTES.CAR.LIST,
      get: API_ROUTES.CAR.LIST,
    };
  }
  // В режиме создания возвращаем только create эндпоинт
  if (isCreateMode) {
    return {
      create: API_ROUTES.CAR.CREATE
    };
  }

  // В режиме редактирования возвращаем только get и update эндпоинты
  return {
    get: id ? API_ROUTES.CAR.GET_BY_ID.replace('{uuid}', id) : undefined,
    update: id ? API_ROUTES.CAR.UPDATE.replace('{uuid}', id) : undefined,
    // Endpoints для работы с водителями
    assignDriver: id ? API_ROUTES.CAR.ASSIGN_DRIVER.replace('{uuid}', id) : undefined,
    updateDriverRelation: id ? API_ROUTES.CAR.ASSIGN_DRIVER.replace('{uuid}', id) : undefined,
    removeDriver: id ? API_ROUTES.CAR.ASSIGN_DRIVER.replace('{uuid}', id) : undefined,
  };
}