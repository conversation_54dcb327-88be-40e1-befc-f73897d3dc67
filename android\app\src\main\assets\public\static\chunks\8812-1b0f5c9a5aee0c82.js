(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8812],{4656:(e,r,o)=>{"use strict";o.d(r,{E9:()=>t,X5:()=>s,oW:()=>c,rA:()=>a});var l=o(75006);let s=l.z.object({email:l.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}),password:l.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"})}),a=l.z.object({email:l.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:l.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"}),fullName:l.z.string().min(1,{message:"ФИО обязательно"}).max(255,{message:"ФИО не должно превышать 255 символов"}),companyName:l.z.string().min(1,{message:"Название компании обязательно"}).max(255,{message:"Название компании не должно превышать 255 символов"}),legalAddress:l.z.string().min(1,{message:"Юридический адрес обязателен"}).max(500,{message:"Юридический адрес не должен превышать 500 символов"})}),t=l.z.object({email:l.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"})}),c=l.z.object({email:l.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"}),resetCode:l.z.string().min(1,{message:"Код сброса пароля обязателен"}),newPassword:l.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"})})},10395:(e,r,o)=>{"use strict";o.r(r),o.d(r,{PartnerRegisterForm:()=>p});var l=o(54568),s=o(90739),a=o(27261),t=o.n(a),c=o(7620),n=o(61938),i=o(27736),d=o(56583),m=o(53635),u=o(79394),x=o(22883),f=o(43356);let p=()=>{var e,r,o,a,p;let{registerPartner:b,isLoading:g,success:h,fieldErrors:v,clearFieldError:y}=(0,f.dO)(),[w,j]=(0,c.useState)(!1),{register:N,handleSubmit:E,formState:{errors:A}}=(0,n.mN)({resolver:(0,s.u)(x.rA)});return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(u.wh,{isVisible:g,message:"Выполняется регистрация партнера..."}),(0,l.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,l.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"Регистрация партнера"}),(0,l.jsx)("p",{children:"Заполните форму для регистрации в качестве партнера"})]}),(0,l.jsxs)("form",{onSubmit:E(e=>b(e)),className:"flex flex-col gap-3",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,l.jsx)("input",{id:"email",type:"email",...N("email",{onChange:()=>{v.email&&y("email")}}),className:"block w-full px-2 py-1  text-xs border ".concat(A.email||v.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:g}),(0,l.jsx)(m.yI,{message:null==(e=A.email)?void 0:e.message}),(0,l.jsx)("label",{htmlFor:"password",className:"block text-xs font-medium",children:"Пароль"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{id:"password",type:w?"text":"password",...N("password"),className:"block w-full px-3 py-2 pr-10  border ".concat(A.password?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:g}),(0,l.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3",onClick:()=>j(!w),tabIndex:-1,children:w?(0,l.jsx)(d.aR,{size:20}):(0,l.jsx)(d.bM,{size:20})})]}),(0,l.jsx)(m.yI,{message:null==(r=A.password)?void 0:r.message}),(0,l.jsx)("label",{htmlFor:"fullName",className:"block text-xs font-medium",children:"ФИО"}),(0,l.jsx)("input",{id:"fullName",type:"text",...N("fullName"),className:"block w-full px-2 py-1  text-xs border ".concat(A.fullName?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:g}),(0,l.jsx)(m.yI,{message:null==(o=A.fullName)?void 0:o.message}),(0,l.jsx)("label",{htmlFor:"companyName",className:"block text-xs font-medium",children:"Название компании"}),(0,l.jsx)("input",{id:"companyName",type:"text",...N("companyName"),className:"block w-full px-2 py-1  text-xs border ".concat(A.companyName?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:g}),(0,l.jsx)(m.yI,{message:null==(a=A.companyName)?void 0:a.message}),(0,l.jsx)("label",{htmlFor:"legalAddress",className:"block text-xs font-medium",children:"Юридический адрес"}),(0,l.jsx)("input",{id:"legalAddress",type:"text",...N("legalAddress"),className:"block w-full px-2 py-1  text-xs border ".concat(A.legalAddress?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:g}),(0,l.jsx)(m.yI,{message:null==(p=A.legalAddress)?void 0:p.message})]}),(0,l.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,l.jsx)("button",{type:"submit",disabled:g||h,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:g?"Регистрация...":"Зарегистрироваться"}),(0,l.jsx)(t(),{href:i.vc.AUTH.LOGIN,className:"w-full block",children:(0,l.jsx)("button",{type:"button",className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-neutral-300)] rounded-md shadow-sm text-sm font-medium  bg-transparent hover:bg-[color:var(--color-neutral-200)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-neutral-300)]",children:"Вернуться к входу"})})]})]})]})]})}},11447:(e,r,o)=>{"use strict";o.d(r,{$A:()=>v,Gw:()=>t,OT:()=>a,Oo:()=>d,Qx:()=>c,bY:()=>s,iY:()=>E,jB:()=>g,qf:()=>n,t0:()=>b});var l=o(52906);o(68998);var s=function(e){return e.ContactFormModal="CONTACT_FORM_MODAL",e.ImageViewerModal="IMAGE_VIEWER_MODAL",e.MediaSelectorModal="MEDIA_SELECTOR_MODAL",e.HistoryViewerModal="HISTORY_VIEWER_MODAL",e.SelectUserRoleModal="SELECT_USER_ROLE_MODAL",e.SelectOrderTypeModal="SELECT_ORDER_TYPE_MODAL",e.NotificationCenterModal="NOTIFICATION_CENTER_MODAL",e.RideRequestModal="RIDE_REQUEST_MODAL",e.QRPaymentModal="QR_PAYMENT_MODAL",e.CardPaymentModal="CARD_PAYMENT_MODAL",e.PotentialDriversModal="POTENTIAL_DRIVERS_MODAL",e.CompanyDetailsModal="COMPANY_DETAILS_MODAL",e.PublicOfferModal="PUBLIC_OFFER_MODAL",e.PrivacyPolicyModal="PRIVACY_POLICY_MODAL",e.FAQModal="FAQ_MODAL",e}({});let a=(0,l.y$)({}),t=(0,l.y$)([]),c=(0,l.y$)(null),n=(0,l.lh)(),i=(0,l.lh)(),d=(0,l.lh)();a.on(i,(e,r)=>r);let m=(0,l.EH)(()=>{});t.on(n,(e,r)=>{if("object"==typeof r&&null!==r&&"type"in r){let{type:o}=r;return o?[...e,o]:e}return r?[...e,r]:e}),t.on(d,function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return 0===e.length||r.allClose?[]:e.slice(0,-1)}),(0,l.XM)({source:d,fn:()=>({}),target:i}),c.on(t,(e,r)=>0===r.length?null:r[r.length-1]),(0,l.XM)({source:d,target:m});let u=(0,l.y$)(null),x=(0,l.lh)();u.on(x,(e,r)=>r),u.reset(d);let f=(0,l.y$)(null),p=(0,l.lh)();f.on(p,(e,r)=>r),f.reset(d);let b=(0,l.y$)(null),g=(0,l.lh)();b.on(g,(e,r)=>r),b.reset(d);let h=(0,l.y$)(null),v=(0,l.lh)(),y=(0,l.lh)();h.on(v,(e,r)=>r),h.reset(y);let w=(0,l.y$)(null),j=(0,l.lh)();w.on(j,(e,r)=>r),w.reset(d);let N=(0,l.y$)(null),E=(0,l.lh)(),A=(0,l.lh)();N.on(E,(e,r)=>r),N.reset(A);let M=(0,l.lh)();(0,l.XM)({source:M,fn:e=>{let{modalType:r,params:o}=e;return{type:r,params:o||{}}},target:n});let R=(0,l.lh)();(0,l.XM)({source:R,fn:e=>({modalType:"POTENTIAL_DRIVERS_MODAL",params:{orderId:e}}),target:M});let O=(0,l.lh)();(0,l.XM)({source:O,fn:()=>({modalType:"SELECT_ORDER_TYPE_MODAL",params:{}}),target:M});let T=(0,l.lh)();(0,l.XM)({source:T,fn:e=>{let{userId:r}=e;return{modalType:"SELECT_USER_ROLE_MODAL",params:{userId:r}}},target:M}),(0,l.XM)({source:n,fn:e=>"object"==typeof e&&null!==e&&"params"in e&&e.params||{},target:i})},16084:(e,r,o)=>{Promise.resolve().then(o.bind(o,98136)),Promise.resolve().then(o.bind(o,93248)),Promise.resolve().then(o.bind(o,10395)),Promise.resolve().then(o.bind(o,73691))},22883:(e,r,o)=>{"use strict";o.d(r,{E9:()=>l.E9,rA:()=>l.rA,oW:()=>l.oW}),o(17691);var l=o(4656);let s={ADMIN:"Admin",PARTNER:"Partner",DRIVER:"Driver",TERMINAL:"Terminal",OPERATOR:"Operator"};s.ADMIN,s.PARTNER,s.DRIVER,s.TERMINAL,s.OPERATOR,s.TERMINAL,s.DRIVER},68998:(e,r,o)=>{"use strict";var l=o(52906);let s=(0,l.lh)(),a=(0,l.lh)(),t=(0,l.lh)(),c=(0,l.lh)(),n=(0,l.y$)(!1).on(s,(e,r)=>r),i=(0,l.y$)({}).on(t,(e,r)=>{let{key:o,loading:l}=r;return{...e,[o]:l}}).on(c,()=>({})).map(e=>Object.values(e).some(e=>e)),d=(0,l.y$)(!1);(0,l.XM)({source:[n,i],fn:e=>{let[r,o]=e;return r||o},target:d});let m=(0,l.y$)(1200).on(a,(e,r)=>r),u=(0,l.lh)(),x=(0,l.EH)(async()=>{let e=Date.now()-f,r=Math.max(0,m.getState()-e);r>0&&await new Promise(e=>setTimeout(e,r))}),f=0;s.watch(e=>{e&&(f=Date.now())}),(0,l.XM)({source:u,target:x}),(0,l.XM)({source:x.doneData,fn:()=>!1,target:s})},73691:(e,r,o)=>{"use strict";o.r(r),o.d(r,{ResetPasswordForm:()=>p});var l=o(54568),s=o(90739),a=o(27261),t=o.n(a),c=o(62942),n=o(7620),i=o(61938),d=o(27736),m=o(56583),u=o(98714),x=o(22883),f=o(43356);let p=()=>{var e,r,o;let{resetPassword:a,isLoading:p,success:b,fieldErrors:g,clearFieldError:h}=(0,f.Qc)(),[v,y]=(0,n.useState)(!1),w=(0,c.useSearchParams)().get("email")||"",{register:j,handleSubmit:N,formState:{errors:E}}=(0,i.mN)({resolver:(0,s.u)(x.oW)});return(0,l.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,l.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"Сброс пароля"}),(0,l.jsx)("p",{children:"Введите код, полученный на email, и новый пароль"})]}),(0,l.jsxs)("form",{onSubmit:N(e=>a(e)),className:"flex flex-col gap-3",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,l.jsx)("input",{id:"email",type:"email",...j("email",{onChange:()=>{g.email&&h("email")}}),className:"block w-full px-2 py-1  text-xs border ".concat(E.email||g.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:p||b||!!w}),(0,l.jsx)(u.yI,{message:null==(e=E.email)?void 0:e.message}),(0,l.jsx)("label",{htmlFor:"resetCode",className:"block text-xs font-medium",children:"Код сброса пароля"}),(0,l.jsx)("input",{id:"resetCode",type:"text",...j("resetCode",{onChange:()=>{g.resetCode&&h("resetCode")}}),className:"block w-full px-2 py-1  text-xs border ".concat(E.resetCode||g.resetCode?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:p||b}),(0,l.jsx)(u.yI,{message:null==(r=E.resetCode)?void 0:r.message}),(0,l.jsx)("label",{htmlFor:"newPassword",className:"block text-xs font-medium",children:"Новый пароль"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{id:"newPassword",type:v?"text":"password",...j("newPassword",{onChange:()=>{g.newPassword&&h("newPassword")}}),className:"block w-full px-3 py-2 pr-10  border ".concat(E.newPassword||g.newPassword?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:p||b}),(0,l.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3  hover:",onClick:()=>y(!v),tabIndex:-1,children:v?(0,l.jsx)(m.aR,{size:20}):(0,l.jsx)(m.bM,{size:20})})]}),(0,l.jsx)(u.yI,{message:null==(o=E.newPassword)?void 0:o.message})]}),(0,l.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,l.jsx)("button",{type:"submit",disabled:p||b,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:p?"Отправка...":"Сбросить пароль"}),(0,l.jsx)(t(),{href:d.vc.AUTH.LOGIN,className:"w-full block",children:(0,l.jsx)("button",{type:"button",className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-neutral-300)] rounded-md shadow-sm text-sm font-medium  bg-transparent hover:bg-[color:var(--color-neutral-200)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-neutral-300)]",children:"Вернуться к входу"})})]})]})]})}},78032:(e,r,o)=>{"use strict";o.d(r,{d:()=>s});var l=o(7620);function s(e,r){let[o,s]=(0,l.useState)(e);return(0,l.useEffect)(()=>{let o=setTimeout(()=>s(e),r);return()=>clearTimeout(o)},[e,r]),o}},93248:(e,r,o)=>{"use strict";o.r(r),o.d(r,{LoginForm:()=>h});var l=o(54568),s=o(90739),a=o(27261),t=o.n(a),c=o(62942),n=o(7620),i=o(61938),d=o(80745),m=o(62015),u=o(53850),x=o(34763),f=o(4656),p=o(4499);let b=(0,n.lazy)(()=>Promise.resolve().then(o.bind(o,87036))),g=(0,n.lazy)(()=>Promise.resolve().then(o.bind(o,66085))),h=e=>{var r,o;let{defaultValues:a,autoLogin:h=!1}=e,v=(0,c.useRouter)(),{login:y,isLoading:w,fieldErrors:j,clearFieldError:N}=(0,p.v)(),[E,A]=(0,n.useState)(!1),[M,R]=(0,n.useState)(!1),[O,T]=(0,n.useState)(!1),{register:_,handleSubmit:I,formState:{errors:P},getValues:C}=(0,i.mN)({resolver:(0,s.u)(f.X5),defaultValues:a});return(0,n.useEffect)(()=>{h&&(null==a?void 0:a.email)&&(null==a?void 0:a.password)&&!w&&!O&&(T(!0),y(C()))},[h,null==a?void 0:a.email,null==a?void 0:a.password,w,O,C,y]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(x.w,{isVisible:w,message:"Выполняется вход в систему..."}),(0,l.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,l.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"Вход в систему"}),(0,l.jsx)("p",{children:"Введите свои учетные данные для входа"})]}),(0,l.jsxs)("form",{onSubmit:I(e=>y(e)),className:"flex flex-col gap-3",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,l.jsx)("input",{id:"email",type:"email",..._("email",{onChange:()=>{j.email&&N("email")}}),className:"block w-full px-2 py-1  text-xs border ".concat(P.email||j.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:w}),(0,l.jsx)(m.y,{message:null==(r=P.email)?void 0:r.message}),(0,l.jsx)("label",{htmlFor:"password",className:"block text-xs font-medium",children:"Пароль"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{id:"password",type:E?"text":"password",..._("password",{onChange:()=>{j.password&&N("password")}}),className:"block w-full px-3 py-2 pr-10  border ".concat(P.password||j.password?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:w}),(0,l.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3  hover:",onClick:()=>A(!E),tabIndex:-1,children:(0,l.jsx)(n.Suspense,{fallback:(0,l.jsx)("div",{className:"w-5 h-5"}),children:E?(0,l.jsx)(g,{size:20}):(0,l.jsx)(b,{size:20})})})]}),(0,l.jsx)(m.y,{message:null==(o=P.password)?void 0:o.message})]}),(0,l.jsx)("div",{className:"flex justify-end",children:M?(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-[color:var(--color-primary)] transition-opacity duration-300",children:[(0,l.jsx)(u.Z,{size:"sm",className:"text-[color:var(--color-primary)]"}),(0,l.jsx)("span",{children:"Переход на страницу сброса пароля..."})]}):(0,l.jsx)("a",{href:d.vc.AUTH.FORGOT_PASSWORD,onClick:e=>{e.preventDefault(),R(!0),setTimeout(()=>{v.push(d.vc.AUTH.FORGOT_PASSWORD)},1e3)},className:"text-sm text-[color:var(--color-primary)] hover:underline ".concat(M?"pointer-events-none opacity-50":""),children:"Забыли пароль?"})}),(0,l.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,l.jsx)("button",{type:"submit",disabled:w,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-brand)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:w?"Вход...":"Войти"}),(0,l.jsx)(t(),{href:d.vc.AUTH.REGISTER,className:"w-full block",children:(0,l.jsx)("button",{type:"button",className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-primary)] rounded-md shadow-sm text-sm font-medium text-[color:var(--color-brand)] bg-transparent hover:text-white hover:bg-[color:var(--color-primary)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)]",children:"Зарегистрироваться"})})]})]})]})]})}},98136:(e,r,o)=>{"use strict";o.r(r),o.d(r,{ForgotPasswordForm:()=>x});var l=o(54568),s=o(90739),a=o(27261),t=o.n(a);o(7620);var c=o(61938),n=o(27736),i=o(98714),d=o(79394),m=o(22883),u=o(43356);let x=()=>{var e;let{forgotPassword:r,isLoading:o,success:a,fieldErrors:x,clearFieldError:f}=(0,u.G7)(),{register:p,handleSubmit:b,formState:{errors:g}}=(0,c.mN)({resolver:(0,s.u)(m.E9)});return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(d.wh,{isVisible:o,message:"Отправка запроса на восстановление пароля..."}),(0,l.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,l.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"Восстановление пароля"}),(0,l.jsx)("p",{children:"Введите email, указанный при регистрации"})]}),(0,l.jsxs)("form",{onSubmit:b(e=>r(e)),className:"flex flex-col gap-3",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,l.jsx)("input",{id:"email",type:"email",...p("email",{onChange:()=>{x.email&&f("email")}}),className:"block w-full px-2 py-1  text-xs border ".concat(g.email||x.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"," rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]"),disabled:o||a}),(0,l.jsx)(i.yI,{message:null==(e=g.email)?void 0:e.message})]}),(0,l.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,l.jsx)("button",{type:"submit",disabled:o||a,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Отправка...":"Отправить код"}),(0,l.jsx)(t(),{href:n.vc.AUTH.LOGIN,className:"w-full block ".concat(a?"pointer-events-none":""),children:(0,l.jsx)("button",{type:"button",disabled:a,className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-neutral-300)] rounded-md shadow-sm text-sm font-medium bg-transparent hover:bg-[color:var(--color-neutral-200)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-neutral-300)] disabled:opacity-50 disabled:cursor-not-allowed",children:"Вернуться к входу"})})]})]})]})]})}},98714:(e,r,o)=>{"use strict";o.d(r,{yI:()=>l.yI}),o(54568),o(62942),o(7620),o(56583);var l=o(53635);o(79394),o(78032);o(48311)}}]);