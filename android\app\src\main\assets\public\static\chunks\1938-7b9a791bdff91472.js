"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1938],{61938:(e,t,r)=>{r.d(t,{Gb:()=>C,Jt:()=>p,hZ:()=>V,mN:()=>eV,xW:()=>x});var i=r(7620),a=e=>"checkbox"===e.type,s=e=>e instanceof Date,l=e=>null==e;let n=e=>"object"==typeof e;var u=e=>!l(e)&&!Array.isArray(e)&&n(e)&&!s(e),o=e=>u(e)&&e.target?a(e.target)?e.target.checked:e.target.value:e,d=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,f=(e,t)=>e.has(d(t)),c=e=>{let t=e.constructor&&e.constructor.prototype;return u(t)&&t.hasOwnProperty("isPrototypeOf")},y="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function m(e){let t,r=Array.isArray(e),i="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(y&&(e instanceof Blob||i))&&(r||u(e))))return e;else if(t=r?[]:{},r||c(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=m(e[r]));else t=e;return t}var h=e=>/^\w*$/.test(e),v=e=>void 0===e,b=e=>Array.isArray(e)?e.filter(Boolean):[],g=e=>b(e.replace(/["|']|\]/g,"").split(/\.|\[/)),p=(e,t,r)=>{if(!t||!u(e))return r;let i=(h(t)?[t]:g(t)).reduce((e,t)=>l(e)?e:e[t],e);return v(i)||i===e?v(e[t])?r:e[t]:i},_=e=>"boolean"==typeof e,V=(e,t,r)=>{let i=-1,a=h(t)?[t]:g(t),s=a.length,l=s-1;for(;++i<s;){let t=a[i],s=r;if(i!==l){let r=e[t];s=u(r)||Array.isArray(r)?r:isNaN(+a[i+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=s,e=e[t]}};let F={BLUR:"blur",FOCUS_OUT:"focusout"},A={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},w={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},S=i.createContext(null);S.displayName="HookFormContext";let x=()=>i.useContext(S);var k=(e,t,r,i=!0)=>{let a={defaultValues:t._defaultValues};for(let s in e)Object.defineProperty(a,s,{get:()=>(t._proxyFormState[s]!==A.all&&(t._proxyFormState[s]=!i||A.all),r&&(r[s]=!0),e[s])});return a};let D="undefined"!=typeof window?i.useLayoutEffect:i.useEffect;var O=e=>"string"==typeof e,E=(e,t,r,i,a)=>O(e)?(i&&t.watch.add(e),p(r,e,a)):Array.isArray(e)?e.map(e=>(i&&t.watch.add(e),p(r,e))):(i&&(t.watchAll=!0),r),C=(e,t,r,i,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[i]:a||!0}}:{},L=e=>Array.isArray(e)?e:[e],T=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},N=e=>l(e)||!n(e);function U(e,t){if(N(e)||N(t))return e===t;if(s(e)&&s(t))return e.getTime()===t.getTime();let r=Object.keys(e),i=Object.keys(t);if(r.length!==i.length)return!1;for(let a of r){let r=e[a];if(!i.includes(a))return!1;if("ref"!==a){let e=t[a];if(s(r)&&s(e)||u(r)&&u(e)||Array.isArray(r)&&Array.isArray(e)?!U(r,e):r!==e)return!1}}return!0}var B=e=>u(e)&&!Object.keys(e).length,M=e=>"file"===e.type,j=e=>"function"==typeof e,R=e=>{if(!y)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},q=e=>"select-multiple"===e.type,P=e=>"radio"===e.type,I=e=>P(e)||a(e),W=e=>R(e)&&e.isConnected;function H(e,t){let r=Array.isArray(t)?t:h(t)?[t]:g(t),i=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,i=0;for(;i<r;)e=v(e)?i++:e[t[i++]];return e}(e,r),a=r.length-1,s=r[a];return i&&delete i[s],0!==a&&(u(i)&&B(i)||Array.isArray(i)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!v(e[t]))return!1;return!0}(i))&&H(e,r.slice(0,-1)),e}var $=e=>{for(let t in e)if(j(e[t]))return!0;return!1};function z(e,t={}){let r=Array.isArray(e);if(u(e)||r)for(let r in e)Array.isArray(e[r])||u(e[r])&&!$(e[r])?(t[r]=Array.isArray(e[r])?[]:{},z(e[r],t[r])):l(e[r])||(t[r]=!0);return t}var G=(e,t)=>(function e(t,r,i){let a=Array.isArray(t);if(u(t)||a)for(let a in t)Array.isArray(t[a])||u(t[a])&&!$(t[a])?v(r)||N(i[a])?i[a]=Array.isArray(t[a])?z(t[a],[]):{...z(t[a])}:e(t[a],l(r)?{}:r[a],i[a]):i[a]=!U(t[a],r[a]);return i})(e,t,z(t));let J={value:!1,isValid:!1},Z={value:!0,isValid:!0};var K=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!v(e[0].attributes.value)?v(e[0].value)||""===e[0].value?Z:{value:e[0].value,isValid:!0}:Z:J}return J},Q=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:i})=>v(e)?e:t?""===e?NaN:e?+e:e:r&&O(e)?new Date(e):i?i(e):e;let X={isValid:!1,value:null};var Y=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,X):X;function ee(e){let t=e.ref;return M(t)?t.files:P(t)?Y(e.refs).value:q(t)?[...t.selectedOptions].map(({value:e})=>e):a(t)?K(e.refs).value:Q(v(t.value)?e.ref.value:t.value,e)}var et=(e,t,r,i)=>{let a={};for(let r of e){let e=p(t,r);e&&V(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:i}},er=e=>e instanceof RegExp,ei=e=>v(e)?e:er(e)?e.source:u(e)?er(e.value)?e.value.source:e.value:e,ea=e=>({isOnSubmit:!e||e===A.onSubmit,isOnBlur:e===A.onBlur,isOnChange:e===A.onChange,isOnAll:e===A.all,isOnTouch:e===A.onTouched});let es="AsyncFunction";var el=e=>!!e&&!!e.validate&&!!(j(e.validate)&&e.validate.constructor.name===es||u(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===es)),en=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),eu=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let eo=(e,t,r,i)=>{for(let a of r||Object.keys(e)){let r=p(e,a);if(r){let{_f:e,...s}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!i)return!0;else if(e.ref&&t(e.ref,e.name)&&!i)return!0;else if(eo(s,t))break}else if(u(s)&&eo(s,t))break}}};function ed(e,t,r){let i=p(e,r);if(i||h(r))return{error:i,name:r};let a=r.split(".");for(;a.length;){let i=a.join("."),s=p(t,i),l=p(e,i);if(s&&!Array.isArray(s)&&r!==i)break;if(l&&l.type)return{name:i,error:l};if(l&&l.root&&l.root.type)return{name:`${i}.root`,error:l.root};a.pop()}return{name:r}}var ef=(e,t,r,i)=>{r(e);let{name:a,...s}=e;return B(s)||Object.keys(s).length>=Object.keys(t).length||Object.keys(s).find(e=>t[e]===(!i||A.all))},ec=(e,t,r)=>!e||!t||e===t||L(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),ey=(e,t,r,i,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?i.isOnBlur:a.isOnBlur)?!e:(r?!i.isOnChange:!a.isOnChange)||e),em=(e,t)=>!b(p(e,t)).length&&H(e,t),eh=(e,t,r)=>{let i=L(p(e,r));return V(i,"root",t[r]),V(e,r,i),e},ev=e=>O(e);function eb(e,t,r="validate"){if(ev(e)||Array.isArray(e)&&e.every(ev)||_(e)&&!e)return{type:r,message:ev(e)?e:"",ref:t}}var eg=e=>u(e)&&!er(e)?e:{value:e,message:""},ep=async(e,t,r,i,s,n)=>{let{ref:o,refs:d,required:f,maxLength:c,minLength:y,min:m,max:h,pattern:b,validate:g,name:V,valueAsNumber:F,mount:A}=e._f,S=p(r,V);if(!A||t.has(V))return{};let x=d?d[0]:o,k=e=>{s&&x.reportValidity&&(x.setCustomValidity(_(e)?"":e||""),x.reportValidity())},D={},E=P(o),L=a(o),T=(F||M(o))&&v(o.value)&&v(S)||R(o)&&""===o.value||""===S||Array.isArray(S)&&!S.length,N=C.bind(null,V,i,D),U=(e,t,r,i=w.maxLength,a=w.minLength)=>{let s=e?t:r;D[V]={type:e?i:a,message:s,ref:o,...N(e?i:a,s)}};if(n?!Array.isArray(S)||!S.length:f&&(!(E||L)&&(T||l(S))||_(S)&&!S||L&&!K(d).isValid||E&&!Y(d).isValid)){let{value:e,message:t}=ev(f)?{value:!!f,message:f}:eg(f);if(e&&(D[V]={type:w.required,message:t,ref:x,...N(w.required,t)},!i))return k(t),D}if(!T&&(!l(m)||!l(h))){let e,t,r=eg(h),a=eg(m);if(l(S)||isNaN(S)){let i=o.valueAsDate||new Date(S),s=e=>new Date(new Date().toDateString()+" "+e),l="time"==o.type,n="week"==o.type;O(r.value)&&S&&(e=l?s(S)>s(r.value):n?S>r.value:i>new Date(r.value)),O(a.value)&&S&&(t=l?s(S)<s(a.value):n?S<a.value:i<new Date(a.value))}else{let i=o.valueAsNumber||(S?+S:S);l(r.value)||(e=i>r.value),l(a.value)||(t=i<a.value)}if((e||t)&&(U(!!e,r.message,a.message,w.max,w.min),!i))return k(D[V].message),D}if((c||y)&&!T&&(O(S)||n&&Array.isArray(S))){let e=eg(c),t=eg(y),r=!l(e.value)&&S.length>+e.value,a=!l(t.value)&&S.length<+t.value;if((r||a)&&(U(r,e.message,t.message),!i))return k(D[V].message),D}if(b&&!T&&O(S)){let{value:e,message:t}=eg(b);if(er(e)&&!S.match(e)&&(D[V]={type:w.pattern,message:t,ref:o,...N(w.pattern,t)},!i))return k(t),D}if(g){if(j(g)){let e=eb(await g(S,r),x);if(e&&(D[V]={...e,...N(w.validate,e.message)},!i))return k(e.message),D}else if(u(g)){let e={};for(let t in g){if(!B(e)&&!i)break;let a=eb(await g[t](S,r),x,t);a&&(e={...a,...N(t,a.message)},k(a.message),i&&(D[V]=e))}if(!B(e)&&(D[V]={ref:x,...e},!i))return D}}return k(!0),D};let e_={mode:A.onSubmit,reValidateMode:A.onChange,shouldFocusError:!0};function eV(e={}){let t=i.useRef(void 0),r=i.useRef(void 0),[n,d]=i.useState({isDirty:!1,isValidating:!1,isLoading:j(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:j(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:n},e.defaultValues&&!j(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:r,...i}=function(e={}){let t,r={...e_,...e},i={submitCount:0,isDirty:!1,isReady:!1,isLoading:j(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},n={},d=(u(r.defaultValues)||u(r.values))&&m(r.defaultValues||r.values)||{},c=r.shouldUnregister?{}:m(d),h={action:!1,mount:!1,watch:!1},g={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},w=0,S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},x={...S},k={array:T(),state:T()},D=r.criteriaMode===A.all,C=e=>t=>{clearTimeout(w),w=setTimeout(e,t)},N=async e=>{if(!r.disabled&&(S.isValid||x.isValid||e)){let e=r.resolver?B((await K()).errors):await Y(n,!0);e!==i.isValid&&k.state.next({isValid:e})}},P=(e,t)=>{!r.disabled&&(S.isValidating||S.validatingFields||x.isValidating||x.validatingFields)&&((e||Array.from(g.mount)).forEach(e=>{e&&(t?V(i.validatingFields,e,t):H(i.validatingFields,e))}),k.state.next({validatingFields:i.validatingFields,isValidating:!B(i.validatingFields)}))},$=(e,t)=>{V(i.errors,e,t),k.state.next({errors:i.errors})},z=(e,t,r,i)=>{let a=p(n,e);if(a){let s=p(c,e,v(r)?p(d,e):r);v(s)||i&&i.defaultChecked||t?V(c,e,t?s:ee(a._f)):ev(e,s),h.mount&&N()}},J=(e,t,a,s,l)=>{let n=!1,u=!1,o={name:e};if(!r.disabled){if(!a||s){(S.isDirty||x.isDirty)&&(u=i.isDirty,i.isDirty=o.isDirty=er(),n=u!==o.isDirty);let r=U(p(d,e),t);u=!!p(i.dirtyFields,e),r?H(i.dirtyFields,e):V(i.dirtyFields,e,!0),o.dirtyFields=i.dirtyFields,n=n||(S.dirtyFields||x.dirtyFields)&&!r!==u}if(a){let t=p(i.touchedFields,e);t||(V(i.touchedFields,e,a),o.touchedFields=i.touchedFields,n=n||(S.touchedFields||x.touchedFields)&&t!==a)}n&&l&&k.state.next(o)}return n?o:{}},Z=(e,a,s,l)=>{let n=p(i.errors,e),u=(S.isValid||x.isValid)&&_(a)&&i.isValid!==a;if(r.delayError&&s?(t=C(()=>$(e,s)))(r.delayError):(clearTimeout(w),t=null,s?V(i.errors,e,s):H(i.errors,e)),(s?!U(n,s):n)||!B(l)||u){let t={...l,...u&&_(a)?{isValid:a}:{},errors:i.errors,name:e};i={...i,...t},k.state.next(t)}},K=async e=>{P(e,!0);let t=await r.resolver(c,r.context,et(e||g.mount,n,r.criteriaMode,r.shouldUseNativeValidation));return P(e),t},X=async e=>{let{errors:t}=await K(e);if(e)for(let r of e){let e=p(t,r);e?V(i.errors,r,e):H(i.errors,r)}else i.errors=t;return t},Y=async(e,t,a={valid:!0})=>{for(let s in e){let l=e[s];if(l){let{_f:e,...n}=l;if(e){let n=g.array.has(e.name),u=l._f&&el(l._f);u&&S.validatingFields&&P([s],!0);let o=await ep(l,g.disabled,c,D,r.shouldUseNativeValidation&&!t,n);if(u&&S.validatingFields&&P([s]),o[e.name]&&(a.valid=!1,t))break;t||(p(o,e.name)?n?eh(i.errors,o,e.name):V(i.errors,e.name,o[e.name]):H(i.errors,e.name))}B(n)||await Y(n,t,a)}}return a.valid},er=(e,t)=>!r.disabled&&(e&&t&&V(c,e,t),!U(ew(),d)),es=(e,t,r)=>E(e,g,{...h.mount?c:v(t)?d:O(e)?{[e]:t}:t},r,t),ev=(e,t,r={})=>{let i=p(n,e),s=t;if(i){let r=i._f;r&&(r.disabled||V(c,e,Q(t,r)),s=R(r.ref)&&l(t)?"":t,q(r.ref)?[...r.ref.options].forEach(e=>e.selected=s.includes(e.value)):r.refs?a(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(s)?e.checked=!!s.find(t=>t===e.value):e.checked=s===e.value||!!s)}):r.refs.forEach(e=>e.checked=e.value===s):M(r.ref)?r.ref.value="":(r.ref.value=s,r.ref.type||k.state.next({name:e,values:m(c)})))}(r.shouldDirty||r.shouldTouch)&&J(e,s,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&eA(e)},eb=(e,t,r)=>{for(let i in t){if(!t.hasOwnProperty(i))return;let a=t[i],l=e+"."+i,o=p(n,l);(g.array.has(e)||u(a)||o&&!o._f)&&!s(a)?eb(l,a,r):ev(l,a,r)}},eg=(e,t,r={})=>{let a=p(n,e),s=g.array.has(e),u=m(t);V(c,e,u),s?(k.array.next({name:e,values:m(c)}),(S.isDirty||S.dirtyFields||x.isDirty||x.dirtyFields)&&r.shouldDirty&&k.state.next({name:e,dirtyFields:G(d,c),isDirty:er(e,u)})):!a||a._f||l(u)?ev(e,u,r):eb(e,u,r),eu(e,g)&&k.state.next({...i}),k.state.next({name:h.mount?e:void 0,values:m(c)})},eV=async e=>{h.mount=!0;let a=e.target,l=a.name,u=!0,d=p(n,l),f=e=>{u=Number.isNaN(e)||s(e)&&isNaN(e.getTime())||U(e,p(c,l,e))},y=ea(r.mode),v=ea(r.reValidateMode);if(d){let s,h,b=a.type?ee(d._f):o(e),_=e.type===F.BLUR||e.type===F.FOCUS_OUT,A=!en(d._f)&&!r.resolver&&!p(i.errors,l)&&!d._f.deps||ey(_,p(i.touchedFields,l),i.isSubmitted,v,y),w=eu(l,g,_);V(c,l,b),_?(d._f.onBlur&&d._f.onBlur(e),t&&t(0)):d._f.onChange&&d._f.onChange(e);let O=J(l,b,_),E=!B(O)||w;if(_||k.state.next({name:l,type:e.type,values:m(c)}),A)return(S.isValid||x.isValid)&&("onBlur"===r.mode?_&&N():_||N()),E&&k.state.next({name:l,...w?{}:O});if(!_&&w&&k.state.next({...i}),r.resolver){let{errors:e}=await K([l]);if(f(b),u){let t=ed(i.errors,n,l),r=ed(e,n,t.name||l);s=r.error,l=r.name,h=B(e)}}else P([l],!0),s=(await ep(d,g.disabled,c,D,r.shouldUseNativeValidation))[l],P([l]),f(b),u&&(s?h=!1:(S.isValid||x.isValid)&&(h=await Y(n,!0)));u&&(d._f.deps&&eA(d._f.deps),Z(l,h,s,O))}},eF=(e,t)=>{if(p(i.errors,t)&&e.focus)return e.focus(),1},eA=async(e,t={})=>{let a,s,l=L(e);if(r.resolver){let t=await X(v(e)?e:l);a=B(t),s=e?!l.some(e=>p(t,e)):a}else e?((s=(await Promise.all(l.map(async e=>{let t=p(n,e);return await Y(t&&t._f?{[e]:t}:t)}))).every(Boolean))||i.isValid)&&N():s=a=await Y(n);return k.state.next({...!O(e)||(S.isValid||x.isValid)&&a!==i.isValid?{}:{name:e},...r.resolver||!e?{isValid:a}:{},errors:i.errors}),t.shouldFocus&&!s&&eo(n,eF,e?l:g.mount),s},ew=e=>{let t={...h.mount?c:d};return v(e)?t:O(e)?p(t,e):e.map(e=>p(t,e))},eS=(e,t)=>({invalid:!!p((t||i).errors,e),isDirty:!!p((t||i).dirtyFields,e),error:p((t||i).errors,e),isValidating:!!p(i.validatingFields,e),isTouched:!!p((t||i).touchedFields,e)}),ex=(e,t,r)=>{let a=(p(n,e,{_f:{}})._f||{}).ref,{ref:s,message:l,type:u,...o}=p(i.errors,e)||{};V(i.errors,e,{...o,...t,ref:a}),k.state.next({name:e,errors:i.errors,isValid:!1}),r&&r.shouldFocus&&a&&a.focus&&a.focus()},ek=e=>k.state.subscribe({next:t=>{ec(e.name,t.name,e.exact)&&ef(t,e.formState||S,eU,e.reRenderRoot)&&e.callback({values:{...c},...i,...t})}}).unsubscribe,eD=(e,t={})=>{for(let a of e?L(e):g.mount)g.mount.delete(a),g.array.delete(a),t.keepValue||(H(n,a),H(c,a)),t.keepError||H(i.errors,a),t.keepDirty||H(i.dirtyFields,a),t.keepTouched||H(i.touchedFields,a),t.keepIsValidating||H(i.validatingFields,a),r.shouldUnregister||t.keepDefaultValue||H(d,a);k.state.next({values:m(c)}),k.state.next({...i,...!t.keepDirty?{}:{isDirty:er()}}),t.keepIsValid||N()},eO=({disabled:e,name:t})=>{(_(e)&&h.mount||e||g.disabled.has(t))&&(e?g.disabled.add(t):g.disabled.delete(t))},eE=(e,t={})=>{let i=p(n,e),a=_(t.disabled)||_(r.disabled);return V(n,e,{...i||{},_f:{...i&&i._f?i._f:{ref:{name:e}},name:e,mount:!0,...t}}),g.mount.add(e),i?eO({disabled:_(t.disabled)?t.disabled:r.disabled,name:e}):z(e,!0,t.value),{...a?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ei(t.min),max:ei(t.max),minLength:ei(t.minLength),maxLength:ei(t.maxLength),pattern:ei(t.pattern)}:{},name:e,onChange:eV,onBlur:eV,ref:a=>{if(a){eE(e,t),i=p(n,e);let r=v(a.value)&&a.querySelectorAll&&a.querySelectorAll("input,select,textarea")[0]||a,s=I(r),l=i._f.refs||[];(s?l.find(e=>e===r):r===i._f.ref)||(V(n,e,{_f:{...i._f,...s?{refs:[...l.filter(W),r,...Array.isArray(p(d,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),z(e,!1,void 0,r))}else(i=p(n,e,{}))._f&&(i._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(f(g.array,e)&&h.action)&&g.unMount.add(e)}}},eC=()=>r.shouldFocusError&&eo(n,eF,g.mount),eL=(e,t)=>async a=>{let s;a&&(a.preventDefault&&a.preventDefault(),a.persist&&a.persist());let l=m(c);if(k.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await K();i.errors=e,l=t}else await Y(n);if(g.disabled.size)for(let e of g.disabled)V(l,e,void 0);if(H(i.errors,"root"),B(i.errors)){k.state.next({errors:{}});try{await e(l,a)}catch(e){s=e}}else t&&await t({...i.errors},a),eC(),setTimeout(eC);if(k.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:B(i.errors)&&!s,submitCount:i.submitCount+1,errors:i.errors}),s)throw s},eT=(e,t={})=>{let a=e?m(e):d,s=m(a),l=B(e),u=l?d:s;if(t.keepDefaultValues||(d=a),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...g.mount,...Object.keys(G(d,c))])))p(i.dirtyFields,e)?V(u,e,p(c,e)):eg(e,p(u,e));else{if(y&&v(e))for(let e of g.mount){let t=p(n,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(R(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of g.mount)eg(e,p(u,e))}c=m(u),k.array.next({values:{...u}}),k.state.next({values:{...u}})}g={mount:t.keepDirtyValues?g.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},h.mount=!S.isValid||!!t.keepIsValid||!!t.keepDirtyValues,h.watch=!!r.shouldUnregister,k.state.next({submitCount:t.keepSubmitCount?i.submitCount:0,isDirty:!l&&(t.keepDirty?i.isDirty:!!(t.keepDefaultValues&&!U(e,d))),isSubmitted:!!t.keepIsSubmitted&&i.isSubmitted,dirtyFields:l?{}:t.keepDirtyValues?t.keepDefaultValues&&c?G(d,c):i.dirtyFields:t.keepDefaultValues&&e?G(d,e):t.keepDirty?i.dirtyFields:{},touchedFields:t.keepTouched?i.touchedFields:{},errors:t.keepErrors?i.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&i.isSubmitSuccessful,isSubmitting:!1})},eN=(e,t)=>eT(j(e)?e(c):e,t),eU=e=>{i={...i,...e}},eB={control:{register:eE,unregister:eD,getFieldState:eS,handleSubmit:eL,setError:ex,_subscribe:ek,_runSchema:K,_focusError:eC,_getWatch:es,_getDirty:er,_setValid:N,_setFieldArray:(e,t=[],a,s,l=!0,u=!0)=>{if(s&&a&&!r.disabled){if(h.action=!0,u&&Array.isArray(p(n,e))){let t=a(p(n,e),s.argA,s.argB);l&&V(n,e,t)}if(u&&Array.isArray(p(i.errors,e))){let t=a(p(i.errors,e),s.argA,s.argB);l&&V(i.errors,e,t),em(i.errors,e)}if((S.touchedFields||x.touchedFields)&&u&&Array.isArray(p(i.touchedFields,e))){let t=a(p(i.touchedFields,e),s.argA,s.argB);l&&V(i.touchedFields,e,t)}(S.dirtyFields||x.dirtyFields)&&(i.dirtyFields=G(d,c)),k.state.next({name:e,isDirty:er(e,t),dirtyFields:i.dirtyFields,errors:i.errors,isValid:i.isValid})}else V(c,e,t)},_setDisabledField:eO,_setErrors:e=>{i.errors=e,k.state.next({errors:i.errors,isValid:!1})},_getFieldArray:e=>b(p(h.mount?c:d,e,r.shouldUnregister?p(d,e,[]):[])),_reset:eT,_resetDefaultValues:()=>j(r.defaultValues)&&r.defaultValues().then(e=>{eN(e,r.resetOptions),k.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of g.unMount){let t=p(n,e);t&&(t._f.refs?t._f.refs.every(e=>!W(e)):!W(t._f.ref))&&eD(e)}g.unMount=new Set},_disableForm:e=>{_(e)&&(k.state.next({disabled:e}),eo(n,(t,r)=>{let i=p(n,r);i&&(t.disabled=i._f.disabled||e,Array.isArray(i._f.refs)&&i._f.refs.forEach(t=>{t.disabled=i._f.disabled||e}))},0,!1))},_subjects:k,_proxyFormState:S,get _fields(){return n},get _formValues(){return c},get _state(){return h},set _state(value){h=value},get _defaultValues(){return d},get _names(){return g},set _names(value){g=value},get _formState(){return i},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(h.mount=!0,x={...x,...e.formState},ek({...e,formState:x})),trigger:eA,register:eE,handleSubmit:eL,watch:(e,t)=>j(e)?k.state.subscribe({next:r=>e(es(void 0,t),r)}):es(e,t,!0),setValue:eg,getValues:ew,reset:eN,resetField:(e,t={})=>{p(n,e)&&(v(t.defaultValue)?eg(e,m(p(d,e))):(eg(e,t.defaultValue),V(d,e,m(t.defaultValue))),t.keepTouched||H(i.touchedFields,e),t.keepDirty||(H(i.dirtyFields,e),i.isDirty=t.defaultValue?er(e,m(p(d,e))):er()),!t.keepError&&(H(i.errors,e),S.isValid&&N()),k.state.next({...i}))},clearErrors:e=>{e&&L(e).forEach(e=>H(i.errors,e)),k.state.next({errors:e?i.errors:{}})},unregister:eD,setError:ex,setFocus:(e,t={})=>{let r=p(n,e),i=r&&r._f;if(i){let e=i.refs?i.refs[0]:i.ref;e.focus&&(e.focus(),t.shouldSelect&&j(e.select)&&e.select())}},getFieldState:eS};return{...eB,formControl:eB}}(e);t.current={...i,formState:n}}let c=t.current.control;return c._options=e,D(()=>{let e=c._subscribe({formState:c._proxyFormState,callback:()=>d({...c._formState}),reRenderRoot:!0});return d(e=>({...e,isReady:!0})),c._formState.isReady=!0,e},[c]),i.useEffect(()=>c._disableForm(e.disabled),[c,e.disabled]),i.useEffect(()=>{e.mode&&(c._options.mode=e.mode),e.reValidateMode&&(c._options.reValidateMode=e.reValidateMode)},[c,e.mode,e.reValidateMode]),i.useEffect(()=>{e.errors&&(c._setErrors(e.errors),c._focusError())},[c,e.errors]),i.useEffect(()=>{e.shouldUnregister&&c._subjects.state.next({values:c._getWatch()})},[c,e.shouldUnregister]),i.useEffect(()=>{if(c._proxyFormState.isDirty){let e=c._getDirty();e!==n.isDirty&&c._subjects.state.next({isDirty:e})}},[c,n.isDirty]),i.useEffect(()=>{e.values&&!U(e.values,r.current)?(c._reset(e.values,c._options.resetOptions),r.current=e.values,d(e=>({...e}))):c._resetDefaultValues()},[c,e.values]),i.useEffect(()=>{c._state.mount||(c._setValid(),c._state.mount=!0),c._state.watch&&(c._state.watch=!1,c._subjects.state.next({...c._formState})),c._removeUnmounted()}),t.current.formState=k(n,c),t.current}}}]);