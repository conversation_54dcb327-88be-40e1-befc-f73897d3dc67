'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { apiClient } from '@shared/api';
import { useDebounce } from '@shared/hooks/useDebounce';
import type { GetDriverDTO } from '@entities/users/interface';
import type { DriverSearchFilters, UseDriverSearchResult } from '../types';

/**
 * Хук для поиска водителей
 * Отвечает только за логику поиска и фильтрации
 */
export const useDriverSearch = (): UseDriverSearchResult => {
  // Состояния поиска
  const [searchQuery, setSearchQuery] = useState('');
  const [searchField, setSearchField] = useState<'fullName' | 'phoneNumber'>('fullName');
  const [driversList, setDriversList] = useState<GetDriverDTO[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Состояние для фильтров
  const [showFilters, setShowFilters] = useState(false);
  const [activeFilters, setActiveFilters] = useState<DriverSearchFilters>({});

  // Мемоизация фильтров для стабильности
  const memoizedActiveFilters = useMemo(() => activeFilters, [JSON.stringify(activeFilters)]);

  // Дебаунсинг поиска
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Поиск водителей
  const searchDrivers = useCallback(async () => {
    // Оптимизация: не делаем запрос если поисковый запрос слишком короткий
    if (debouncedSearchQuery && debouncedSearchQuery.length < 2) {
      return;
    }

    setIsLoading(true);
    try {
      const params = new URLSearchParams();

      params.append('Size', '20');

      // Добавляем поисковые параметры только если есть текст
      if (debouncedSearchQuery && debouncedSearchQuery.length >= 2) {
        if (searchField === 'fullName') {
          params.append('FullName', debouncedSearchQuery);
          params.append('FullNameOp', 'Contains');
        } else {
          params.append('PhoneNumber', debouncedSearchQuery);
          params.append('PhoneNumberOp', 'Contains');
        }
      }

      // Фильтр по роли - только водители
      params.append('Role', 'Driver');

      // Добавляем активные фильтры
      Object.entries(memoizedActiveFilters).forEach(([key, value]) => {
        if (value) {
          if (key === 'DrivingExperience') {
            params.append('DrivingExperience', value);
            params.append('DrivingExperienceOp', 'GreaterThanOrEqual');
          } else {
            params.append(key, value);
          }
        }
      });

      const response = await apiClient.get<any>(`/User/Driver?${params.toString()}`);

      // Проверяем формат ответа
      if (response.data && Array.isArray(response.data.data)) {
        setDriversList(response.data.data);
      } else if (Array.isArray(response.data)) {
        setDriversList(response.data);
      } else {
        setDriversList([]);
      }
    } catch (error) {
      console.error('Ошибка поиска водителей:', error);
      setDriversList([]);
    } finally {
      setIsLoading(false);
    }
  }, [debouncedSearchQuery, searchField, memoizedActiveFilters]);

  // Запускаем поиск при изменении запроса или фильтров
  useEffect(() => {
    searchDrivers();
  }, [searchDrivers]);

  // Загружаем начальный список водителей при монтировании
  useEffect(() => {
    const loadInitialDrivers = async () => {
      setIsLoading(true);
      try {
        const params = new URLSearchParams();

        params.append('Size', '20');
        params.append('Role', 'Driver');

        const response = await apiClient.get<any>(`/User/Driver?${params.toString()}`);

        if (response.data && Array.isArray(response.data.data)) {
          setDriversList(response.data.data);
        } else if (Array.isArray(response.data)) {
          setDriversList(response.data);
        }
      } catch (error) {
        console.error('Ошибка загрузки начального списка водителей:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialDrivers();
  }, []);

  return {
    driversList,
    isLoading,
    searchQuery,
    setSearchQuery,
    searchField,
    setSearchField,
    activeFilters,
    setActiveFilters,
    showFilters,
    setShowFilters,
  };
};
