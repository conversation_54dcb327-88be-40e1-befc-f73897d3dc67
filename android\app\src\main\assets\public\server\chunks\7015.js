exports.id=7015,exports.ids=[7015],exports.modules={8413:(e,r,o)=>{"use strict";o.r(r),o.d(r,{ResetPasswordForm:()=>s});let s=(0,o(33952).registerClientReference)(function(){throw Error("Attempted to call ResetPasswordForm() from the server but ResetPasswordForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\auth\\reset-password\\ui\\ResetPasswordForm.tsx","ResetPasswordForm")},13550:(e,r,o)=>{"use strict";o.r(r),o.d(r,{ForgotPasswordForm:()=>x});var s=o(13486),l=o(93958),a=o(49989),t=o.n(a);o(60159);var i=o(85626),n=o(96274),c=o(34080),m=o(80988),d=o(43655),u=o(35925);let x=()=>{let{forgotPassword:e,isLoading:r,success:o,fieldErrors:a,clearFieldError:x}=(0,u.G7)(),{register:f,handleSubmit:p,formState:{errors:b}}=(0,i.mN)({resolver:(0,l.u)(d.E9)});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.wh,{isVisible:r,message:"Отправка запроса на восстановление пароля..."}),(0,s.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,s.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Восстановление пароля"}),(0,s.jsx)("p",{children:"Введите email, указанный при регистрации"})]}),(0,s.jsxs)("form",{onSubmit:p(r=>e(r)),className:"flex flex-col gap-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,s.jsx)("input",{id:"email",type:"email",...f("email",{onChange:()=>{a.email&&x("email")}}),className:`block w-full px-2 py-1  text-xs border ${b.email||a.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r||o}),(0,s.jsx)(c.yI,{message:b.email?.message})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,s.jsx)("button",{type:"submit",disabled:r||o,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:r?"Отправка...":"Отправить код"}),(0,s.jsx)(t(),{href:n.vc.AUTH.LOGIN,className:`w-full block ${o?"pointer-events-none":""}`,children:(0,s.jsx)("button",{type:"button",disabled:o,className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-neutral-300)] rounded-md shadow-sm text-sm font-medium bg-transparent hover:bg-[color:var(--color-neutral-200)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-neutral-300)] disabled:opacity-50 disabled:cursor-not-allowed",children:"Вернуться к входу"})})]})]})]})]})}},34080:(e,r,o)=>{"use strict";o.d(r,{yI:()=>s.yI}),o(13486),o(2984),o(60159),o(89327);var s=o(50534);o(80988),o(20882);o(64651)},43655:(e,r,o)=>{"use strict";o.d(r,{E9:()=>s.E9,rA:()=>s.rA,oW:()=>s.oW}),o(75385);var s=o(66512);let l={ADMIN:"Admin",PARTNER:"Partner",DRIVER:"Driver",TERMINAL:"Terminal",OPERATOR:"Operator"};l.ADMIN,l.PARTNER,l.DRIVER,l.TERMINAL,l.OPERATOR,l.TERMINAL,l.DRIVER},44156:(e,r,o)=>{Promise.resolve().then(o.bind(o,59900)),Promise.resolve().then(o.bind(o,95304)),Promise.resolve().then(o.bind(o,57097)),Promise.resolve().then(o.bind(o,8413))},52164:(e,r,o)=>{"use strict";o.d(r,{Q:()=>s.LoginForm});var s=o(95304)},53884:(e,r,o)=>{Promise.resolve().then(o.bind(o,13550)),Promise.resolve().then(o.bind(o,64390)),Promise.resolve().then(o.bind(o,58747)),Promise.resolve().then(o.bind(o,63935))},57097:(e,r,o)=>{"use strict";o.r(r),o.d(r,{PartnerRegisterForm:()=>s});let s=(0,o(33952).registerClientReference)(function(){throw Error("Attempted to call PartnerRegisterForm() from the server but PartnerRegisterForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\auth\\partner-register\\ui\\PartnerRegisterForm.tsx","PartnerRegisterForm")},58747:(e,r,o)=>{"use strict";o.r(r),o.d(r,{PartnerRegisterForm:()=>p});var s=o(13486),l=o(93958),a=o(49989),t=o.n(a),i=o(60159),n=o(85626),c=o(96274),m=o(89327),d=o(50534),u=o(80988),x=o(43655),f=o(35925);let p=()=>{let{registerPartner:e,isLoading:r,success:o,fieldErrors:a,clearFieldError:p}=(0,f.dO)(),[b,g]=(0,i.useState)(!1),{register:h,handleSubmit:v,formState:{errors:w}}=(0,n.mN)({resolver:(0,l.u)(x.rA)});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.wh,{isVisible:r,message:"Выполняется регистрация партнера..."}),(0,s.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,s.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Регистрация партнера"}),(0,s.jsx)("p",{children:"Заполните форму для регистрации в качестве партнера"})]}),(0,s.jsxs)("form",{onSubmit:v(r=>e(r)),className:"flex flex-col gap-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,s.jsx)("input",{id:"email",type:"email",...h("email",{onChange:()=>{a.email&&p("email")}}),className:`block w-full px-2 py-1  text-xs border ${w.email||a.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r}),(0,s.jsx)(d.yI,{message:w.email?.message}),(0,s.jsx)("label",{htmlFor:"password",className:"block text-xs font-medium",children:"Пароль"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",type:b?"text":"password",...h("password"),className:`block w-full px-3 py-2 pr-10  border ${w.password?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3",onClick:()=>g(!b),tabIndex:-1,children:b?(0,s.jsx)(m.aR,{size:20}):(0,s.jsx)(m.bM,{size:20})})]}),(0,s.jsx)(d.yI,{message:w.password?.message}),(0,s.jsx)("label",{htmlFor:"fullName",className:"block text-xs font-medium",children:"ФИО"}),(0,s.jsx)("input",{id:"fullName",type:"text",...h("fullName"),className:`block w-full px-2 py-1  text-xs border ${w.fullName?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r}),(0,s.jsx)(d.yI,{message:w.fullName?.message}),(0,s.jsx)("label",{htmlFor:"companyName",className:"block text-xs font-medium",children:"Название компании"}),(0,s.jsx)("input",{id:"companyName",type:"text",...h("companyName"),className:`block w-full px-2 py-1  text-xs border ${w.companyName?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r}),(0,s.jsx)(d.yI,{message:w.companyName?.message}),(0,s.jsx)("label",{htmlFor:"legalAddress",className:"block text-xs font-medium",children:"Юридический адрес"}),(0,s.jsx)("input",{id:"legalAddress",type:"text",...h("legalAddress"),className:`block w-full px-2 py-1  text-xs border ${w.legalAddress?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r}),(0,s.jsx)(d.yI,{message:w.legalAddress?.message})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,s.jsx)("button",{type:"submit",disabled:r||o,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:r?"Регистрация...":"Зарегистрироваться"}),(0,s.jsx)(t(),{href:c.vc.AUTH.LOGIN,className:"w-full block",children:(0,s.jsx)("button",{type:"button",className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-neutral-300)] rounded-md shadow-sm text-sm font-medium  bg-transparent hover:bg-[color:var(--color-neutral-200)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-neutral-300)]",children:"Вернуться к входу"})})]})]})]})]})}},59900:(e,r,o)=>{"use strict";o.r(r),o.d(r,{ForgotPasswordForm:()=>s});let s=(0,o(33952).registerClientReference)(function(){throw Error("Attempted to call ForgotPasswordForm() from the server but ForgotPasswordForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\auth\\forgot-password-form\\ui\\ForgotPasswordForm.tsx","ForgotPasswordForm")},63935:(e,r,o)=>{"use strict";o.r(r),o.d(r,{ResetPasswordForm:()=>p});var s=o(13486),l=o(93958),a=o(49989),t=o.n(a),i=o(2984),n=o(60159),c=o(85626),m=o(96274),d=o(89327),u=o(34080),x=o(43655),f=o(35925);let p=()=>{let{resetPassword:e,isLoading:r,success:o,fieldErrors:a,clearFieldError:p}=(0,f.Qc)(),[b,g]=(0,n.useState)(!1),h=(0,i.useSearchParams)().get("email")||"",{register:v,handleSubmit:w,formState:{errors:y}}=(0,c.mN)({resolver:(0,l.u)(x.oW)});return(0,s.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,s.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Сброс пароля"}),(0,s.jsx)("p",{children:"Введите код, полученный на email, и новый пароль"})]}),(0,s.jsxs)("form",{onSubmit:w(r=>e(r)),className:"flex flex-col gap-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,s.jsx)("input",{id:"email",type:"email",...v("email",{onChange:()=>{a.email&&p("email")}}),className:`block w-full px-2 py-1  text-xs border ${y.email||a.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r||o||!!h}),(0,s.jsx)(u.yI,{message:y.email?.message}),(0,s.jsx)("label",{htmlFor:"resetCode",className:"block text-xs font-medium",children:"Код сброса пароля"}),(0,s.jsx)("input",{id:"resetCode",type:"text",...v("resetCode",{onChange:()=>{a.resetCode&&p("resetCode")}}),className:`block w-full px-2 py-1  text-xs border ${y.resetCode||a.resetCode?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r||o}),(0,s.jsx)(u.yI,{message:y.resetCode?.message}),(0,s.jsx)("label",{htmlFor:"newPassword",className:"block text-xs font-medium",children:"Новый пароль"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"newPassword",type:b?"text":"password",...v("newPassword",{onChange:()=>{a.newPassword&&p("newPassword")}}),className:`block w-full px-3 py-2 pr-10  border ${y.newPassword||a.newPassword?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:r||o}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3  hover:",onClick:()=>g(!b),tabIndex:-1,children:b?(0,s.jsx)(d.aR,{size:20}):(0,s.jsx)(d.bM,{size:20})})]}),(0,s.jsx)(u.yI,{message:y.newPassword?.message})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,s.jsx)("button",{type:"submit",disabled:r||o,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-primary)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:r?"Отправка...":"Сбросить пароль"}),(0,s.jsx)(t(),{href:m.vc.AUTH.LOGIN,className:"w-full block",children:(0,s.jsx)("button",{type:"button",className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-neutral-300)] rounded-md shadow-sm text-sm font-medium  bg-transparent hover:bg-[color:var(--color-neutral-200)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-neutral-300)]",children:"Вернуться к входу"})})]})]})]})}},64390:(e,r,o)=>{"use strict";o.r(r),o.d(r,{LoginForm:()=>h});var s=o(13486),l=o(93958),a=o(49989),t=o.n(a),i=o(2984),n=o(60159),c=o(85626),m=o(50947),d=o(65363),u=o(49272),x=o(99087),f=o(66512),p=o(58277);let b=(0,n.lazy)(()=>Promise.resolve().then(o.bind(o,33504))),g=(0,n.lazy)(()=>Promise.resolve().then(o.bind(o,19263))),h=({defaultValues:e,autoLogin:r=!1})=>{let o=(0,i.useRouter)(),{login:a,isLoading:h,fieldErrors:v,clearFieldError:w}=(0,p.v)(),[y,j]=(0,n.useState)(!1),[N,F]=(0,n.useState)(!1),[P,R]=(0,n.useState)(!1),{register:C,handleSubmit:k,formState:{errors:A},getValues:E}=(0,c.mN)({resolver:(0,l.u)(f.X5),defaultValues:e});return(0,n.useEffect)(()=>{r&&e?.email&&e?.password&&!h&&!P&&(R(!0),a(E()))},[r,e?.email,e?.password,h,P,E,a]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.w,{isVisible:h,message:"Выполняется вход в систему..."}),(0,s.jsxs)("div",{className:"w-full max-w-md p-8 flex flex-col gap-8  rounded-lg shadow-md",children:[(0,s.jsxs)("div",{className:"text-center flex flex-col gap-2",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Вход в систему"}),(0,s.jsx)("p",{children:"Введите свои учетные данные для входа"})]}),(0,s.jsxs)("form",{onSubmit:k(e=>a(e)),className:"flex flex-col gap-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-xs font-medium",children:"Email"}),(0,s.jsx)("input",{id:"email",type:"email",...C("email",{onChange:()=>{v.email&&w("email")}}),className:`block w-full px-2 py-1  text-xs border ${A.email||v.email?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:h}),(0,s.jsx)(d.y,{message:A.email?.message}),(0,s.jsx)("label",{htmlFor:"password",className:"block text-xs font-medium",children:"Пароль"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",type:y?"text":"password",...C("password",{onChange:()=>{v.password&&w("password")}}),className:`block w-full px-3 py-2 pr-10  border ${A.password||v.password?"border-[color:var(--color-error)]":"border-[color:var(--color-neutral-300)]"} rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[color:var(--color-primary)] focus:border-[color:var(--color-primary)]`,disabled:h}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3  hover:",onClick:()=>j(!y),tabIndex:-1,children:(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)("div",{className:"w-5 h-5"}),children:y?(0,s.jsx)(g,{size:20}):(0,s.jsx)(b,{size:20})})})]}),(0,s.jsx)(d.y,{message:A.password?.message})]}),(0,s.jsx)("div",{className:"flex justify-end",children:N?(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-[color:var(--color-primary)] transition-opacity duration-300",children:[(0,s.jsx)(u.Z,{size:"sm",className:"text-[color:var(--color-primary)]"}),(0,s.jsx)("span",{children:"Переход на страницу сброса пароля..."})]}):(0,s.jsx)("a",{href:m.vc.AUTH.FORGOT_PASSWORD,onClick:e=>{e.preventDefault(),F(!0),setTimeout(()=>{o.push(m.vc.AUTH.FORGOT_PASSWORD)},1e3)},className:`text-sm text-[color:var(--color-primary)] hover:underline ${N?"pointer-events-none opacity-50":""}`,children:"Забыли пароль?"})}),(0,s.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,s.jsx)("button",{type:"submit",disabled:h,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[color:var(--color-brand)] hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)] disabled:opacity-50 disabled:cursor-not-allowed",children:h?"Вход...":"Войти"}),(0,s.jsx)(t(),{href:m.vc.AUTH.REGISTER,className:"w-full block",children:(0,s.jsx)("button",{type:"button",className:"w-full flex justify-center py-2 px-4 border border-[color:var(--color-primary)] rounded-md shadow-sm text-sm font-medium text-[color:var(--color-brand)] bg-transparent hover:text-white hover:bg-[color:var(--color-primary)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[color:var(--color-primary)]",children:"Зарегистрироваться"})})]})]})]})]})}},66512:(e,r,o)=>{"use strict";o.d(r,{E9:()=>t,X5:()=>l,oW:()=>i,rA:()=>a});var s=o(43888);let l=s.z.object({email:s.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}),password:s.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"})}),a=s.z.object({email:s.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"}),password:s.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"}),fullName:s.z.string().min(1,{message:"ФИО обязательно"}).max(255,{message:"ФИО не должно превышать 255 символов"}),companyName:s.z.string().min(1,{message:"Название компании обязательно"}).max(255,{message:"Название компании не должно превышать 255 символов"}),legalAddress:s.z.string().min(1,{message:"Юридический адрес обязателен"}).max(500,{message:"Юридический адрес не должен превышать 500 символов"})}),t=s.z.object({email:s.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"})}),i=s.z.object({email:s.z.string().min(1,{message:"Email обязателен"}).email({message:"Некорректный формат email"}).max(255,{message:"Email не должен превышать 255 символов"}),resetCode:s.z.string().min(1,{message:"Код сброса пароля обязателен"}),newPassword:s.z.string().min(6,{message:"Пароль должен содержать минимум 6 символов"}).max(100,{message:"Пароль не должен превышать 100 символов"})})},72437:(e,r,o)=>{"use strict";o.d(r,{F2:()=>l.ForgotPasswordForm,az:()=>s.PartnerRegisterForm,FN:()=>a.ResetPasswordForm}),o(52164);var s=o(57097),l=o(59900),a=o(8413)},95304:(e,r,o)=>{"use strict";o.r(r),o.d(r,{LoginForm:()=>s});let s=(0,o(33952).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\games\\compass2.0\\packages\\widgets\\auth\\login\\ui\\LoginForm.tsx","LoginForm")}};